import json
import logging
import re
from typing import Dict, List, Optional, Tuple, Any
import os
from logger_config import log_with_request_id  # 从新模块导入
from getAnswer import SmartChatbot, get_document_chunks, get_vidpdf_content, count_text_length, get_matching_ids
import duckduckgosearch
import uuid
request_id = str(uuid.uuid4())[:8]
class QuestionGenerator(SmartChatbot):
    def __init__(self, api_key: str, api_key_rag: str, api_key_type: str = "deepseek", folder: str = None):
        super().__init__(api_key=api_key, api_key_rag=api_key_rag, api_key_type=api_key_type, folder=folder)

    def _get_subject_from_folder(self):
        """从文件夹名称获取学科名称"""
        if not self.folder:
            return "物理学"
        
        # 从文件夹路径中提取最后一个文件夹名
        folder_name = self.folder.rstrip("/\\").split("/")[-1]
        return folder_name.replace("_", " ")


    def _parse_generated_questions(self, generated_text: str) -> Dict:
        """解析大模型生成的不规范JSON文本，采用更健壮的匹配方式"""
        questions = []
        
        # 预处理文本，使其更容易解析
        def clean_text(text: str) -> str:
            # 统一引号格式
            text = re.sub(r'[\u201c\u201d\u2018\u2019]', '"', text)
            # 处理中文冒号
            text = re.sub(r'：', ':', text)
            # 处理键值对格式
            text = re.sub(r'([{,])\s*(\w+)\s*:', r'\1"\2":', text)
            # 清理多余的空白字符
            text = re.sub(r'\s+', ' ', text)
            return text.strip()
        
        cleaned_text = clean_text(generated_text)
        
        # 使用更灵活的问题块匹配模式
        question_pattern = re.compile(
            r'{'
            r'[^{]*?"id"\s*:\s*"?(\d+)"?[^{]*?'
            r'"question"\s*:\s*"([^"]+)"[^{]*?'
            r'"options"\s*:\s*\[((?:[^[\]]+|\[[^[\]]*\])*)\][^{]*?'
            r'"correct_answer"\s*:\s*"([^"]+)"[^{]*?'
            r'"explanation"\s*:\s*"([^"]+)"'
            r'[^}]*}',
            re.DOTALL
        )
        
        # 匹配所有问题块
        for match in question_pattern.finditer(cleaned_text):
            try:
                # 提取基本信息
                question_id, question_text, options_text, correct_answer, explanation = match.groups()
                
                # 处理选项
                options_pattern = re.compile(r'"([^"]+)"')
                options = [opt.strip() for opt in options_pattern.findall(options_text)]
                
                # 如果没有找到选项，尝试其他格式匹配
                if not options:
                    options = [opt.strip() for opt in options_text.split(',') if opt.strip()]
                
                # 数据验证和补全
                if not options:
                    options = ["A. 选项A", "B. 选项B", "C. 选项C", "D. 选项D"]
                
                if not correct_answer or correct_answer not in "ABCD":
                    correct_answer = "A"
                
                # 构建问题对象
                question_obj = {
                    "id": question_id,
                    "question": question_text.strip(),
                    "options": options[:4],  # 确保只有4个选项
                    "correct_answer": correct_answer,
                    "explanation": explanation.strip()
                }
                
                # 验证必要字段和内容有效性
                if all(question_obj.values()) and self._is_valid_question_content(question_obj):
                    questions.append(question_obj)
                    
            except Exception as e:
                log_with_request_id(f"解析单个问题时出错: {str(e)}", level=logging.WARNING, request_id=request_id)
                continue
        
        # 如果没有成功解析任何问题，尝试备用解析方法
        if not questions:
            try:
                # 使用更宽松的模式匹配问题块
                fallback_pattern = re.compile(
                    r'问题.*?[:：]\s*([^答案]+)'
                    r'(?:选项.*?[:：]\s*([^答案]+))?'
                    r'(?:答案.*?[:：]\s*([^解析]+))?'
                    r'(?:解析.*?[:：]\s*(.+?))?(?=问题|\Z)',
                    re.DOTALL
                )
                
                for idx, match in enumerate(fallback_pattern.finditer(generated_text), 1):
                    question_text = match.group(1).strip()
                    options_text = match.group(2) or "A.选项A B.选项B C.选项C D.选项D"
                    correct_answer = match.group(3) or "A"
                    explanation = match.group(4) or "暂无解析"
                    
                    # 处理选项
                    options = re.findall(r'[A-D]\s*[.、]\s*([^A-D]+)', options_text)
                    if not options:
                        options = ["选项A", "选项B", "选项C", "选项D"]
                    
                    questions.append({
                        "id": str(idx),
                        "question": question_text,
                        "options": [f"{chr(65+i)}. {opt}" for i, opt in enumerate(options[:4])],
                        "correct_answer": correct_answer.strip()[0] if correct_answer else "A",
                        "explanation": explanation.strip()
                    })
                    
            except Exception as e:
                log_with_request_id(f"备用解析方法失败: {str(e)}", level=logging.ERROR, request_id=request_id)
        
        return {"questions": questions} if questions else {"error": "无法解析任何题目"}


    def _enhance_explanation_with_knowledge_base(self, question: str, explanation: str) -> str:
        """使用知识库内容增强题目解析"""
        try:
            # 使用题目内容作为检索关键词获取知识库内容
            print(f"self.enhance_explanation_with_knowledge_base folder: {self.folder},self.api_key_rag: {self.api_key_rag}")
            kb_content = get_vidpdf_content(self.folder, question, self.api_key_rag)
            print(f"kb_content: {kb_content}")

            # 如果中文内容少于100字，尝试英文查询
            if not kb_content or count_text_length(kb_content) < 100:
                # 使用WebAgent将问题翻译成英文
                WebAgentClass = duckduckgosearch.Web_Agent
                web_agent = WebAgentClass(api_key=self.api_key, api_key_type=self.api_key_type)
                
                system_prompt = """你是一位专业的翻译专家。请将给定的中文问题翻译成英文。
翻译时注意：
1. 保持专业术语的准确性
2. 使用自然的英文表达
3. 只返回翻译结果，不要有任何其他内容
"""
                prompt = f"请将以下问题翻译成英文：\n{question}"
                
                english_question, _ = web_agent._generate_summary_with_system_prompt(
                    system_prompt,
                    prompt,
                    enable_thinking=False
                )
                
                # 获取英文内容
                english_kb_content = get_vidpdf_content(self.folder, english_question, self.api_key_rag)
                print(f"english_kb_content: {english_kb_content}")
                
                # 合并中英文内容
                if english_kb_content:
                    kb_content = f"{kb_content}\n\nEnglish Content:\n{english_kb_content}"
                    print(f"Combined kb_content length: {count_text_length(kb_content)}")

            # 构建提示词让模型融合原解析和知识库内容
            if kb_content and count_text_length(kb_content) > 100:
                system_prompt = """你是一位经验丰富的教育专家。请根据提供的原始解析内容和知识库补充内容，生成一个更详细的解析。新的解析应该：\n1. 包含原始解析的核心观点\n2. 融入知识库中的相关概念和原理\n3. 如果涉及公式，确保所有公式使用LaTeX格式：$...$ 或 $$...$$如$\\frac{q}{12\\epsilon_0}$，禁止使用反斜杠表示公式字符如\\(\\frac{q}{12\\epsilon_0}\\) \n"""
                prompt = f"""
题目：
{question}

原始解析：
{explanation}

知识库补充内容：
{kb_content}

请生成一个更详细的中文解析：
"""
            else:
                system_prompt = """你是一位经验丰富的教育专家。请根据提供的原始解析内容，生成一个更详细的解析。新的解析应该：\n1. 包含原始解析的核心观点\n2. 融入相关概念和原理\n3. 如果涉及公式，确保所有公式使用LaTeX格式：$...$ 或 $$...$$如$\\frac{q}{12\\epsilon_0}$，禁止使用反斜杠表示公式字符如\\(\\frac{q}{12\\epsilon_0}\\)\n"""
                prompt = f"""
题目：
{question}

原始解析：
{explanation}

请生成一个更详细的中文解析：
"""
            # 初始化WebAgent
            WebAgentClass = duckduckgosearch.Web_Agent
            web_agent = WebAgentClass(api_key=self.api_key, api_key_type=self.api_key_type)
            
            enhanced_explanation, _ = web_agent._generate_summary_with_system_prompt(
                system_prompt,
                prompt,
                enable_thinking=True
            )
            return enhanced_explanation
        except Exception as e:
            print(f"增强解析时出错: {str(e)}")
            return explanation
        
        return explanation

    def _is_valid_question_content(self, question: Dict) -> bool:
        """检查选择题内容是否有效（不包含占位符文本）"""
        # 检查题目内容
        question_text = question.get("question", "").lower()
        if not question_text.strip():
            return False

        # 占位符文本列表
        placeholder_texts = [
            "题目内容", "question content", "题目", "问题内容",
            "根据课程内容提出的具体问题", "具体问题"
        ]

        for placeholder in placeholder_texts:
            if placeholder in question_text:
                log_with_request_id(f"发现占位符文本题目: {question_text[:50]}", level=logging.WARNING, request_id=request_id)
                return False

        # 检查选项内容
        options = question.get("options", [])
        for option in options:
            option_text = str(option).lower()
            if any(placeholder in option_text for placeholder in ["选项a", "选项b", "选项c", "选项d", "option a", "option b"]):
                log_with_request_id(f"发现占位符选项: {option}", level=logging.WARNING, request_id=request_id)
                return False

        # 检查解析内容
        explanation = question.get("explanation", "").lower()
        if any(placeholder in explanation for placeholder in ["详细解析", "解析内容", "explanation"]):
            log_with_request_id(f"发现占位符解析: {explanation[:50]}", level=logging.WARNING, request_id=request_id)
            return False

        return True

    def _is_valid_calculation_question_content(self, question: Dict) -> bool:
        """检查计算题内容是否有效（不包含占位符文本）"""
        # 检查题目内容
        question_text = question.get("question", "").lower()
        if not question_text.strip():
            return False

        # 占位符文本列表 - 更严格的匹配，避免误判
        placeholder_texts = [
            "题目内容", "question content", "包含已知条件和要求计算的内容"
        ]

        for placeholder in placeholder_texts:
            if placeholder in question_text:
                log_with_request_id(f"发现占位符文本计算题: {question_text[:50]}", level=logging.WARNING, request_id=request_id)
                return False

        # 检查解题步骤 - 更严格的匹配，避免误判
        solution_steps = question.get("solution_steps", [])
        for step in solution_steps:
            if isinstance(step, dict):
                description = step.get("description", "").lower()
                if any(placeholder in description for placeholder in [
                    "解题步骤说明", "具体的解题步骤描述", "step description"
                ]):
                    log_with_request_id(f"发现占位符解题步骤: {description[:50]}", level=logging.WARNING, request_id=request_id)
                    return False

        # 检查最终答案
        final_answer = question.get("final_answer", "")
        if isinstance(final_answer, dict):
            # 兼容旧格式
            value = str(final_answer.get("value", "")).lower()
            unit = str(final_answer.get("unit", "")).lower()
            answer_text = f"{value} {unit}".strip().lower()
        else:
            answer_text = str(final_answer).lower()

        if any(placeholder in answer_text for placeholder in ["最终答案", "具体的数值答案", "具体的单位"]):
            log_with_request_id(f"发现占位符答案: {answer_text}", level=logging.WARNING, request_id=request_id)
            return False

        # 检查关键知识点 - 更严格的匹配，避免误判
        key_points = question.get("key_points", [])
        for point in key_points:
            point_text = str(point).lower()
            if any(placeholder in point_text for placeholder in [
                "关键知识点", "本题的关键知识点", "具体的知识点名称"
            ]):
                log_with_request_id(f"发现占位符知识点: {point}", level=logging.WARNING, request_id=request_id)
                return False

        return True

    def _validate_and_normalize_choice_questions(self, questions_data: Dict) -> Dict:
        """验证和标准化选择题数据格式"""
        try:
            if "questions" not in questions_data:
                questions_data["questions"] = []

            normalized_questions = []
            for i, question in enumerate(questions_data.get("questions", [])):
                # 确保必要字段存在
                normalized_question = {
                    "id": str(question.get("id", i + 1)),
                    "question": str(question.get("question", "")).strip(),
                    "options": [],
                    "correct_answer": str(question.get("correct_answer", "A")).strip().upper(),
                    "explanation": str(question.get("explanation", "")).strip()
                }

                # 处理选项
                options = question.get("options", [])
                if isinstance(options, list):
                    # 确保选项格式正确
                    for j, option in enumerate(options[:4]):  # 最多4个选项
                        option_str = str(option).strip()
                        # 如果选项没有字母前缀，添加它
                        if not option_str.startswith(f"{chr(65+j)}."):
                            option_str = f"{chr(65+j)}. {option_str}"
                        normalized_question["options"].append(option_str)

                # 确保有4个选项
                while len(normalized_question["options"]) < 4:
                    letter = chr(65 + len(normalized_question["options"]))
                    normalized_question["options"].append(f"{letter}. 选项{letter}")

                # 验证正确答案
                if normalized_question["correct_answer"] not in "ABCD":
                    normalized_question["correct_answer"] = "A"

                # 只添加有效的题目（过滤占位符文本）
                if self._is_valid_question_content(normalized_question):
                    normalized_questions.append(normalized_question)

            questions_data["questions"] = normalized_questions
            return questions_data

        except Exception as e:
            log_with_request_id(f"验证选择题数据时出错: {str(e)}", level=logging.WARNING, request_id=request_id)
            return questions_data

    def _validate_and_normalize_calculation_questions(self, questions_data: Dict) -> Dict:
        """验证和标准化计算题数据格式"""
        try:
            if "calculation_questions" not in questions_data:
                questions_data["calculation_questions"] = []

            normalized_questions = []
            for i, question in enumerate(questions_data.get("calculation_questions", [])):
                # 确保必要字段存在
                normalized_question = {
                    "id": str(question.get("id", i + 1)),
                    "question": str(question.get("question", "")).strip(),
                    "solution_steps": [],
                    "final_answer": "",
                    "key_points": []
                }

                # 处理solution_steps
                solution_steps = question.get("solution_steps", [])
                if isinstance(solution_steps, str):
                    try:
                        solution_steps = json.loads(solution_steps)
                    except json.JSONDecodeError:
                        solution_steps = []

                if isinstance(solution_steps, list):
                    normalized_steps = []
                    for j, step in enumerate(solution_steps):
                        if isinstance(step, dict):
                            normalized_step = {
                                "step_number": str(step.get("step_number", j + 1)),
                                "description": str(step.get("description", "")).strip(),
                                "calculation": str(step.get("calculation", "")).strip()
                            }
                            normalized_steps.append(normalized_step)
                    normalized_question["solution_steps"] = normalized_steps

                # 处理final_answer
                final_answer = question.get("final_answer", "")
                if isinstance(final_answer, dict):
                    # 兼容旧格式，合并value和unit
                    value = str(final_answer.get("value", "")).strip()
                    unit = str(final_answer.get("unit", "")).strip()
                    normalized_question["final_answer"] = f"{value} {unit}".strip()
                else:
                    normalized_question["final_answer"] = str(final_answer).strip()

                # 处理key_points
                key_points = question.get("key_points", [])
                if isinstance(key_points, str):
                    try:
                        key_points = json.loads(key_points)
                    except json.JSONDecodeError:
                        key_points = [key_points] if key_points.strip() else []

                if isinstance(key_points, list):
                    normalized_question["key_points"] = [str(point).strip() for point in key_points if str(point).strip()]

                # 只添加有效的题目（过滤占位符文本）
                is_valid = self._is_valid_calculation_question_content(normalized_question)
                log_with_request_id(f"题目 {normalized_question['id']} 验证结果: {is_valid}", level=logging.INFO, request_id=request_id)
                if not is_valid:
                    log_with_request_id(f"题目被过滤: {normalized_question['question'][:100]}", level=logging.WARNING, request_id=request_id)
                else:
                    normalized_questions.append(normalized_question)

            questions_data["calculation_questions"] = normalized_questions
            return questions_data

        except Exception as e:
            log_with_request_id(f"验证计算题数据时出错: {str(e)}", level=logging.WARNING, request_id=request_id)
            return questions_data

    def generate_questions(self,
                         dataset_ids: Optional[List[str]] = None,
                         document_ids: Optional[List[str]] = None,
                         num_questions: int = 5,
                         **kwargs) -> Dict:
        """
        根据视频内容生成题目和解析
        
        Args:
            dataset_ids: 数据集ID列表
            document_ids: 文档ID列表
            num_questions: 要生成的题目数量
            **kwargs: 其他参数
            
        Returns:
            Dict: 包含生成的题目、选项、答案和解析的字典
        """
        try:
            # 生成题目
            try:
                # 如果没有提供dataset_ids，使用默认值
                if not dataset_ids:
                    print(f"使用默认dataset_ids: {dataset_ids}")
                    return {"error": "未找到匹配的dataset_ids"}

                # 获取内容
                print(f"self.folder: {self.folder},self.api_key_rag: {self.api_key_rag}")
                content = ""
                try:
                    # 首先获取视频字幕内容，支持多个文档ID
                    if dataset_ids and document_ids:
                        # 如果document_ids是字符串，转换为列表
                        if isinstance(document_ids, str):
                            document_ids = [document_ids]

                        # 合并多个文档的内容
                        all_content = []
                        for doc_id in document_ids:
                            doc_content = get_document_chunks(
                                api_key_rag=self.api_key_rag,
                                base_url="http://localhost:8080",
                                dataset_id=dataset_ids[0],
                                document_id=doc_id,
                                keywords=None,
                                chunk_id=None,
                                offset=0,
                                limit=1024
                            )

                            if doc_content and doc_content.get('code') == 0:
                                doc_text = doc_content['data']['content']
                                if doc_text:
                                    all_content.append(doc_text)
                                    print(f"成功获取文档 {doc_id} 内容，长度: {count_text_length(doc_text)}")

                        # 合并所有文档内容
                        if all_content:
                            content = "\n\n=== 文档分隔 ===\n\n".join(all_content)
                            print(f"成功合并 {len(all_content)} 个文档内容，总长度: {count_text_length(content)}")
                except Exception as e:
                    print(f"获取内容时出错: {str(e)}")
                    return {"error": f"获取内容失败: {str(e)}"}

                if not content or content.strip() == "":
                    print("未找到有效的课程内容")
                    return {"error": "未找到有效的课程内容"}

                # 构建系统提示词
                subject = self._get_subject_from_folder()
                system_prompt = f"""你是一位经验丰富的{subject}教师。请根据提供的课程内容，生成{num_questions}道选择题。

每道题目应该：
1. 基于课程内容中的具体知识点，不要使用占位符或模板文本
2. 测试学生对关键概念的理解
3. 有4个选项(A,B,C,D)，每个选项都应该是具体的、有意义的内容
4. 只有一个正确答案
5. 包含详细的解析，解释为什么正确答案是正确的，其他选项为什么不正确
6. 如果涉及公式，确保所有公式使用LaTeX格式：$...$ 或 $$...$$如 $\\frac{{q}}{{12\\epsilon_0}}$，禁止使用反斜杠表示公式字符如\\(\\frac{{q}}{{12\\epsilon_0}}\\)

重要提醒：
- 绝对不要使用"题目内容"、"选项A"、"选项B"等占位符文本
- 每道题目都必须是基于课程内容的具体、真实的题目

请以JSON格式输出，严格按照以下格式：
{{
    "questions": [
        {{
            "id": "1",
            "question": "根据课程内容提出的具体问题",
            "options": [
                "A. 具体的选项内容1",
                "B. 具体的选项内容2",
                "C. 具体的选项内容3",
                "D. 具体的选项内容4"
            ],
            "correct_answer": "A",
            "explanation": "基于课程内容的详细解析"
        }}
    ]
}}
"""
                prompt = f"""
课程内容：
{content}

请用中文语言生成题目："""

                # 生成题目
                WebAgentClass = duckduckgosearch.Web_Agent
                web_agent = WebAgentClass(api_key=self.api_key, api_key_type=self.api_key_type)
                questions_json, totaltokens = web_agent._generate_summary_with_system_prompt(
                    system_prompt,
                    prompt,
                    enable_thinking=True
                )
                
                # 解析JSON
                questions_data = json.loads(questions_json)

                # 验证和标准化数据格式
                questions_data = self._validate_and_normalize_choice_questions(questions_data)

                # 增强每道题的解析
                for question in questions_data.get("questions", []):
                    question["explanation"] = self._enhance_explanation_with_knowledge_base(
                        question["question"],
                        question["explanation"],
                    )
                    log_with_request_id(f"Enhanced explanation for question {question.get('id', 'unknown')}", level=logging.INFO, request_id=request_id)

                questions_data["tokens_used"] = totaltokens
                # 添加课程名称信息
                questions_data["course_name"] = self._get_subject_from_folder()
                return questions_data
                
            except json.JSONDecodeError as e:
                log_with_request_id(f"JSON解析错误: {str(e)}", level=logging.INFO, request_id=request_id)

                # 进入容错解析流程
                log_with_request_id(f"进入容错解析流程", level=logging.INFO, request_id=request_id)
                questions_data = self._parse_generated_questions(questions_json)
                # 保留原有增强解析逻辑
                for question in questions_data.get("questions", []):
                    question["explanation"] = self._enhance_explanation_with_knowledge_base(
                        question["question"],
                        question["explanation"],
                    )

                # 添加课程名称信息
                questions_data["course_name"] = self._get_subject_from_folder()
                return questions_data

            except Exception as e:
                log_with_request_id(f"生成题目时出错: {str(e)}", level=logging.INFO, request_id=request_id)
                return {"error": f"生成题目失败: {str(e)}"}
                
        except Exception as e:
            log_with_request_id(f"题目生成过程出错: {str(e)}", level=logging.INFO, request_id=request_id)
            return {"error": f"题目生成过程失败: {str(e)}"}


    def _parse_calculation_questions(self, generated_text: str) -> Dict:
        """解析大模型生成的不规范计算题JSON文本，采用更健壮的匹配方式"""
        calculation_questions = []

        def clean_text(text: str) -> str:
            """预处理文本，使其更容易解析"""
            # 统一引号格式
            text = re.sub(r'[\u201c\u201d\u2018\u2019]', '"', text)
            # 处理中文冒号和其他标点
            text = re.sub(r'[：，；]', lambda x: {'：': ':', '，': ',', '；': ';'}[x.group()], text)
            # 处理LaTeX公式
            text = re.sub(r'\\\(|\\\)', '$', text)
            text = re.sub(r'\\\[|\\\]', '$$', text)
            # 处理键值对格式
            text = re.sub(r'([{,])\s*(\w+)\s*[:：]', r'\1"\2":', text)
            # 清理多余的空白字符
            text = re.sub(r'\s+', ' ', text)
            return text.strip()

        def extract_steps(steps_text: str) -> List[Dict]:
            """从文本中提取解题步骤"""
            steps = []
            try:
                # 首先尝试作为JSON解析
                try:
                    json_steps = json.loads(steps_text)
                    if isinstance(json_steps, list):
                        return json_steps
                except json.JSONDecodeError:
                    pass

                # 使用正则表达式匹配步骤 - 新的简化格式
                step_patterns = [
                    # 新的简化JSON格式
                    r'\{\s*"step_number"\s*:\s*"?(\d+)"?,\s*"description"\s*:\s*"([^"]+)",\s*"calculation"\s*:\s*"([^"]*)"\s*\}',
                    # 兼容旧的标准JSON格式
                    r'\{\s*"step_number"\s*:\s*"?(\d+)"?,\s*"description"\s*:\s*"([^"]+)",\s*"formula"\s*:\s*"([^"]*)",\s*"calculation"\s*:\s*"([^"]*)"\s*\}',
                    # 中文格式
                    r'步骤\s*(\d+)\s*[:：]\s*([^公式计算]+)(?:公式\s*[:：]\s*([^计算]*))?(?:计算\s*[:：]\s*([^步骤]*))?',
                    # 简单格式
                    r'(\d+)\.\s*([^=]+)(?:=\s*([^计算]*))?(?:计算[:：]\s*(.+?))?(?=\d+\.|$)'
                ]

                for i, pattern in enumerate(step_patterns):
                    matches = re.finditer(pattern, steps_text, re.DOTALL)
                    for match in matches:
                        if i == 0:  # 新的简化格式
                            step = {
                                "step_number": match.group(1),
                                "description": match.group(2).strip(),
                                "calculation": match.group(3).strip() if match.group(3) else ""
                            }
                        else:  # 其他格式，兼容旧格式
                            step = {
                                "step_number": match.group(1),
                                "description": match.group(2).strip(),
                                "calculation": match.group(4).strip() if len(match.groups()) >= 4 and match.group(4) else match.group(3).strip() if match.group(3) else ""
                            }
                        steps.append(step)
                    if steps:
                        break

            except Exception as e:
                log_with_request_id(f"解析步骤时出错: {str(e)}", level=logging.WARNING, request_id=request_id)

            # 如果没有找到任何步骤，创建一个默认步骤
            if not steps:
                steps = [{
                    "step_number": "1",
                    "description": "解题步骤",
                    "calculation": ""
                }]

            return steps

        def extract_final_answer(text: str) -> str:
            """提取最终答案，返回简化的字符串格式"""
            patterns = [
                # 新的简化JSON格式
                r'"final_answer"\s*:\s*"([^"]+)"',
                # 标准JSON格式 - 兼容旧格式
                r'"final_answer"\s*:\s*\{\s*"value"\s*:\s*"([^"]+)"\s*,\s*"unit"\s*:\s*"([^"]*)"\s*\}',
                # 中文格式 - 限制匹配范围
                r'(?:最终)?答案\s*[:：]\s*([^单位\n,}]+?)(?:\s*单位\s*[:：]\s*([^\n,}]*?))?(?=\s*[,}\n]|$)',
                # 简单格式 - 更精确的匹配
                r'(?:=|得)\s*([0-9.]+(?:\s*×\s*10\^?[+-]?\d+)?)\s*([a-zA-Z°/%²³\s]*?)(?=\s*[,}\n]|$)'
            ]

            for i, pattern in enumerate(patterns):
                match = re.search(pattern, text, re.DOTALL)
                if match:
                    if i == 0:  # 新的简化格式
                        answer = match.group(1).strip()
                    elif i == 1:  # 旧的复合格式
                        value = match.group(1).strip()
                        unit = match.group(2).strip() if match.group(2) else ""
                        answer = f"{value} {unit}".strip()
                    else:  # 其他格式
                        value = match.group(1).strip()
                        unit = match.group(2).strip() if len(match.groups()) > 1 and match.group(2) else ""
                        answer = f"{value} {unit}".strip()

                    # 验证提取的内容不包含JSON片段
                    if not ('"' in answer and ('solution_steps' in answer or 'key_points' in answer)):
                        return answer

            return ""

        def extract_key_points(text: str) -> List[str]:
            """提取关键知识点"""
            patterns = [
                # 标准JSON格式
                r'"key_points"\s*:\s*\[(.*?)\]',
                # 中文格式
                r'(?:关键|重要)(?:知识)?点\s*[:：]\s*(.*?)(?=\n|$)',
                # 列表格式
                r'(?:知识点|要点)\s*[:：]\s*(?:1\.|①|\(1\))\s*(.*?)(?=\n|$)'
            ]

            for pattern in patterns:
                match = re.search(pattern, text, re.DOTALL)
                if match:
                    points_text = match.group(1)
                    # 处理不同的分隔符
                    for separator in [',', '，', ';', '；', '\n']:
                        if separator in points_text:
                            return [point.strip().strip('"') for point in points_text.split(separator) if point.strip()]
                    return [points_text.strip()]

            return []

        # 主处理逻辑
        try:
            cleaned_text = clean_text(generated_text)
            
            # 使用多种模式匹配问题块
            question_patterns = [
                # 标准JSON格式
                r'\{\s*"id"\s*:\s*"?(\d+)"?,\s*"question"\s*:\s*"([^"]+)".*?\}',
                # 中文格式
                r'(?:问题|题目)\s*(\d+)\s*[:：]\s*([^解答]+)(?:解答|解题|步骤)',
                # 简单格式
                r'(\d+)[.、]\s*([^解答]+)(?:解答|解题|步骤)'
            ]

            for pattern in question_patterns:
                matches = re.finditer(pattern, cleaned_text, re.DOTALL)
                for match in matches:
                    try:
                        question_id = match.group(1)
                        question_text = match.group(2).strip()
                        
                        # 获取当前问题块的完整文本
                        question_block = match.group(0)
                        
                        # 提取解题步骤
                        steps_match = re.search(r'(?:"solution_steps"|解题步骤|解答步骤)\s*[:：]?\s*(\[.*?\]|.*?)(?=(?:final_answer|最终答案|答案|$))', question_block, re.DOTALL)
                        solution_steps = extract_steps(steps_match.group(1) if steps_match else "")
                        
                        # 提取最终答案和关键知识点
                        final_answer = extract_final_answer(question_block)
                        key_points = extract_key_points(question_block)
                        
                        # 构建问题对象
                        question_data = {
                            "id": question_id,
                            "question": question_text,
                            "solution_steps": solution_steps,
                            "final_answer": final_answer,
                            "key_points": key_points
                        }
                        
                        # 验证必要字段
                        if question_data["question"].strip():
                            calculation_questions.append(question_data)
                            
                    except Exception as e:
                        log_with_request_id(f"解析单个问题时出错: {str(e)}", level=logging.WARNING, request_id=request_id)
                        continue
                
                if calculation_questions:
                    break

        except Exception as e:
            log_with_request_id(f"解析计算题时出错: {str(e)}", level=logging.ERROR, request_id=request_id)
        
        return {"calculation_questions": calculation_questions} if calculation_questions else {"error": "无法解析任何计算题"}










    def generate_calculation_questions(self,
                                    dataset_ids: Optional[List[str]] = None,
                                    document_ids: Optional[List[str]] = None,
                                    num_questions: int = 1,
                                    **kwargs) -> Dict:
        """
        根据视频内容生成计算题和详细解答
        
        Args:
            dataset_ids: 数据集ID列表
            document_ids: 文档ID列表
            num_questions: 要生成的题目数量
            **kwargs: 其他参数
            
        Returns:
            Dict: 包含生成的计算题、解题步骤和答案的字典
        """
        try:
            # 初始化WebAgent
            WebAgentClass = duckduckgosearch.Web_Agent
            web_agent = WebAgentClass(api_key=self.api_key, api_key_type=self.api_key_type)
            
            # 获取文档内容
            content = ""
            try:
                # 首先获取视频字幕内容，支持多个文档ID
                if dataset_ids and document_ids:
                    # 如果document_ids是字符串，转换为列表
                    if isinstance(document_ids, str):
                        document_ids = [document_ids]

                    # 合并多个文档的内容
                    all_content = []
                    for doc_id in document_ids:
                        doc_content = get_document_chunks(
                            api_key_rag=self.api_key_rag,
                            base_url="http://localhost:8080",
                            dataset_id=dataset_ids[0],
                            document_id=doc_id,
                            keywords=None,
                            chunk_id=None,
                            offset=0,
                            limit=1024
                        )

                        if doc_content and doc_content.get('code') == 0:
                            doc_text = doc_content['data']['content']
                            if doc_text:
                                all_content.append(doc_text)
                                log_with_request_id(f"成功获取文档 {doc_id} 内容，长度: {count_text_length(doc_text)}", level=logging.INFO, request_id=request_id)

                    # 合并所有文档内容
                    if all_content:
                        content = "\n\n=== 文档分隔 ===\n\n".join(all_content)
                        log_with_request_id(f"成功合并 {len(all_content)} 个文档内容，总长度: {count_text_length(content)}", level=logging.INFO, request_id=request_id)
                
            except Exception as e:



                log_with_request_id(f"获取内容时出错: {str(e)}", level=logging.INFO, request_id=request_id)
                return {"error": f"获取内容失败: {str(e)}"}

            if not content or content.strip() == "":
                return {"error": "未找到有效的课程内容"}

            # 清理和处理内容
            if isinstance(content, bytes):
                content = content.decode('utf-8', errors='ignore')
            elif isinstance(content, str):
                content = content.encode('utf-8', errors='ignore').decode('utf-8')

            content = re.sub(r'[*#]', '', content)
            content = re.sub(r'\s+', ' ', content).strip()
            
            # 截取合适长度的内容
            if all('\u4e00' <= char <= '\u9fff' for char in content):
                content_compact = content[:50000]
            else:
                content_words = content.split()
                content_compact = ' '.join(content_words[:50000])

            # 获取学科名称
            subject = self._get_subject_from_folder()

            # 构建提示词
            system_prompt = f"""你是一位经验丰富的{subject}教育专家，擅长设计计算题。

请根据提供的课程内容，用中文生成{num_questions}道计算题。

重要要求：
1. 绝对不要使用占位符文本，如"题目内容"、"解题步骤说明"、"关键知识点1"等
2. 每道题目都必须基于课程内容的具体知识点
3. 题目必须包含具体的数值、条件和要求
4. 解题步骤必须是具体的计算过程，不能是模板文本
5. 最终答案必须是具体的数值和单位
6. 关键知识点必须是具体的概念名称
7. 所有的公式和数学字符必须使用LaTeX格式：$...$ 或 $$...$$如 $\\frac{{q}}{{12\\epsilon_0}}$，禁止使用反斜杠表示公式字符如\\(\\frac{{q}}{{12\\epsilon_0}}\\)
你的输出必须是一个合法的JSON格式：
{{
    "calculation_questions": [
        {{
            "id": "1",
            "question": "基于课程内容的具体计算题目，包含具体数值和条件",
            "solution_steps": [
                {{
                    "step_number": "1",
                    "description": "具体的解题步骤描述",
                    "calculation": "具体的计算过程"
                }}
            ],
            "final_answer": "具体的数值答案和单位",
            "key_points": ["具体的知识点名称1", "具体的知识点名称2"]
        }}
    ]
}}

技术要求：
- 计算过程要清晰，注意标注单位
- solution_steps必须是数组，不能是字符串
"""

            prompt = f"""
                        请根据以下课程内容生成{num_questions}道计算题：

                        课程内容：
                        {content_compact}

                        请确保题目类型多样，涵盖本节课程的重要计算知识点。每道题的解答过程要详细，便于学生理解。
                        """

            # 生成题目
            try:
                questions_json, totaltokens = web_agent._generate_summary_with_system_prompt(
                    system_prompt,
                    prompt,
                    enable_thinking=True
                )
                
                # 解析JSON
                questions_data = json.loads(questions_json)

                # 验证和标准化数据格式
                log_with_request_id(f"生成的原始数据包含 {len(questions_data.get('calculation_questions', []))} 道题目", level=logging.INFO, request_id=request_id)
                questions_data = self._validate_and_normalize_calculation_questions(questions_data)
                log_with_request_id(f"验证后的数据包含 {len(questions_data.get('calculation_questions', []))} 道题目", level=logging.INFO, request_id=request_id)
                
                # 增强每道题的解析
                for question in questions_data.get("calculation_questions", []):
                    # 构建搜索关键词，包含题目和关键知识点
                    search_terms = [question["question"]] + question.get("key_points", [])
                    enhanced_content = ""
                    
                    # 对每个关键词进行搜索
                    for term in search_terms:
                        kb_content = get_vidpdf_content(self.folder, term, self.api_key_rag)
                        log_with_request_id(f"kb_content: {kb_content[:100]}", level=logging.INFO, request_id=request_id)
                        # 如果中文内容少于100字，尝试英文查询
                        if not kb_content or count_text_length(kb_content) < 100:
                            # 使用WebAgent将问题翻译成英文
                            WebAgentClass = duckduckgosearch.Web_Agent
                            web_agent = WebAgentClass(api_key=self.api_key, api_key_type=self.api_key_type)
                            
                            system_prompt = """你是一位专业的翻译专家。请将给定的中文问题翻译成英文。
                                                翻译时注意：
                                                1. 保持专业术语的准确性
                                                2. 使用自然的英文表达
                                                3. 只返回翻译结果，不要有任何其他内容
                                                """
                            prompt = f"请将以下问题翻译成英文：\n{term}"
                            
                            english_term, _ = web_agent._generate_summary_with_system_prompt(
                                system_prompt,
                                prompt,
                                enable_thinking=False
                            )
                            
                            # 获取英文内容
                            english_kb_content = get_vidpdf_content(self.folder, english_term, self.api_key_rag)
                            log_with_request_id(f"English search term: {english_term[:100]}", level=logging.INFO, request_id=request_id)
                            log_with_request_id(f"English kb_content: {english_kb_content[:100]}", level=logging.INFO, request_id=request_id)
                            
                            # 合并中英文内容
                            if english_kb_content:
                                kb_content = f"{kb_content}\n\nEnglish Content:\n{english_kb_content}"
                                log_with_request_id(f"Combined kb_content length: {count_text_length(kb_content)}", level=logging.INFO, request_id=request_id)
                        
                        if kb_content:
                            enhanced_content += f"\n{kb_content}"
                    
                    # 使用知识库内容增强解题步骤
                    if enhanced_content and count_text_length(enhanced_content) > 100:
                        system_prompt = """你是一位经验丰富的教育专家。请根据提供的原始解题步骤和知识库内容，生成更详细的中文解题步骤。新的步骤应该：\n1. 保持原有的计算逻辑和步骤\n2. 融入知识库中的相关概念和原理解释\n3. 确保所有公式使用LaTeX格式：$...$ 或 $$...$$如 $\\frac{{q}}{{12\\epsilon_0}}$，禁止使用反斜杠表示公式字符如\\(\\frac{{q}}{{12\\epsilon_0}}\\)\n4. 添加更多的物理概念解释\n5. 保持步骤清晰，便于学生理解\n6. 保持与原始步骤相同的格式和结构\n7. 确保每个步骤都包含step_number、description和calculation字段\n8. 返回的必须是一个JSON数组，不能是字符串\n"""
                        prompt = f"""
题目：
{question['question']}

原始解题步骤：
{question['solution_steps']}

知识库补充内容：
{enhanced_content}

请生成更详细的解题步骤，确保保持原有格式：
"""
                    else:
                        system_prompt = """你是一位经验丰富的教育专家。请根据提供的原始解题步骤，生成更详细的中文解题步骤。新的步骤应该：\n1. 保持原有的计算逻辑和步骤\n2. 融入相关概念和原理解释\n3. 确保所有公式使用LaTeX格式：$...$ 或 $$...$$如 $\\frac{{q}}{{12\\epsilon_0}}$，禁止使用反斜杠表示公式字符如\\(\\frac{{q}}{{12\\epsilon_0}}\\)\n4. 添加更多的物理概念解释\n5. 保持步骤清晰，便于学生理解\n6. 保持与原始步骤相同的格式和结构\n7. 确保每个步骤都包含step_number、description和calculation字段\n8. 返回的必须是一个JSON数组，不能是字符串\n"""
                        prompt = f"""
题目：
{question['question']}

原始解题步骤：
{question['solution_steps']}

请生成更详细的解题步骤，确保保持原有格式：
"""
                    # 生成增强的解题步骤
                    enhanced_steps, _ = web_agent._generate_summary_with_system_prompt(
                        system_prompt,
                        prompt,
                        enable_thinking=True
                    )
                    if enhanced_steps and isinstance(enhanced_steps, str):
                        try:
                            # 尝试解析为JSON（因为原始数据是JSON格式）
                            parsed_steps = json.loads(enhanced_steps)
                            log_with_request_id(f"Enhanced steps: {parsed_steps}", level=logging.INFO, request_id=request_id)
                            if isinstance(parsed_steps, list):
                                question["solution_steps"] = parsed_steps
                            elif isinstance(parsed_steps, dict) and "solution_steps" in parsed_steps:
                                question["solution_steps"] = parsed_steps["solution_steps"]
                        except json.JSONDecodeError:
                            # 如果不是JSON格式，保持原有步骤
                            log_with_request_id(f"解析增强步骤失败，保持原有步骤", level=logging.INFO, request_id=request_id)
                
                questions_data["tokens_used"] = totaltokens
                # 添加课程名称信息
                questions_data["course_name"] = self._get_subject_from_folder()
                return questions_data
                
            except json.JSONDecodeError as e:

                # 进入容错解析流程
                print(f"进入容错解析流程，原因: {str(e)}")
                log_with_request_id(f"JSON解析错误: {str(e)}", level=logging.INFO, request_id=request_id)
                questions_data = self._parse_calculation_questions(questions_json)

                # 验证和标准化数据格式
                log_with_request_id(f"容错解析的原始数据包含 {len(questions_data.get('calculation_questions', []))} 道题目", level=logging.INFO, request_id=request_id)
                questions_data = self._validate_and_normalize_calculation_questions(questions_data)
                log_with_request_id(f"容错解析验证后的数据包含 {len(questions_data.get('calculation_questions', []))} 道题目", level=logging.INFO, request_id=request_id)

                    
             
                # 增强每道题的解析
                for question in questions_data.get("calculation_questions", []):
                    # 构建搜索关键词，包含题目和关键知识点
                    search_terms = [question["question"]] + question.get("key_points", [])
                    enhanced_content = ""
                    
                    # 对每个关键词进行搜索
                    for term in search_terms:
                        kb_content = get_vidpdf_content(self.folder, term, self.api_key_rag)
                        log_with_request_id(f"kb_content: {kb_content[:100]}", level=logging.INFO, request_id=request_id)
                        # 如果中文内容少于100字，尝试英文查询
                        if not kb_content or count_text_length(kb_content) < 100:
                            # 使用WebAgent将问题翻译成英文
                            WebAgentClass = duckduckgosearch.Web_Agent
                            web_agent = WebAgentClass(api_key=self.api_key, api_key_type=self.api_key_type)
                            
                            system_prompt = """你是一位专业的翻译专家。请将给定的中文问题翻译成英文。
                                                翻译时注意：
                                                1. 保持专业术语的准确性
                                                2. 使用自然的英文表达
                                                3. 只返回翻译结果，不要有任何其他内容
                                                """
                            prompt = f"请将以下问题翻译成英文：\n{term}"
                            
                            english_term, _ = web_agent._generate_summary_with_system_prompt(
                                system_prompt,
                                prompt,
                                enable_thinking=False  
                            )
                            
                            # 获取英文内容
                            english_kb_content = get_vidpdf_content(self.folder, english_term, self.api_key_rag)
                            log_with_request_id(f"English search term: {english_term[:100]}", level=logging.INFO, request_id=request_id)
                            log_with_request_id(f"English kb_content: {english_kb_content[:100]}", level=logging.INFO, request_id=request_id)
                            
                            # 合并中英文内容
                            if english_kb_content:
                                kb_content = f"{kb_content}\n\nEnglish Content:\n{english_kb_content}"
                                log_with_request_id(f"Combined kb_content length: {count_text_length(kb_content)}", level=logging.INFO, request_id=request_id)
                        
                        if kb_content:
                            enhanced_content += f"\n{kb_content}"
                    
                    # 使用知识库内容增强解题步骤
                    if enhanced_content and count_text_length(enhanced_content) > 100:
                        system_prompt = """你是一位经验丰富的教育专家。请根据提供的原始解题步骤和知识库内容，生成更详细的中文解题步骤。新的步骤应该：\n1. 保持原有的计算逻辑和步骤\n2. 融入知识库中的相关概念和原理解释\n3. 确保所有公式使用LaTeX格式：$...$ 或 $$...$$如 $\\frac{{q}}{{12\\epsilon_0}}$，禁止使用反斜杠表示公式字符如\\(\\frac{{q}}{{12\\epsilon_0}}\\)\n4. 添加更多的物理概念解释\n5. 保持步骤清晰，便于学生理解\n6. 保持与原始步骤相同的格式和结构\n7. 确保每个步骤都包含step_number、description和calculation字段\n8. 返回的必须是一个JSON数组，不能是字符串如：\n"""
                        prompt = f"""
题目：
{question['question']}

原始解题步骤：
{question['solution_steps']}

知识库补充内容：
{enhanced_content}

请生成更详细的解题步骤，确保保持原有格式：
"""
                    else:
                        system_prompt = """你是一位经验丰富的教育专家。请根据提供的原始解题步骤，生成更详细的中文解题步骤。新的步骤应该：\n1. 保持原有的计算逻辑和步骤\n2. 融入相关概念和原理解释\n3. 确保所有公式使用LaTeX格式：$...$ 或 $$...$$如 $\\frac{{q}}{{12\\epsilon_0}}$，禁止使用反斜杠表示公式字符如\\(\\frac{{q}}{{12\\epsilon_0}}\\)\n4. 添加更多的物理概念解释\n5. 保持步骤清晰，便于学生理解\n6. 保持与原始步骤相同的格式和结构\n7. 确保每个步骤都包含step_number、description和calculation字段\n8. 返回的必须是一个JSON数组，不能是字符串\n"""
                        prompt = f"""
题目：
{question['question']}

原始解题步骤：
{question['solution_steps']}

请生成更详细的解题步骤，确保保持原有格式：
"""
                    # 生成增强的解题步骤
                    enhanced_steps, _ = web_agent._generate_summary_with_system_prompt(
                        system_prompt,
                        prompt,
                        enable_thinking=True
                    )
                    if enhanced_steps and isinstance(enhanced_steps, str):
                        try:
                            # 尝试解析为JSON（因为原始数据是JSON格式）
                            parsed_steps = json.loads(enhanced_steps)
                            log_with_request_id(f"Enhanced steps: {parsed_steps}", level=logging.INFO, request_id=request_id)
                            if isinstance(parsed_steps, list):
                                question["solution_steps"] = parsed_steps
                            elif isinstance(parsed_steps, dict) and "solution_steps" in parsed_steps:
                                question["solution_steps"] = parsed_steps["solution_steps"]
                        except json.JSONDecodeError:
                            # 如果不是JSON格式，保持原有步骤
                            log_with_request_id(f"解析增强步骤失败，保持原有步骤", level=logging.INFO, request_id=request_id)
                
                questions_data["tokens_used"] = totaltokens
                # 添加课程名称信息
                questions_data["course_name"] = self._get_subject_from_folder()

                return questions_data


            
            except Exception as e:
                log_with_request_id(f"生成题目时出错: {str(e)}", level=logging.INFO, request_id=request_id)
                return {"error": f"生成题目失败: {str(e)}"}
                
        except Exception as e:
            log_with_request_id(f"题目生成过程出错: {str(e)}", level=logging.INFO, request_id=request_id)
            return {"error": f"题目生成过程失败: {str(e)}"}

if __name__ == "__main__":
    # 配置日志输出
    logging.basicConfig(level=logging.INFO)
    
    # 配置API密钥
    test_config = {
        "api_key": "sk-ccb8d787d11c44b98a884424a682bd2c",  # deepseek API密钥
        "api_key_rag": "ragflow-Y1NTkyNDBlZmZhNTExZWY5ZmNhZWUzZD",  # RAG API密钥
        "api_key_type": "deepseek",
        "folder": "en-MIT Physics II Electricity and Magnetism",
        "document_ids": "40e51a52ffae11efafa3ee3d992e99ff",
        "dataset_ids": "a3870650ffa111ef92aa2a5c03e306d6"
    }
    
    # 设置RAGFlow API的base_url
    os.environ["RAGFLOW_BASE_URL"] = "http://localhost:8080/v1/api"
    
    # 初始化问题生成器
    generator = QuestionGenerator(
        api_key=test_config["api_key"],
        api_key_rag=test_config["api_key_rag"],
        api_key_type=test_config["api_key_type"],
        folder=test_config["folder"]
    )
    
    # 获取dataset_ids
    # main_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    # COURSE_VIDEO_BASE = os.path.join(main_dir, 'coursevideo')
    # dataset_ids = get_matching_ids(test_config["folder"], COURSE_VIDEO_BASE)
    # print(f"找到的dataset_ids: {dataset_ids}")
    document_ids = test_config["document_ids"]
    dataset_ids = test_config["dataset_ids"]

    if not dataset_ids:
        print("未找到匹配的dataset_ids，使用默认值")

    # 生成选择题
    result = generator.generate_questions(dataset_ids=[dataset_ids], document_ids=[document_ids], num_questions=2)
    print("\n生成的选择题:")
    print(json.dumps(result, indent=2, ensure_ascii=False))
    
    # 生成计算题
    calc_result = generator.generate_calculation_questions(dataset_ids=[dataset_ids],document_ids=[document_ids], num_questions=1)
    print("\n生成的计算题:")
    print(json.dumps(calc_result, indent=2, ensure_ascii=False)) 