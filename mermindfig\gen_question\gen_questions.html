<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI 题目生成器 - AI 助教平台</title>

    <!-- Highlight.js for syntax highlighting -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/highlight.js@11.6.0/styles/github.min.css">

    <!-- 加载MathJax -->
    <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.9/MathJax.js?config=TeX-AMS-MML_HTMLorMML"></script>
    <script type="text/x-mathjax-config">
    MathJax.Hub.Config({
        tex2jax: {
            inlineMath: [['$', '$'], ['\\(', '\\)']],
            displayMath: [['$$', '$$'], ['\\[', '\\]']],
            processEscapes: true
        },
        "HTML-CSS": {
            fonts: ["TeX"],
            scale: 100,
            linebreaks: { automatic: true },
            availableFonts: ["TeX"],
            preferredFont: "TeX",
            webFont: "TeX",
            imageFont: null,
            undefinedFamily: "STIXGeneral,'Arial Unicode MS',serif",
            mtextFontInherit: false,
            EqnChunk: 50,
            EqnChunkFactor: 1.5,
            EqnChunkDelay: 100,
            matchFontHeight: true,
            noReflows: true,
            styles: {
                ".MathJax_Display": {
                    "overflow": "visible !important",
                    "max-width": "100%"
                },
                ".MathJax": {
                    "overflow": "visible !important",
                    "max-width": "100%"
                },
                ".MathJax_SVG_Display": {
                    "overflow": "visible !important",
                    "max-width": "100%"
                },
                ".MathJax_SVG": {
                    "overflow": "visible !important",
                    "max-width": "100%"
                },
                ".MathJax_CHTML": {
                    "overflow": "visible !important",
                    "max-width": "100%"
                }
            }
        },
        messageStyle: "none",
        showProcessingMessages: false,
        showMathMenu: false,
        showMathMenuMSIE: false
    });
    </script>

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .main-content {
            padding: 40px;
        }

        .config-section {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            border: 2px solid #e9ecef;
        }

        .section-title {
            font-size: 1.5em;
            color: #2c3e50;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .section-title::before {
            content: "⚙️";
            font-size: 1.2em;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            font-weight: 600;
            color: #34495e;
            margin-bottom: 8px;
            font-size: 1.1em;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            padding: 12px 15px;
            border: 2px solid #e1e8ed;
            border-radius: 10px;
            font-size: 1em;
            transition: all 0.3s ease;
            background: white;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #4facfe;
            box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);
        }

        .question-config {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .btn {
            padding: 15px 30px;
            border: none;
            border-radius: 10px;
            font-size: 1.1em;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .btn-secondary {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
        }

        .btn-secondary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(240, 147, 251, 0.3);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .action-buttons {
            display: flex;
            gap: 20px;
            justify-content: center;
            margin-top: 30px;
        }

        .status-section {
            background: #fff;
            border-radius: 15px;
            padding: 20px;
            margin-top: 30px;
            border: 2px solid #e9ecef;
        }

        .status-message {
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 15px;
            font-weight: 500;
        }

        .status-info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .status-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .results-section {
            background: #fff;
            border-radius: 15px;
            padding: 30px;
            margin-top: 30px;
            border: 2px solid #e9ecef;
            display: none;
        }

        .results-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            flex-wrap: wrap;
            gap: 15px;
        }

        .results-header h3 {
            margin: 0;
            color: #2c3e50;
        }

        .results-actions {
            display: flex;
            gap: 10px;
        }

        .history-section {
            background: #fff;
            border-radius: 15px;
            padding: 30px;
            margin-top: 30px;
            border: 2px solid #e9ecef;
        }

        .history-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            flex-wrap: wrap;
            gap: 15px;
        }

        .history-header h3 {
            margin: 0;
            color: #2c3e50;
        }

        .history-actions {
            display: flex;
            gap: 10px;
        }

        .history-item {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 15px;
            border-left: 4px solid #28a745;
            transition: all 0.3s ease;
        }

        .history-item:hover {
            background: #e9ecef;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .history-item-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 10px;
            flex-wrap: wrap;
            gap: 10px;
        }

        .history-item-title {
            font-size: 1.1em;
            font-weight: 600;
            color: #2c3e50;
        }

        .history-item-actions {
            display: flex;
            gap: 8px;
        }

        .history-item-actions .btn {
            padding: 6px 12px;
            font-size: 0.85em;
        }

        .history-item-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin-bottom: 10px;
            font-size: 0.9em;
            color: #6c757d;
        }

        .history-item-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.85em;
            color: #6c757d;
            border-top: 1px solid #dee2e6;
            padding-top: 10px;
            margin-top: 10px;
        }

        .question-item {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            border-left: 4px solid #4facfe;
        }

        .question-title {
            font-size: 1.2em;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 15px;
        }

        .question-content {
            line-height: 1.6;
            color: #34495e;
        }

        /* 渲染内容样式 */
        .rendered-content {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .rendered-content h1, .rendered-content h2, .rendered-content h3,
        .rendered-content h4, .rendered-content h5, .rendered-content h6 {
            color: #2c3e50;
            margin-top: 1.5em;
            margin-bottom: 0.5em;
        }

        .rendered-content p {
            margin-bottom: 1em;
            line-height: 1.6;
        }

        .rendered-content strong {
            font-weight: 600;
            color: #2c3e50;
        }

        .rendered-content code {
            background: #f8f9fa;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
        }

        .rendered-content pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            overflow-x: auto;
            margin: 1em 0;
        }

        .rendered-content pre code {
            background: none;
            padding: 0;
        }

        .rendered-content ul, .rendered-content ol {
            margin-left: 20px;
            margin-bottom: 1em;
        }

        .rendered-content li {
            margin-bottom: 0.5em;
        }

        .rendered-content blockquote {
            border-left: 4px solid #4facfe;
            padding-left: 15px;
            margin: 1em 0;
            color: #6c757d;
            font-style: italic;
        }

        /* MathJax数学公式样式 */
        .math-display {
            display: block;
            margin: 1.5em 0;
            text-align: center;
            overflow-x: auto;
            max-width: 100%;
            padding: 1em 0;
            background: rgba(248, 249, 250, 0.8);
            border-radius: 8px;
            border: 1px solid rgba(0, 0, 0, 0.05);
        }

        .math-inline {
            display: inline-block;
            vertical-align: middle;
            margin: 0 0.2em;
            padding: 0 0.3em;
        }

        /* 确保MathJax生成的元素可见 */
        .MathJax {
            display: inline-block !important;
            overflow: visible !important;
            max-width: 100%;
        }

        .MathJax_Display {
            overflow: visible !important;
            margin: 1.5em 0;
            padding: 1em 0;
            max-width: 100%;
            text-align: center;
        }

        .MathJax_SVG_Display {
            overflow: visible !important;
            max-width: 100%;
        }

        .MathJax_SVG {
            display: inline-block !important;
            overflow: visible !important;
            max-width: 100%;
        }

        .MathJax_CHTML {
            max-width: 100%;
            overflow: visible !important;
        }

        .MJX-TEX {
            font-size: 120% !important;
        }

        /* 代码语法高亮样式 */
        .rendered-content pre code.language-python {
            color: #d73a49;
        }

        .rendered-content pre code.language-javascript {
            color: #6f42c1;
        }

        .rendered-content pre code.language-math {
            font-family: 'Times New Roman', serif;
            color: #2c3e50;
        }

        /* 改进的表格样式 */
        .rendered-content table {
            border-collapse: collapse;
            width: 100%;
            margin: 1em 0;
            border: 1px solid #dee2e6;
        }

        .rendered-content th,
        .rendered-content td {
            border: 1px solid #dee2e6;
            padding: 8px 12px;
            text-align: left;
        }

        .rendered-content th {
            background: #f8f9fa;
            font-weight: 600;
        }

        /* 改进的链接样式 */
        .rendered-content a {
            color: #4facfe;
            text-decoration: none;
            border-bottom: 1px solid transparent;
            transition: all 0.3s ease;
        }

        .rendered-content a:hover {
            color: #2c3e50;
            border-bottom-color: #4facfe;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #4facfe;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .file-name-section {
            background: #e8f4fd;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
            border: 1px solid #b8daff;
        }

        .file-name-section h3 {
            color: #0056b3;
            margin-bottom: 15px;
        }

        .radio-group {
            display: flex;
            gap: 20px;
            margin-top: 10px;
        }

        .radio-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .radio-item input[type="radio"] {
            width: 18px;
            height: 18px;
        }

        .file-selection-container {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            border: 1px solid #e9ecef;
        }

        .file-controls {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .file-controls .btn {
            padding: 8px 16px;
            font-size: 0.9em;
        }

        .file-list {
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            background: white;
            padding: 10px;
        }

        .file-item {
            display: flex;
            align-items: center;
            padding: 10px;
            border-radius: 6px;
            margin-bottom: 5px;
            cursor: pointer;
            transition: all 0.2s ease;
            border: 1px solid transparent;
        }

        .file-item:hover {
            background: #f8f9fa;
            border-color: #dee2e6;
        }

        .file-item.selected {
            background: #e3f2fd;
            border-color: #2196f3;
        }

        .file-item input[type="checkbox"] {
            margin-right: 10px;
            width: 16px;
            height: 16px;
        }

        .file-name {
            flex: 1;
            font-weight: 500;
            color: #2c3e50;
        }

        .file-id {
            font-size: 0.8em;
            color: #6c757d;
            font-family: monospace;
        }

        .selected-info {
            margin-top: 15px;
            padding: 10px;
            background: #e8f4fd;
            border-radius: 6px;
            border: 1px solid #b8daff;
            text-align: center;
            font-weight: 600;
            color: #0056b3;
        }

        .loading-text {
            text-align: center;
            color: #6c757d;
            font-style: italic;
            padding: 20px;
        }

        @media (max-width: 768px) {
            .form-grid {
                grid-template-columns: 1fr;
            }

            .question-config {
                grid-template-columns: 1fr;
            }

            .action-buttons {
                flex-direction: column;
            }

            .header h1 {
                font-size: 2em;
            }

            .main-content {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 AI 题目生成器</h1>
            <p>基于人工智能的智能题目生成系统</p>
        </div>

        <div class="main-content">
            <!-- 配置区域 -->
            <div class="config-section">
                <h2 class="section-title">基本配置</h2>
                <div class="form-grid">
                    <div class="form-group">
                        <label for="folder">题目所属课程</label>
                        <input type="text" id="folder" placeholder="例如: 电磁学">
                    </div>
                </div>
            </div>

            <!-- 文件选择区域 -->
            <div class="config-section">
                <h2 class="section-title">文件选择</h2>
                <div class="file-selection-container">
                    <div class="file-controls">
                        <button class="btn btn-secondary" id="refresh-files-btn">🔄 刷新文件列表</button>
                        <button class="btn btn-secondary" id="select-all-btn">☑️ 全选</button>
                        <button class="btn btn-secondary" id="clear-selection-btn">❌ 清空选择</button>
                    </div>
                    <div id="file-list-container" class="file-list">
                        <p class="loading-text">正在加载文件列表...</p>
                    </div>
                    <div class="selected-info">
                        <span id="selected-count">已选择: 0 个文件</span>
                    </div>
                </div>
            </div>

            <!-- 题目配置区域 -->
            <div class="config-section">
                <h2 class="section-title">题目配置</h2>
                <div class="question-config">
                    <div class="form-group">
                        <label for="choice-count">选择题数量</label>
                        <select id="choice-count">
                            <option value="0">0</option>
                            <option value="3">3</option>
                            <option value="5" selected>5</option>
                            <option value="8">8</option>
                            <option value="10">10</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="calculation-count">计算题数量</label>
                        <select id="calculation-count">
                            <option value="0">0</option>
                            <option value="1">1</option>
                            <option value="2" selected>2</option>
                            <option value="3">3</option>
                            <option value="5">5</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- 文件保存配置 -->
            <div class="file-name-section">
                <h3>📁 文件保存设置</h3>
                <div class="form-group">
                    <label>文件命名方式</label>
                    <div class="radio-group">
                        <div class="radio-item">
                            <input type="radio" id="timestamp-name" name="filename-option" value="timestamp" checked>
                            <label for="timestamp-name">时间戳命名</label>
                        </div>
                        <div class="radio-item">
                            <input type="radio" id="custom-name" name="filename-option" value="custom">
                            <label for="custom-name">自定义命名</label>
                        </div>
                    </div>
                </div>
                <div class="form-group" id="custom-filename-group" style="display: none;">
                    <label for="custom-filename">自定义文件名</label>
                    <input type="text" id="custom-filename" placeholder="请输入文件名（不含扩展名）">
                </div>
            </div>

            <!-- 操作按钮 -->
            <div class="action-buttons">
                <button class="btn btn-primary" id="load-config-btn">📋 加载默认配置</button>
                <button class="btn btn-primary" id="generate-btn">🚀 开始生成题目</button>
                <button class="btn btn-secondary" id="load-history-btn">📚 查看历史题目</button>
            </div>

            <!-- 历史题目区域 -->
            <div class="history-section" id="history-section" style="display: none;">
                <div class="history-header">
                    <h3>📚 历史题目</h3>
                    <div class="history-actions">
                        <button class="btn btn-secondary" id="refresh-history-btn">🔄 刷新</button>
                        <button class="btn btn-secondary" id="hide-history-btn">❌ 隐藏</button>
                    </div>
                </div>
                <div id="history-content">
                    <p class="loading-text">正在加载历史题目...</p>
                </div>
            </div>

            <!-- 状态显示区域 -->
            <div class="status-section">
                <h3>📊 生成状态</h3>
                <div id="status-messages"></div>
            </div>

            <!-- 结果显示区域 -->
            <div class="results-section" id="results-section">
                <div class="results-header">
                    <h3>📝 生成结果</h3>
                    <div class="results-actions">
                        <button class="btn btn-secondary" id="download-markdown-btn" style="display: none;">📥 下载 Markdown</button>
                    </div>
                </div>
                <div id="results-content"></div>
            </div>
        </div>
    </div>

    <!-- Core libraries for markdown rendering -->
    <script src="https://cdn.jsdelivr.net/npm/markdown-it@13.0.1/dist/markdown-it.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/highlight.js@11.6.0/highlight.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@11.6.0/dist/mermaid.min.js"></script>

    <script>
        // 全局变量
        let markdownRenderer = null;

        // 初始化markdown渲染器
        function initMarkdownRenderer() {
            console.log('初始化 Markdown 渲染器...');

            // 使用 marked 作为主要的 markdown 渲染器
            if (typeof marked !== 'undefined') {
                marked.setOptions({
                    highlight: function(code, lang) {
                        if (lang && hljs.getLanguage(lang)) {
                            try {
                                return hljs.highlight(code, { language: lang }).value;
                            } catch (__) {}
                        }
                        return code;
                    },
                    breaks: true,
                    gfm: true
                });

                markdownRenderer = marked;
                console.log('Marked 渲染器初始化完成');
            } else {
                console.warn('Marked 未加载，使用简单渲染');
            }
        }

        // 基于 MathJax 的数学公式处理函数
        function preprocessLatexFormula(formula) {
            // 新规则：将双反斜杠转换为单反斜杠
            // 首先将双反斜杠替换为临时标记
            let processed = formula.replace(/\\\\/g, '___SINGLE_BACKSLASH___');

            // 处理其他可能的转义序列
            processed = processed.replace(/\\([^a-zA-Z0-9])/g, '___ESCAPED_$1___');

            return processed;
        }

        // 还原LaTeX公式中的特殊标记
        function restoreLatexFormula(formula) {
            // 还原双反斜杠为单反斜杠（新规则）
            let restored = formula.replace(/___SINGLE_BACKSLASH___/g, '\\');

            // 还原其他转义序列
            restored = restored.replace(/___ESCAPED_([^a-zA-Z0-9])___/g, '\\$1');

            return restored;
        }

        // 处理数学公式的主函数
        function processMathFormulas(content) {
            try {
                // 1. 预处理内容，保护数学公式
                let processedContent = content;
                const mathPlaceholders = [];
                let mathCounter = 0;

                console.log('=== processMathFormulas 开始 ===');
                console.log('原始内容:', JSON.stringify(content));

                // 首先处理最具体的格式：\\[...\\] 包含换行符
                processedContent = processedContent.replace(/\\\\\[\s*\n([\s\S]*?)\n\s*\\\\\]/g, function(match, formula) {
                    console.log('匹配到 \\\\[...\\\\] 格式 (换行):', match);
                    const preprocessedFormula = preprocessLatexFormula(formula);
                    const placeholder = `MATH_BLOCK_${mathCounter}`;
                    mathPlaceholders.push({
                        placeholder: placeholder,
                        formula: preprocessedFormula.trim(),
                        type: 'block',
                        delimiter: '\\\\[',
                        needsRestore: true
                    });
                    mathCounter++;
                    return placeholder;
                });

                // 处理 \\[...\\] 单行格式
                processedContent = processedContent.replace(/\\\\\[([\s\S]*?)\\\\\]/g, function(match, formula) {
                    console.log('匹配到 \\\\[...\\\\] 格式 (单行):', match);
                    const preprocessedFormula = preprocessLatexFormula(formula);
                    const placeholder = `MATH_BLOCK_${mathCounter}`;
                    mathPlaceholders.push({
                        placeholder: placeholder,
                        formula: preprocessedFormula.trim(),
                        type: 'block',
                        delimiter: '\\\\[',
                        needsRestore: true
                    });
                    mathCounter++;
                    return placeholder;
                });

                // 处理 \[...] 包含换行符
                processedContent = processedContent.replace(/\\\[\s*\n([\s\S]*?)\n\s*\\\]/g, function(match, formula) {
                    console.log('匹配到 \\[...\\] 格式 (换行):', match);
                    const preprocessedFormula = preprocessLatexFormula(formula);
                    const placeholder = `MATH_BLOCK_${mathCounter}`;
                    mathPlaceholders.push({
                        placeholder: placeholder,
                        formula: preprocessedFormula.trim(),
                        type: 'block',
                        delimiter: '\\[',
                        needsRestore: true
                    });
                    mathCounter++;
                    return placeholder;
                });

                // 处理 \[...] 单行格式
                processedContent = processedContent.replace(/\\\[([\s\S]*?)\\\]/g, function(match, formula) {
                    console.log('匹配到 \\[...\\] 格式 (单行):', match);
                    const preprocessedFormula = preprocessLatexFormula(formula);
                    const placeholder = `MATH_BLOCK_${mathCounter}`;
                    mathPlaceholders.push({
                        placeholder: placeholder,
                        formula: preprocessedFormula.trim(),
                        type: 'block',
                        delimiter: '\\[',
                        needsRestore: true
                    });
                    mathCounter++;
                    return placeholder;
                });

                // 处理块级公式 $$ ... $$
                processedContent = processedContent.replace(/\$\$([\s\S]*?)\$\$/g, function(match, formula) {
                    console.log('匹配到 $$...$$ 格式:', match);
                    const preprocessedFormula = preprocessLatexFormula(formula);
                    const placeholder = `MATH_BLOCK_${mathCounter}`;
                    mathPlaceholders.push({
                        placeholder: placeholder,
                        formula: preprocessedFormula.trim(),
                        type: 'block',
                        delimiter: '$$',
                        needsRestore: true
                    });
                    mathCounter++;
                    return placeholder;
                });

                // 处理行内公式 \( ... \)
                processedContent = processedContent.replace(/\\\(([\s\S]*?)\\\)/g, function(match, formula) {
                    console.log('匹配到 \\(...\\) 格式:', match);
                    const preprocessedFormula = preprocessLatexFormula(formula);
                    const placeholder = `MATH_INLINE_${mathCounter}`;
                    mathPlaceholders.push({
                        placeholder: placeholder,
                        formula: preprocessedFormula.trim(),
                        type: 'inline',
                        delimiter: '\\(',
                        needsRestore: true
                    });
                    mathCounter++;
                    return placeholder;
                });

                // 处理行内公式 $ ... $
                processedContent = processedContent.replace(/\$([^$\n]+?)\$/g, function(match, formula) {
                    console.log('匹配到 $...$ 格式:', match);
                    const preprocessedFormula = preprocessLatexFormula(formula);
                    const placeholder = `MATH_INLINE_${mathCounter}`;
                    mathPlaceholders.push({
                        placeholder: placeholder,
                        formula: preprocessedFormula.trim(),
                        type: 'inline',
                        delimiter: '$',
                        needsRestore: true
                    });
                    mathCounter++;
                    return placeholder;
                });

                console.log('处理后内容:', JSON.stringify(processedContent));
                console.log('数学占位符:', mathPlaceholders);
                console.log('=== processMathFormulas 结束 ===');

                return { processedContent, mathPlaceholders };
            } catch (error) {
                console.error('Error processing math formulas:', error);
                return { processedContent: content, mathPlaceholders: [] };
            }
        }

        // MathJax 渲染函数
        function renderMathJax() {
            if (window.MathJax) {
                try {
                    if (typeof MathJax.typeset === 'function') {
                        // MathJax 3.x
                        MathJax.typeset();
                    } else if (MathJax.Hub && typeof MathJax.Hub.Queue === 'function') {
                        // MathJax 2.x
                        MathJax.Hub.Queue(["Typeset", MathJax.Hub]);
                    }
                } catch (e) {
                    console.error('MathJax 渲染错误:', e);
                }
            }
        }

        // 为单个元素渲染MathJax的函数
        function renderMathJaxForElement(element) {
            if (!element || element.getAttribute('data-processed') === 'true') {
                return;
            }

            if (typeof MathJax !== 'undefined') {
                try {
                    console.log('开始渲染公式...');

                    if (typeof MathJax.typeset === 'function') {
                        // MathJax 3.x
                        MathJax.typeset([element]);
                    } else if (MathJax.Hub && typeof MathJax.Hub.Queue === 'function') {
                        // MathJax 2.x
                        MathJax.Hub.Process(element);
                        MathJax.Hub.Queue(["Typeset", MathJax.Hub, element]);
                    }

                    element.setAttribute('data-processed', 'true');
                } catch (e) {
                    console.error('MathJax渲染错误:', e);
                }
            }
        }

        // 从URL获取用户信息
        function getUserInfoFromURL() {
            const urlParams = new URLSearchParams(window.location.search);
            return {
                user: urlParams.get('user') || '',
                key: urlParams.get('key') || '',
                token: urlParams.get('token') || ''
            };
        }

        // 全局变量
        let userInfo = getUserInfoFromURL();
        let selectedDocIds = [];
        let userFiles = {};
        let currentResults = null; // 存储当前生成的结果数据

        // 默认配置
        const defaultConfig = {
            "folder": "电磁学",
            "dataset_ids": "a3870650ffa111ef92aa2a5c03e306d6"
        };

        // DOM 元素
        const elements = {
            folder: document.getElementById('folder'),
            choiceCount: document.getElementById('choice-count'),
            calculationCount: document.getElementById('calculation-count'),
            customFilename: document.getElementById('custom-filename'),
            customFilenameGroup: document.getElementById('custom-filename-group'),
            loadConfigBtn: document.getElementById('load-config-btn'),
            generateBtn: document.getElementById('generate-btn'),
            loadHistoryBtn: document.getElementById('load-history-btn'),
            statusMessages: document.getElementById('status-messages'),
            resultsSection: document.getElementById('results-section'),
            resultsContent: document.getElementById('results-content'),
            downloadMarkdownBtn: document.getElementById('download-markdown-btn'),
            historySection: document.getElementById('history-section'),
            historyContent: document.getElementById('history-content'),
            refreshHistoryBtn: document.getElementById('refresh-history-btn'),
            hideHistoryBtn: document.getElementById('hide-history-btn'),
            refreshFilesBtn: document.getElementById('refresh-files-btn'),
            selectAllBtn: document.getElementById('select-all-btn'),
            clearSelectionBtn: document.getElementById('clear-selection-btn'),
            fileListContainer: document.getElementById('file-list-container'),
            selectedCount: document.getElementById('selected-count')
        };

        // 获取用户文件列表
        async function fetchUserFiles() {
            if (!userInfo.user) {
                showStatus('error', '❌ 无法获取用户信息，请从正确的页面访问');
                return;
            }

            try {
                showStatus('info', '📁 正在获取文件列表...');
                const response = await fetch(`/api/ragfiles?username=${userInfo.user}`, {
                    method: 'GET',
                    cache: 'no-cache'
                });

                if (response.ok) {
                    userFiles = await response.json();
                    displayFiles(userFiles);
                    showStatus('success', `✅ 成功获取 ${Object.keys(userFiles).length} 个文件`);
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                console.error('获取文件列表失败:', error);
                showStatus('error', `❌ 获取文件列表失败: ${error.message}`);
                elements.fileListContainer.innerHTML = '<p class="loading-text">获取文件列表失败</p>';
            }
        }

        // 显示文件列表
        function displayFiles(files) {
            if (!files || Object.keys(files).length === 0) {
                elements.fileListContainer.innerHTML = '<p class="loading-text">没有找到可用的文件</p>';
                return;
            }

            let html = '';
            Object.entries(files).forEach(([filename, docId]) => {
                const isSelected = selectedDocIds.includes(docId);
                html += `
                    <div class="file-item ${isSelected ? 'selected' : ''}" onclick="toggleFileSelection('${docId}', '${filename}')">
                        <input type="checkbox" ${isSelected ? 'checked' : ''} onchange="event.stopPropagation(); toggleFileSelection('${docId}', '${filename}')">
                        <div class="file-name">${filename}</div>
                        <div class="file-id">${docId}</div>
                    </div>
                `;
            });

            elements.fileListContainer.innerHTML = html;
            updateSelectedCount();
        }

        // 切换文件选择状态
        function toggleFileSelection(docId, filename) {
            const index = selectedDocIds.indexOf(docId);
            if (index > -1) {
                selectedDocIds.splice(index, 1);
            } else {
                selectedDocIds.push(docId);
            }

            // 重新显示文件列表以更新选中状态
            displayFiles(userFiles);
        }

        // 更新选中文件数量显示
        function updateSelectedCount() {
            elements.selectedCount.textContent = `已选择: ${selectedDocIds.length} 个文件`;
        }

        // 全选文件
        function selectAllFiles() {
            selectedDocIds = Object.values(userFiles);
            displayFiles(userFiles);
        }

        // 清空选择
        function clearFileSelection() {
            selectedDocIds = [];
            displayFiles(userFiles);
        }

        // 检查 MathJax 是否已初始化
        function checkMathJaxReady() {
            if (typeof MathJax === 'undefined' || !MathJax.Hub) {
                console.log('MathJax not ready yet, waiting...');
                setTimeout(checkMathJaxReady, 300);
                return;
            }

            console.log('MathJax is ready');
            // MathJax 准备就绪后的初始化
            initMarkdownRenderer();
        }

        // 事件监听器
        document.addEventListener('DOMContentLoaded', function() {
            // 检查 MathJax 是否就绪
            checkMathJaxReady();

            // 检查用户信息
            if (!userInfo.user || !userInfo.key || !userInfo.token) {
                showStatus('error', '❌ 缺少用户认证信息，请从正确的页面访问');
                elements.generateBtn.disabled = true;
                return;
            }

            showStatus('info', `👤 当前用户: ${userInfo.user}`);

            // 显示用户信息用于调试
            console.log('用户信息:', userInfo);

            // 文件命名方式切换
            document.querySelectorAll('input[name="filename-option"]').forEach(radio => {
                radio.addEventListener('change', function() {
                    if (this.value === 'custom') {
                        elements.customFilenameGroup.style.display = 'block';
                    } else {
                        elements.customFilenameGroup.style.display = 'none';
                    }
                });
            });

            // 文件操作按钮
            elements.refreshFilesBtn.addEventListener('click', fetchUserFiles);
            elements.selectAllBtn.addEventListener('click', selectAllFiles);
            elements.clearSelectionBtn.addEventListener('click', clearFileSelection);

            // 加载默认配置
            elements.loadConfigBtn.addEventListener('click', loadDefaultConfig);

            // 生成题目
            elements.generateBtn.addEventListener('click', generateQuestions);

            // 下载Markdown
            elements.downloadMarkdownBtn.addEventListener('click', downloadMarkdown);

            // 历史题目相关
            elements.loadHistoryBtn.addEventListener('click', loadUserQuestions);
            elements.refreshHistoryBtn.addEventListener('click', loadUserQuestions);
            elements.hideHistoryBtn.addEventListener('click', hideHistory);

            // 初始化：加载默认配置和文件列表
            loadDefaultConfig();
            fetchUserFiles();
            
            // 检查是否处于开发模式（通过URL参数判断）
            const urlParams = new URLSearchParams(window.location.search);
            if (urlParams.get('debug') === 'true') {
                // 在开发模式下自动运行LaTeX渲染测试
                console.log("开发模式：运行LaTeX渲染测试");
                setTimeout(testLatexRendering, 1000); // 延迟1秒执行，确保页面已完全加载
            }
        });

        // 加载默认配置
        function loadDefaultConfig() {
            elements.folder.value = defaultConfig.folder;
            showStatus('success', '✅ 默认配置已加载');
        }



        // 显示状态消息（改进版本）
        function showStatus(type, message) {
            const statusDiv = document.createElement('div');
            statusDiv.className = `status-message status-${type}`;
            statusDiv.innerHTML = message;

            elements.statusMessages.appendChild(statusDiv);

            // 限制状态消息数量，避免页面过长
            const maxMessages = 10;
            const messages = elements.statusMessages.children;
            if (messages.length > maxMessages) {
                // 移除最旧的消息
                for (let i = 0; i < messages.length - maxMessages; i++) {
                    messages[0].remove();
                }
            }

            // 自动滚动到状态消息
            statusDiv.scrollIntoView({ behavior: 'smooth' });

            // 根据消息类型设置不同的自动移除时间
            let removeDelay = 0;
            switch (type) {
                case 'info':
                    removeDelay = 8000; // 8秒
                    break;
                case 'success':
                    removeDelay = 10000; // 10秒
                    break;
                case 'error':
                    removeDelay = 0; // 错误消息不自动移除
                    break;
                default:
                    removeDelay = 5000; // 默认5秒
            }

            if (removeDelay > 0) {
                setTimeout(() => {
                    if (statusDiv.parentNode) {
                        statusDiv.remove();
                    }
                }, removeDelay);
            }
        }

        // 验证配置
        function validateConfig() {
            // 检查用户信息
            if (!userInfo.user || !userInfo.key || !userInfo.token) {
                showStatus('error', '❌ 缺少用户认证信息');
                return false;
            }

            // 检查课程文件夹
            if (!elements.folder.value.trim()) {
                showStatus('error', '❌ 请填写课程文件夹');
                elements.folder.focus();
                return false;
            }

            // 检查文件选择
            if (selectedDocIds.length === 0) {
                showStatus('error', '❌ 请至少选择一个文件');
                return false;
            }

            const choiceCount = parseInt(elements.choiceCount.value);
            const calculationCount = parseInt(elements.calculationCount.value);

            if (choiceCount === 0 && calculationCount === 0) {
                showStatus('error', '❌ 请至少选择生成一种类型的题目');
                return false;
            }

            return true;
        }

        // 生成文件名
        function generateFilename() {
            const filenameOption = document.querySelector('input[name="filename-option"]:checked').value;

            if (filenameOption === 'custom') {
                const customName = elements.customFilename.value.trim();
                if (!customName) {
                    showStatus('error', '❌ 请输入自定义文件名');
                    elements.customFilename.focus();
                    return null;
                }
                return customName;
            } else {
                // 使用时间戳
                const now = new Date();
                const timestamp = now.getFullYear() +
                    String(now.getMonth() + 1).padStart(2, '0') +
                    String(now.getDate()).padStart(2, '0') + '_' +
                    String(now.getHours()).padStart(2, '0') +
                    String(now.getMinutes()).padStart(2, '0') +
                    String(now.getSeconds()).padStart(2, '0');
                return `questions_${timestamp}`;
            }
        }

        // 清空状态消息
        function clearStatusMessages() {
            elements.statusMessages.innerHTML = '';
        }



        // 渲染Markdown内容（使用 MathJax 渲染器）
        function renderMarkdown(content) {
            try {
                // 1. 预处理内容，保护数学公式
                let processedContent = content;

                // 处理换行符转换
                processedContent = processedContent.replace(/\\n/g, '\n');

                const { processedContent: contentWithPlaceholders, mathPlaceholders } = processMathFormulas(processedContent);

                // 2. 使用 marked 处理 Markdown
                let renderedContent;
                if (markdownRenderer && typeof markdownRenderer.parse === 'function') {
                    renderedContent = markdownRenderer.parse(contentWithPlaceholders);
                } else if (markdownRenderer) {
                    renderedContent = markdownRenderer(contentWithPlaceholders);
                } else {
                    // 简单的换行处理
                    renderedContent = escapeHtml(contentWithPlaceholders).replace(/\n/g, '<br>');
                }

                // 3. 恢复数学公式
                mathPlaceholders.forEach(item => {
                    const uniqueId = `math-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

                    // 还原公式中的特殊标记
                    let formula = item.formula;
                    if (item.needsRestore) {
                        formula = restoreLatexFormula(formula);
                    }

                    if (item.type === 'block') {
                        renderedContent = renderedContent.replace(
                            item.placeholder,
                            `<div class="math-display" id="${uniqueId}">\\[${formula}\\]</div>`
                        );
                    } else {
                        renderedContent = renderedContent.replace(
                            item.placeholder,
                            `<span class="math-inline" id="${uniqueId}">\\(${formula}\\)</span>`
                        );
                    }
                });

                return renderedContent;
            } catch (error) {
                console.error('Markdown渲染错误:', error);
                return `<div class="error">渲染错误: ${error.message}</div>`;
            }
        }

        // HTML转义函数
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // 生成Markdown内容
        function generateMarkdownContent(results) {
            if (!results) return '';

            // 优先使用结果中的课程名称，其次使用输入框的值
            const courseName = results.course_name || elements.folder.value.trim() || '未指定课程';
            const timestamp = new Date().toLocaleString('zh-CN');

            let markdown = `# ${courseName} - 题目集\n\n`;
            markdown += `**生成时间：** ${timestamp}\n`;
            markdown += `**文件名：** ${results.filename}\n\n`;

            // 添加选择题
            if (results.choice_questions && results.choice_questions.length > 0) {
                markdown += `## 📝 选择题 (${results.choice_questions.length} 道)\n\n`;

                results.choice_questions.forEach((question, index) => {
                    markdown += `### 第 ${index + 1} 题\n\n`;
                    markdown += `**题目：** ${question.question}\n\n`;

                    if (question.options && question.options.length > 0) {
                        question.options.forEach(option => {
                            markdown += `${option}\n\n`;
                        });
                    }

                    markdown += `**正确答案：** ${question.correct_answer}\n\n`;
                    markdown += `**解析：** ${question.explanation}\n\n`;
                    markdown += `---\n\n`;
                });
            }

            // 添加计算题
            if (results.calculation_questions && results.calculation_questions.length > 0) {
                markdown += `## 🧮 计算题 (${results.calculation_questions.length} 道)\n\n`;

                results.calculation_questions.forEach((question, index) => {
                    markdown += `### 第 ${index + 1} 题\n\n`;
                    markdown += `**题目：** ${question.question}\n\n`;

                    if (question.solution_steps && question.solution_steps.length > 0) {
                        markdown += `**解题步骤：**\n\n`;
                        question.solution_steps.forEach(step => {
                            markdown += `${step.step_number}. ${step.description}\n\n`;
                            if (step.calculation) {
                                markdown += `   \`${step.calculation}\`\n\n`;
                            }
                        });
                    }

                    markdown += `**最终答案：** ${question.final_answer}\n\n`;

                    if (question.key_points && question.key_points.length > 0) {
                        markdown += `**关键知识点：** ${question.key_points.join(', ')}\n\n`;
                    }

                    markdown += `---\n\n`;
                });
            }

            return markdown;
        }

        // 下载Markdown文件
        function downloadMarkdown() {
            if (!currentResults) {
                showStatus('error', '❌ 没有可下载的结果数据');
                return;
            }

            try {
                const markdownContent = generateMarkdownContent(currentResults);
                const blob = new Blob([markdownContent], { type: 'text/markdown;charset=utf-8' });
                const url = URL.createObjectURL(blob);

                const a = document.createElement('a');
                a.href = url;
                a.download = `${currentResults.filename}.md`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);

                showStatus('success', '✅ Markdown文件下载成功');
            } catch (error) {
                console.error('下载Markdown文件失败:', error);
                showStatus('error', `❌ 下载失败: ${error.message}`);
            }
        }

        // 加载用户历史题目
        async function loadUserQuestions() {
            if (!userInfo.user) {
                showStatus('error', '❌ 无法获取用户信息');
                return;
            }

            try {
                elements.historyContent.innerHTML = '<p class="loading-text">正在加载历史题目...</p>';
                elements.historySection.style.display = 'block';
                elements.historySection.scrollIntoView({ behavior: 'smooth' });

                const response = await fetch(`/api/user_questions?user=${encodeURIComponent(userInfo.user)}&key=${encodeURIComponent(userInfo.key)}&token=${encodeURIComponent(userInfo.token)}`, {
                    method: 'GET',
                    cache: 'no-cache'
                });

                if (response.ok) {
                    const data = await response.json();
                    if (data.success) {
                        displayUserQuestions(data.questions);
                        showStatus('success', `✅ 成功加载 ${data.total_count} 个历史题目文件`);
                    } else {
                        throw new Error(data.message || '加载失败');
                    }
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                console.error('加载历史题目失败:', error);
                showStatus('error', `❌ 加载历史题目失败: ${error.message}`);
                elements.historyContent.innerHTML = '<p class="loading-text">加载历史题目失败</p>';
            }
        }

        // 显示用户历史题目
        function displayUserQuestions(questions) {
            if (!questions || questions.length === 0) {
                elements.historyContent.innerHTML = '<p class="loading-text">暂无历史题目</p>';
                return;
            }

            let html = '';
            questions.forEach(question => {
                const fileSize = (question.file_size / 1024).toFixed(1);
                html += `
                    <div class="history-item">
                        <div class="history-item-header">
                            <div class="history-item-title">
                                ${question.course_name} - ${question.question_type_name}
                            </div>
                            <div class="history-item-actions">
                                <button class="btn btn-primary" onclick="loadQuestionDetail('${question.filename}')">📖 查看</button>
                                <button class="btn btn-secondary" onclick="downloadQuestionFile('${question.filename}')">📥 下载</button>
                                <button class="btn btn-secondary" onclick="deleteQuestionFile('${question.filename}')" style="background: #dc3545;">🗑️ 删除</button>
                            </div>
                        </div>
                        <div class="history-item-info">
                            <div><strong>题目数量:</strong> ${question.question_count} 道</div>
                            <div><strong>文件大小:</strong> ${fileSize} KB</div>
                            <div><strong>生成时间:</strong> ${question.modified_time}</div>
                        </div>
                        <div class="history-item-meta">
                            <span>文件名: ${question.filename}</span>
                            <span>时间戳: ${question.timestamp}</span>
                        </div>
                    </div>
                `;
            });

            elements.historyContent.innerHTML = html;
        }

        // 隐藏历史题目区域
        function hideHistory() {
            elements.historySection.style.display = 'none';
        }

        // 加载题目详情（支持渲染显示）
        async function loadQuestionDetail(filename) {
            try {
                showStatus('info', '📖 正在加载题目详情...');

                const response = await fetch(`/api/question_detail?user=${encodeURIComponent(userInfo.user)}&key=${encodeURIComponent(userInfo.key)}&token=${encodeURIComponent(userInfo.token)}&filename=${encodeURIComponent(filename)}`, {
                    method: 'GET',
                    cache: 'no-cache'
                });

                if (response.ok) {
                    const data = await response.json();
                    if (data.success) {
                        // 将加载的内容设置为当前结果并显示（支持渲染）
                        currentResults = {
                            choice_questions: data.content.questions || [],
                            calculation_questions: data.content.calculation_questions || [],
                            filename: filename.replace('.json', ''),
                            timestamp: new Date().toISOString(),
                            course_name: data.content.folder || '历史题目'
                        };
                        displayResults(currentResults);
                        showStatus('success', '✅ 题目详情加载成功');
                    } else {
                        throw new Error(data.message || '加载失败');
                    }
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                console.error('加载题目详情失败:', error);
                showStatus('error', `❌ 加载题目详情失败: ${error.message}`);
            }
        }

        // 下载题目文件
        async function downloadQuestionFile(filename) {
            try {
                showStatus('info', '📥 正在准备下载...');

                const response = await fetch(`/api/question_detail?user=${encodeURIComponent(userInfo.user)}&key=${encodeURIComponent(userInfo.key)}&token=${encodeURIComponent(userInfo.token)}&filename=${encodeURIComponent(filename)}`, {
                    method: 'GET',
                    cache: 'no-cache'
                });

                if (response.ok) {
                    const data = await response.json();
                    if (data.success) {
                        // 生成Markdown内容，包含课程信息
                        const tempResults = {
                            choice_questions: data.content.questions || [],
                            calculation_questions: data.content.calculation_questions || [],
                            filename: filename.replace('.json', ''),
                            timestamp: new Date().toISOString(),
                            course_name: data.content.folder || '历史题目'
                        };

                        const markdownContent = generateMarkdownContent(tempResults);
                        const blob = new Blob([markdownContent], { type: 'text/markdown;charset=utf-8' });
                        const url = URL.createObjectURL(blob);

                        const a = document.createElement('a');
                        a.href = url;
                        a.download = `${filename.replace('.json', '')}.md`;
                        document.body.appendChild(a);
                        a.click();
                        document.body.removeChild(a);
                        URL.revokeObjectURL(url);

                        showStatus('success', '✅ 文件下载成功');
                    } else {
                        throw new Error(data.message || '下载失败');
                    }
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                console.error('下载题目文件失败:', error);
                showStatus('error', `❌ 下载失败: ${error.message}`);
            }
        }

        // 删除题目文件
        async function deleteQuestionFile(filename) {
            if (!confirm(`确定要删除题目文件 "${filename}" 吗？此操作不可恢复！`)) {
                return;
            }

            try {
                showStatus('info', '🗑️ 正在删除文件...');

                const response = await fetch('/api/delete_question_file', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        user: userInfo.user,
                        key: userInfo.key,
                        token: userInfo.token,
                        filename: filename
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    if (data.success) {
                        showStatus('success', '✅ 文件删除成功');
                        // 重新加载历史题目列表
                        loadUserQuestions();
                    } else {
                        throw new Error(data.message || '删除失败');
                    }
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                console.error('删除题目文件失败:', error);
                showStatus('error', `❌ 删除失败: ${error.message}`);
            }
        }

        // 生成题目（改进版本，带更好的错误处理）
        async function generateQuestions() {
            if (!validateConfig()) {
                return;
            }

            const filename = generateFilename();
            if (!filename) {
                return;
            }

            // 禁用生成按钮并显示加载状态
            elements.generateBtn.disabled = true;
            elements.generateBtn.innerHTML = '<span class="loading"></span>正在生成题目...';

            // 清空之前的状态消息和结果
            clearStatusMessages();
            elements.resultsSection.style.display = 'none';

            try {
                const choiceCount = parseInt(elements.choiceCount.value);
                const calculationCount = parseInt(elements.calculationCount.value);
                const totalQuestions = choiceCount + calculationCount;
                let currentStep = 0;
                const totalSteps = (choiceCount > 0 ? 1 : 0) + (calculationCount > 0 ? 1 : 0);

                showStatus('info', `🚀 开始生成题目，共需生成 ${totalQuestions} 道题目，请耐心等待...`);

                const results = {
                    choice_questions: [],
                    calculation_questions: [],
                    filename: filename,
                    timestamp: new Date().toISOString()
                };

                let successCount = 0;
                let failureCount = 0;

                // 生成选择题
                if (choiceCount > 0) {
                    currentStep++;
                    showStatus('info', `📝 [${currentStep}/${totalSteps}] 正在生成 ${choiceCount} 道选择题，请耐心等待（可能需要2-5分钟）...`);
                    try {
                        const choiceResult = await callGenerateAPI('choice', choiceCount);
                        if (choiceResult.success && choiceResult.questions) {
                            // 验证和标准化数据
                            const validatedData = validateAndNormalizeQuestionsData(choiceResult.questions, 'choice');
                            if (validatedData && validatedData.questions) {
                                results.choice_questions = validatedData.questions;
                                const actualCount = results.choice_questions.length;
                                if (actualCount > 0) {
                                    showStatus('success', `✅ 成功生成 ${actualCount} 道选择题`);
                                    successCount += actualCount;
                                } else {
                                    showStatus('error', `❌ 选择题生成失败: 生成的题目质量不符合要求`);
                                    failureCount++;
                                }
                            } else {
                                showStatus('error', `❌ 选择题生成失败: 数据格式验证失败`);
                                failureCount++;
                            }
                        } else {
                            showStatus('error', `❌ 选择题生成失败: ${choiceResult.message || '未知错误'}`);
                            failureCount++;
                        }
                    } catch (error) {
                        showStatus('error', `❌ 选择题生成失败: ${error.message}`);
                        console.error('选择题生成错误:', error);
                        failureCount++;
                    }
                }

                // 生成计算题
                if (calculationCount > 0) {
                    currentStep++;
                    showStatus('info', `🧮 [${currentStep}/${totalSteps}] 正在生成 ${calculationCount} 道计算题，请耐心等待（可能需要2-5分钟）...`);
                    try {
                        const calcResult = await callGenerateAPI('calculation', calculationCount);
                        if (calcResult.success && calcResult.questions) {
                            // 验证和标准化数据
                            const validatedData = validateAndNormalizeQuestionsData(calcResult.questions, 'calculation');
                            if (validatedData && validatedData.calculation_questions) {
                                results.calculation_questions = validatedData.calculation_questions;
                                const actualCount = results.calculation_questions.length;
                                if (actualCount > 0) {
                                    showStatus('success', `✅ 成功生成 ${actualCount} 道计算题`);
                                    successCount += actualCount;
                                } else {
                                    showStatus('error', `❌ 计算题生成失败: 生成的题目质量不符合要求`);
                                    failureCount++;
                                }
                            } else {
                                showStatus('error', `❌ 计算题生成失败: 数据格式验证失败`);
                                failureCount++;
                            }
                        } else {
                            showStatus('error', `❌ 计算题生成失败: ${calcResult.message || '未知错误'}`);
                            failureCount++;
                        }
                    } catch (error) {
                        showStatus('error', `❌ 计算题生成失败: ${error.message}`);
                        console.error('计算题生成错误:', error);
                        failureCount++;
                    }
                }

                // 显示最终结果
                if (successCount > 0) {
                    showStatus('success', `🎉 题目生成完成！成功生成 ${successCount} 道题目并已自动保存`);
                    currentResults = results; // 保存结果数据
                    displayResults(results);
                } else {
                    showStatus('error', '❌ 没有成功生成任何题目，请检查网络连接或稍后重试');
                }

                // 显示统计信息
                if (failureCount > 0) {
                    showStatus('info', `📊 生成统计：成功 ${successCount} 道，失败 ${failureCount} 道`);
                }

            } catch (error) {
                console.error('生成题目时出错:', error);
                showStatus('error', `❌ 生成过程中发生错误: ${error.message}`);
            } finally {
                // 恢复生成按钮
                elements.generateBtn.disabled = false;
                elements.generateBtn.innerHTML = '🚀 开始生成题目';
            }
        }

        // 调用生成API（带重试机制）
        async function callGenerateAPI(questionType, numQuestions, retryCount = 0) {
            const maxRetries = 3;
            const requestData = {
                user: userInfo.user,
                key: userInfo.key,
                token: userInfo.token,
                folder: elements.folder.value.trim(),
                selected_doc_ids: selectedDocIds.join(','),
                dataset_id: defaultConfig.dataset_ids,
                question_type: questionType,
                num_questions: numQuestions
            };

            try {
                const response = await fetch('/api/gen_questions', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestData)
                });

                if (!response.ok) {
                    const errorText = await response.text();
                    throw new Error(`HTTP ${response.status}: ${errorText}`);
                }

                const result = await response.json();
                return result;
            } catch (error) {
                console.error(`API调用失败 (尝试 ${retryCount + 1}/${maxRetries + 1}):`, error);

                // 处理特定错误类型并决定是否重试
                let shouldRetry = false;
                let errorMessage = error.message;

                // 处理Invalid argument错误
                if (errorMessage.includes('Invalid argument') || errorMessage.includes('Errno 22')) {
                    errorMessage = '正在准备生成题目，请稍候...';
                    shouldRetry = true;
                }
                // 处理网络错误
                else if (errorMessage.includes('network') || errorMessage.includes('Failed to fetch') ||
                         errorMessage.includes('TypeError') || errorMessage.includes('NetworkError')) {
                    errorMessage = '网络连接不稳定，正在重试...';
                    shouldRetry = true;
                }
                // 处理服务器错误
                else if (errorMessage.includes('500') || errorMessage.includes('502') ||
                         errorMessage.includes('503') || errorMessage.includes('504')) {
                    errorMessage = '服务器暂时繁忙，正在重试...';
                    shouldRetry = true;
                }

                // 如果应该重试且还有重试次数
                if (shouldRetry && retryCount < maxRetries) {
                    const retryDelay = Math.min(2000 * Math.pow(2, retryCount), 10000); // 指数退避，最大10秒
                    showStatus('info', `${errorMessage} (${retryCount + 1}/${maxRetries} 次重试，${retryDelay/1000}秒后重试)`);

                    await new Promise(resolve => setTimeout(resolve, retryDelay));
                    return callGenerateAPI(questionType, numQuestions, retryCount + 1);
                }

                // 重试次数用完或不应该重试，抛出错误
                throw new Error(errorMessage);
            }
        }

        // 数据验证和标准化函数
        function validateAndNormalizeQuestionsData(questionsData, questionType) {
            if (!questionsData || typeof questionsData !== 'object') {
                console.error('Invalid questions data:', questionsData);
                return null;
            }

            if (questionType === 'choice') {
                return validateChoiceQuestions(questionsData);
            } else if (questionType === 'calculation') {
                return validateCalculationQuestions(questionsData);
            }

            return questionsData;
        }

        // 检查是否包含占位符文本
        function isPlaceholderText(text) {
            const placeholders = [
                '题目内容', '选项A', '选项B', '选项C', '选项D',
                '详细解析', '解析内容', '解题步骤说明', '关键知识点',
                'question content', 'option a', 'option b', 'explanation'
            ];

            const lowerText = text.toLowerCase();
            return placeholders.some(placeholder => lowerText.includes(placeholder.toLowerCase()));
        }

        function validateChoiceQuestions(questionsData) {
            if (!questionsData.questions || !Array.isArray(questionsData.questions)) {
                console.error('Invalid choice questions data');
                return { questions: [] };
            }

            const validatedQuestions = questionsData.questions.map((question, index) => {
                return {
                    id: question.id || (index + 1).toString(),
                    question: question.question || '',
                    options: Array.isArray(question.options) ? question.options : [],
                    correct_answer: question.correct_answer || 'A',
                    explanation: question.explanation || ''
                };
            }).filter(q => {
                // 过滤空题目和占位符题目
                if (!q.question.trim()) return false;
                if (isPlaceholderText(q.question)) {
                    console.warn('过滤占位符题目:', q.question);
                    return false;
                }

                // 检查选项是否包含占位符
                const hasPlaceholderOptions = q.options.some(option => isPlaceholderText(option));
                if (hasPlaceholderOptions) {
                    console.warn('过滤包含占位符选项的题目:', q.question);
                    return false;
                }

                return true;
            });

            return { questions: validatedQuestions };
        }

        function validateCalculationQuestions(questionsData) {
            if (!questionsData.calculation_questions || !Array.isArray(questionsData.calculation_questions)) {
                console.error('Invalid calculation questions data');
                return { calculation_questions: [] };
            }

            const validatedQuestions = questionsData.calculation_questions.map((question, index) => {
                return {
                    id: question.id || (index + 1).toString(),
                    question: question.question || '',
                    solution_steps: Array.isArray(question.solution_steps) ? question.solution_steps : [],
                    final_answer: question.final_answer || '',
                    key_points: Array.isArray(question.key_points) ? question.key_points : []
                };
            }).filter(q => {
                // 过滤空题目和占位符题目
                if (!q.question.trim()) return false;
                if (isPlaceholderText(q.question)) {
                    console.warn('过滤占位符题目:', q.question);
                    return false;
                }

                return true;
            });

            return { calculation_questions: validatedQuestions };
        }

        // 显示结果（支持渲染）
        function displayResults(results) {
            let html = '<div class="rendered-content">';

            // 如果是历史题目，显示课程信息
            if (results.course_name) {
                html += `<div style="background: #e3f2fd; padding: 15px; border-radius: 8px; margin-bottom: 20px; border-left: 4px solid #2196f3;">
                    <h4 style="margin: 0; color: #1976d2;">📚 ${renderMarkdown(results.course_name)}</h4>
                    <p style="margin: 5px 0 0 0; color: #666; font-size: 0.9em;">历史题目 - ${results.filename}</p>
                </div>`;
            }

            // 显示选择题
            if (results.choice_questions && results.choice_questions.length > 0) {
                html += '<h4>📝 选择题</h4>';
                results.choice_questions.forEach((question, index) => {
                    html += `
                        <div class="question-item">
                            <div class="question-title">第 ${index + 1} 题</div>
                            <div class="question-content">
                                <p><strong>题目：</strong></p>
                                <div style="margin: 10px 0; padding: 10px; background: #f8f9fa; border-radius: 6px;">
                                    ${renderMarkdown(question.question || '')}
                                </div>
                                <div style="margin: 15px 0;">
                                    <strong>选项：</strong>
                                    <div style="margin-left: 15px;">
                                        ${(question.options || []).map(option =>
                                            `<div style="margin: 8px 0; padding: 8px; background: #fff; border: 1px solid #e9ecef; border-radius: 4px;">
                                                ${renderMarkdown(option)}
                                            </div>`
                                        ).join('')}
                                    </div>
                                </div>
                                <p><strong>正确答案：</strong><span style="color: #28a745; font-weight: bold;">${question.correct_answer || ''}</span></p>
                                <div style="margin-top: 15px;">
                                    <p><strong>解析：</strong></p>
                                    <div style="margin: 10px 0; padding: 10px; background: #fff3cd; border-radius: 6px; border-left: 4px solid #ffc107;">
                                        ${renderMarkdown(question.explanation || '')}
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                });
            }

            // 显示计算题
            if (results.calculation_questions && results.calculation_questions.length > 0) {
                html += '<h4>🧮 计算题</h4>';
                results.calculation_questions.forEach((question, index) => {
                    html += `
                        <div class="question-item">
                            <div class="question-title">第 ${index + 1} 题</div>
                            <div class="question-content">
                                <p><strong>题目：</strong></p>
                                <div style="margin: 10px 0; padding: 10px; background: #f8f9fa; border-radius: 6px;">
                                    ${renderMarkdown(question.question || '')}
                                </div>
                                <div style="margin: 15px 0;">
                                    <strong>解题步骤：</strong>
                                    <div style="margin-left: 15px;">
                                        ${(question.solution_steps || []).map(step =>
                                            `<div style="margin: 10px 0; padding: 10px; background: #fff; border: 1px solid #e9ecef; border-radius: 4px;">
                                                <div style="font-weight: 600; color: #495057; margin-bottom: 8px;">
                                                    ${step.step_number}. ${renderMarkdown(step.description || '')}
                                                </div>
                                                ${step.calculation ?
                                                    `<div style="margin-left: 20px; padding: 8px; background: #f8f9fa; border-radius: 4px; font-family: 'Courier New', monospace; font-size: 0.9em;">
                                                        ${renderMarkdown(step.calculation)}
                                                    </div>` : ''
                                                }
                                            </div>`
                                        ).join('')}
                                    </div>
                                </div>
                                <div style="margin-top: 15px;">
                                    <p><strong>最终答案：</strong></p>
                                    <div style="margin: 10px 0; padding: 10px; background: #d4edda; border-radius: 6px; border-left: 4px solid #28a745;">
                                        ${renderMarkdown(question.final_answer || '')}
                                    </div>
                                </div>
                                ${(question.key_points && question.key_points.length > 0) ?
                                    `<div style="margin-top: 15px;">
                                        <p><strong>关键知识点：</strong></p>
                                        <div style="margin: 10px 0; padding: 10px; background: #e2e3e5; border-radius: 6px;">
                                            ${question.key_points.map(point => renderMarkdown(point)).join(' • ')}
                                        </div>
                                    </div>` : ''
                                }
                            </div>
                        </div>
                    `;
                });
            }

            html += '</div>';

            elements.resultsContent.innerHTML = html;
            elements.resultsSection.style.display = 'block';
            elements.downloadMarkdownBtn.style.display = 'inline-block'; // 显示下载按钮
            elements.resultsSection.scrollIntoView({ behavior: 'smooth' });

            // 渲染数学公式
            setTimeout(() => {
                renderMathJaxForElement(elements.resultsSection);
                // 延迟再次渲染确保所有公式都被处理
                setTimeout(() => {
                    renderMathJax();
                }, 200);
            }, 100);
        }

        // 测试LaTeX公式渲染函数 - 用于调试
        function testLatexRendering() {
            console.log("测试LaTeX公式渲染");

            // 用户提供的完整测试文本
            const userTestText = "### 详细解析\\n\\n本题涉及一个边长为 \\\\(a\\\\) 的正方形平面，$$a^2+\\\\frac{a}{2}$$, 在其垂直轴线上距离中心点 \\\\(O\\\\) 为 \\\\(a/2\\\\) 处放置一个点电荷 \\\\(q\\\\)（正电荷），要求计算通过该平面的电场强度通量。原始解析的核心观点是正确的：基于高斯定律和对称性分析，通过该平面的通量为 \\\\(\\\\frac{q}{6\\\\epsilon_0}\\\\)（对应选项 A）。下面我将详细展开这一结论，融入相关物理概念和原理，确保解析清晰易懂。\\n\\n#### 1. **核心概念回顾：高斯定律**\\n   - **高斯定律**是电磁学的基本原理之一，它描述了电场与电荷分布的关系。定律指出：通过任意封闭曲面（高斯面）的电场强度通量 \\\\(\\\\Phi_E\\\\) 等于该曲面内包围的净电荷 \\\\(q_{\\\\text{enc}}\\\\) 除以真空介电常数 \\\\(\\\\epsilon_0\\\\)：\\n     \\\\[\\n     \\\\Phi_E = \\\\oint \\\\mathbf{E} \\\\cdot d\\\\mathbf{A} = \\\\frac{q_{\\\\text{enc}}}{\\\\epsilon_0}\\n     \\\\]\\n     其中，\\\\(\\\\mathbf{E}\\\\) 是电场强度矢量，\\\\(d\\\\mathbf{A}\\\\) 是曲面面积微元的矢量（方向垂直于曲面），积分表示对整个封闭曲面的通量求和。\\n   - **点电荷的特殊性**：对于一个孤立的点电荷 \\\\(q\\\\)，其电场是球对称的（电场强度大小为 \\\\(E = \\\\frac{1}{4\\\\pi\\\\epsilon_0} \\\\frac{q}{r^2}\\\\)，方向沿径向）。如果点电荷位于封闭曲面内，则通过该封闭曲面的总通量恒为 \\\\(\\\\frac{q}{\\\\epsilon_0}\\\\)，与曲面的形状和大小无关（前提是电荷在曲面内）。这源于电场线的连续性：点电荷发出的电场线均匀发散，总通量由电荷量决定。\\n\\n#### 2. **开曲面的处理：构造封闭曲面**\\n   - 本题中的正方形平面是一个**开曲面**（非封闭），因此不能直接应用高斯定律计算通量。为了求解，需要构造一个合适的封闭曲面，利用对称性间接推导。\\n   - **构造立方体高斯面**：以点电荷为中心，构造一个边长为 \\\\(a\\\\) 的立方体（如图示意）。点电荷位于立方体一个面的中心，因为题目给定点电荷在正方形平面中垂线上距中心 \\\\(O\\\\) 点 \\\\(a/2\\\\) 处，而立方体面中心到其几何中心的距离恰好为 \\\\(a/2\\\\)（立方体边长 \\\\(a\\\\)，几何中心到面中心的距离为半边长，即 \\\\(a/2\\\\)）。\\n     - 为什么选择立方体？点电荷的电场具有径向对称性，而立方体具有高度对称性（各面几何形状相同），这确保了电场线在立方体各面上均匀分布。\\n   - **封闭曲面的通量**：点电荷位于立方体内部，因此通过整个立方体的总通量为：\\n     \\\\[\\n     \\\\Phi_{\\\\text{total}} = \\\\frac{q}{\\\\epsilon_0}\\n     \\\\]\\n\\n#### 3. **对称性分析与通量分配**\\n   - **对称性原理**：点电荷位于立方体几何中心，电场线呈放射状均匀分布。立方体有 6 个完全相同的面（每个面为正方形，边长 \\\\(a\\\\)），因此电场线等量地穿过每个面。这意味着通过每个面的通量相等。\\n   - **计算单个面的通量**：设通过每个面的通量为 \\\\(\\\\Phi_{\\\\text{face}}\\\\)，则总通量可分解为：\\n     \\\\[\\n     \\\\Phi_{\\\\text{total}} = 6 \\\\Phi_{\\\\text{face}}\\n     \\\\]\\n     代入总通量公式：\\n     \\\\[\\n     6 \\\\Phi_{\\\\text{face}} = \\\\frac{q}{\\\\epsilon_0} \\\\implies \\\\Phi_{\\\\text{face}} = \\\\frac{q}{6\\\\epsilon_0}\\n     \\\\]\\n     因此，通过原始正方形平面（即立方体中的一个面）的通量即为 \\\\(\\\\frac{q}{6\\\\epsilon_0}\\\\)。\\n\\n#### 4. **结论与答案验证**\\n   - 通过该平面的电场强度通量为 \\\\(\\\\frac{q}{6\\\\epsilon_0}\\\\)，选项 A 正确。\\n   - **错误选项分析**：\\n     - **选项 B**：可能是点电荷的电场强度公式 \\\\(E = \\\\frac{1}{4\\\\pi\\\\epsilon_0} \\\\frac{q}{r^2}\\\\)，但问题要求的是通量（标量，单位 N·m²/C），而非电场强度（矢量）。混淆两者会导致错误。\\n     - **选项 C 和 D**：如 \\\\(\\\\frac{q}{12\\\\epsilon_0}\\\\) 或类似值，缺乏物理依据。可能源于错误假设（如误用距离或不对称构造），但本题的对称性确保了 \\\\(\\\\frac{q}{6\\\\epsilon_0}\\\\) 是唯一合理结果。\\n\\n#### 5. **附加说明：通量计算的通用方法**\\n   - 如果对称性不足，可直接积分求解（通量定义为 \\\\(\\\\Phi_E = \\\\int \\\\mathbf{E} \\\\cdot d\\\\mathbf{A}\\\\))，但本题的点电荷位置和平面几何使积分复杂（需考虑电场强度的空间变化和角度）。对称性方法更简洁高效，体现了高斯定律的威力。\\n   - **物理意义**：通量 \\\\(\\\\frac{q}{6\\\\epsilon_0}\\\\) 表示穿过平面的电场线比例（约 16.7%）。如果点电荷位置改变（如不在面中心），对称性破坏，通量会不同，但本题条件确保了解答的准确性。\\n\\n本解析强化了原始解析的核心逻辑，同时融入高斯定律、对称性和电场分布原理，帮助深入理解问题。最终答案：\\\\(\\\\boxed{\\\\dfrac{q}{6\\\\epsilon_0}}\\\\)（选项 A）。";

            // 专门测试 \\[...\\] 格式的公式
            const bracketFormulaTest = "测试 \\\\[...\\\\] 格式：\\n\\n\\\\[\\n     6 \\\\Phi_{\\\\text{face}} = \\\\frac{q}{\\\\epsilon_0} \\\\implies \\\\Phi_{\\\\text{face}} = \\\\frac{q}{6\\\\epsilon_0}\\n     \\\\]\\n\\n这个公式应该能正确渲染。";

            console.log("用户测试文本:", userTestText);
            console.log("方括号公式测试:", bracketFormulaTest);

            // 渲染测试文本
            const renderedText = renderMarkdown(userTestText);
            const renderedBracketTest = renderMarkdown(bracketFormulaTest);
            console.log("渲染结果:", renderedText);
            console.log("方括号公式渲染结果:", renderedBracketTest);

            // 在页面中显示测试结果
            const testResults = {
                choice_questions: [],
                calculation_questions: [
                    {
                        question: "用户提供的完整测试文本",
                        solution_steps: [],
                        final_answer: userTestText,
                        key_points: ["完整LaTeX渲染测试", "行内公式 \\(\\)", "行间公式 $$$$", "\\[ \\] 格式"]
                    },
                    {
                        question: "专门测试 \\\\[...\\\\] 格式",
                        solution_steps: [],
                        final_answer: bracketFormulaTest,
                        key_points: ["\\\\[ \\\\] 格式测试", "多行公式", "换行符处理"]
                    }
                ],
                filename: "user_latex_test_enhanced",
                course_name: "增强LaTeX渲染测试"
            };

            // 将测试结果显示在页面上
            currentResults = testResults;
            displayResults(testResults);
        }

        // 测试HTML标签处理函数
        function testHtmlTagProcessing() {
            console.log("测试HTML标签处理");

            // 测试用例：模拟用户遇到的问题
            const testContent = "点电荷通量与立体角关系：对于一个点电荷，通过任意开放曲面的通量取决于该曲面对点电荷所张的立体角：<br>$<br>\\Phi_E = \\frac{q}{4\\pi\\epsilon_0} \\cdot \\Omega<br>$<br>其中 \\Omega 是立体角";

            console.log("原始内容:", testContent);

            // 渲染
            const rendered = renderMarkdown(testContent);
            console.log("渲染结果:", rendered);

            return {
                original: testContent,
                rendered: rendered
            };
        }

        // 在控制台中提供测试函数，可通过浏览器控制台调用
        window.testLatexRendering = testLatexRendering;
        window.testHtmlTagProcessing = testHtmlTagProcessing;

    </script>
</body>
</html>