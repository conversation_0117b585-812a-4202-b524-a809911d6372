import os
from duckduckgosearch import Web_Agent
from moviepy.editor import VideoFileClip
import speech_recognition as sr
import whisper
import torch
import re
import time
import json
from getDocId import upload_or_get_doc_id,uploadvideo_or_get_doc_id,main_upload_video_documents, match_and_record_files, upload_and_parse_documents
from josn2Srt import json_to_srt
import yaml
import threading

###########################################







def load_user_config(username):
    with open('user_configs.json', 'r', encoding='utf-8') as f:
        configs = json.load(f)
    return configs.get(username, {})


# api_key = "sk-izwfqiufrwdqfkvqzklcyaashpmraqaaosuchkjnettqpabb"  #deepseek


SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
VIDEO_DIR = os.path.join(SCRIPT_DIR, "video")
api_key_model= "ragflow-Y1NTkyNDBlZmZhNTExZWY5ZmNhZWUzZD" 
RAGDOC_DIR = os.path.join(SCRIPT_DIR, "ragdoc")
RAGDOC_DIR_user = os.path.join(SCRIPT_DIR, "user")
RAGDOC_DIR_user_files = os.path.join(RAGDOC_DIR_user, "user_files.json")
DOC_IDS_FILE = os.path.join(RAGDOC_DIR, "doc_ids.json")
blog_dir = os.path.join(SCRIPT_DIR, "blogs")
COURSE_VIDEO_BASE = r"D:\github\ragproj\coursevideo"  # coursevideo 基础目录
config_file=os.path.join(SCRIPT_DIR, "config.yaml")
def get_course_video_dirs():
    """
    获取 coursevideo 目录下的所有文件夹
    """
    video_dirs = [{"path": VIDEO_DIR, "type": "user"}]  # 保留原始用户目录
    
    try:
        # 确保目录存在
        if not os.path.exists(COURSE_VIDEO_BASE):
            print(f"Warning: Course video base directory not found: {COURSE_VIDEO_BASE}")
            return video_dirs

        # 获取所有子目录
        for item in os.listdir(COURSE_VIDEO_BASE):
            full_path = os.path.join(COURSE_VIDEO_BASE, item)
            if os.path.isdir(full_path):
                # 检查文件夹名称是否符合 "zh-xxx" 格式
                language = "en"  # 默认语言为英语
                if "-" in item:
                    lang_prefix = item.split("-")[0].lower()
                    if lang_prefix in ["zh", "en", "ja", "ko","de","fr"]:  # 可以添加更多支持的语言前缀
                        language = lang_prefix
                
                # 将每个子目录添加到列表中
                video_dirs.append({
                    "path": full_path,
                    "type": "admin",
                    "name": language  # 使用提取的语言代码
                })
                print(f"Added course directory: {item} with language: {language}")

        return video_dirs
    except Exception as e:
        print(f"Error scanning course video directories: {str(e)}")
        return video_dirs

# 初始化视频目录列表
VIDEO_DIRS = get_course_video_dirs()

#####################################################################
#rag 


def get_username_by_file(file_name: str) -> str:
    """
    根据文件名查找对应的用户名。
    如果在user_files.json中找不到，则从文件名中提取用户名（第一个_前的内容）。

    参数:
    - file_name: 要查找的文件名

    返回:
    - str: 对应的用户名，如果未找到则返回 None
    """
    user_files_path = 'user/user_files.json'
    user_files = {}
    
    # 确保user目录存在
    try:
        os.makedirs('user', exist_ok=True)
        print(f"当前工作目录: {os.getcwd()}")
        print(f"user_files.json 完整路径: {os.path.abspath(user_files_path)}")
    except Exception as e:
        print(f"创建目录失败: {str(e)}")
    
    # 尝试读取现有的user_files.json
    try:
        if os.path.exists(user_files_path):
            # 检查文件权限
            if not os.access(user_files_path, os.R_OK):
                print(f"警告: 没有读取 {user_files_path} 的权限")
                return None

            # 首先检查文件大小
            file_size = os.path.getsize(user_files_path)
            print(f"文件大小: {file_size} 字节")
            
            if file_size == 0:
                print(f"警告: {user_files_path} 是空文件")
                # 创建一个基本的JSON结构
                with open(user_files_path, 'w', encoding='utf-8') as f:
                    json.dump({"admin": []}, f, indent=2, ensure_ascii=False)
            else:
                try:
                    # 先读取原始内容进行检查
                    with open(user_files_path, 'rb') as f:
                        raw_content = f.read()
                        print(f"文件前10个字节: {raw_content[:10]}")
                        
                        # 检查并移除BOM
                        if raw_content.startswith(b'\xef\xbb\xbf'):
                            print("检测到BOM标记，将会移除")
                            content = raw_content[3:].decode('utf-8')
                        else:
                            content = raw_content.decode('utf-8')
                        
                        content = content.strip()
                        if content:
                            try:
                                user_files = json.loads(content)
                                print(f"成功加载JSON文件，包含的键: {list(user_files.keys())}")
                            except json.JSONDecodeError as je:
                                print(f"JSON解析错误: {str(je)}")
                                print(f"问题位置: 行 {je.lineno}, 列 {je.colno}")
                                print(f"文件内容片段: {content[:100]}...")
                        else:
                            print(f"警告: {user_files_path} 内容为空")
                except UnicodeDecodeError as ue:
                    print(f"文件编码错误: {str(ue)}")
                    # 尝试使用不同的编码重新读取
                    with open(user_files_path, 'r', encoding='gbk') as f:
                        content = f.read().strip()
                        if content:
                            user_files = json.loads(content)
        else:
            print(f"警告: {user_files_path} 文件不存在")
            # 创建一个新的空JSON文件
            with open(user_files_path, 'w', encoding='utf-8') as f:
                json.dump({"admin": []}, f, indent=2, ensure_ascii=False)
    except Exception as e:
        print(f"读取 {user_files_path} 时发生错误: {str(e)}")
        print(f"错误类型: {type(e).__name__}")
        import traceback
        print(f"错误堆栈: {traceback.format_exc()}")
        # 不中断执行，继续使用空的user_files字典

    # 首先在现有的user_files.json中查找
    for username, files in user_files.items():
        if file_name in files:
            print(f"在user_files.json中找到文件 {file_name} 所属用户名: {username}")
            return username
    
    # 如果在user_files.json中找不到，尝试从文件名中提取用户名
    if '_' in file_name:
        extracted_username = file_name.split('_')[0]
        print(f"从文件名中提取到用户名: {extracted_username}")
        return extracted_username
            
    print(f"无法从文件名 {file_name} 中提取用户名")
    return None





def is_user_vip(username: str) -> bool:
    """
    判断给定用户名是否为 VIP 用户。

    参数:
    - username: 要检查的用户名
    - config_file: 存储用户信息的配置文件路径

    返回:
    - bool: 如果用户是 VIP，返回 True；否则返回 False

    """
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            user_key_config = yaml.safe_load(f)

        if username in user_key_config['credentials']['usernames']:
            user_info = user_key_config['credentials']['usernames'][username]
            if 'vip_expiry' in user_info:
                expiry_timestamp = user_info['vip_expiry']
                current_time = time.time()
                if expiry_timestamp > current_time:  # VIP 未过期则返回 True
                    print(f"用户 {username} 是 VIP 用户，VIP 过期时间: {expiry_timestamp}, 当前时间: {current_time}") 
                    return True
                elif username=="admin":
                    print(f"用户 {username} 是 admin 用户")
                    return True
                else:
                    print(f"用户 {username} 不是 VIP 用户，VIP 过期时间: {expiry_timestamp}, 当前时间: {current_time}")
                    return False
            elif username=="admin":
                # print(f"用户 {username} 是 admin 用户")
                return True
            else:
                print(f"用户 {username} 不是 VIP 用户")
                return False
        else:
            print(f"用户 {username} 不存在")
        return False  # 用户不存在或不是 VIP
    except FileNotFoundError:
        print(f"配置文件未找到: {config_file}")
        return False
    except yaml.YAMLError as e:
        print(f"解析 YAML 文件时发生错误: {str(e)}")
        return False
    except Exception as e:
        print(f"发生未知错误: {str(e)}")    
        return False









def process_ragdoc_files():
    # 加载已存在的 doc_ids
    doc_ids = {}
    if os.path.exists(DOC_IDS_FILE):
        try:
            with open(DOC_IDS_FILE, 'r', encoding='utf-8') as f:
                content = f.read().strip()
                if content:
                    doc_ids = json.loads(content)
                else:
                    print(f"警告: {DOC_IDS_FILE} 是空文件")
        except json.JSONDecodeError as e:
            print(f"错误: 无法解析 {DOC_IDS_FILE}. 错误信息: {str(e)}")
            print("创建新的 doc_ids 字典")
        except Exception as e:
            print(f"读取 {DOC_IDS_FILE} 时发生未知错误: {str(e)}")
            print("创建新的 doc_ids 字典")
    else:
        print(f"{DOC_IDS_FILE} 不存在，将创建新文件")
    
    # 处理 ragdoc 和 blog 文件夹
    folders_to_process = [
        (RAGDOC_DIR, "test"),
        (blog_dir, "blog")
    ]
    
    for folder_path, kb_name in folders_to_process:
        # 确保目录存在
        if not os.path.exists(folder_path):
            os.makedirs(folder_path, exist_ok=True)
            continue

        files = [f for f in os.listdir(folder_path) if os.path.isfile(os.path.join(folder_path, f))]
        # print(f"知识库{kb_name}中所有文件{files}")
        
        for file in files:
            try:

                # 跳过 doc_ids.json 文件
                if file == os.path.basename(DOC_IDS_FILE):
                    continue

                file_path = os.path.join(folder_path, file)
                
                # 检查文件是否已经在 doc_ids 中且上传成功
                if file in doc_ids:
                    # print(f"文件 {file} 已经上传过，文档ID: {doc_ids[file]}")
                    continue

                # 获取该文件在user_files.json中的用户名
                username = get_username_by_file(file)
                if not username:
                    print(f"无法获取文件 {file} 的用户名，跳过处理")
                    continue

                if kb_name=="test":
                    # 通过查询判断username是否为vip
                    if is_user_vip(username):
                        print(f"用户 {username} 是 VIP 用户，上传文件 {file} 到{kb_name}知识库...")
                        parser_id = "ragvip"
                        doc_id = upload_and_parse_documents(file_path, kb_name, parser_id)
                        time.sleep(200)
                    else:
                        print(f"用户 {username} 不是 VIP 用户，使用基础解析方式上传文件 {file} 到{kb_name}知识库...")
                        parser_id = "naive"
                        doc_id = upload_and_parse_documents(file_path, kb_name, parser_id)
                        time.sleep(200)
       
                if kb_name=="blog":
                    parser_id = "naive"
                    doc_id = upload_and_parse_documents(file_path, kb_name, parser_id)
                    time.sleep(10)
       
                if doc_id:
                    print(f"文件 {file} 上传成功，文档ID: {doc_id}")
                    # 更新 doc_ids
                    doc_ids[file] = doc_id
                    # 保存更新后的 doc_ids
                    try:
                        with open(DOC_IDS_FILE, 'w', encoding='utf-8') as f:
                            json.dump(doc_ids, f, indent=2, ensure_ascii=False)
                        print(f"已更新 {DOC_IDS_FILE}")
                    except Exception as e:
                        print(f"保存 {DOC_IDS_FILE} 时发生错误: {str(e)}")
                else:
                    print(f"文件 {file} 上传失败")
            except Exception as e:
                print(f"处理文件 '{file}' 时发生错误: {str(e)}，跳过继续处理下一个文件")



    


#########################################################################
def contains_japanese(text):
    # 日文独有字符范围
    japanese_only_ranges = [
        (0x3040, 0x309F),  # 平假名
        (0x30A0, 0x30FF),  # 片假名
        (0x31F0, 0x31FF),  # 片假名语音扩展
        (0xFF66, 0xFF9F),  # 半角片假名
    ]
    
    # 日文特有的标点符号
    japanese_punctuation = '「」『』・〜ー'
    
    # 检查是否包含日文独有字符
    has_japanese_char = any(
        any(start <= ord(char) <= end for start, end in japanese_only_ranges)
        for char in text
    )
    
    # 检查日文标点
    has_japanese_punct = any(char in japanese_punctuation for char in text)
    
    # 如果包含平假名或片假名，几乎可以确定是日文
    return has_japanese_char or has_japanese_punct

def contains_chinese_only(text):
    # 中文独有字符范围（排除日文假名）
    chinese_only_ranges = [
        (0x4E00, 0x9FFF),  # CJK统一汉字
        (0x3400, 0x4DBF),  # CJK扩展A
        (0x20000, 0x2A6DF),  # CJK扩展B
        (0x2A700, 0x2B73F),  # CJK扩展C
        (0x2B740, 0x2B81F),  # CJK扩展D
    ]
    
    # 中文特有的标点符号
    chinese_punctuation = '。，、：；！？（）【】《》""''…'
    
    # 检查是否只包含中文字符
    has_chinese = False
    has_japanese = contains_japanese(text)
    
    for char in text:
        code = ord(char)
        # 检查是否是中文字符
        is_chinese = any(start <= code <= end for start, end in chinese_only_ranges)
        if is_chinese:
            has_chinese = True
    
    # 如果包含日文特征，则不算纯中文
    return has_chinese and not has_japanese

def add_translation(text, username, context="", languages=None):
    if contains_chinese_only(text):
        # 如果是纯中文，不需要翻译
        return text
    elif contains_japanese(text):
        # 如果包含日文，进行翻译
        translation = translate_to_chinese(text, username, context, languages="Japanese")
        return f"{text}\n{translation}"
    else:
        # 其他语言的处理
        if languages:
            translation = translate_to_chinese(text, username, context, languages=languages)
        else:
            translation = translate_to_chinese(text, username, context)
        return f"{text}\n{translation}"

def translate_to_chinese(text, username, context="", languages=None):
    # 根据上下文创建更好的翻译提示
    if languages:
        translation_prompt = create_translation_prompt_cn(text, context, languages=languages)
    else:
        translation_prompt = create_translation_prompt_cn(text, context)
    
    user_config = load_user_config(username)
    api_key_type = user_config.get('api_key_type', '')
    if api_key_type == "deepseek":
        api_key = user_config.get('api_key_deepseek', '')
    elif api_key_type == "siliconflow":
        api_key = user_config.get('api_key_siliconflow', '')
    elif api_key_type == "qwen":
        api_key = user_config.get('api_key_qwen', '')
    translation, totaltokens = Web_Agent(api_key,api_key_type)._generate_summary_translation(translation_prompt)
    # print(f"翻译消耗的token: {totaltokens}")
    return translation




def create_translation_prompt_cn(text, context="", languages=None):
    source_lang = f"from {languages}" if languages else ""
    
    if context:
        return f"""
        Context: {context}
        
        Please translate the following text {source_lang} into Chinese, considering the context above:
        "{text}"
        
        Requirements:
        1. Maintain consistency with the context
        2. Ensure professional terminology is correctly translated
        3. Keep the translation natural and fluent
        4. Output only the translated text without any additional information
        """
    else:
        return f"""
        Translate the following text {source_lang} into Chinese:
        "{text}"   
        Ensure that the output contains only the translated result, without any additional text or commentary.
        """

####################################################
#字幕生成和翻译
###################################################


def generate_subtitles(video_path, username, language=None):
    try:
        print(f"Current working directory: {os.getcwd()}")
        print(f"Video path: {video_path}")
        
        if not os.path.exists(video_path):
            raise FileNotFoundError(f"Video file not found: {video_path}")

        video = VideoFileClip(video_path)
        audio_path = video_path.replace('.mp4', '.wav')       
        print(f"Audio path: {audio_path}")
        
        if not os.path.exists(audio_path):
            if video.audio is None:
                print("警告: 视频没有音轨或无法读取音轨")
                return None  # 或者在这里处理没有音轨的情况
            audio = video.audio
            audio.write_audiofile(audio_path)
            print(f"Audio file written: {os.path.exists(audio_path)}")
        else:
            print(f"Audio file already exists: {audio_path}")

        if not os.path.exists(audio_path):
            raise FileNotFoundError(f"Generated audio file not found: {audio_path}")

        device = "cuda" if torch.cuda.is_available() else "cpu"
        print(f"Using device: {device}")
        
        print("Loading Whisper model...")
        model = whisper.load_model("medium", device=device)
        print("Whisper model loaded successfully")

        print(f"Transcribing audio file: {audio_path}")
        # 创建语言代码到英语全称的映射
        language_map = {
            "en": "English",
            "zh": "Chinese",
            "ja": "Japanese",
            "ko": "Korean",
            "de": "German",
            "fr": "French"
        }

        # 准备 transcribe 参数
        transcribe_params = {
            "task": "transcribe",
            "beam_size": 3,
            "best_of": 3,
            "temperature": 0.2,
            "condition_on_previous_text": True
        }

        # 根据条件添加 language 和 initial_prompt 参数
        if username == "admin" and language:
            transcribe_params["language"] = language
            if language in language_map:
                language_name = language_map[language]
                transcribe_params["initial_prompt"] = f"This is a {language_name} lesson."

        # 执行转录，audio_path 需要直接作为第一个参数传递
        result = model.transcribe(audio_path, **transcribe_params)

        subtitles = []
        # 添加上下文处理
        context_window = []  # 存储上下文窗口
        window_size = 5  # 上下文窗口大小

        segments = result["segments"]
        for i, segment in enumerate(segments):
            start_time = segment["start"]
            end_time = segment["end"]
            text = segment["text"].strip()

            # 构建上下文窗口
            context_window.append(text)
            if len(context_window) > window_size:
                context_window.pop(0)

            # 如果当前文本过短或可能不完整，尝试与下一段合并
            if i < len(segments) - 1 and len(text) < 20:
                next_segment = segments[i + 1]
                if next_segment["start"] - end_time < 1.0:  # 如果时间间隔小于1秒
                    text += " " + next_segment["text"]
                    end_time = next_segment["end"]

            # 使用上下文进行翻译
            context_text = " ".join(context_window)
            translated_text = (
                add_translation(text, username, context=context_text, languages=language_name)
                if 'language_name' in locals()
                else add_translation(text, username, context=context_text)
            )
            subtitles.append((start_time, end_time, translated_text))
            print(f"翻译上下文: {context_text}")
            print(f"识别并翻译的文本: {start_time}-{end_time}: {translated_text}")

        os.remove(audio_path)
        print("Audio file removed")
        return subtitles
    except Exception as e:
        print(f"Error in generate_subtitles: {str(e)}")
        import traceback
        print(traceback.format_exc())
        return None  # 返回 None 而不是 raise



def get_username_by_video_name(video_name):
    with open('user_videos.json', 'r') as f:
        user_videos = json.load(f)
    
    for username, videos in user_videos.items():
        for video in videos:
            if video_name in video:  # 检查 video_name 是否是 video 的一部分
                print(f"获取到 {video_name} 视频所属用户名 {username}")
                return username
    
    print(f"未找到 {video_name} 对应的用户名")
    return None  # 如果没有找到对应的用户名

# 添加全局变量来跟踪函数是否运行过
_has_matched_files = False

# 添加退出标志
_exit_flag = threading.Event()

def check_and_process_videos():
    global _has_matched_files
    while not _exit_flag.is_set():
        try:
            print("开始检查视频文件...")
            
            # 如果还没运行过匹配函数，则运行一次
            if not _has_matched_files:
                print("首次运行文件匹配...")
                match_and_record_files()
                main_upload_video_documents()
                _has_matched_files = True
                print("文件匹配完成")
            print(f'获取了所有视频目录有:{VIDEO_DIRS}')
            
            # 处理所有视频目录
            for video_dir_config in VIDEO_DIRS:
                if _exit_flag.is_set():
                    break
                video_dir = video_dir_config["path"]
                dir_type = video_dir_config["type"]               
                # 确保视频目录存在
                if not os.path.exists(video_dir):
                    print(f"创建视频目录: {video_dir}")
                    os.makedirs(video_dir, exist_ok=True)
                
                print(f"检查目录: {video_dir}")
                video_files = [f for f in os.listdir(video_dir) if f.endswith('.mp4')]
                
                for video_file in video_files:
                    video_name = os.path.splitext(video_file)[0]
                    json_file = f"{video_name}.json"
                    video_path = os.path.join(video_dir, video_file)
                    json_path = os.path.join(video_dir, json_file)
                    srt_file_path = os.path.splitext(json_path)[0] + '.srt'
                    
                    # 根据目录类型决定用户名
                    if dir_type == "user":
                        username = get_username_by_video_name(video_name)
                        if username is None:
                            print(f"无法找到视频 {video_file} 对应的用户名，跳过")
                            continue
                        # print(f"使用用户 {username} 处理视频: {video_file}")
                        
                        if not os.path.exists(json_path):
                            print(f"开始处理视频字幕 {video_file}...")
                            doc_id = process_video(video_file, video_path, json_path, username)  # 不传递 language 参数
                            if doc_id is None:
                                print(f"无法处理视频 {video_file}，跳过")
                                continue
                        else:
                            doc_id = process_video(video_file, video_path, json_path, username)  # 不传递 language 参数
                            # print(f"{json_file} 已存在，跳过处理")
                    else:  # dir_type == "admin"
                        username = "admin"
                        language = video_dir_config["name"]
                        # print(f"使用用户 {username} 处理视频: {video_file}")
                        
                        if not os.path.exists(json_path):
                            print(f"开始处理视频字幕 {video_file}...")
                            subtitles = process_video(video_file, video_path, json_path, username, language)  # admin 用户传递 language 参数
                            if subtitles is None:
                                print(f"无法处理视频 {video_file}，跳过")
                                continue
                        else:
                            subtitles = process_video(video_file, video_path, json_path, username, language)  # admin 用户传递 language 参数
                            # logger.info(f"{json_file} 已存在，跳过处理")

                    if not os.path.exists(srt_file_path):
                        json_to_srt(json_path)
                    else:
                        # print(f"{srt_file_path} 已存在，跳过处理")    
                        pass

            print("检查完成，开始上传视频文档资料...")
            main_upload_video_documents()
            print("视频处理完成，等待下一次检查...")
            # 使用带超时的等待，这样可以及时响应退出信号
            _exit_flag.wait(60)
        except Exception as e:
            print(f"Error in check_and_process_videos: {e}")
            _exit_flag.wait(5)

def check_and_process_ragdocs_loop():
    while not _exit_flag.is_set():
        print("开始检查 ragdoc 文件...")
        # process_ragdoc_files()
        try:
            # 加载已存在的 doc_ids
            doc_ids = {}
            if os.path.exists(DOC_IDS_FILE):
                try:
                    with open(DOC_IDS_FILE, 'r', encoding='utf-8') as f:
                        content = f.read().strip()
                        if content:
                            doc_ids = json.loads(content)
                        else:
                            print(f"警告: {DOC_IDS_FILE} 是空文件")
                except json.JSONDecodeError as e:
                    print(f"错误: 无法解析 {DOC_IDS_FILE}. 错误信息: {str(e)}")
                    print("创建新的 doc_ids 字典")
                except Exception as e:
                    print(f"读取 {DOC_IDS_FILE} 时发生未知错误: {str(e)}")
                    print("创建新的 doc_ids 字典")
            else:
                print(f"{DOC_IDS_FILE} 不存在，将创建新文件")
            
            # 处理 ragdoc 和 blog 文件夹
            folders_to_process = [
                (RAGDOC_DIR, "test"),
                (blog_dir, "blog")
            ]
            
            for folder_path, kb_name in folders_to_process:
                if _exit_flag.is_set():
                    break
                    
                # 确保目录存在
                if not os.path.exists(folder_path):
                    os.makedirs(folder_path, exist_ok=True)
                    continue

                try:
                    files = [f for f in os.listdir(folder_path) if os.path.isfile(os.path.join(folder_path, f))]
                    # print(f"知识库{kb_name}中所有文件{files}")
                except Exception as e:
                    print(f"读取目录 {folder_path} 失败: {str(e)}，跳过此目录")
                    continue
                
                for file in files:
                    if _exit_flag.is_set():
                        break
                        
                    try:
                        # 跳过 doc_ids.json 文件
                        if file == os.path.basename(DOC_IDS_FILE):
                            continue

                        file_path = os.path.join(folder_path, file)
                        
                        # 检查文件是否已经在 doc_ids 中且上传成功
                        if file in doc_ids:
                            print(f"文件 {file} 已经上传过")
                            continue
                        else:
                            print(f"文件 {file} 未上传过，开始上传...")

                        # 获取该文件在user_files.json中的用户名
                        username = get_username_by_file(file)
                        if not username:
                            print(f"无法获取文件 {file} 的用户名，跳过处理")
                            continue

                        if kb_name=="test":
                            # 通过查询判断username是否为vip
                            if is_user_vip(username):
                                print(f"用户 {username} 是 VIP 用户，上传文件 {file} 到{kb_name}知识库...")
                                parser_id = "ragvip"
                                doc_id = upload_and_parse_documents(file_path, kb_name, parser_id)
                                time.sleep(200)
                            else:
                                print(f"用户 {username} 不是 VIP 用户，使用基础解析方式上传文件 {file} 到{kb_name}知识库...")
                                parser_id = "naive"
                                doc_id = upload_and_parse_documents(file_path, kb_name, parser_id)
                                time.sleep(200)
               
                        if kb_name=="blog":
                            parser_id = "naive"
                            doc_id = upload_and_parse_documents(file_path, kb_name, parser_id)
                            time.sleep(10)
               
                        if doc_id:
                            print(f"文件 {file} 上传成功，文档ID: {doc_id}")
                            # 更新 doc_ids
                            doc_ids[file] = doc_id
                            # 保存更新后的 doc_ids
                            try:
                                with open(DOC_IDS_FILE, 'w', encoding='utf-8') as f:
                                    json.dump(doc_ids, f, indent=2, ensure_ascii=False)
                                print(f"已更新 {DOC_IDS_FILE}")
                            except Exception as e:
                                print(f"保存 {DOC_IDS_FILE} 时发生错误: {str(e)}")
                        else:
                            print(f"文件 {file} 上传失败")
                    except Exception as e:
                        print(f"处理文件 '{file}' 时发生错误: {str(e)}，跳过继续处理下一个文件")
                        continue

            print("Ragdoc处理完成，等待下一次检查...")
            # 使用带超时的等待，这样可以及时响应退出信号
            _exit_flag.wait(60)
        except Exception as e:
            print(f"Ragdoc处理过程中发生错误: {str(e)}，等待下一次检查...")
            _exit_flag.wait(60)  # 出错后等待60秒再重试

def start_processing_threads():
    # 确保退出标志是清除状态
    _exit_flag.clear()
    
    # 创建视频处理线程
    video_thread = threading.Thread(target=check_and_process_videos, name="VideoProcessingThread")
    video_thread.daemon = True
    
    # 创建ragdoc处理线程
    ragdoc_thread = threading.Thread(target=check_and_process_ragdocs_loop, name="RagdocProcessingThread")
    ragdoc_thread.daemon = True
    
    # 启动线程
    video_thread.start()
    ragdoc_thread.start()
    
    print("所有处理线程已启动")
    
    # 保持主线程运行
    try:
        while not _exit_flag.is_set():
            time.sleep(1)
    except KeyboardInterrupt:
        print("正在退出程序...")
        _exit_flag.set()  # 设置退出标志
        # 等待线程完成当前工作
        video_thread.join(timeout=5)
        ragdoc_thread.join(timeout=5)
        print("程序已安全退出")

def process_video(video_file, video_path, json_path, username, language=None):
    # print(f"正在为 {video_file} 生成字幕...")
    video_name = os.path.splitext(video_file)[0]
    # print(f"视频匹配到用户名{username}")
    if is_user_vip(username):
        parser_id="ragvip"
    else:
        parser_id="naive"

    if video_path.endswith(".mp4"):
    # 获取视频文件的上级文件夹名称
      parent_dir_name = os.path.basename(os.path.dirname(video_path))
    #   print(f"视频所在上级文件夹: {parent_dir_name}")
    #   parent_dir_name为视频文件知识库
    # 检查 json_path 是否存在
    if os.path.exists(json_path):
        # print(f"{os.path.basename(json_path)} 已存在，跳过生成字幕文件")
        doc_id = uploadvideo_or_get_doc_id(json_path, parent_dir_name, api_key_model, "naive")
        if doc_id:
            # print(f"字幕文件已经上传成功，文档ID: {doc_id}")
            return doc_id
        else:                                                                                             
            print(f"字幕文件 {os.path.basename(json_path)} 上传失败")
            return None

    # 根据 language 参数决定如何调用 generate_subtitles
    if language:
        subtitles = generate_subtitles(video_path, username, language=language)
    else:
        subtitles = generate_subtitles(video_path, username)

    if subtitles is None:
        print(f"生成字幕失败，跳过")
        return None
        
    match_and_record_files()
    print(f"保存字幕文件 {os.path.basename(json_path)}...")
    with open(json_path, 'w', encoding='utf-8') as f:
        json.dump(subtitles, f, ensure_ascii=False, indent=2)
    print(f"上传字幕文件 {os.path.basename(json_path)} 到知识库...")
    doc_id = uploadvideo_or_get_doc_id(json_path, parent_dir_name, api_key_model, parser_id)
    if doc_id:
        print(f"字幕文件上传成功，文档ID: {doc_id}")

    else:
        print(f"字幕文件 {os.path.basename(json_path)} 上传失败")
    return doc_id

if __name__ == "__main__":
    # 确保视频目录存在
    os.makedirs(VIDEO_DIR, exist_ok=True)
    os.makedirs(RAGDOC_DIR, exist_ok=True)
    # 启动处理线程
    start_processing_threads()
