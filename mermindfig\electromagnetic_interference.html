<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <title>电磁波干涉与衍射演示</title>
  <style>
    body {
      margin: 0;
      background: #001133;
      overflow: hidden;
      user-select: none;
      font-family: 'Microsoft YaHei', <PERSON><PERSON>, sans-serif;
    }
    #controls {
      position: absolute;
      top: 15px;
      left: 15px;
      background: rgba(255,255,255,0.95);
      border-radius: 12px;
      padding: 15px 20px;
      z-index: 10;
      box-shadow: 0 4px 12px rgba(0,0,0,0.3);
      min-width: 320px;
    }
    #controls h3 {
      margin: 0 0 12px 0;
      color: #333;
      font-size: 16px;
    }
    #controls .control-group {
      margin-bottom: 12px;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
    #controls label {
      font-weight: 500;
      color: #555;
      min-width: 80px;
    }
    #controls input[type=range] {
      width: 120px;
      margin: 0 8px;
    }
    #controls .value-display {
      min-width: 60px;
      text-align: right;
      font-weight: bold;
      color: #2196F3;
    }
    #controls button {
      background: #4ECDC4;
      color: white;
      border: none;
      padding: 8px 16px;
      border-radius: 6px;
      cursor: pointer;
      margin: 4px;
      font-size: 14px;
    }
    #controls button:hover {
      background: #45B7B8;
    }
    #controls button:active {
      background: #3D9A9B;
    }
    #info {
      position: absolute;
      bottom: 15px;
      left: 15px;
      background: rgba(255,255,255,0.9);
      border-radius: 8px;
      padding: 10px 15px;
      z-index: 10;
      font-size: 14px;
      color: #666;
      max-width: 350px;
    }
    #wave-sources-info {
      position: absolute;
      top: 15px;
      right: 15px;
      background: rgba(255,255,255,0.95);
      border-radius: 8px;
      padding: 15px;
      z-index: 10;
      font-size: 13px;
      color: #666;
      min-width: 280px;
      max-width: 350px;
      box-shadow: 0 4px 15px rgba(0,0,0,0.2);
    }
  </style>
</head>
<body>
  <div id="controls">
    <h3>🔄 多波源干涉演示</h3>
    <div style="margin: 10px 0; padding: 8px; background: #f0f8ff; border-radius: 5px; border: 1px solid #ddd;">
      <div style="font-weight: bold; margin-bottom: 5px; color: #333;">新波源默认参数:</div>
      <div class="control-group">
        <label>振幅:</label>
        <input id="amplitude" type="range" min="30" max="120" value="70">
        <span id="ampval" class="value-display">70</span>
      </div>
      <div class="control-group">
        <label>频率:</label>
        <input id="frequency" type="range" min="0.5" max="4" step="0.1" value="1.5">
        <span id="freqval" class="value-display">1.5 Hz</span>
      </div>
      <div class="control-group">
        <label>波速:</label>
        <input id="wavespeed" type="range" min="150" max="400" step="10" value="280">
        <span id="speedval" class="value-display">280 px/s</span>
      </div>
    </div>
    <div style="margin-top: 15px; border-top: 1px solid #ddd; padding-top: 10px;">
      <button onclick="addWaveSource()">➕ 添加波源</button>
      <button onclick="removeWaveSource()">➖ 删除波源</button>
      <button onclick="resetDemo()">🔄 重置</button>
    </div>
  </div>
  
  <div id="wave-sources-info">
    <strong>波源列表:</strong>
    <div id="sources-list"></div>
  </div>
  
  <div id="info">
    💡 <strong>操作提示:</strong><br>
    • 拖动彩色圆点移动波源<br>
    • 添加多个波源观察干涉现象<br>
    • 每个波源可独立调节振幅、频率、波速<br>
    • 顶部控件设置新波源的默认参数<br>
    • 移动波源后原波继续传播<br>
    • 蓝色=无波动，红色=最大强度
  </div>
  
  <canvas id="wavecanvas"></canvas>

  <script>
    const canvas = document.getElementById('wavecanvas');
    const ctx = canvas.getContext('2d');
    let width = window.innerWidth;
    let height = window.innerHeight;
    canvas.width = width;
    canvas.height = height;

    // 控件
    const ampSlider = document.getElementById('amplitude');
    const freqSlider = document.getElementById('frequency');
    const speedSlider = document.getElementById('wavespeed');
    const ampVal = document.getElementById('ampval');
    const freqVal = document.getElementById('freqval');
    const speedVal = document.getElementById('speedval');

    // 波参数
    let amplitude = parseFloat(ampSlider.value);
    let frequency = parseFloat(freqSlider.value);
    let waveSpeed = parseFloat(speedSlider.value);

    // 波源数组
    let waveSources = [
      {
        id: 1,
        x: width * 0.3,
        y: height * 0.5,
        color: '#FF6B6B',
        startTime: performance.now() / 1000,
        dragging: false,
        dragOffset: {x: 0, y: 0},
        amplitude: amplitude,
        frequency: frequency,
        waveSpeed: waveSpeed
      }
    ];
    
    // 历史波源（已移动或删除的波源）
    let oldWaveSources = [];
    let nextSourceId = 2;

    // 波源颜色列表
    const sourceColors = ['#FF6B6B', '#4ECDC4', '#45B7B8', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8', '#F7DC6F'];

    // 改进的颜色映射函数
    function amplitudeToColor(amplitude, maxAmplitude) {
      let intensity = Math.abs(amplitude) / maxAmplitude;
      intensity = Math.max(0, Math.min(1, intensity));
      
      const bgR = 0, bgG = 17, bgB = 51; // #001133
      
      let r, g, b;
      
      if (intensity < 0.15) {
        let t = intensity / 0.15;
        r = bgR + t * (0 - bgR);
        g = bgG + t * (80 - bgG);
        b = bgB + t * (200 - bgB);
      } else if (intensity < 0.35) {
        let t = (intensity - 0.15) / 0.2;
        r = 0 + t * (0 - 0);
        g = 80 + t * (180 - 80);
        b = 200 + t * (255 - 200);
      } else if (intensity < 0.55) {
        let t = (intensity - 0.35) / 0.2;
        r = 0 + t * (100 - 0);
        g = 180 + t * (255 - 180);
        b = 255 + t * (100 - 255);
      } else if (intensity < 0.75) {
        let t = (intensity - 0.55) / 0.2;
        r = 100 + t * (255 - 100);
        g = 255 + t * (200 - 255);
        b = 100 + t * (0 - 100);
      } else {
        let t = (intensity - 0.75) / 0.25;
        r = 255 + t * (255 - 255);
        g = 200 + t * (50 - 200);
        b = 0 + t * (0 - 0);
      }
      
      return [Math.round(r), Math.round(g), Math.round(b)];
    }

    // 控件监听器（用于新波源的默认参数）
    ampSlider.oninput = () => {
      amplitude = parseFloat(ampSlider.value);
      ampVal.textContent = amplitude;
    };
    freqSlider.oninput = () => {
      frequency = parseFloat(freqSlider.value);
      freqVal.textContent = frequency.toFixed(1) + ' Hz';
    };
    speedSlider.oninput = () => {
      waveSpeed = parseFloat(speedSlider.value);
      speedVal.textContent = waveSpeed + ' px/s';
    };

    // 添加波源
    function addWaveSource() {
      if (waveSources.length < 8) {
        let newSource = {
          id: nextSourceId++,
          x: Math.random() * (width - 100) + 50,
          y: Math.random() * (height - 100) + 50,
          color: sourceColors[waveSources.length % sourceColors.length],
          startTime: performance.now() / 1000,
          dragging: false,
          dragOffset: {x: 0, y: 0},
          amplitude: amplitude,
          frequency: frequency,
          waveSpeed: waveSpeed
        };
        waveSources.push(newSource);
        updateSourcesList();
      }
    }

    // 删除波源
    function removeWaveSource() {
      if (waveSources.length > 1) {
        let removedSource = waveSources.pop();
        // 将删除的波源添加到历史中
        oldWaveSources.push({
          ...removedSource,
          amplitude: removedSource.amplitude,
          frequency: removedSource.frequency,
          waveSpeed: removedSource.waveSpeed,
          endTime: performance.now() / 1000
        });
        updateSourcesList();
      }
    }

    // 重置演示
    function resetDemo() {
      waveSources = [{
        id: 1,
        x: width * 0.3,
        y: height * 0.5,
        color: '#FF6B6B',
        startTime: performance.now() / 1000,
        dragging: false,
        dragOffset: {x: 0, y: 0},
        amplitude: amplitude,
        frequency: frequency,
        waveSpeed: waveSpeed
      }];
      oldWaveSources = [];
      nextSourceId = 2;
      updateSourcesList();
    }

    // 更新波源列表显示
    function updateSourcesList() {
      const list = document.getElementById('sources-list');
      list.innerHTML = '';
      waveSources.forEach((source, index) => {
        const div = document.createElement('div');
        div.style.marginBottom = '10px';
        div.style.padding = '8px';
        div.style.border = '1px solid #ddd';
        div.style.borderRadius = '5px';
        div.style.backgroundColor = '#f9f9f9';

        div.innerHTML = `
          <div style="font-weight: bold; color: ${source.color}; margin-bottom: 5px;">● 波源 ${source.id}</div>
          <div style="display: flex; align-items: center; margin: 3px 0;">
            <label style="min-width: 50px; font-size: 12px;">振幅:</label>
            <input type="range" min="30" max="120" value="${source.amplitude}"
                   style="flex: 1; margin: 0 5px;"
                   onchange="updateSourceParameter(${index}, 'amplitude', this.value)">
            <span style="min-width: 30px; font-size: 12px;">${source.amplitude}</span>
          </div>
          <div style="display: flex; align-items: center; margin: 3px 0;">
            <label style="min-width: 50px; font-size: 12px;">频率:</label>
            <input type="range" min="0.5" max="4" step="0.1" value="${source.frequency}"
                   style="flex: 1; margin: 0 5px;"
                   onchange="updateSourceParameter(${index}, 'frequency', this.value)">
            <span style="min-width: 30px; font-size: 12px;">${source.frequency.toFixed(1)}</span>
          </div>
          <div style="display: flex; align-items: center; margin: 3px 0;">
            <label style="min-width: 50px; font-size: 12px;">波速:</label>
            <input type="range" min="150" max="400" step="10" value="${source.waveSpeed}"
                   style="flex: 1; margin: 0 5px;"
                   onchange="updateSourceParameter(${index}, 'waveSpeed', this.value)">
            <span style="min-width: 30px; font-size: 12px;">${source.waveSpeed}</span>
          </div>
        `;
        list.appendChild(div);
      });
    }

    // 更新单个波源参数
    function updateSourceParameter(sourceIndex, parameter, value) {
      if (sourceIndex >= 0 && sourceIndex < waveSources.length) {
        waveSources[sourceIndex][parameter] = parseFloat(value);
        // 更新显示值
        const spans = document.querySelectorAll('#sources-list > div');
        if (spans[sourceIndex]) {
          const span = spans[sourceIndex].querySelector(`input[onchange*="${parameter}"] + span`);
          if (span) {
            if (parameter === 'frequency') {
              span.textContent = parseFloat(value).toFixed(1);
            } else {
              span.textContent = value;
            }
          }
        }
      }
    }

    // 初始化波源列表
    updateSourcesList();

    // 鼠标事件处理
    canvas.addEventListener('mousedown', e => {
      const rect = canvas.getBoundingClientRect();
      const mx = e.clientX - rect.left;
      const my = e.clientY - rect.top;

      // 检查是否点击在某个波源上
      for (let source of waveSources) {
        if (Math.hypot(mx - source.x, my - source.y) < 20) {
          source.dragging = true;
          source.dragOffset.x = mx - source.x;
          source.dragOffset.y = my - source.y;
          canvas.style.cursor = 'grabbing';
          break;
        }
      }
    });

    window.addEventListener('mousemove', e => {
      const rect = canvas.getBoundingClientRect();
      const mx = e.clientX - rect.left;
      const my = e.clientY - rect.top;

      let isDragging = false;
      let isHovering = false;

      for (let source of waveSources) {
        if (source.dragging) {
          source.x = Math.max(20, Math.min(width-20, mx - source.dragOffset.x));
          source.y = Math.max(20, Math.min(height-20, my - source.dragOffset.y));
          isDragging = true;
        } else if (Math.hypot(mx - source.x, my - source.y) < 20) {
          isHovering = true;
        }
      }

      if (isDragging) {
        canvas.style.cursor = 'grabbing';
      } else if (isHovering) {
        canvas.style.cursor = 'grab';
      } else {
        canvas.style.cursor = 'default';
      }
    });

    window.addEventListener('mouseup', e => {
      let wasDragging = false;

      for (let source of waveSources) {
        if (source.dragging) {
          source.dragging = false;
          wasDragging = true;

          // 保存移动前的波源状态到历史中
          oldWaveSources.push({
            id: source.id,
            x: source.x,
            y: source.y,
            color: source.color,
            amplitude: source.amplitude,
            frequency: source.frequency,
            waveSpeed: source.waveSpeed,
            startTime: source.startTime,
            endTime: performance.now() / 1000
          });

          // 重置当前波源的开始时间
          source.startTime = performance.now() / 1000;
        }
      }

      if (wasDragging) {
        canvas.style.cursor = 'default';
      }
    });

    // 计算单个波源的波动贡献
    function calculateWaveAmplitude(x, y, waveSource, currentTime) {
      let dx = x - waveSource.x;
      let dy = y - waveSource.y;
      let distance = Math.hypot(dx, dy);

      let waveTime = currentTime - waveSource.startTime;

      // 如果是历史波源，检查波的传播范围
      if (waveSource.endTime && currentTime > waveSource.endTime) {
        let maxWaveDistance = waveSource.waveSpeed * (waveSource.endTime - waveSource.startTime);
        let currentWaveDistance = waveSource.waveSpeed * waveTime;

        if (distance > currentWaveDistance || distance < currentWaveDistance - maxWaveDistance) {
          return 0;
        }
      }

      // 波还没到达
      if (distance > waveSource.waveSpeed * waveTime || waveTime <= 0) {
        return 0;
      }

      // 计算相位
      let phase = 2 * Math.PI * waveSource.frequency * (waveTime - distance / waveSource.waveSpeed);

      // 距离衰减
      let attenuation = 1 / (1 + distance / 60);

      return waveSource.amplitude * attenuation * Math.sin(phase);
    }

    // 窗口自适应
    window.addEventListener('resize', () => {
      width = window.innerWidth;
      height = window.innerHeight;
      canvas.width = width;
      canvas.height = height;
    });

    // 主绘制函数
    function draw() {
      let now = performance.now() / 1000;

      // 清空画布
      ctx.fillStyle = '#001133';
      ctx.fillRect(0, 0, width, height);

      // 创建图像数据
      let imageData = ctx.createImageData(width, height);
      let data = imageData.data;

      let step = 2; // 采样步长

      for (let y = 0; y < height; y += step) {
        for (let x = 0; x < width; x += step) {
          let totalAmplitude = 0;

          // 计算所有当前波源的贡献
          for (let source of waveSources) {
            let currentWaveSource = {
              x: source.x,
              y: source.y,
              amplitude: source.amplitude,
              frequency: source.frequency,
              waveSpeed: source.waveSpeed,
              startTime: source.startTime
            };
            totalAmplitude += calculateWaveAmplitude(x, y, currentWaveSource, now);
          }

          // 计算所有历史波源的贡献
          for (let oldSource of oldWaveSources) {
            totalAmplitude += calculateWaveAmplitude(x, y, oldSource, now);
          }

          // 转换为颜色
          let color = amplitudeToColor(totalAmplitude, amplitude);

          // 填充像素
          for (let dy = 0; dy < step && y + dy < height; dy++) {
            for (let dx = 0; dx < step && x + dx < width; dx++) {
              let idx = ((y + dy) * width + (x + dx)) * 4;
              data[idx] = color[0];
              data[idx + 1] = color[1];
              data[idx + 2] = color[2];
              data[idx + 3] = 255;
            }
          }
        }
      }

      ctx.putImageData(imageData, 0, 0);

      // 绘制所有波源
      for (let source of waveSources) {
        ctx.beginPath();
        ctx.arc(source.x, source.y, 18, 0, 2 * Math.PI);
        ctx.fillStyle = 'white';
        ctx.fill();
        ctx.strokeStyle = source.color;
        ctx.lineWidth = 4;
        ctx.stroke();

        // 波源中心点
        ctx.beginPath();
        ctx.arc(source.x, source.y, 6, 0, 2 * Math.PI);
        ctx.fillStyle = source.color;
        ctx.fill();
      }

      // 清理过期的历史波源
      let maxDistance = Math.max(width, height) * 1.5;
      oldWaveSources = oldWaveSources.filter(oldSource => {
        if (!oldSource.endTime) return true;
        let timeSinceEnd = now - oldSource.endTime;
        let maxWaveDistance = oldSource.waveSpeed * timeSinceEnd;
        return maxWaveDistance < maxDistance;
      });

      requestAnimationFrame(draw);
    }

    // 启动动画
    draw();
  </script>
</body>
</html>
