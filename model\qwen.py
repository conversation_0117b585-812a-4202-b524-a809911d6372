import json
import uuid
import os
import sys
import dashscope
from typing import List, Tuple, Generator, Optional, Dict, Any
import re
import time
import random
# 获取当前文件的目录
current_dir = os.path.dirname(os.path.abspath(__file__))
# 获取项目根目录
root_dir = os.path.dirname(current_dir)
# 将项目根目录添加到Python路径
if root_dir not in sys.path:
    sys.path.insert(0, root_dir)

from logger_config import log_with_request_id
from openai import OpenAI

request_id = str(uuid.uuid4())[:8]
qwen_model="qwen-turbo-latest"
qwen_model_1="qwq-plus"
def load_sensitive_words() -> List[str]:
    """
    加载敏感词列表
    
    Returns:
        List[str]: 敏感词列表
    """
    sensitive_words = []
    file_path = os.path.join(root_dir, 'General_SpamWords.txt')        
    with open(file_path, 'r', encoding='utf-8') as file:
        sensitive_words = [line.strip() for line in file if line.strip()]
    return sensitive_words

def filter_sensitive_content(text: str) -> str:
    """
    过滤文本中的敏感内容
    
    Args:
        text (str): 需要过滤的文本
        
    Returns:
        str: 过滤后的文本
    """
    original_text = text
    sensitive_words = load_sensitive_words()
    for word in sensitive_words:
        pattern = re.escape(word)
        if re.search(pattern, text, flags=re.UNICODE):
            print(f"Filtering sensitive word: '{word}'")
        text = re.sub(pattern, '[FILTERED]', text, flags=re.UNICODE)
    
    if text != original_text:
        print(f"Sensitive content filtered. Original length: {len(original_text)}, New length: {len(text)}")
    
    return text


def estimate_token_count(text: str) -> int:
    """
    估算文本的token数量
    
    英文：每个字符约 0.3 个token
    中文：每个字符约 0.6 个token
    
    Args:
        text (str): 要估算的文本
        
    Returns:
        int: 估算的token数量
    """
    # 分离英文和中文字符
    english_chars = len(re.findall(r'[a-zA-Z0-9]', text))
    # 计算中文字符（假设非英文数字的都是中文或其他Unicode字符）
    chinese_chars = len(text) - english_chars
    
    # 根据给定的比例估算token数量
    estimated_tokens = english_chars * 0.3 + chinese_chars * 0.6
    
    return int(estimated_tokens)


def truncate_text(text: str, max_tokens: int = 120000) -> str:
    """
    截断文本，使其token数量不超过指定限制
    
    Args:
        text (str): 要截断的文本
        max_tokens (int): 最大token数量，默认为120000
        
    Returns:
        str: 截断后的文本
    """
    if not text:
        return text
        
    estimated_tokens = estimate_token_count(text)
    
    # 如果估算的token数量小于最大限制，直接返回原文本
    if estimated_tokens <= max_tokens:
        return text
    
    # 计算需要保留的字符比例
    keep_ratio = max_tokens / estimated_tokens
    # 计算需要保留的字符数
    keep_chars = int(len(text) * keep_ratio)
    
    # 截断文本，保留前面部分
    truncated_text = text[:keep_chars]
       
    print(f"文本已被截断: 原有约{estimated_tokens}个tokens，截断为约{max_tokens}个tokens")
    log_with_request_id(f"文本已被截断: 原有约{estimated_tokens}个tokens，截断为约{max_tokens}个tokens", request_id=request_id)
    
    return truncated_text




def qwen_generate_summary(api_key: str, prompt: str, enable_thinking: bool = False, temperature: float = 0.5) -> Tuple[str, int]:
    """
    使用Qwen模型（OpenAI兼容模式）生成文本摘要（流式输出）
    Args:
        api_key (str): API密钥
        prompt (str): 提示词
        model (str): 模型名称，默认为qwen-plus
        enable_thinking (bool): 是否启用思考过程，默认为False
    Returns:
        Tuple[str, int]: 生成的摘要文本和使用的token数
    """
    filtered_prompt = filter_sensitive_content(prompt)
    filtered_prompt = truncate_text(filtered_prompt)
    log_with_request_id(f"是否使用enable_thinking: {enable_thinking}", request_id=request_id)
    # 兼容阿里云OpenAI接口
    client = OpenAI(
        api_key=api_key,
        base_url="https://dashscope.aliyuncs.com/compatible-mode/v1"
    )

    messages = [
        {"role": "system", "content": "You are a helpful assistant."},
        {"role": "user", "content": filtered_prompt}
    ]

    full_content = ""
    total_tokens = 0
    # 流式输出
    completion = client.chat.completions.create(
        model=qwen_model,
        messages=messages,
        stream=True,
        stream_options={"include_usage": True},
        extra_body={"enable_thinking": enable_thinking, "temperature": temperature},
    )
    try:
        for chunk in completion:
            # 最后一个chunk的choices为空，需要跳过
            if hasattr(chunk, 'choices') and chunk.choices:
                delta = getattr(chunk.choices[0], 'delta', None)
                if delta and hasattr(delta, 'content') and delta.content:
                    full_content += delta.content
            # 统计token
            if hasattr(chunk, 'usage') and chunk.usage and hasattr(chunk.usage, 'total_tokens'):
                total_tokens = chunk.usage.total_tokens
    except Exception as e:
        print(f"OpenAI compatible streaming error: {e}")
        total_tokens = estimate_token_count(full_content)

    if not total_tokens:
        total_tokens = estimate_token_count(full_content)

    log_with_request_id(f"大模型的输出: {full_content[:100]},消耗的token为:{total_tokens}", request_id=request_id)
    return full_content, total_tokens

def qwen_generate_summary_with_system_prompt(api_key: str, sys_prompt: str, prompt: str, enable_thinking: bool = False, temperature: float = 0.5) -> Tuple[str, int]:
    """
    使用Qwen模型（OpenAI兼容模式）生成文本摘要，支持自定义系统提示词（流式输出）
    """
    filtered_prompt = filter_sensitive_content(prompt)
    filtered_prompt = truncate_text(filtered_prompt)
    log_with_request_id(f"是否使用enable_thinking: {enable_thinking}", request_id=request_id)
    client = OpenAI(
        api_key=api_key,
        base_url="https://dashscope.aliyuncs.com/compatible-mode/v1"
    )
    messages = [
        {"role": "system", "content": sys_prompt},
        {"role": "user", "content": filtered_prompt}
    ]
    full_content = ""
    total_tokens = 0
    completion = client.chat.completions.create(
        model=qwen_model,
        messages=messages,
        stream=True,
        stream_options={"include_usage": True},
        extra_body={"enable_thinking": enable_thinking, "temperature": temperature},
    )
    try:
        for chunk in completion:
            if hasattr(chunk, 'choices') and chunk.choices:
                delta = getattr(chunk.choices[0], 'delta', None)
                if delta and hasattr(delta, 'content') and delta.content:
                    full_content += delta.content
            if hasattr(chunk, 'usage') and chunk.usage and hasattr(chunk.usage, 'total_tokens'):
                total_tokens = chunk.usage.total_tokens
    except Exception as e:
        print(f"OpenAI compatible streaming error: {e}")
        total_tokens = estimate_token_count(full_content)
    if not total_tokens:
        total_tokens = estimate_token_count(full_content)
    log_with_request_id(f"大模型的输出: {full_content[:100]},消耗的token为:{total_tokens}", request_id=request_id)
    return full_content, total_tokens

def qwen_generate_summary_r1(api_key: str, prompt: str, enable_thinking: bool = False, temperature: float = 0.5) -> Tuple[str, int]:
    """
    使用Qwen-Max模型生成文本摘要（OpenAI兼容流式输出）
    """
    log_with_request_id(f"是否使用enable_thinking: {enable_thinking}", request_id=request_id)
    return qwen_generate_summary(api_key, prompt, enable_thinking=enable_thinking, temperature=temperature)

def qwen_generate_summary_r11(api_key: str, prompt: str, enable_thinking: bool = False, temperature: float = 0.5) -> Tuple[str, int]:
    """
    使用Qwen模型生成知识图谱（OpenAI兼容流式输出，文本三元组格式）
    """
    log_with_request_id(f"是否使用enable_thinking: {enable_thinking}", request_id=request_id)
    filtered_prompt = filter_sensitive_content(prompt)
    client = OpenAI(
        api_key=api_key,
        base_url="https://dashscope.aliyuncs.com/compatible-mode/v1"
    )
    sys_prompt = (
        "你是一个专业的知识图谱生成助手。你的任务是从用户提供的内容中提取实体和关系，并生成标准格式的知识图谱。\n"
        "请严格遵循以下输出格式规范：\n"
        "1. 每行表示一个关系三元组\n"
        "2. 每个三元组的格式必须是：实体1,实体2,{{关系}}\n"
        "3. 实体和关系之间用英文逗号分隔\n"
        "4. 关系必须用双大括号{{}}包围\n"
        "5. 不要输出任何解释、标题或额外文本\n"
        "6. 每行只包含一个三元组\n"
        "7. 实体应该是名词或名词短语\n"
        "8. 关系应该是动词或动词短语\n"
        "示例输出：\n"
        "量子力学,波函数,{{使用波函数描述粒子状态}}\n"
        "光子,光,{{是基本组成单位}}\n"
        "欧姆定律,电流,{{规定电流与电压成正比}}\n"
        "电磁感应,法拉第定律,{{由法拉第发现并描述}}\n"
        "无论用户提供什么内容，你都必须严格按照上述格式输出知识图谱，不添加任何额外说明。"
    )
    messages = [
        {"role": "system", "content": sys_prompt},
        {"role": "user", "content": filtered_prompt}
    ]
    full_content = ""
    total_tokens = 0
    completion = client.chat.completions.create(
        model=qwen_model,
        messages=messages,
        stream=True,
        stream_options={"include_usage": True},
        extra_body={"enable_thinking": enable_thinking, "temperature": temperature},
    )
    try:
        for chunk in completion:
            if hasattr(chunk, 'choices') and chunk.choices:
                delta = getattr(chunk.choices[0], 'delta', None)
                if delta and hasattr(delta, 'content') and delta.content:
                    full_content += delta.content
            if hasattr(chunk, 'usage') and chunk.usage and hasattr(chunk.usage, 'total_tokens'):
                total_tokens = chunk.usage.total_tokens
    except Exception as e:
        print(f"OpenAI compatible streaming error: {e}")
        total_tokens = estimate_token_count(full_content)
    if not total_tokens:
        total_tokens = estimate_token_count(full_content)
    log_with_request_id(f"大模型的输出: {full_content[:100]},消耗的token为:{total_tokens}", request_id=request_id)
    return full_content, total_tokens

def qwen_generate_summary_graph(api_key: str, prompt: str, enable_thinking: bool = False, temperature: float = 0.5) -> Tuple[str, int]:
    """
    使用Qwen模型生成知识图谱，以JSON格式输出（OpenAI兼容流式输出）
    """
    log_with_request_id(f"是否使用enable_thinking: {enable_thinking}", request_id=request_id)
    filtered_prompt = filter_sensitive_content(prompt)
    client = OpenAI(
        api_key=api_key,
        base_url="https://dashscope.aliyuncs.com/compatible-mode/v1"
    )
    sys_prompt = (
        "你是一个专业的知识图谱生成助手。你的任务是从用户提供的内容中提取实体和关系，并生成JSON格式的知识图谱。\n"
        "请严格遵循以下输出格式规范：\n"
        "1. 输出必须是有效的JSON格式\n"
        "2. JSON结构应该包含一个数组，每个元素代表一个关系三元组\n"
        "3. 每个三元组包含三个字段：entity1, entity2, relation\n"
        "4. 所有字段都必须是字符串类型\n"
        "5. 不要输出任何解释或额外文本\n"
        "6. 实体应该是名词或名词短语\n"
        "7. 关系应该是描述实体之间的物理联系的语句\n"
        "示例输出：\n"
        "{\n    \"knowledge_graph\": [\n        {\n            \"entity1\": \"量子力学\",\n            \"entity2\": \"波函数\",\n            \"relation\": \"使用波函数描述粒子状态\"\n        },\n        {\n            \"entity1\": \"光子\",\n            \"entity2\": \"光\",\n            \"relation\": \"是基本组成单位\"\n        }\n    ]\n}\n"
        "请确保输出是严格的JSON格式，可以被JSON解析器直接解析。不要输出任何其他内容。"
    )
    messages = [
        {"role": "system", "content": sys_prompt},
        {"role": "user", "content": filtered_prompt}
    ]
    full_content = ""
    total_tokens = 0
    completion = client.chat.completions.create(
        model=qwen_model,
        messages=messages,
        stream=True,
        stream_options={"include_usage": True},
        extra_body={"enable_thinking": enable_thinking, "temperature": temperature},
    )
    try:
        for chunk in completion:
            if hasattr(chunk, 'choices') and chunk.choices:
                delta = getattr(chunk.choices[0], 'delta', None)
                if delta and hasattr(delta, 'content') and delta.content:
                    full_content += delta.content
            if hasattr(chunk, 'usage') and chunk.usage and hasattr(chunk.usage, 'total_tokens'):
                total_tokens = chunk.usage.total_tokens
    except Exception as e:
        print(f"OpenAI compatible streaming error: {e}")
        total_tokens = estimate_token_count(full_content)
    if not total_tokens:
        total_tokens = estimate_token_count(full_content)
    log_with_request_id(f"大模型的输出: {full_content[:100]},消耗的token为:{total_tokens}", request_id=request_id)
    return full_content, total_tokens

def generate_summary_translation(api_key: str, prompt: str, temperature: float = 0.5) -> Tuple[str, int]:
    """
    使用Qwen-Max模型生成翻译
    
    Args:
        api_key (str): API密钥
        prompt (str): 提示词
        
    Returns:
        Tuple[str, int]: 生成的翻译文本和使用的token数
    """
    return qwen_generate_summary(api_key, prompt, enable_thinking=False, temperature=temperature)

if __name__ == "__main__":
    api_key_qwen = "sk-61010d52e30c4fb096d240ad7fae39df"
    enable_thinking = True
    # 测试 qwen_generate_summary_with_system_prompt
    sys_prompt = "你是一个专业的科技论文写作专家，请根据提问问题给出专业的回答"
    query = "请简要介绍量子力学的基本原理"
    content, tokens = qwen_generate_summary_with_system_prompt(api_key_qwen, sys_prompt, query, enable_thinking=enable_thinking)
    print(f"[with_system_prompt] content: {content}\ntokens: {tokens}\n")

    # # 测试 qwen_generate_summary_r1
    # query = "请简要介绍量子力学的基本原理。"
    # content, tokens = qwen_generate_summary_r1(api_key_qwen, query)
    # print(f"[r1] content: {content}\ntokens: {tokens}\n")

    # # 测试 qwen_generate_summary_r11
    # query = "请根据以下内容生成物理知识图谱：光子是光的基本组成单位，欧姆定律规定电流与电压成正比。"
    # content, tokens = qwen_generate_summary_r11(api_key_qwen, query)
    # print(f"[r11] content: {content}\ntokens: {tokens}\n")

    # # 测试 qwen_generate_summary_graph
    # query = "请根据以下内容生成物理知识图谱（JSON格式）：光子是光的基本组成单位，欧姆定律规定电流与电压成正比。"
    # content, tokens = qwen_generate_summary_graph(api_key_qwen, query)
    # print(f"[graph] content: {content}\ntokens: {tokens}\n")
