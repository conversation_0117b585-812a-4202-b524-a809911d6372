<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>博客列表</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f7fa;
            padding-top: 60px; /* 为固定定位的header-container留出空间 */
        }
        #blogList {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 25px;
        }
        .blog-item {
            border: none;
            border-radius: 12px;
            padding: 20px;
            display: flex;
            flex-direction: column;
            cursor: pointer;
            transition: all 0.3s ease;
            height: 350px;
            overflow: hidden;
            background-color: #ffffff;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .blog-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
        }
        .blog-info {
            flex: 1;
            overflow: hidden;
        }
        .blog-info h2 {
            margin-top: 0;
            font-size: 1.4em;
            color: #2c3e50;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .blog-info p {
            margin: 8px 0;
            font-size: 0.95em;
            color: #34495e;
        }
        .blog-thumbnail {
            width: 100%;
            height: 160px;
            object-fit: cover;
            border-radius: 8px;
            margin-top: 15px;
        }
        .blog-stats {
            font-size: 0.85em;
            color: #7f8c8d;
            margin-top: 10px;
            display: flex;
            justify-content: space-between;
        }
        .blog-author {
            font-weight: bold;
            color: #3498db;
        }
        .blog-date {
            font-style: italic;
            color: #95a5a6;
        }
        .blog-list-header {
            text-align: center;
            margin: 40px 0 20px;
            position: relative;
            padding-bottom: 20px;
        }
        .blog-list-title {
            font-family: 'Arial', sans-serif;
            font-size: 42px;
            color: #2c3e50;
            margin: 0;
            padding: 0;
            text-transform: uppercase;
            letter-spacing: 3px;
            font-weight: bold;
        }
        .blog-list-subtitle {
            font-family: 'Georgia', serif;
            font-size: 18px;
            color: #7f8c8d;
            margin-top: 10px;
            font-style: italic;
        }
        .blog-list-header::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 100px;
            height: 4px;
            background-color: #3498db;
            border-radius: 2px;
        }
        @media (max-width: 768px) {
            .blog-list-title {
                font-size: 32px;
            }
            .blog-list-subtitle {
                font-size: 16px;
            }
        }
        .header-container {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background-color: #f5f7fa;
            padding: 10px 20px;
            z-index: 1000;
            display: flex;
            justify-content: flex-end;
            align-items: center;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .search-container {
            display: flex;
            justify-content: center;
            margin-bottom: 20px;
        }
        #searchInput {
            padding: 10px;
            font-size: 16px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-right: 10px;
        }
        #searchButton {
            padding: 10px 20px;
            font-size: 16px;
            background-color: #3498db;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        #searchButton:hover {
            background-color: #2980b9;
        }
        /* 添加一个用于显示错误信息的样式 */
        #errorMessage {
            color: red;
            text-align: center;
            margin-top: 20px;
        }
        .user-info {
            font-size: 16px;
            color: #2c3e50;
        }
        #userDisplay {
            background-color: #3498db;
            color: white;
            padding: 8px 15px;
            border-radius: 20px;
            font-weight: bold;
        }
        .login-button {
            background-color: #2ecc71;
            color: white;
            padding: 8px 15px;
            border-radius: 20px;
            text-decoration: none;
            font-weight: bold;
            transition: background-color 0.3s;
        }
        .login-button:hover {
            background-color: #27ae60;
        }
        @media (max-width: 768px) {
            .header-container {
                padding: 10px;
            }
            
            .user-info {
                font-size: 14px;
            }
            
            #userDisplay, .login-button {
                padding: 6px 12px;
            }
        }
        .delete-button {
            background-color: #e74c3c;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        .delete-button:hover {
            background-color: #c0392b;
        }
        /* 添加分类过滤按钮的样式 */
        .category-filter-container {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin: 20px 0;
            justify-content: center;
        }

        .category-filter-btn {
            padding: 8px 16px;
            border: none;
            border-radius: 20px;
            background-color: #f0f0f0;
            color: #333;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .category-filter-btn:hover {
            background-color: #e0e0e0;
        }

        .category-filter-btn.active {
            background-color: #3498db;
            color: white;
        }
    </style>
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/katex@0.13.11/dist/katex.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/katex@0.13.11/dist/contrib/auto-render.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@11.3.0/dist/mermaid.min.js"></script>
</head>
<body>
    <div class="header-container">
        <div class="user-info">
            <span id="userDisplay"></span>
            <a id="loginButton" href="https://xpuai.20140806.xyz" class="login-button" style="display: none;">登录</a>
        </div>
    </div>
    <header class="blog-list-header">
        <h1 class="blog-list-title">博客列表</h1>
        <p class="blog-list-subtitle">探索精彩内容，分享你的想法</p>
    </header>
    <div class="search-container">
        <input type="text" id="searchInput" placeholder="搜索博客...">
        <button id="searchButton">搜索</button>
    </div>
    <div class="category-filter-container">
        <!-- 分类按钮将通过 JavaScript 动态添加 -->
    </div>
    <div id="errorMessage"></div>
    <div id="blogList"></div>

    <script>
        // 获取 URL 参数
        const urlParams = new URLSearchParams(window.location.search);
        const user = urlParams.get('user');
        const key = urlParams.get('key');
        const searchTerm = urlParams.get('search') || '';
        const token = urlParams.get('token');
        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleString('zh-CN');
        }

        function openBlog(filename) {
            window.open(`/api/view_blog/?filename=${filename}&user=${user}&key=${key}&token=${token}`, '_blank');
        }

        // 获取博客统计信息
        function getBlogStats() {
            return fetch('/api/get_blog_stats')
                .then(response => response.json())
                .then(data => {
                    console.log('Received blog stats:', data);
                    return data.success ? data.stats : {};
                })
                .catch(error => {
                    console.error('Error fetching blog stats:', error);
                    return {};
                });
        }

        // 添加全局变量来存储当前的博客列表和统计信息
        let currentBlogs = [];
        let currentStats = {};
        let currentScores = {}; // 添加全局变量来存储分数

        // 获取并渲染博客列表
        function fetchBlogsAndRender() {
            const searchTerm = urlParams.get('search') || '';
            let url = `/api/search_blogs?search=${encodeURIComponent(searchTerm)}`;
            if (user && key) {
                url += `&user=${encodeURIComponent(user)}&key=${encodeURIComponent(key)}`;
            }

            Promise.all([
                fetch(url, {
                    method: 'GET',
                    headers: {
                        'Cache-Control': 'no-cache, no-store, must-revalidate',
                        'Pragma': 'no-cache',
                        'Expires': '0'
                    },
                    cache: 'no-store'
                }).then(response => response.json()),
                getBlogStats(),
                fetch('/api/get_scores').then(response => response.json())
            ])
            .then(([searchData, stats, scoresData]) => {
                if (searchData.success) {
                    currentBlogs = searchData.blogs;
                    currentStats = stats;
                    currentScores = scoresData.scores; // 保存分数到全局变量
                    updateCategoryButtons(searchData.blogs);
                    renderBlogs(searchData.blogs, stats, scoresData.scores);
                } else {
                    document.getElementById('errorMessage').textContent = searchData.message;
                }
            })
            .catch(error => {
                console.error('Error:', error);
                document.getElementById('errorMessage').textContent = '加载博客列表时出错。请检查网络连接或联系管理员。';
            });
        }

        // 渲染博客列表
        function renderBlogs(blogs, stats, scores) {
            console.log('Rendering blogs with stats:', stats);
            const blogListElement = document.getElementById('blogList');
            blogListElement.innerHTML = ''; // 清空现有内容
            if (!Array.isArray(blogs) || blogs.length === 0) {
                blogListElement.innerHTML = '<p>没有找到匹配的博客。</p>';
                return;
            }
            blogs.forEach(blog => {
                if (!blog) return; // 跳过无效的博客对象
                const blogItem = document.createElement('div');
                blogItem.className = 'blog-item';
                
                const blogStats = stats[blog.filename] || { views: 0, comments: 0 };
                console.log(`Stats for ${blog.filename}:`, blogStats);

                // 获取博客分数
                const score = scores ? scores[blog.filename] : null;
                
                let blogContent = `
                    <div class="blog-info">
                        <h2>${blog.title || '无标题'}</h2>
                        <p class="blog-author">作者: ${blog.user || '未知'}</p>
                        <p class="blog-date">创建于 ${blog.created_at ? formatDate(blog.created_at) : '未知日期'}</p>
                        <div class="blog-stats">
                            <span>👁️ ${blogStats.views} 阅读</span>
                            <span>💬 ${blogStats.comments} 评论</span>
                            ${blog.category && blog.category.endsWith('作业') ? 
                                `<span class="blog-score" style="
                                    font-weight: bold;
                                    font-size: 1.2em;
                                    color: ${score >= 90 ? '#4CAF50' : score >= 60 ? '#FFA500' : '#808080'};
                                    margin-left: 10px;
                                ">
                                    ${score !== null ? `分数: ${score}` : '未评分'}
                                </span>` 
                                : ''}
                        </div>
                    </div>
                `;
                
                if (blog.first_image && blog.first_image.src) {
                    blogContent += `<img src="${blog.first_image.src}" alt="${blog.first_image.alt || ''}" class="blog-thumbnail">`;
                }
                
                // 添加删除按钮
                blogContent += `<button class="delete-button" data-filename="${blog.filename}">删除</button>`;
                
                blogItem.innerHTML = blogContent;
                
                // 为整个博客项添加点击事件（除了删除按钮）
                blogItem.addEventListener('click', (e) => {
                    if (!e.target.classList.contains('delete-button')) {
                        openBlog(blog.filename);
                    }
                });
                
                // 为删除按钮添加点击事件
                const deleteButton = blogItem.querySelector('.delete-button');
                deleteButton.addEventListener('click', (e) => {
                    e.stopPropagation(); // 阻止事件冒泡
                    deleteBlog(blog.filename,blog.user);
                });
                
                blogListElement.appendChild(blogItem);
            });
        }

        // 搜索功能
        function searchBlogs() {
            const searchInput = document.getElementById('searchInput');
            const searchTerm = searchInput.value.trim();
            if (searchTerm) {
                // 构建新的 URL，包含所有现有参数和新的搜索词
                const currentUrl = new URL(window.location.href);
                currentUrl.searchParams.set('search', searchTerm);
                // 重定向到新的 URL
                window.location.href = currentUrl.toString();
            }
        }

        // 显示用户名或登录按钮
        function displayUserInfo() {
            const userDisplay = document.getElementById('userDisplay');
            const loginButton = document.getElementById('loginButton');

            if (user && key) {
                userDisplay.textContent = `欢迎，${user}`;
                userDisplay.style.display = 'inline-block';
                loginButton.style.display = 'none';
            } else {
                userDisplay.style.display = 'none';
                loginButton.style.display = 'inline-block';
            }
        }

        // 初始加载
        document.addEventListener('DOMContentLoaded', function() {
            displayUserInfo(); // 替换原来的 displayUsername()
            fetchBlogsAndRender();

            // 如果有搜索关键词，将其填入搜索框
            if (searchTerm) {
                document.getElementById('searchInput').value = searchTerm;
            }

            // 添加搜索按钮事件监听器
            document.getElementById('searchButton').addEventListener('click', searchBlogs);

            // 添加搜索输入框的回车键事件监听器
            document.getElementById('searchInput').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    searchBlogs();
                }
            });
        });

        // 添加deleteBlog函数
        function deleteBlog(filename, blog_user) {
            if (confirm('确定要删除这篇博客吗？')) {
                fetch('/api/delete_blog', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        user: user,
                        key: key,
                        blog_user: blog_user,
                        filename: filename,
                        token: token
                    }),
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('博客已成功删除');
                        fetchBlogsAndRender(); // 重新加载博客列表
                    } else {
                        alert('删除博客失败: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('删除博客时发生错误');
                });
            }
        }

        // 修改分类按钮更新函数
        function updateCategoryButtons(blogs) {
            const categories = new Set(['all']);
            blogs.forEach(blog => {
                if (blog.category) {  // 移除了 !blog.category.endsWith('作业') 的条件
                    categories.add(blog.category);
                }
            });

            const containerElement = document.querySelector('.category-filter-container');
            containerElement.innerHTML = '';

            categories.forEach(category => {
                const button = document.createElement('button');
                button.className = 'category-filter-btn';
                button.textContent = category === 'all' ? '全部' : category;
                button.dataset.category = category;
                if (category === 'all') {
                    button.classList.add('active');
                }
                button.onclick = () => filterByCategory(category);
                containerElement.appendChild(button);
            });
        }

        // 添加分类过滤函数
        function filterByCategory(category) {
            document.querySelectorAll('.category-filter-btn').forEach(btn => {
                btn.classList.remove('active');
                if (btn.dataset.category === category) {
                    btn.classList.add('active');
                }
            });

            const filteredBlogs = category === 'all' 
                ? currentBlogs 
                : currentBlogs.filter(blog => blog.category === category);
            
            console.log('Filtering by category:', category);
            console.log('Filtered blogs count:', filteredBlogs.length);
            console.log('Current scores:', currentScores);

            renderBlogs(filteredBlogs, currentStats, currentScores); // 传递分数参数
        }
    </script>
</body>
</html>