# eqnarray 环境渲染功能实现总结

## 修改概述

在不改变 `blog_page.html` 现有预览和分屏渲染功能的前提下，成功增加了对 LaTeX `eqnarray` 公式组环境的渲染支持。

## 具体修改内容

### 1. LaTeX 预处理增强 (第2248-2270行)

添加了对 `eqnarray` 和 `eqnarray*` 环境的预处理：

```javascript
// 处理 eqnarray* 环境（无编号） - 转换为 align* 环境
processedContent = processedContent.replace(/\\begin\{eqnarray\*\}([\s\S]*?)\\end\{eqnarray\*\}/g, (match, p1) => {
    let alignContent = p1
        .replace(/\*/g, '\\ast')  // 避免被 Markdown 解析为斜体
        .replace(/&=/g, '&=')     // 保持等号对齐
        .replace(/&\s*&/g, '&')   // 移除多余的 & 符号
        .trim();
    
    return `<span class="math-display">$$\\begin{align*}${alignContent}\\end{align*}$$</span>`;
});

// 处理 eqnarray 环境（带编号） - 转换为 align 环境
processedContent = processedContent.replace(/\\begin\{eqnarray\}([\s\S]*?)\\end\{eqnarray\}/g, (match, p1) => {
    let alignContent = p1
        .replace(/\*/g, '\\ast')  // 避免被 Markdown 解析为斜体
        .replace(/&=/g, '&=')     // 保持等号对齐
        .replace(/&\s*&/g, '&')   // 移除多余的 & 符号
        .trim();
    
    return `<span class="math-display">$$\\begin{align}${alignContent}\\end{align}$$</span>`;
});
```

### 2. KaTeX 宏定义更新 (第2307-2312行)

增强了 KaTeX 的宏定义，支持更完整的 `eqnarray` 环境：

```javascript
macros: {
    "\\eqnarray": "\\begin{align}",
    "\\endeqnarray": "\\end{align}",
    "\\eqnarray*": "\\begin{align*}",
    "\\endeqnarray*": "\\end{align*}"
}
```

### 3. 数学公式工具栏增强 (第3011行)

在数学公式下拉菜单中添加了 `eqnarray` 环境的快捷按钮：

```javascript
{ formula: '\\begin{eqnarray}\na &= b \\\\\nc &= d\n\\end{eqnarray}', title: '公式组', symbol: 'eqn' }
```

## 支持的功能

### 1. 基本 eqnarray 环境
```latex
\begin{eqnarray}
y_{1}+y_{2} &= 表达式1 \\
&= 表达式2 \\
&= 表达式3
\end{eqnarray}
```

### 2. 无编号 eqnarray* 环境
```latex
\begin{eqnarray*}
a &= b + c \\
d &= e + f \\
g &= h + i
\end{eqnarray*}
```

### 3. 复杂数学表达式支持
- 分数：`\frac{a}{b}`
- 上下标：`x^{y}`, `x_{y}`
- 希腊字母：`\alpha`, `\beta`, `\omega` 等
- 数学函数：`\sin`, `\cos`, `\tan` 等
- 特殊符号：`\infty`, `\partial`, `\nabla` 等

## 技术实现细节

### 1. 预处理策略
- 将 `eqnarray` 环境转换为 KaTeX 支持的 `align` 环境
- 处理 `*` 符号避免与 Markdown 斜体语法冲突
- 清理多余的对齐符号 `&`
- 保持等号对齐结构

### 2. 渲染流程
1. 用户输入 LaTeX `eqnarray` 环境
2. 预处理器将其转换为 `align` 环境
3. Markdown 解析器处理内容
4. KaTeX 渲染数学公式
5. 在预览面板中显示渲染结果

### 3. 兼容性保证
- 保持现有的分屏预览功能不变
- 保持现有的 LaTeX 公式渲染功能不变
- 保持现有的 Mermaid 图表渲染功能不变
- 保持现有的代码高亮功能不变

## 测试文件

创建了以下测试文件验证功能：

1. `test_eqnarray.html` - 独立的 eqnarray 渲染测试页面
2. `eqnarray_example.md` - 博客编辑器使用示例

## 使用方法

1. 在博客编辑器的内容区域直接输入 `\begin{eqnarray}...\end{eqnarray}`
2. 使用数学公式工具栏中的"公式组"按钮快速插入模板
3. 在分屏预览模式下实时查看渲染效果
4. 支持复制粘贴现有的 LaTeX eqnarray 代码

## 注意事项

1. 确保 LaTeX 语法正确，特别是对齐符号 `&` 的使用
2. 避免在公式中直接使用 `*` 符号，系统会自动转换为 `\ast`
3. 复杂公式建议使用适当的括号和空格提高可读性
4. 支持嵌套的数学环境和复杂表达式
