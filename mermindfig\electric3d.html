<!DOCTYPE html>
<html>
<head>
    <title>3D带电圆盘电场线</title>
    <style>
        body { margin: 0; font-family: Arial, sans-serif; }
        canvas { display: block; }
        
        #controls {
            position: absolute;
            top: 10px;
            left: 10px;
            background-color: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 10px;
            border-radius: 5px;
            z-index: 100;
            max-width: 300px;
        }
        
        #controls h2 {
            margin-top: 0;
            font-size: 18px;
        }
        
        #controls label {
            display: block;
            margin: 5px 0;
        }
        
        #controls button {
            margin-top: 10px;
            padding: 5px 10px;
            background-color: #4488ff;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        
        #controls button:hover {
            background-color: #3377ee;
        }
        
        #loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background-color: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 20px;
            border-radius: 5px;
            z-index: 200;
            text-align: center;
            display: none;
        }
        
        #startScreen {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 50, 0.9);
            color: white;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 300;
        }
        
        #startScreen h1 {
            font-size: 28px;
            margin-bottom: 20px;
        }
        
        #startScreen p {
            max-width: 600px;
            text-align: center;
            margin-bottom: 30px;
            line-height: 1.5;
        }
        
        #startScreen button {
            padding: 10px 20px;
            background-color: #4488ff;
            color: white;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
        }
        
        #startScreen button:hover {
            background-color: #3377ee;
        }
        
        /* 当作为iframe嵌入时隐藏开始屏幕 */
        body.embedded #startScreen {
            display: none;
        }
        
        /* 当作为iframe嵌入时自动显示控制面板 */
        body.embedded #controls {
            display: block !important;
        }
    </style>
</head>
<body>
    <div id="startScreen">
        <h1>3D带电圆盘电场线可视化</h1>
        <p>
            本可视化展示了均匀带电圆盘周围的电场线分布。电场线显示了电场的方向和相对强度。
            圆盘上方的电场线为蓝色系，下方为红色系，对称轴上的电场线为白色。
            您可以通过控制面板调整显示参数。
        </p>
        <button id="startButton">显示均匀带电圆盘电场线</button>
    </div>
    
    <div id="loading">
        <p>正在生成电场线，请稍候...</p>
    </div>
    
    <div id="controls" style="display: none;">
        <h2>电场线控制面板</h2>
        <label><input type="checkbox" id="edgeLines" checked> 边缘电场线</label>
        <label><input type="checkbox" id="surfaceLines" checked> 上表面电场线</label>
        <label><input type="checkbox" id="bottomLines" checked> 下表面电场线</label>
        <label><input type="checkbox" id="sideLines" checked> 侧面电场线</label>
        <label><input type="checkbox" id="axisLines" checked> 对称轴电场线</label>
        <label><input type="checkbox" id="nearFieldLines" checked> 近场电场线</label>
        <label><input type="checkbox" id="rotateView" checked> 自动旋转视图</label>
        <label>旋转速度: <input type="range" id="rotationSpeed" min="1" max="10" value="2"></label>
        <button id="regenerateButton">重新生成电场线</button>
    </div>
    
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script>
        // 全局变量
        let scene, camera, renderer, disk;
        let fieldLines = []; // 存储所有电场线对象
        
        // 物理参数
        let Q = 1e-6;        // 电荷量（库仑）
        let R = 1.5;          // 圆盘半径（米）
        const epsilon0 = 8.85e-12;
        const k = 1/(4*Math.PI*epsilon0);

        // 三维电场线参数
        const edgeLines = 36;   // 圆盘边缘电场线数量
        const surfaceLines = 120; // 圆盘上表面电场线数量
        const bottomLines = 120;  // 圆盘下表面电场线数量
        const sideLines = 24;    // 圆盘侧面发出的电场线数量
        const axisLines = 15;    // 对称轴上的电场线数量
        const maxSteps = 250;   // 每条线的最大步数
        const stepSize = 0.04;  // 积分步长
        
        // 控制面板
        const controls = {
            showEdgeLines: true,
            showSurfaceLines: true,
            showBottomLines: true,
            showSideLines: true,
            showAxisLines: true,  // 显示对称轴上的电场线
            showNearFieldLines: true, // 显示近场电场线
            rotateView: true,
            rotationSpeed: 0.0002 // 旋转速度
        };
        
        // 检查是否作为iframe嵌入
        function checkIfEmbedded() {
            try {
                // 如果能访问parent且parent不等于window，则是嵌入的iframe
                return (window.parent !== window);
            } catch (e) {
                // 如果出现跨域错误，也可能是嵌入的iframe
                return true;
            }
        }
        
        // 从URL获取参数
        function getParameterByName(name, url = window.location.href) {
            name = name.replace(/[\[\]]/g, '\\$&');
            const regex = new RegExp('[?&]' + name + '(=([^&#]*)|&|#|$)'),
                results = regex.exec(url);
            if (!results) return null;
            if (!results[2]) return '';
            return decodeURIComponent(results[2].replace(/\+/g, ' '));
        }
        
        // 应用从父页面传递的参数
        function applyParentParameters() {
            // 从URL获取参数
            const diskRadius = getParameterByName('radius');
            const diskCharge = getParameterByName('charge');
            
            // 如果有参数，则应用它们
            if (diskRadius) {
                R = parseFloat(diskRadius);
            }
            
            if (diskCharge) {
                Q = parseFloat(diskCharge) * 1e-6; // 转换为适当的单位
            }
        }

        // 初始化函数
        function init() {
            // 检查是否作为iframe嵌入
            const isEmbedded = checkIfEmbedded();
            
            if (isEmbedded) {
                // 如果是嵌入的，添加embedded类
                document.body.classList.add('embedded');
                
                // 应用从父页面传递的参数
                applyParentParameters();
            }
            
            // 初始化场景
            scene = new THREE.Scene();
            camera = new THREE.PerspectiveCamera(75, window.innerWidth/window.innerHeight, 0.1, 1000);
            renderer = new THREE.WebGLRenderer({ antialias: true });
            renderer.setSize(window.innerWidth, window.innerHeight);
            document.body.appendChild(renderer.domElement);
            
            // 相机位置
            camera.position.z = 6;
            camera.position.y = 3;
            camera.position.x = 2;
            camera.lookAt(0, 0, 0);

            // 添加环境光
            const light = new THREE.AmbientLight(0xffffff, 0.8);
            scene.add(light);
            
            // 创建带电圆盘
            createDisk();
            
            // 窗口大小调整
            window.addEventListener('resize', onWindowResize, false);
            
            // 开始动画循环
            animate();
            
            // 如果是嵌入的，直接生成电场线
            if (isEmbedded) {
                generateFieldLines();
            }
        }

        // 动画循环
        function animate() {
            requestAnimationFrame(animate);
            
            // 可选的视图旋转
            if (controls.rotateView) {
                // 减慢旋转速度
                camera.position.x = 6 * Math.sin(Date.now() * controls.rotationSpeed);
                camera.position.z = 6 * Math.cos(Date.now() * controls.rotationSpeed);
                // 保持一定的高度，以获得更好的三维效果
                camera.position.y = 3;
                camera.lookAt(0, 0, 0);
            }
            
            renderer.render(scene, camera);
        }

        // 窗口大小调整
        function onWindowResize() {
            camera.aspect = window.innerWidth/window.innerHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(window.innerWidth, window.innerHeight);
        }

        function createDisk() {
            const geometry = new THREE.CircleGeometry(R, 32);
            const material = new THREE.MeshPhongMaterial({
                color: 0x4488ff,
                side: THREE.DoubleSide
            });
            disk = new THREE.Mesh(geometry, material);
            disk.rotation.x = Math.PI/2;
            scene.add(disk);
        }
        
        // 清除所有电场线
        function clearFieldLines() {
            for (let line of fieldLines) {
                scene.remove(line);
            }
            fieldLines = [];
        }

        // 生成所有电场线
        function generateFieldLines() {
            // 显示加载提示
            document.getElementById('loading').style.display = 'block';
            
            // 清除现有电场线
            clearFieldLines();
            
            // 使用setTimeout让UI有时间更新
            setTimeout(() => {
                // 创建新的电场线
                createFieldLines();
                
                // 隐藏加载提示
                document.getElementById('loading').style.display = 'none';
            }, 50);
        }

        function createFieldLines() {
            // 从圆盘边缘发出的电场线
            if (controls.showEdgeLines) {
                // 生成均匀分布的边缘起始点
                for(let i=0; i<edgeLines; i++) {
                    const phi = (i * 2*Math.PI)/edgeLines;
                    const theta = Math.PI/2;  // 在赤道平面
                    
                    const startPos = sphericalToCartesian(R, theta, phi);
                    createFieldLine(startPos, 0xffaa00);
                }
            }
            
            // 从对称轴上发出的电场线
            if (controls.showAxisLines) {
                // 在对称轴上选取点，使用非线性分布使靠近圆盘处更密集
                const maxDistance = 5;
                
                // 对称轴上方
                for(let i=1; i<=axisLines; i++) {
                    // 使用平方根分布使点在靠近圆盘处更密集
                    const t = i / axisLines;
                    const y = 0.1 + (maxDistance - 0.1) * t * t; // 从0.1开始，避免太靠近圆盘
                    createFieldLine(new THREE.Vector3(0, y, 0), 0xffffff);
                }
                
                // 对称轴下方
                for(let i=1; i<=axisLines; i++) {
                    const t = i / axisLines;
                    const y = -0.1 - (maxDistance - 0.1) * t * t; // 从-0.1开始，避免太靠近圆盘
                    createFieldLine(new THREE.Vector3(0, y, 0), 0xffffff, true);
                }
            }
            
            // 从圆盘上表面发出的电场线
            if (controls.showSurfaceLines) {
                // 使用同心圆和角度均匀分布的方式生成点
                const radialDivisions = 10;  // 径向分割数
                const angularDivisions = 12; // 角度分割数
                
                // 对每个径向位置
                for(let i=0; i<radialDivisions; i++) {
                    // 使用非线性分布使点在靠近圆心处更密集
                    const t = (i + 0.5) / radialDivisions;
                    const radiusFraction = t * t; // 平方分布，靠近圆心更密集
                    const radius = R * radiusFraction;
                    
                    // 角度分割随半径增加而增加，使外圈点更多
                    const currentAngularDivisions = Math.max(4, Math.floor(angularDivisions * radiusFraction) + 6);
                    
                    // 对每个角度位置
                    for(let j=0; j<currentAngularDivisions; j++) {
                        const angle = (j * 2 * Math.PI) / currentAngularDivisions;
                        
                        // 转换为笛卡尔坐标
                        const x = radius * Math.cos(angle);
                        const z = radius * Math.sin(angle);
                        
                        // 稍微偏移起始点，使其位于圆盘上方
                        const startPos = new THREE.Vector3(x, 0.01, z);
                        
                        // 根据到圆心的距离设置不同颜色
                        const colorValue = Math.floor(0x00ffff * (1 - radiusFraction) + 0x00aaff * radiusFraction);
                        createFieldLine(startPos, colorValue);
                    }
                }
            }
            
            // 从圆盘下表面发出的电场线
            if (controls.showBottomLines) {
                // 使用同心圆和角度均匀分布的方式生成点
                const radialDivisions = 10;  // 径向分割数
                const angularDivisions = 12; // 角度分割数
                
                // 对每个径向位置
                for(let i=0; i<radialDivisions; i++) {
                    // 使用非线性分布使点在靠近圆心处更密集
                    const t = (i + 0.5) / radialDivisions;
                    const radiusFraction = t * t; // 平方分布，靠近圆心更密集
                    const radius = R * radiusFraction;
                    
                    // 角度分割随半径增加而增加，使外圈点更多
                    const currentAngularDivisions = Math.max(4, Math.floor(angularDivisions * radiusFraction) + 6);
                    
                    // 对每个角度位置
                    for(let j=0; j<currentAngularDivisions; j++) {
                        const angle = (j * 2 * Math.PI) / currentAngularDivisions;
                        
                        // 转换为笛卡尔坐标
                        const x = radius * Math.cos(angle);
                        const z = radius * Math.sin(angle);
                        
                        // 稍微偏移起始点，使其位于圆盘下方
                        const startPos = new THREE.Vector3(x, -0.01, z);
                        
                        // 根据到圆心的距离设置不同颜色
                        const colorValue = Math.floor(0xff5500 * (1 - radiusFraction) + 0xff0000 * radiusFraction);
                        createFieldLine(startPos, colorValue, true);
                    }
                }
            }
            
            // 从圆盘侧面不同角度发出的电场线
            if (controls.showSideLines) {
                // 在圆盘周围的不同角度生成起始点
                for(let i=0; i<sideLines; i++) {
                    const phi = (i * 2*Math.PI)/sideLines;
                    
                    // 从不同仰角发出电场线
                    for(let j=1; j<=2; j++) {
                        // 使用非线性分布使点在靠近赤道处更密集
                        const angleFraction = j / 6; // 1/6, 2/6
                        
                        // 上半球
                        const thetaUp = Math.PI/2 - angleFraction * Math.PI/2;
                        const startPosUp = sphericalToCartesian(R, thetaUp, phi);
                        createFieldLine(startPosUp, 0x66ff66);
                        
                        // 下半球
                        const thetaDown = Math.PI/2 + angleFraction * Math.PI/2;
                        const startPosDown = sphericalToCartesian(R, thetaDown, phi);
                        createFieldLine(startPosDown, 0xff66ff, true);
                    }
                }
            }
            
            // 添加额外的近场电场线
            if (controls.showNearFieldLines) {
                addNearFieldLines();
            }
        }
        
        // 添加靠近圆盘的密集电场线
        function addNearFieldLines() {
            // 在圆盘上方近场区域添加密集电场线
            const nearFieldRadius = R * 1.2; // 略大于圆盘半径
            const nearFieldHeight = 0.3;     // 近场高度
            const radialDivisions = 8;       // 径向分割
            const angularDivisions = 16;     // 角度分割
            const heightDivisions = 2;       // 高度分割
            
            // 在近场区域生成起始点
            for(let i=0; i<radialDivisions; i++) {
                // 使用线性分布
                const radiusFraction = (i + 0.5) / radialDivisions;
                const radius = nearFieldRadius * radiusFraction;
                
                for(let j=0; j<angularDivisions; j++) {
                    const angle = (j * 2 * Math.PI) / angularDivisions;
                    
                    // 转换为笛卡尔坐标
                    const x = radius * Math.cos(angle);
                    const z = radius * Math.sin(angle);
                    
                    // 在不同高度生成点
                    for(let k=1; k<=heightDivisions; k++) {
                        // 上方近场
                        const y = (k * nearFieldHeight) / heightDivisions;
                        const startPosUp = new THREE.Vector3(x, y, z);
                        
                        // 使用淡蓝色
                        const colorUp = 0xaaddff;
                        createFieldLine(startPosUp, colorUp);
                        
                        // 下方近场
                        const startPosDown = new THREE.Vector3(x, -y, z);
                        
                        // 使用淡红色
                        const colorDown = 0xffaaaa;
                        createFieldLine(startPosDown, colorDown, true);
                    }
                }
            }
        }

        function createFieldLine(startPos, color = 0xffaa00, isBottomSide = false) {
            const points = [];
            let currentPos = startPos.clone();
            
            // 正向追踪
            for(let i=0; i<maxSteps; i++) {
                const E = calculateEField(currentPos);
                if(E.length() < 0.01) break;
                
                // 确保电场线方向正确
                let direction = E.normalize();
                
                // 如果是下方的电场线，确保它们向外发散
                if(isBottomSide && currentPos.y < 0) {
                    // 确保y方向为负（向下）
                    if(direction.y > 0) {
                        direction.y = -direction.y;
                    }
                } else if(!isBottomSide && currentPos.y > 0) {
                    // 上方电场线确保y方向为正（向上）
                    if(direction.y < 0) {
                        direction.y = -direction.y;
                    }
                }
                
                currentPos.add(direction.multiplyScalar(stepSize));
                points.push(currentPos.clone());
                
                // 如果电场线太长，停止追踪
                if(currentPos.length() > 15) break;
            }
            
            // 如果电场线太短，不显示
            if(points.length < 5) return;
            
            // 创建线对象
            const geometry = new THREE.BufferGeometry().setFromPoints(points);
            const material = new THREE.LineBasicMaterial({
                color: color,
                linewidth: 2
            });
            const line = new THREE.Line(geometry, material);
            scene.add(line);
            
            // 将线添加到数组中，以便后续清除
            fieldLines.push(line);
        }

        function calculateEField(position) {
            // 轴对称坐标系简化计算
            const r = Math.sqrt(position.x**2 + position.z**2);
            const y = position.y;
            
            // 均匀带电圆盘的电场公式
            const sigma = Q/(Math.PI*R**2); // 面电荷密度
            
            // 计算电场
            let Ex = 0, Ey = 0, Ez = 0;
            
            // 轴上精确解
            if(r < 1e-3) {
                // 在轴上，电场只有y分量
                Ey = (sigma/(2*epsilon0)) * (1 - y/Math.sqrt(y**2 + R**2)) * Math.sign(y);
                if (Math.abs(y) < 1e-3) {
                    // 在圆盘中心，电场为0
                    Ey = 0;
                }
            } else {
                // 非轴上点的电场计算
                // 使用更精确的公式
                const rho = r/R; // 归一化半径
                const h = y/R;   // 归一化高度
                
                // 计算电场的y分量
                Ey = (sigma/(2*epsilon0)) * (
                    h / Math.sqrt(h*h + 1) - 
                    h / Math.sqrt(h*h + rho*rho)
                );
                
                // 计算电场的径向分量
                const Er = (sigma/(2*epsilon0)) * (
                    rho / Math.sqrt(h*h + rho*rho)
                );
                
                // 将径向分量分解到x和z方向
                Ex = Er * position.x / r;
                Ez = Er * position.z / r;
            }
            
            return new THREE.Vector3(Ex, Ey, Ez);
        }

        function sphericalToCartesian(r, theta, phi) {
            return new THREE.Vector3(
                r * Math.sin(theta) * Math.cos(phi),
                r * Math.cos(theta),
                r * Math.sin(theta) * Math.sin(phi)
            );
        }
        
        // 初始化控制面板事件监听
        function initControls() {
            // 复选框控制
            document.getElementById('edgeLines').addEventListener('change', function() {
                controls.showEdgeLines = this.checked;
                generateFieldLines();
            });
            
            document.getElementById('surfaceLines').addEventListener('change', function() {
                controls.showSurfaceLines = this.checked;
                generateFieldLines();
            });
            
            document.getElementById('bottomLines').addEventListener('change', function() {
                controls.showBottomLines = this.checked;
                generateFieldLines();
            });
            
            document.getElementById('sideLines').addEventListener('change', function() {
                controls.showSideLines = this.checked;
                generateFieldLines();
            });
            
            document.getElementById('axisLines').addEventListener('change', function() {
                controls.showAxisLines = this.checked;
                generateFieldLines();
            });
            
            document.getElementById('nearFieldLines').addEventListener('change', function() {
                controls.showNearFieldLines = this.checked;
                generateFieldLines();
            });
            
            document.getElementById('rotateView').addEventListener('change', function() {
                controls.rotateView = this.checked;
            });
            
            // 旋转速度滑块
            document.getElementById('rotationSpeed').addEventListener('input', function() {
                controls.rotationSpeed = this.value * 0.0001;
            });
            
            // 重新生成按钮
            document.getElementById('regenerateButton').addEventListener('click', function() {
                generateFieldLines();
            });
            
            // 开始按钮
            document.getElementById('startButton').addEventListener('click', function() {
                document.getElementById('startScreen').style.display = 'none';
                document.getElementById('controls').style.display = 'block';
                generateFieldLines();
            });
        }
        
        // 页面加载完成后初始化
        window.addEventListener('load', function() {
            init();
            initControls();
        });
    </script>
</body>
</html>