/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Roboto', sans-serif;
    background-color: #FFFBE6;
    color: #2C3E2C;
    line-height: 1.6;
    min-height: 100vh;
}

/* 滚动文字容器 */
.scrolling-text-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    background: linear-gradient(90deg, rgba(240,240,240,0.5) 0%, rgba(255,255,255,0.6) 50%, rgba(240,240,240,0.5) 100%);
    padding: 12px 0;
    overflow: hidden;
    z-index: 999999;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    pointer-events: none;
}

.scrolling-text {
    display: inline-block;
    white-space: nowrap;
    animation: scroll 30s linear infinite;
    font-weight: bold;
    font-family: 'Roboto', sans-serif;
    font-size: 16px;
    color: #333;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
}

@keyframes scroll {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* 主容器 */
.container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    padding: 60px 20px 20px;
}

/* 认证容器 */
.auth-container {
    background-color: #FFF8DC;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    border: 1px solid #F3E2A9;
    max-width: 450px;
    width: 100%;
}

/* 认证选项 */
.auth-options {
    display: flex;
    justify-content: center;
    margin-bottom: 2rem;
    background-color: #F3E2A9;
    border-radius: 10px;
    padding: 5px;
}

.auth-option {
    flex: 1;
    padding: 10px 15px;
    border: none;
    background: transparent;
    color: #5A4A1C;
    font-weight: 500;
    cursor: pointer;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.auth-option.active {
    background-color: #FFF8DC;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* 认证标签 */
.auth-tab {
    display: none;
}

.auth-tab.active {
    display: block;
}

/* 登录标题 */
.login-title-container {
    text-align: center;
    padding: 20px 0;
    margin-bottom: 20px;
}

.login-title {
    color: #5A4A1C;
    font-family: 'Roboto', sans-serif;
    font-size: 1.8em;
    font-weight: 500;
    margin: 0;
    padding: 10px 30px;
    display: inline-block;
    background: linear-gradient(120deg, #FFF8DC 0%, #F3E2A9 100%);
    border-radius: 10px;
    box-shadow: 0 2px 6px rgba(0,0,0,0.05);
    border: 1px solid #F3E2A9;
}

.login-title span {
    background: linear-gradient(120deg, #8B7355 0%, #5A4A1C 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

/* 表单样式 */
.form-title {
    text-align: center;
    color: #5A4A1C;
    font-weight: 500;
    margin-bottom: 1.5rem;
}

.auth-form {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.form-group {
    position: relative;
}

.form-group input {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #F3E2A9;
    border-radius: 5px;
    background-color: #FFFDF0;
    font-size: 16px;
    min-height: 44px;
    color: #2C3E2C;
    transition: border-color 0.3s ease;
}

.form-group input:focus {
    outline: none;
    border-color: #F0C674;
    box-shadow: 0 0 0 2px rgba(240,198,116,0.5);
}

.form-group input::placeholder {
    color: #8B7355;
    opacity: 0.7;
}

/* 验证消息 */
.validation-message {
    font-size: 0.9em;
    margin-top: 0.5rem;
    padding: 0.5rem;
    border-radius: 4px;
}

.validation-message.success {
    color: #2C3E2C;
    background-color: #E8F0E0;
    border: 1px solid #D7E3D7;
}

.validation-message.error {
    color: #8B3E38;
    background-color: #FFE8E6;
    border: 1px solid #F3D1D0;
}

/* 提交按钮 */
.submit-btn {
    padding: 12px 24px;
    background-color: #F0C674;
    color: #5A4A1C;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 16px;
    font-weight: 500;
    transition: all 0.3s ease;
    margin-top: 1rem;
}

.submit-btn:hover {
    background-color: #E6B84D;
    transform: translateY(-1px);
}

.submit-btn:disabled {
    background-color: #F5E6B3;
    cursor: not-allowed;
    transform: none;
}

/* API链接容器 */
.api-links-container {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 10px;
    border: 1px solid #e9ecef;
    margin: 10px 0;
}

.api-link-title {
    color: #495057;
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 10px;
}

.api-link-button {
    display: block;
    background-color: #ffffff;
    border: 1px solid #dee2e6;
    border-radius: 5px;
    padding: 8px 12px;
    margin: 8px 0;
    color: #0366d6;
    text-decoration: none;
    transition: all 0.3s ease;
}

.api-link-button:hover {
    background-color: #f1f8ff;
    border-color: #0366d6;
    text-decoration: none;
}

.api-link-icon {
    margin-right: 8px;
    font-size: 16px;
}

/* 消息容器 */
.message-container {
    position: fixed;
    top: 80px;
    right: 20px;
    z-index: 10000;
}

.message {
    padding: 12px 20px;
    margin-bottom: 10px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transform: translateX(100%);
    opacity: 0;
    transition: all 0.3s ease;
    max-width: 300px;
    word-wrap: break-word;
}

.message.show {
    transform: translateX(0);
    opacity: 1;
}

.message.success {
    background-color: #E8F0E0;
    color: #2C3E2C;
    border: 1px solid #D7E3D7;
}

.message.error {
    background-color: #FFE8E6;
    color: #8B3E38;
    border: 1px solid #F3D1D0;
}

.message.info {
    background-color: #E3F2FD;
    color: #1565C0;
    border: 1px solid #BBDEFB;
}

.message.warning {
    background-color: #FFF8E1;
    color: #F57C00;
    border: 1px solid #FFE0B2;
}

/* 仪表板样式 */
.dashboard-body {
    display: flex;
    padding-top: 50px;
}

.sidebar {
    width: 300px;
    background-color: #FFF8DC;
    padding: 20px;
    border-right: 1px solid #F3E2A9;
    height: calc(100vh - 50px);
    overflow-y: auto;
    position: fixed;
    left: 0;
    top: 50px;
}

.main-content {
    margin-left: 300px;
    padding: 20px;
    flex: 1;
    min-height: calc(100vh - 50px);
}

/* 用户欢迎信息 */
.user-welcome {
    padding: 1rem;
    background: linear-gradient(135deg, #F0F5F0, #E8F0E8);
    border-radius: 10px;
    border: 1px solid #D7E3D7;
    margin-bottom: 1rem;
    text-align: center;
}

.user-welcome-text {
    color: #2C3E2C;
    font-size: 1.1em;
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.user-name {
    color: #4A6B4A;
    font-size: 1.2em;
    font-weight: bold;
    margin: 0.5rem 0;
}

/* VIP用户样式 */
.vip-welcome {
    background: linear-gradient(135deg, #FFF8E1, #FFECB3);
    border: 1px solid #FFD54F;
}

.vip-user {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    flex-wrap: wrap;
}

.vip-username {
    background: linear-gradient(90deg, #FF8C00 0%, #FFD700 20%, #FFA500 40%, #FFD700 60%, #FF8C00 80%, #FFD700 100%);
    background-size: 200% auto;
    color: transparent;
    -webkit-background-clip: text;
    background-clip: text;
    animation: shine 3s linear infinite;
    font-weight: bold;
    font-size: 1.3em;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
    padding: 0 5px;
    letter-spacing: 1px;
}

@keyframes shine {
    0% { background-position: -100% center; }
    100% { background-position: 200% center; }
}

.vip-badge {
    background: linear-gradient(45deg, #FF8C00, #FFD700);
    color: #000;
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 0.8em;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    display: inline-flex;
    align-items: center;
    gap: 4px;
}

.sidebar-divider {
    margin: 1rem 0;
    border-top: 1px solid #E0E8E0;
}

/* API配置区域 */
.api-config-section h4 {
    color: #5A4A1C;
    margin-bottom: 1rem;
}

.api-key-selector {
    margin-bottom: 1rem;
}

.api-key-selector label {
    display: block;
    margin-bottom: 0.5rem;
    color: #5A4A1C;
    font-weight: 500;
}

.api-key-selector select {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid #F3E2A9;
    border-radius: 5px;
    background-color: #FFFDF0;
}

.api-key-input {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.api-key-input input {
    flex: 1;
    padding: 0.5rem;
    border: 1px solid #F3E2A9;
    border-radius: 5px;
    background-color: #FFFDF0;
}

.api-key-input button {
    padding: 0.5rem 1rem;
    background-color: #F0C674;
    color: #5A4A1C;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.api-key-input button:hover {
    background-color: #E6B84D;
}

/* 管理员区域样式 */
.admin-section {
    margin-bottom: 1rem;
    padding: 1rem 0;
}

.admin-section h4 {
    color: #8B7355;
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 0.8rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    display: flex;
    align-items: center;
}

.admin-section h4::before {
    content: '👑';
    margin-right: 0.5rem;
    font-size: 1rem;
}

.admin-links {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.admin-link {
    display: flex;
    align-items: center;
    padding: 0.7rem 1rem;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    text-decoration: none;
    border-radius: 8px;
    font-weight: 500;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
}

.admin-link:hover {
    background: linear-gradient(135deg, #5a6fd8, #6a4190);
    transform: translateX(5px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.admin-icon {
    margin-right: 0.8rem;
    font-size: 1rem;
    opacity: 0.9;
}

/* 退出按钮 */
.logout-section {
    margin-top: auto;
}

.logout-btn {
    display: block;
    width: 100%;
    padding: 12px;
    background-color: #F0C674;
    color: #5A4A1C;
    text-decoration: none;
    text-align: center;
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.logout-btn:hover {
    background-color: #E6B84D;
    transform: translateY(-1px);
}

/* 页面标题 */
.page-title {
    text-align: center;
    margin-bottom: 2rem;
}

.page-title h1 {
    color: #FFB74D;
    background-color: #FFF8DC;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    font-weight: bold;
    font-size: 36px;
    max-width: 800px;
    margin: 0 auto;
    border: 2px solid #D4B176;
}

/* 功能网格 */
.feature-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.feature-card {
    background-color: #FFF8DC;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    border: 1px solid #F3E2A9;
    transition: transform 0.3s, box-shadow 0.3s;
    text-align: center;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.2);
}

.feature-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.feature-card h3 {
    color: #5A4A1C;
    margin-bottom: 1rem;
    font-size: 1.5rem;
}

.feature-card p {
    color: #8B7355;
    margin-bottom: 1.5rem;
}

.feature-btn {
    padding: 10px 20px;
    background-color: #3498DB;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.feature-btn:hover {
    background-color: #2980B9;
    transform: scale(1.05);
}

/* 知识库卡片特殊样式 */
.knowledge-card {
    grid-column: span 2;
}

.knowledge-sections {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin-top: 1rem;
}

.knowledge-section h4 {
    color: #5A4A1C;
    margin-bottom: 1rem;
}

.upload-section {
    text-align: center;
}

.upload-btn {
    padding: 10px 20px;
    background-color: #28a745;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    margin-bottom: 1rem;
    transition: background-color 0.3s ease;
}

.upload-btn:hover {
    background-color: #218838;
}

.upload-info {
    font-size: 0.9rem;
    color: #8B7355;
    margin-bottom: 0.5rem;
}

.knowledge-access {
    text-align: center;
}

.knowledge-access p {
    margin-bottom: 1rem;
}

/* 使用说明 */
.usage-instructions {
    background-color: #FFF8DC;
    border: 1px solid #E6B84D;
    border-radius: 10px;
    padding: 15px;
    margin-top: 2rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    color: #5A4A1C;
    font-size: 0.95em;
}

/* 快捷入口样式 */
.entrance-section {
    margin-bottom: 2rem;
}

.section-title {
    text-align: center;
    font-size: 2rem;
    font-weight: 700;
    color: #495057;
    margin-bottom: 2rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    position: relative;
}

.section-title::before {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 2px;
}

.section-title i {
    background: linear-gradient(135deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-size: 1.8rem;
}

.entrance-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.entrance-btn {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding: 1.5rem;
    border-radius: 16px;
    text-decoration: none;
    font-weight: bold;
    font-size: 1rem;
    transition: all 0.3s ease;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
    border: none;
    cursor: pointer;
    min-height: 80px;
    position: relative;
    overflow: hidden;
    gap: 1rem;
}

.entrance-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
    filter: brightness(1.1);
}

.entrance-btn:active {
    transform: translateY(-1px);
}

.entrance-icon {
    font-size: 2.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    backdrop-filter: blur(10px);
    position: relative;
    transition: all 0.3s ease;
}

.entrance-btn:hover .entrance-icon {
    transform: scale(1.1) rotate(5deg);
    background: rgba(255, 255, 255, 0.3);
}

/* 图标动画效果 */
@keyframes sparkle {
    0%, 100% {
        opacity: 0.7;
        transform: scale(1) rotate(0deg);
    }
    50% {
        opacity: 1;
        transform: scale(1.2) rotate(180deg);
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 0.8;
        transform: scale(1);
    }
    50% {
        opacity: 1;
        transform: scale(1.1);
    }
}

/* 增强入口按钮的图标效果 */
.entrance-btn .entrance-icon i {
    transition: all 0.3s ease;
}

.entrance-btn:hover .entrance-icon i:first-child {
    transform: scale(1.1);
    filter: drop-shadow(0 0 8px rgba(255,255,255,0.8));
}

.entrance-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.entrance-text {
    font-size: 1.2rem;
    font-weight: 700;
    text-align: left;
    margin: 0;
}

.entrance-description {
    font-size: 0.9rem;
    opacity: 0.9;
    font-weight: 400;
    text-align: left;
    margin: 0;
}

/* 入口按钮悬停效果 */
.entrance-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.entrance-btn:hover::before {
    left: 100%;
}

/* 错误状态样式 */
.entrance-error {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    color: #d32f2f;
    font-style: italic;
    grid-column: 1 / -1; /* 占据整行 */
}

.entrance-error::before {
    content: '⚠️';
    margin-right: 0.5rem;
}

/* 天气信息样式 */
.weather-section {
    margin-bottom: 2rem;
    width: 100%;
    display: block;
    clear: both;
    grid-column: 1 / -1; /* 如果在网格中，占满整行 */
    flex-basis: 100%; /* 如果在flex中，占满整行 */
    background: linear-gradient(135deg,
        rgba(102, 126, 234, 0.05) 0%,
        rgba(118, 75, 162, 0.05) 50%,
        rgba(64, 93, 230, 0.05) 100%);
    border-radius: 20px;
    padding: 12px;
    backdrop-filter: blur(5px);
    border: 1px solid rgba(102, 126, 234, 0.1);
}

.weather-card {
    background: transparent;
    border: none;
    display: block;
    width: 100%;
    box-sizing: border-box;
}

.weather-full-width {
    max-width: 100%;
    width: 100%;
    display: block;
    box-sizing: border-box;
}

.weather-info {
    text-align: center;
    padding: 1rem;
}

/* 确保天气卡片独占一行，不与其他元素并排 */
.weather-section::before,
.weather-section::after {
    content: "";
    display: table;
    clear: both;
}

.weather-section {
    float: none !important;
    display: block !important;
    width: 100% !important;
}

.weather-city {
    font-size: 1.2rem;
    font-weight: bold;
    color: #1565C0;
    margin-bottom: 0.5rem;
}

.weather-temp {
    font-size: 2rem;
    font-weight: bold;
    color: #0D47A1;
    margin-bottom: 0.5rem;
}

.weather-desc {
    font-size: 1.1rem;
    color: #1976D2;
    margin-bottom: 1rem;
}

.weather-details {
    display: flex;
    justify-content: space-around;
    font-size: 0.9rem;
    color: #1565C0;
}

.weather-details span {
    padding: 0.25rem 0.5rem;
    background-color: rgba(255, 255, 255, 0.7);
    border-radius: 4px;
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 文件拖拽区域 */
.file-drop-zone {
    border: 2px dashed #F3E2A9;
    border-radius: 8px;
    padding: 2rem;
    text-align: center;
    background-color: #FFFDF0;
    transition: all 0.3s ease;
    cursor: pointer;
}

.file-drop-zone:hover {
    border-color: #E6B84D;
    background-color: #FFF8DC;
}

.file-drop-zone.dragover {
    border-color: #F0C674;
    background-color: #FFF8DC;
    transform: scale(1.02);
}

.file-drop-zone .drop-text {
    color: #8B7355;
    font-size: 1.1rem;
    margin-bottom: 1rem;
}

.file-drop-zone .drop-icon {
    font-size: 3rem;
    color: #F0C674;
    margin-bottom: 1rem;
}

/* 进度条 */
.progress-bar {
    width: 100%;
    height: 8px;
    background-color: #F3E2A9;
    border-radius: 4px;
    overflow: hidden;
    margin: 1rem 0;
}

.progress-fill {
    height: 100%;
    background-color: #F0C674;
    transition: width 0.3s ease;
    border-radius: 4px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .dashboard-body {
        flex-direction: column;
    }

    .sidebar {
        width: 100%;
        height: auto;
        position: relative;
        top: 0;
    }

    .main-content {
        margin-left: 0;
    }

    .knowledge-sections {
        grid-template-columns: 1fr;
    }

    .knowledge-card {
        grid-column: span 1;
    }

    .auth-container {
        margin: 0 10px;
        padding: 1.5rem;
    }

    .form-group input {
        font-size: 16px;
        padding: 0.5rem;
    }

    .weather-details {
        flex-direction: column;
        gap: 0.5rem;
    }

    .feature-grid {
        grid-template-columns: 1fr;
    }

    .entrance-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .entrance-btn {
        padding: 1rem;
        font-size: 0.9rem;
        min-height: 70px;
        gap: 0.8rem;
    }

    .entrance-icon {
        font-size: 2rem;
        min-width: 50px;
        height: 50px;
    }

    .entrance-text {
        font-size: 1rem;
    }

    .entrance-description {
        font-size: 0.8rem;
    }

    .section-title {
        font-size: 1.5rem;
    }

    .admin-link {
        padding: 0.6rem 0.8rem;
        font-size: 0.85rem;
    }

    .admin-icon {
        margin-right: 0.6rem;
        font-size: 0.9rem;
    }

    .admin-section h4 {
        font-size: 0.8rem;
    }

    .page-title h1 {
        font-size: 28px;
        padding: 15px;
    }
}
