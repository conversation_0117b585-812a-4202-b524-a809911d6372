import requests
import json
from typing import Optional, List, Dict, Any
import os
import time
from pathlib import Path
import re
import random
from ragflow_sdk import RAGFlow
from logger_config import video_server_logger as logger
api_key = "ragflow-Y1NTkyNDBlZmZhNTExZWY5ZmNhZWUzZD"
base_url = f"http://localhost:8080"

def upload_and_parse_documents( file_paths: List[str], knowledge_base_name: str, chunk_method: str = "naive") -> None:
    """
    上传文件到指定名称的知识库，更新解析参数，然后开始解析文档。

    参数:
    - api_key: RAGFlow API密钥
    - base_url: RAGFlow API基础URL
    - file_paths: 要上传的文件路径列表
    - knowledge_base_name: 知识库名称
    - chunk_method: 解析方法
    """
    # 初始化 RAGFlow 对象
    ragflow = RAGFlow(api_key=api_key, base_url=base_url)
    file_name = os.path.basename(file_paths)
    doc_id = ""
    # 首先检查本地的 doc_ids.json 文件
    doc_ids_path = 'ragdoc/doc_ids.json'
    try:
        with open(doc_ids_path, 'r', encoding='utf-8') as f:
            doc_ids = json.load(f)       
        # 检查文件名是否在 doc_ids 中
        if file_name in doc_ids:
            doc_id = doc_ids[file_name]
            # logger.info(f"Found file '{file_name}' in local doc_ids.json with doc_id: {doc_id}")
            return doc_id
    except Exception as e:
        logger.info(f"Error reading doc_ids.json: {str(e)}")



    try:
        # 读取现有的 doc_ids.json
        with open(doc_ids_path, 'r', encoding='utf-8') as f:
            doc_ids = json.load(f)
        # 更新 doc_ids 字典
        doc_ids[file_name] = "start00000000000000000000000000000000"
        # 写回到 doc_ids.json
        with open(doc_ids_path, 'w', encoding='utf-8') as f:
            json.dump(doc_ids, f, indent=2)
        logger.info(f"Updated doc_ids.json with file '{file_name}' and doc_id: {new_doc_id}")
    except Exception as e:
        logger.info(f"Error writing to doc_ids.json: {str(e)}")


     
    # 获取知识库
    datasets = ragflow.list_datasets()
    kb_id = None
    for dataset in datasets:
        if dataset.name == knowledge_base_name:
            kb_id = dataset
            break
    if kb_id.id is None:
        logger.info(f"无法找到知识库 '{knowledge_base_name}' 的ID")
        return

    # 上传文件到知识库
    documents = []

    blob = open(file_paths, "rb").read()
    # 只添加文件名和二进制内容
    documents.append({"display_name": file_name, "blob": blob})
    if kb_id.id is None:
        dataset = ragflow.create_dataset(name=knowledge_base_name)
    else:
        dataset = kb_id
    dataset.upload_documents(documents)

    logger.info(f"文件已成功上传到知识库 '{knowledge_base_name}'")
    parser_config = get_parser_config(chunk_method)
    logger.info(f"chunk_methodchunk_methodchunk_methodchunk_methodchunk_methodchunk_methodchunk_method: {chunk_method}")
    if chunk_method == "ragvip":
        logger.info(f"chunk_method_vipchunk_method_vipchunk_method_vipchunk_method_vipchunk_method_vipchunk_method_vip:naive")
        update_message = {"parser_config": parser_config, "chunk_method": "naive"}
    else:
        update_message = {"parser_config": parser_config, "chunk_method": chunk_method} 
    logger.info(update_message)
    # 列出上传的文档并更新解析参数
    uploaded_docs = dataset.list_documents(keywords=file_name)
    time.sleep(2)
    for doc in uploaded_docs:
        if doc.name == file_name:
            logger.info(f"上传的文件为文件名: {doc.name}, 文件ID: {doc.id}")
            doc.update(update_message)  # 直接传递字典
            time.sleep(5)
            ids=doc.id
            break
    # 开始解析文档
    if ids:
        logger.info(f"ids: {ids}")
        dataset.async_parse_documents([ids])
        time.sleep(200)
        logger.info("异步解析已启动。")
        new_doc_id = ids
        try:
            # 读取现有的 doc_ids.json
            with open(doc_ids_path, 'r', encoding='utf-8') as f:
                doc_ids = json.load(f)
            # 更新 doc_ids 字典
            doc_ids[file_name] = new_doc_id
            # 写回到 doc_ids.json
            with open(doc_ids_path, 'w', encoding='utf-8') as f:
                json.dump(doc_ids, f, indent=2)
            logger.info(f"Updated doc_ids.json with file '{file_name}' and doc_id: {new_doc_id}")
        except Exception as e:
            logger.info(f"Error writing to doc_ids.json: {str(e)}")
    else:
        logger.error("upload_and_parse_documents函数上传失败")
        try:
            # 读取现有的 doc_ids.json
            with open(doc_ids_path, 'r', encoding='utf-8') as f:
                doc_ids = json.load(f)
            # 更新 doc_ids 字典
            doc_ids[file_name] = "error00000000000000000000000000000000"
            # 写回到 doc_ids.json
            with open(doc_ids_path, 'w', encoding='utf-8') as f:
                json.dump(doc_ids, f, indent=2)
            logger.info(f"Updated doc_ids.json with file '{file_name}' and doc_id: {new_doc_id}")
        except Exception as e:
            logger.info(f"Error writing to doc_ids.json: {str(e)}")
    return ids



def get_parser_config(chunk_method: str) -> Dict[str, Any]:
    """
    根据解析方法返回相应的解析配置。

    参数:
    - chunk_method: 解析方法

    返回:
    - dict: 解析配置
    """
    if chunk_method == "naive":
        return {
            "chunk_token_num": 128,
            "delimiter": "\\n!?;。；！？",
            "html4excel": False,
            "layout_recognize": False,
            "raptor": {"user_raptor": False}
        }
    
    elif chunk_method == "ragvip":
        return {
            "delimiter": "\\n!?;。；！？",
            "html4excel": True,
            "layout_recognize": "DeepDOC",
            "raptor": {"user_raptor": True}
        }
    elif chunk_method in ["qa", "manual", "paper", "book", "laws", "presentation"]:
        return {"raptor": {"user_raptor": False}}
    elif chunk_method == "knowledge_graph":
        return {
            "chunk_token_num": 128,
            "delimiter": "\\n!?;。；！？",
            "entity_types": ["organization", "person", "location", "event", "time"]
        }
    else:
        return {}  # 对于其他方法返回空配置


def delete_documents(base_url, api_key, kb_id, doc_names=None, doc_ids=None):
    """
    删除知识库中的文档

    参数:
    base_url (str): RAGFlow API的基础URL
    api_key (str): RAGFlow API密钥
    kb_id (str): 知识库ID
    doc_names (list, 可选): 要删除的文档名称列表
    doc_ids (list, 可选): 要删除的文档ID列表

    返回:
    dict: API的响应结果
    """
    # 构建API端点URL
    endpoint = f"{base_url}/document"

    # 准备请求参数
    params = {"kb_id": kb_id}
    if doc_names:
        params['doc_names'] = doc_names
    if doc_ids:
        params['doc_ids'] = doc_ids

    # 确保至少提供了一个文档参数
    if not doc_names and not doc_ids:
        raise ValueError("必须提供doc_names或doc_ids中的至少一个")

    # 准备请求头，包含API密钥
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }

    # 打印请求信息以进行调试
    logger.info(f"发送请求到: {endpoint}")
    logger.info(f"请求参数: {json.dumps(params, ensure_ascii=False)}")
    logger.info(f"请求头: {headers}")

    try:
        # 发送DELETE请求
        response = requests.delete(endpoint, json=params, headers=headers)
        
        # 打印响应状态码和内容以进行调试
        logger.info(f"响应状态码: {response.status_code}")
        logger.info(f"响应内容: {response.text}")

        # 检查响应状态
        response.raise_for_status()

        # 返回JSON响应
        result = response.json()
        if result['retcode'] != 0:
            logger.info(f"警告：API返回非零retcode: {result['retcode']}, 消息: {result['retmsg']}")
        return result
    except requests.exceptions.RequestException as e:
        logger.info(f"请求发生错误: {e}")
        return {"error": str(e)}


def sync_user_files_with_doc_ids(base_url, api_key, knowledge_base_name):
    # 读取 user_files.json
    user_files_path = 'user/user_files.json'
    with open(user_files_path, 'r', encoding='utf-8') as f:
        user_files = json.load(f)

    doc_ids_path = 'ragdoc/doc_ids.json'
    with open(doc_ids_path, 'r', encoding='utf-8') as f:
        doc_ids = json.load(f)
    all_user_files = set(file for files in user_files.values() for file in files)

    files_removed_from_doc_ids = []
    for file in list(doc_ids.keys()):
        if file not in all_user_files:
            file_path = os.path.join('ragdoc', file)
            if os.path.exists(file_path):
                try:
                    os.remove(file_path)
                    logger.info(f"已删除本地文件: {file_path}")
                    # 只有本地文件成功删除后，才从doc_ids中删除记录
                    doc_id = doc_ids[file]
                    del doc_ids[file]
                    files_removed_from_doc_ids.append(file)
                    # 只有本地文件成功删除后，才从知识库中删除
                    try:
                        delete_result = delete_documents(base_url, api_key, knowledge_base_name, doc_ids=[doc_id])
                        if 'error' in delete_result:
                            logger.info(f"从知识库删除文件 {file} 时出错: {delete_result['error']}")
                        elif delete_result.get('retcode', -1) != 0:
                            logger.info(f"从知识库删除文件 {file} 时出错: {delete_result.get('retmsg', '未知错误')}")
                        else:
                            logger.info(f"已从知识库中删除文件: {file}")
                    except Exception as e:
                        logger.info(f"调用 delete_documents 函数删除文件 {file} 时发生异常: {str(e)}")
                except Exception as e:
                    logger.info(f"删除本地文件 {file_path} 时出错: {str(e)}")
            else:
                logger.info(f"本地文件不存在: {file_path}，不从doc_ids中删除记录，也不执行知识库删除操作")

            # 可选：在每次删除操作之间添加短暂延迟
            time.sleep(0.1)

    with open(doc_ids_path, 'w', encoding='utf-8') as f:
         json.dump(doc_ids, f, indent=2)

    logger.info(f"已从 doc_ids.json 中移除并尝试删除 {len(files_removed_from_doc_ids)} 个文件")
    logger.info("doc_ids.json 已更新")
    
def get_document_ids(kb_name: str, token: str) -> Optional[dict]:
    """
    Retrieves document IDs from a specific knowledge base.

    Args:
    - kb_name: Name of the target knowledge base.
    - token: API token for authentication.
    """
    url = 'http://localhost:8080/v1/api/list_kb_docs'  # Verify this URL
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    data = {'kb_name': kb_name}

    logger.info(f"Sending request to: {url}")
    logger.info(f"Headers: {headers}")
    logger.info(f"Data: {data}")

    response = requests.post(url, headers=headers, json=data)

    logger.info(f"Response status code: {response.status_code}")
    logger.info(f"Response headers: {response.headers}")

    if response.status_code == 200:
        try:
            content = response.json()
            # logger.info("Response content:")
            # logger.info(json.dumps(content, indent=2))
            return content
        except json.JSONDecodeError:
            logger.info("Response is not JSON. Raw content:")
            logger.info(response.text[:100])
            return response.text
    else:
        logger.info(f"Error: {response.status_code}")
        logger.info(response.text[:100])
        return None


def upload_file_to_kb(file_path: str, kb_name: str, token: str, parser_id) -> Optional[dict]:
    """  
    Uploads a file to a knowledge base.  

    Args:  
    - file_path: Path to the file to upload.  
    - kb_name: Name of the target knowledge base.  
    - parser_id: ID of the chosen file parser (defaults to 'naive').  
    - token: API token for authentication.  
    
    Returns:
    - dict: Response data containing doc_id if successful, None otherwise
    """
    url = 'http://localhost:8080/v1/api/document/upload'
    
    try:
        with open(file_path, 'rb') as f:
            files = {'file': f}
            data = {'kb_name': kb_name, 'parser_id': parser_id, 'token_num': '428','run': '1'}
            headers = {'Authorization': f'Bearer {token}'}

            response = requests.post(url, files=files, data=data, headers=headers)
                     
            logger.info(f"Upload response status: {response.status_code}")
            logger.info(f"Upload response content: {response.text}")
            
            if response.status_code == 200:
                result = response.json()
                new_doc_id = result.get('data', {}).get('doc_id')

                logger.info("File uploaded successfully:", json.dumps(result, indent=2, ensure_ascii=False))
                return result
            else:
                logger.info("Failed to upload file:", response.status_code, response.text)
                return None
                
    except Exception as e:
        logger.info(f"Error during file upload: {str(e)}")
        return None


def upload_or_get_doc_id(file_to_upload: str, knowledge_base_name: str, token: str, parser_id: str) -> Optional[str]:
   
    file_name = os.path.basename(file_to_upload)
    doc_id = ""
    # 首先检查本地的 doc_ids.json 文件
    doc_ids_path = 'ragdoc/doc_ids.json'
    try:
        with open(doc_ids_path, 'r', encoding='utf-8') as f:
            doc_ids = json.load(f)
            
        # 检查文件名是否在 doc_ids 中
        if file_name in doc_ids:
            doc_id = doc_ids[file_name]
            # logger.info(f"Found file '{file_name}' in local doc_ids.json with doc_id: {doc_id}")
            return doc_id
    except Exception as e:
        logger.info(f"Error reading doc_ids.json: {str(e)}")
  
    # 首先获取知识库中的所有文档
    documents = get_document_ids(knowledge_base_name, token)
    logger.info(f"Documents response: {json.dumps(documents, indent=2, ensure_ascii=False)}")
    
    if documents and isinstance(documents, dict) and 'data' in documents:
        # 获取上传文件的文件名（不包含路径）
        file_name_without_ext = os.path.splitext(file_name)[0]       
        # 检查是否存在同名文件或类似名称的文件
        for doc in documents['data'].get('docs', []):  # 添加 .get('docs', []) 以防止键不存在
            doc_name = doc.get('doc_name', '')  # 使用 .get() 以防止键不存在
            if doc_name == file_name or doc_name.startswith(file_name_without_ext):
                logger.info(f"File '{doc_name}' already exists in the knowledge base.")
                try:
                        # 读取现有的 doc_ids.json
                        with open(doc_ids_path, 'r', encoding='utf-8') as f:
                            doc_ids = json.load(f)
                        # 更新 doc_ids 字典
                        doc_ids[doc_name] = doc.get('doc_id')
                        # 写回到 doc_ids.json
                        with open(doc_ids_path, 'w', encoding='utf-8') as f:
                            json.dump(doc_ids, f, ensure_ascii=False, indent=2)
                except Exception as e:
                        logger.info(f"Error writing to doc_ids.json: {str(e)}")    
                return doc.get('doc_id')  # 使用 .get() 以防止键不存在
                
        # 如果没有找到同名或类似名称的文件，上传新文件
        logger.info(f"File '{file_name}' not found in the knowledge base. Uploading...")
        result = upload_file_to_kb(file_to_upload, knowledge_base_name, token, parser_id)
        # 检查上传结果并获取 doc_id
        time.sleep(1)
        if result and isinstance(result, dict):
            new_doc_id = result.get('data', {}).get('doc_id')
            if new_doc_id:               
                # 将 file_name 和 new_doc_id 写入 doc_ids.json
                try:
                    # 读取现有的 doc_ids.json
                    with open(doc_ids_path, 'r', encoding='utf-8') as f:
                        doc_ids = json.load(f)

                    # 更新 doc_ids 字典
                    doc_ids[file_name] = new_doc_id

                    # 写回到 doc_ids.json
                    with open(doc_ids_path, 'w', encoding='utf-8') as f:
                        json.dump(doc_ids, f, ensure_ascii=False, indent=2)

                    logger.info(f"Updated doc_ids.json with file '{file_name}' and doc_id: {new_doc_id}")
                except Exception as e:
                    logger.info(f"Error writing to doc_ids.json: {str(e)}")
                return new_doc_id
                
        return None
    else:
        logger.info("Failed to retrieve document IDs or unexpected response format.")
        return None


def uploadvideo_or_get_doc_id(file_to_upload: str, knowledge_base_name: str, token: str, parser_id: str) -> Optional[str]:
    """
    获取或上传视频文件到知识库，并管理doc_id映射。
    
    Args:
        file_to_upload (str): 要上传的文件路径
        knowledge_base_name (str): 知识库名称
        token (str): API令牌
        parser_id (str): 解析器ID
    
    Returns:
        Optional[str]: 文档ID，如果处理失败则返回None
    """
    # 检查文件路径是否在coursevideo目录下
    base_path = r"D:\github\ragproj\coursevideo"
    if base_path not in file_to_upload:
        logger.info(f"文件路径不在coursevideo目录下: {file_to_upload}")
        return None

    # 获取文件名和config.json路径
    file_name = os.path.basename(file_to_upload)
    config_path = os.path.join(base_path, 'config.json')
    
    # 读取或创建config.json
    doc_ids = {}
    if os.path.exists(config_path):
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                doc_ids = json.load(f)
                # logger.info(f"Loaded doc_ids from config.json: {json.dumps(doc_ids, indent=2)}")
        except Exception as e:
            logger.info(f"Error reading config.json: {str(e)}")
    else:
        try:
            os.makedirs(os.path.dirname(config_path), exist_ok=True)
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(doc_ids, f, indent=2, ensure_ascii=False)
            logger.info(f"Created new config.json file at {config_path}")
        except Exception as e:
            logger.info(f"Error creating config.json: {str(e)}")
    
    # 检查文件是否在config.json中
    # logger.info(f"Checking for file: '{file_name}'")
    # logger.info(f"Available keys in doc_ids: {list(doc_ids.keys())}")
    
    if file_name in doc_ids:
        doc_id = doc_ids[file_name]
        # logger.info(f"Found file '{file_name}' in config.json with doc_id: {doc_id}")
        return doc_id
    else:
        logger.info(f"File '{file_name}' not found in config.json")
    
    # 尝试从知识库获取doc_id
    documents = get_document_ids(knowledge_base_name, token)
    logger.info(f"Documents response: {json.dumps(documents, indent=2, ensure_ascii=False)}")
    if documents and isinstance(documents, dict) and 'data' in documents:
        file_name_without_ext = os.path.splitext(file_name)[0]
        
        for doc in documents['data'].get('docs', []):
            doc_name = doc.get('doc_name', '')
            if doc_name == file_name or doc_name.startswith(file_name_without_ext):
                doc_id = doc.get('doc_id')
                if doc_id:
                    # 保存到config.json
                    doc_ids[file_name] = doc_id
                    try:
                        with open(config_path, 'w', encoding='utf-8') as f:
                            json.dump(doc_ids, f, indent=2, ensure_ascii=False)
                        logger.info(f"Saved doc_id for '{file_name}' to config.json")
                    except Exception as e:
                        logger.info(f"Error saving to config.json: {str(e)}")
                    return doc_id
    
    # 如果还没有找到doc_id，上传文件
    logger.info(f"File '{file_name}' not found in knowledge base. Uploading...")
    # result = upload_file_to_kb(file_to_upload, knowledge_base_name, token, parser_id)
    new_doc_id =upload_and_parse_documents(file_to_upload, knowledge_base_name, chunk_method=parser_id)
    time.sleep(20)
    logger.info(f"上传视频后字幕获得doc_id: {new_doc_id}")
    if new_doc_id:
        # 保存到config.json
        doc_ids[file_name] = new_doc_id
        try:
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(doc_ids, f, indent=2, ensure_ascii=False)
            logger.info(f"Saved new doc_id for '{file_name}' to config.json")
        except Exception as e:
            logger.info(f"Error saving to config.json: {str(e)}")
        return new_doc_id

    return None


def create_datasets():
    # API配置
    API_KEY = "ragflow-Y1NTkyNDBlZmZhNTExZWY5ZmNhZWUzZD"
    BASE_URL = "http://localhost:8080"
    base_path = r"D:\github\ragproj\coursevideo"
    
    # 首先读取现有的dataset_info.json文件
    output_file = os.path.join(base_path, "dataset_info.json")
    existing_datasets = {}
    if os.path.exists(output_file):
        try:
            with open(output_file, 'r', encoding='utf-8') as f:
                existing_datasets = json.load(f)
            logger.info("Loaded existing dataset information")
        except Exception as e:
            logger.info(f"Error loading existing dataset information: {str(e)}")
    
    # 获取所有文件夹名称
    folders = [f.name for f in Path(base_path).iterdir() if f.is_dir()]
    
    # 存储知识库信息的字典，初始化为现有的数据
    dataset_info = existing_datasets
    
    # 创建知识库
    headers = {
        'Content-Type': 'application/json',
        'Authorization': f'Bearer {API_KEY}'
    }
    
    for folder in folders:
        # 如果知识库已存在，跳过创建
        if folder in existing_datasets:
            logger.info(f"Dataset for {folder} already exists with ID: {existing_datasets[folder]['id']}, skipping...")
            continue
            
        # 创建知识库的请求数据
        data = {
            "name": folder,
            "description": f"Knowledge base for {folder}",
            "language": "English",
            "embedding_model": "EntropyYue/jina-embeddings-v2-base-zh",
            "permission": "me",
            "chunk_method": "naive",
            "parser_config": {
                "chunk_token_num": 64,
                "layout_recognize": "DeepDOC",
                "delimiter": "\\n!?;。；！？",
                "html4excel": True,
                "auto_keywords": 2, 
                "auto_questions": 1,
                 "raptor": {"use_raptor": True, 
                            "prompt": "Please summarize the following paragraphs. Be careful with the numbers and don't make up. The paragraphs are as follows:\n      {cluster_content}\nThe above is what you need to summarize.", 
                            "max_token": 64, 
                            "threshold": 0.4, 
                            "max_cluster": 64, 
                            "random_seed": 0},
            }
        }
        
        try:
            # 发送创建知识库的请求
            response = requests.post(
                f"{BASE_URL}/api/v1/datasets",
                headers=headers,
                json=data
            )
            logger.info(f"创建知识库请求响应: {response.json()}")
            if response.status_code == 200:
                result = response.json()
                if result["code"] == 0:  # 成功创建
                    dataset_id = result["data"]["id"]
                    dataset_info[folder] = {
                        "id": dataset_id,
                        "name": folder,
                        "create_time": result["data"]["create_date"]
                    }
                    logger.info(f"Successfully created dataset for {folder} with ID: {dataset_id}")
                else:
                    logger.info(f"Failed to create dataset for {folder}: {result['message']}")
            else:
                logger.info(f"Failed to create dataset for {folder}: HTTP {response.status_code}")
                
        except Exception as e:
            logger.info(f"Error creating dataset for {folder}: {str(e)}")
    
    # 保存更新后的知识库信息到JSON文件
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(dataset_info, f, ensure_ascii=False, indent=4)
        logger.info(f"\nDataset information saved to {output_file}")
    except Exception as e:
        logger.info(f"Error saving dataset information: {str(e)}")
    
    return dataset_info


def get_ragfiles_paths():
    logger.info("开始创建知识库...")
    create_datasets()
    logger.info("知识库创建完成")
    # 基础路径配置
    base_path = r"D:\github\ragproj\coursevideo"
    output_file = os.path.join(base_path, "ragfiles_paths.json")
    
    # 存储文件路径信息的字典
    files_info = {}
    
    # 获取所有文件夹
    folders = [f for f in Path(base_path).iterdir() if f.is_dir()]
    
    for folder in folders:
        # 检查ragfiles目录是否存在
        ragfiles_path = folder / "ragfiles"
        if not ragfiles_path.exists() or not ragfiles_path.is_dir():
            logger.info(f"No ragfiles directory found in {folder.name}")
            continue
            
        # 获取ragfiles目录下的所有文件
        files = list(ragfiles_path.glob('*.*'))  # 获取所有文件
        
        if files:  # 如果有文件
            # 将文件名列表添加到字典中
            files_info[folder.name] = [file.name for file in files]
            logger.info(f"Found {len(files)} files in {folder.name}/ragfiles")
    
    # 保存文件路径信息到JSON文件
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(files_info, f, ensure_ascii=False, indent=4)
        logger.info(f"\nFile paths information saved to {output_file}")
    except Exception as e:
        logger.info(f"Error saving file paths information: {str(e)}")
    
    return files_info


def upload_documents(dataset_id, file_path, api_key):
    """上传文档到指定数据集"""
    url = f"http://localhost:8080/api/v1/datasets/{dataset_id}/documents"
    headers = {
        'Authorization': f'Bearer {api_key}'
    }
    
    with open(file_path, 'rb') as f:
        files = {'file': f}
        response = requests.post(url, headers=headers, files=files)
    
    if response.status_code == 200:
        return response.json()
    else:
        raise Exception(f"Upload failed: {response.text}")


def main_upload_video_documents():
    # 设置基础路径
    base_path = Path("D:/github/ragproj/coursevideo")
    get_ragfiles_paths()
    logger.info("ragfiles_paths.json读取完成")
    # 读取ragfiles_paths.json
    with open(base_path / "ragfiles_paths.json", "r", encoding="utf-8") as f:
        rag_files = json.load(f)
    
    # 读取dataset_info.json
    with open(base_path / "dataset_info.json", "r", encoding="utf-8") as f:
        dataset_info = json.load(f)
    
    # 读取现有的document_ids.json（如果存在）
    upload_results = {}
    doc_ids_file = base_path / "document_ids.json"
    if doc_ids_file.exists():
        try:
            with open(doc_ids_file, "r", encoding="utf-8") as f:
                content = f.read().strip()
                if content:  # 检查文件是否为空
                    try:
                        # 尝试直接解析文件内容
                        upload_results = json.loads(content)
                        # logger.info(f"Successfully loaded document_ids.json with content: {json.dumps(upload_results, indent=2)}")
                    except json.JSONDecodeError as e:
                        logger.info(f"Error parsing document_ids.json content: {e}")
                        logger.info("Current file content:")
                        logger.info(content)
                        # 尝试修复JSON格式
                        try:
                            # 移除可能的BOM和空白字符
                            content = content.encode().decode('utf-8-sig').strip()
                            upload_results = json.loads(content)
                            logger.info("Successfully parsed content after cleanup")
                        except Exception as e2:
                            logger.info(f"Failed to parse even after cleanup: {e2}")
                            # 如果还是失败，创建新文件备份并使用空字典
                            backup_file = doc_ids_file.with_suffix('.json.bak')
                            with open(backup_file, 'w', encoding='utf-8') as bf:
                                bf.write(content)
                            logger.info(f"Original content backed up to {backup_file}")
                            upload_results = {}
                else:
                    logger.info("document_ids.json is empty, starting with empty results")
        except Exception as e:
            logger.info(f"Error reading document_ids.json: {str(e)}")
            upload_results = {}
    
    API_KEY = "ragflow-Y1NTkyNDBlZmZhNTExZWY5ZmNhZWUzZD"
    
    # 遍历知识库和文件
    for kb_name, files in rag_files.items():
        if kb_name not in dataset_info:
            logger.info(f"Warning: No dataset_id found for {kb_name}")
            continue
            
        dataset_id = dataset_info[kb_name]["id"]
        
        # 确保知识库在upload_results中有条目
        if kb_name not in upload_results:
            upload_results[kb_name] = []
        
        # 获取已上传文件的名称列表
        existing_files = {item["file_name"] for item in upload_results[kb_name]}
        # logger.info(f"\nKnowledge base: {kb_name}")
        # logger.info(f"Existing files: {existing_files}")
        # logger.info(f"Files to process: {files}")
        
        # 遍历并上传新文件
        for file_name in files:
            # 检查文件是否已经上传（使用精确匹配）
            if file_name in existing_files:
                # logger.info(f"Skipping {file_name} as it's already uploaded to {kb_name}")
                continue
            
            logger.info(f"File {file_name} not found in existing files, proceeding with upload...")
            
            file_path = base_path / kb_name / "ragfiles" / file_name
            
            try:
                logger.info(f"Uploading {file_name} to {kb_name}...")
                response = upload_documents(dataset_id, file_path, API_KEY)
                
                if response["code"] == 0 and "data" in response:
                    file_id = response["data"][0]["id"]
                    upload_results[kb_name].append({
                        "file_name": file_name,
                        "file_id": file_id
                    })
                    logger.info(f"Successfully uploaded {file_name}, file_id: {file_id}")
                    
                    # 调用Parse API开始向量化处理
                    parse_url = f"http://localhost:8080/api/v1/datasets/{dataset_id}/chunks"
                    parse_data = {
                        "document_ids": [file_id]
                    }
                    parse_response = requests.post(
                        parse_url,
                        headers={
                            'Content-Type': 'application/json',
                            'Authorization': f'Bearer {API_KEY}'
                        },
                        json=parse_data
                    )
                    if parse_response.json()["code"] == 0:
                        logger.info(f"Started parsing document: {file_name}")
                        time.sleep(200)
                    else:
                        logger.info(f"Failed to start parsing {file_name}: {parse_response.json()['message']}")
                    match_and_record_files()

                else:
                    logger.info(f"Failed to upload {file_name}: {response}")
            except Exception as e:
                logger.info(f"Error uploading {file_name}: {str(e)}")
                continue
            
            # 每次成功上传后都保存结果
            with open(doc_ids_file, "w", encoding="utf-8") as f:
                json.dump(upload_results, f, indent=4, ensure_ascii=False)
            time.sleep(10)
    logger.info(f"Upload results saved to {doc_ids_file}")


def match_and_record_files():
    """
    匹配课程视频json文件和ragfiles文件夹中的PDF文件，并记录它们的ID对应关系
    排除以_graph.json结尾的文件
    """
    base_path = Path("D:/github/ragproj/coursevideo")
    output_file = base_path / "video_pdf_matches.json"
    
    # 读取现有的document_ids.json
    try:
        with open(base_path / "document_ids.json", "r", encoding="utf-8") as f:
            doc_ids = json.load(f)
    except Exception as e:
        logger.info(f"Error reading document_ids.json: {str(e)}")
        return
    
    # 存储匹配结果
    matches = {}
    
    # 遍历所有课程目录
    for course_dir in base_path.iterdir():
        if not course_dir.is_dir():
            continue
            
        course_name = course_dir.name
        matches[course_name] = []
        
        # 获取json文件，排除以_graph.json结尾的文件
        json_files = []
        for json_file in course_dir.glob("*.json"):
            if not json_file.name.endswith("_graph.json"):
                json_files.append(json_file)
        
        # 获取ragfiles目录下的PDF文件
        ragfiles_dir = course_dir / "ragfiles"
        if not ragfiles_dir.exists():
            continue
            
        pdf_files = list(ragfiles_dir.glob("*.pdf"))
        
        # 对每个json文件进行匹配
        for json_file in json_files:
            # 提取json文件名中的数字
            json_number = re.search(r'^(\d+)', json_file.name)
            if not json_number:
                continue
                
            json_num = json_number.group(1)
            
            # 查找匹配的PDF文件
            matching_pdfs = []
            for pdf_file in pdf_files:
                pdf_number = re.search(r'Class\s+(\d+)', pdf_file.name)
                if pdf_number and pdf_number.group(1) == json_num:
                    matching_pdfs.append(pdf_file.name)
            
            # 如果找到匹配，记录ID对应关系
            if matching_pdfs:
                match_entry = {
                    "video_json": json_file.name,
                    "matching_pdfs": []
                }
                
                # 从document_ids.json中查找对应的ID
                if course_name in doc_ids:
                    for pdf_name in matching_pdfs:
                        for doc_entry in doc_ids[course_name]:
                            if doc_entry["file_name"] == pdf_name:
                                match_entry["matching_pdfs"].append({
                                    "file_name": pdf_name,
                                    "file_id": doc_entry["file_id"]
                                })
                
                if match_entry["matching_pdfs"]:  # 只添加有ID匹配的条目
                    matches[course_name].append(match_entry)
    
    # 保存匹配结果
    try:
        with open(output_file, "w", encoding="utf-8") as f:
            json.dump(matches, f, indent=4, ensure_ascii=False)
        logger.info(f"Match results saved to {output_file}")
    except Exception as e:
        logger.info(f"Error saving match results: {str(e)}")


# 使用示例
if __name__ == "__main__":
    # file_to_upload = r"D:\github\ragproj\coursevideo\en-MIT Physics II Electricity and Magnetism\28-8.02x - Lect 28 - Poynting Vector, Oscillating Charges, Polarization, Radiation.json"
    # knowledge_base_name = 'test'
    # token = 'ragflow-Y1NTkyNDBlZmZhNTExZWY5ZmNhZWUzZD'  # 替换为您的实际token
    # parser_id="naive"
    # doc_id = uploadvideo_or_get_doc_id(file_to_upload, knowledge_base_name, token, parser_id)
    # if doc_id:
    #     log_with_request_id(f"Document ID: {doc_id}")
    # else:
    #     log_with_request_id("Failed to upload or retrieve document ID.")
    #  file_paths = ["./test_data/test1.txt", "./test_data/test2.txt", "./test_data/test3.txt"]
    # main_upload_video_documents()


    # file_paths = r"D:\github\ragproj\coursevideo\en-MIT Physics II Electricity and Magnetism\7-8.02x - Lect 7 - Capacitance, Electric Field Energy - 副本.json"
    # knowledge_base_name = "test"
    # chunk_method = "ragvip"
    # upload_and_parse_documents( file_paths, knowledge_base_name, chunk_method=chunk_method)




    match_and_record_files()

    # documents = get_document_ids(knowledge_base_name, token)
    # logger.info(documents)
    # knowledge_base_name = 'test'
    # base_url = 'http://localhost:8080/v1/api'
    # api_key = 'ragflow-Y1NTkyNDBlZmZhNTExZWY5ZmNhZWUzZD'
    # delete_result = delete_documents(base_url, api_key, knowledge_base_name, doc_ids=['156dae86995911ef86df0242ac120003'])
    # logger.info(delete_result)

    # 删除所有文档
    # base_url = 'http://localhost:8080/v1/api'
    # api_key = 'ragflow-Y1NTkyNDBlZmZhNTExZWY5ZmNhZWUzZD'
    # knowledge_base_name = 'test'
