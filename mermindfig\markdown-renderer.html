<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Markdown Live Preview</title>
  
  <!-- Highlight.js for syntax highlighting - using complete bundle instead of individual components -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/highlight.js@11.6.0/styles/github.min.css">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/highlight.js@11.6.0/styles/github-dark.min.css" id="dark-highlight-theme" disabled>
  
  <!-- KaTeX for math formulas -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.16.0/dist/katex.min.css">
  
  <!-- Custom styles -->
  <style>
    :root {
      --background-color: #ffffff;
      --text-color: #333333;
      --link-color: #0366d6;
      --border-color: #ddd;
      --code-bg-color: #f6f8fa;
      --blockquote-border-color: #dfe2e5;
      --table-border-color: #dfe2e5;
      --table-header-bg: #f6f8fa;
    }

    body.dark-theme {
      --background-color: #0d1117;
      --text-color: #c9d1d9;
      --link-color: #58a6ff;
      --border-color: #30363d;
      --code-bg-color: #161b22;
      --blockquote-border-color: #30363d;
      --table-border-color: #30363d;
      --table-header-bg: #161b22;
    }

    body {
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, sans-serif;
      line-height: 1.6;
      color: var(--text-color);
      background-color: var(--background-color);
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
      transition: background-color 0.3s, color 0.3s;
    }

    .container {
      display: flex;
      flex-direction: column;
      height: 100vh;
    }

    .editor-container {
      display: flex;
      flex: 1;
      margin-bottom: 20px;
      gap: 20px;
    }

    #markdown-input {
      flex: 0.4;
      font-family: 'Fira Code', 'Monaco', 'Consolas', monospace;
      padding: 15px;
      border: 2px solid var(--border-color);
      border-radius: 8px;
      background-color: var(--code-bg-color);
      color: var(--text-color);
      resize: none;
      height: 500px;
      font-size: 14px;
      line-height: 1.5;
      transition: border-color 0.3s ease, box-shadow 0.3s ease;
      box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.05);
    }

    #markdown-input:focus {
      outline: none;
      border-color: var(--link-color);
      box-shadow: 0 0 0 3px rgba(3, 102, 214, 0.1), inset 0 2px 4px rgba(0, 0, 0, 0.05);
    }

    .preview-container {
      flex: 0.6;
      border: 2px solid var(--border-color);
      border-radius: 8px;
      overflow: auto;
      min-height: 500px;
      background: var(--background-color);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      transition: box-shadow 0.3s ease;
    }

    .preview-container:hover {
      box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
    }

    #markdown-output {
      padding: 25px;
      animation: fadeIn 0.3s ease-in-out;
    }

    @keyframes fadeIn {
      from {
        opacity: 0;
        transform: translateY(10px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    .toolbar {
      display: flex;
      gap: 10px;
      margin-bottom: 20px;
      padding: 15px;
      background: linear-gradient(135deg, var(--code-bg-color), rgba(0, 123, 255, 0.1));
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      border: 1px solid var(--border-color);
    }

    button {
      background: linear-gradient(135deg, var(--link-color), #4a90e2);
      color: white;
      border: none;
      padding: 12px 20px;
      border-radius: 6px;
      cursor: pointer;
      font-weight: 600;
      font-size: 14px;
      transition: all 0.3s ease;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      position: relative;
      overflow: hidden;
    }

    button:hover {
      transform: translateY(-3px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    }

    button:active {
      transform: translateY(-1px);
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
    }

    button::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
      transition: left 0.5s;
    }

    button:hover::before {
      left: 100%;
    }

    /* Markdown content styles */
    #markdown-output h1, 
    #markdown-output h2, 
    #markdown-output h3, 
    #markdown-output h4, 
    #markdown-output h5, 
    #markdown-output h6 {
      margin-top: 24px;
      margin-bottom: 16px;
      font-weight: 600;
      line-height: 1.25;
    }

    #markdown-output h1 {
      font-size: 2em;
      border-bottom: 1px solid var(--border-color);
      padding-bottom: 0.3em;
    }

    #markdown-output h2 {
      font-size: 1.5em;
      border-bottom: 1px solid var(--border-color);
      padding-bottom: 0.3em;
    }

    #markdown-output a {
      color: var(--link-color);
      text-decoration: none;
    }

    #markdown-output a:hover {
      text-decoration: underline;
    }

    #markdown-output blockquote {
      padding: 0 1em;
      color: #6a737d;
      border-left: 0.25em solid var(--blockquote-border-color);
      margin: 0 0 16px 0;
    }

    #markdown-output blockquote.warning {
      background-color: rgba(255, 229, 100, 0.3);
      border-left-color: #e7c000;
      color: inherit;
    }

    #markdown-output blockquote.tip {
      background-color: rgba(0, 255, 0, 0.1);
      border-left-color: #42b983;
      color: inherit;
    }

    #markdown-output pre {
      background-color: var(--code-bg-color);
      border-radius: 3px;
      padding: 16px;
      overflow: auto;
      position: relative;
    }

    #markdown-output code {
      font-family: SFMono-Regular, Consolas, "Liberation Mono", Menlo, monospace;
      background-color: var(--code-bg-color);
      border-radius: 3px;
      padding: 0.2em 0.4em;
      font-size: 85%;
    }

    #markdown-output pre code {
      background-color: transparent;
      padding: 0;
      font-size: 100%;
    }

    #markdown-output table {
      border-collapse: collapse;
      width: 100%;
      margin-bottom: 16px;
    }

    #markdown-output table th,
    #markdown-output table td {
      padding: 6px 13px;
      border: 1px solid var(--table-border-color);
    }

    #markdown-output table th {
      background-color: var(--table-header-bg);
      font-weight: 600;
    }

    #markdown-output table tr:nth-child(2n) {
      background-color: rgba(175, 184, 193, 0.2);
    }

    #markdown-output img {
      max-width: 100%;
      box-sizing: content-box;
    }

    #markdown-output ul,
    #markdown-output ol {
      padding-left: 2em;
    }

    #markdown-output .task-list-item {
      list-style-type: none;
    }

    #markdown-output .task-list-item-checkbox {
      margin-right: 0.5em;
    }

    /* Mermaid diagrams */
    .mermaid {
      text-align: center;
    }

    /* Copy button for code blocks */
    .copy-button {
      position: absolute;
      top: 5px;
      right: 5px;
      padding: 4px 8px;
      font-size: 12px;
      background-color: rgba(0, 0, 0, 0.1);
      color: var(--text-color);
      border: none;
      border-radius: 3px;
      cursor: pointer;
      opacity: 0;
      transition: opacity 0.3s;
    }

    pre:hover .copy-button {
      opacity: 1;
    }

    .toc-container {
      background-color: var(--code-bg-color);
      padding: 15px;
      border-radius: 5px;
      margin: 20px 0;
    }

    .dark-mode-toggle {
      position: fixed;
      top: 10px;
      right: 10px;
      z-index: 1000;
    }

    @media (min-width: 768px) {
      .editor-container {
        flex-direction: row;
        height: calc(100vh - 150px);
        min-height: 500px;
      }
      #markdown-input {
        margin-right: 0;
      }
      .preview-container {
        max-height: 100%;
      }
    }

    @media (max-width: 767px) {
      .editor-container {
        flex-direction: column;
      }
      #markdown-input {
        margin-bottom: 15px;
        height: 300px;
      }
      .preview-container {
        min-height: 300px;
      }
      .toolbar {
        flex-wrap: wrap;
        justify-content: center;
      }
      .header h1 {
        font-size: 2em;
      }
      .header p {
        font-size: 1em;
      }
    }

    /* Math formula styles */
    .katex-display {
      overflow-x: auto;
      overflow-y: hidden;
      padding: 0.5em 0;
      margin: 1em 0;
      text-align: center;
    }
    
    .katex-block-wrapper {
      margin: 1.5em 0;
      clear: both;
    }
    
    .katex {
      font-size: 1.1em;
    }
    
    .katex-display .katex {
      font-size: 1.21em;
      display: block;
    }
    
    .katex-error {
      color: red;
      border: 1px solid #ffcccc;
      padding: 0.2em;
      background-color: #fff8f8;
    }

    /* 通知样式 */
    .notification {
      position: fixed;
      top: 20px;
      right: 20px;
      padding: 12px 20px;
      background-color: var(--link-color);
      color: white;
      border-radius: 4px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      z-index: 1000;
      opacity: 0;
      transform: translateY(-20px);
      transition: all 0.3s ease;
    }
    
    .notification.show {
      opacity: 1;
      transform: translateY(0);
    }
    
    /* PDF生成加载指示器 */
    .pdf-loading {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.5);
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      z-index: 2000;
      color: white;
      font-size: 18px;
    }
    
    .pdf-loading .spinner {
      border: 5px solid rgba(255, 255, 255, 0.3);
      border-radius: 50%;
      border-top: 5px solid white;
      width: 50px;
      height: 50px;
      animation: spin 1s linear infinite;
      margin-bottom: 20px;
    }
    
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    
    /* Button specific styles */
    #theme-toggle {
      background: linear-gradient(135deg, #6c5ce7, #a29bfe);
    }

    #example-button {
      background: linear-gradient(135deg, #00b894, #00cec9);
    }

    #export-html-button {
      background: linear-gradient(135deg, #2e7d32, #4caf50);
    }

    #print-button {
      background: linear-gradient(135deg, #1976d2, #2196f3);
    }
    
    /* PDF文件名对话框 */
    .pdf-filename-dialog {
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background-color: var(--background-color);
      border: 1px solid var(--border-color);
      border-radius: 8px;
      padding: 20px;
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
      z-index: 2001;
      width: 350px;
      max-width: 90%;
    }
    
    .pdf-filename-dialog h3 {
      margin-top: 0;
      color: var(--text-color);
    }
    
    .pdf-filename-dialog .form-group {
      margin-bottom: 15px;
    }
    
    .pdf-filename-dialog label {
      display: block;
      margin-bottom: 5px;
      color: var(--text-color);
    }
    
    .pdf-filename-dialog input {
      width: 100%;
      padding: 8px;
      border: 1px solid var(--border-color);
      border-radius: 4px;
      background-color: var(--code-bg-color);
      color: var(--text-color);
    }
    
    .pdf-filename-dialog .dialog-buttons {
      display: flex;
      justify-content: flex-end;
      gap: 10px;
      margin-top: 15px;
    }
    
    .pdf-filename-dialog .checkbox-group {
      margin-top: 10px;
    }
    
    .pdf-filename-dialog .checkbox-label {
      display: flex;
      align-items: center;
      gap: 5px;
      cursor: pointer;
    }
    
    .pdf-overlay {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.5);
      z-index: 2000;
    }

    /* Header styles */
    .header {
      text-align: center;
      margin-bottom: 30px;
      padding: 20px 0;
      border-bottom: 2px solid var(--border-color);
    }

    .header h1 {
      margin: 0;
      color: var(--text-color);
      font-size: 2.5em;
      font-weight: 700;
      background: linear-gradient(135deg, var(--link-color), #4a90e2);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    .header p {
      margin: 10px 0 0 0;
      color: var(--text-color);
      opacity: 0.7;
      font-size: 1.1em;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>Markdown Live Preview</h1>
      <p>Real-time markdown rendering with math formulas, diagrams, and more</p>
    </div>

    <div class="toolbar">
      <button id="theme-toggle">🌓 Toggle Theme</button>
      <button id="example-button">📝 Load Example</button>
      <button id="export-html-button">💾 Export HTML</button>
      <button id="print-button">🖨️ Print</button>
    </div>
    
    <div class="editor-container">
      <textarea id="markdown-input" placeholder="Start typing your markdown here...

Try these features:
• **Bold** and *italic* text
• # Headers and ## Subheaders
• [Links](https://example.com)
• `inline code` and code blocks
• Math formulas: $E = mc^2$ or $$\sum_{i=1}^n x_i$$
• Tables, lists, and more!

The preview updates automatically as you type."></textarea>
      <div class="preview-container">
        <div id="markdown-output"></div>
      </div>
    </div>
  </div>
  
  <!-- 添加通知元素 -->
  <div id="notification" class="notification"></div>

  <!-- Core libraries - Using full browser bundles instead of individual module files -->
  <script src="https://cdn.jsdelivr.net/npm/markdown-it@13.0.1/dist/markdown-it.min.js"></script>
  <!-- Make sure highlight.js is properly loaded with common languages -->
  <script src="https://cdn.jsdelivr.net/npm/highlight.js@11.6.0/highlight.min.js"></script>
  
  <!-- KaTeX for math formulas -->
  <script src="https://cdn.jsdelivr.net/npm/katex@0.16.0/dist/katex.min.js"></script>
  
  <!-- Mermaid for diagrams -->
  <script src="https://cdn.jsdelivr.net/npm/mermaid@11.6.0/dist/mermaid.min.js"></script>
  
  <!-- html2pdf.js for PDF generation -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.10.1/html2pdf.bundle.min.js"></script>
  
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // Elements
      const markdownInput = document.getElementById('markdown-input');
      const markdownOutput = document.getElementById('markdown-output');
      const themeToggle = document.getElementById('theme-toggle');
      const exampleButton = document.getElementById('example-button');
      const exportHtmlButton = document.getElementById('export-html-button');
      const printButton = document.getElementById('print-button');
      const darkHighlightTheme = document.getElementById('dark-highlight-theme');
      
      // Ensure highlight.js is properly initialized
      if (typeof hljs === 'undefined') {
        window.hljs = window.hljs || {};
        window.hljs.highlightElement = function(element) {
          console.warn('Highlight.js not properly loaded, using fallback');
        };
        window.hljs.getLanguage = function(lang) {
          return lang ? true : false; // Basic fallback
        };
        window.hljs.highlight = function(text, opts) {
          return { value: text };
        };
      }
      
      // Initialize Mermaid
      mermaid.initialize({
        startOnLoad: false,
        theme: document.body.classList.contains('dark-theme') ? 'dark' : 'default',
        securityLevel: 'loose'
      });
      
      // Helper function to safely use plugins
      function safeUsePlugin(md, plugin, ...args) {
        try {
          if (typeof plugin === 'function') {
            md.use(plugin, ...args);
          }
        } catch (err) {
          console.warn(`Failed to load plugin:`, err);
        }
      }
      
      // Initialize Markdown-it
      const md = window.markdownit({
        html: true,
        breaks: false,
        linkify: true,
        xhtmlOut: true,
        typographer: true,
        highlight: function(str, lang) {
          // Safely handle highlight.js
          try {
            // Check if language exists and hljs is available
            if (lang && hljs && hljs.getLanguage && hljs.getLanguage(lang)) {
              try {
                // For highlight.js v11+
                return `<pre class="hljs"><code class="hljs language-${lang}">${hljs.highlight(str, {language: lang, ignoreIllegals: true}).value}</code><button class="copy-button">Copy</button></pre>`;
              } catch (err) {
                console.error('Highlight.js error:', err);
              }
            }
          } catch (e) {
            console.warn('Error with syntax highlighting:', e);
          }
          
          // Fallback rendering without syntax highlighting
          return `<pre class="hljs"><code class="hljs">${md.utils.escapeHtml(str)}</code><button class="copy-button">Copy</button></pre>`;
        }
      });
      
      // Custom table rule to handle tables without preceding blank lines
      md.use(function(md) {
        // Get original parser function without triggering recursion
        const tableRule = md.block.ruler.__rules__.find(rule => rule.name === 'table');
        const originalFn = tableRule ? tableRule.fn : null;
        
        if (originalFn) {
          // Replace with a safer implementation
          md.block.ruler.at('table', function tableWithoutNewlines(state, startLine, endLine, silent) {
            // Direct implementation for tables without blank lines
            const startPos = state.bMarks[startLine] + state.tShift[startLine];
            const endPos = state.eMarks[startLine];
            const lineText = state.src.slice(startPos, endPos);
            
            // Quick checks to see if this looks like a table
            if (lineText.indexOf('|') === -1) {
              return false;
            }
            
            // Check if there's a header row and separator row
            if (startLine + 1 >= endLine) {
              return false;
            }
            
            // Check the separator row (second line)
            const sepLineStart = state.bMarks[startLine + 1] + state.tShift[startLine + 1];
            const sepLineEnd = state.eMarks[startLine + 1];
            const sepLine = state.src.slice(sepLineStart, sepLineEnd);
            
            // Make sure it has | and - characters (table separator)
            if (sepLine.indexOf('|') === -1 || sepLine.indexOf('-') === -1) {
              return false;
            }
            
            // Let's validate this is actually a table separator row
            const sepParts = sepLine.split('|');
            let validSeparator = true;
            
            for (let i = 0; i < sepParts.length; i++) {
              const trimmed = sepParts[i].trim();
              // Each cell in separator must contain at least one dash
              if (trimmed.length > 0 && !/-+/.test(trimmed)) {
                validSeparator = false;
                break;
              }
            }
            
            if (!validSeparator) {
              return false;
            }
            
            // At this point we're confident this is a table
            if (silent) {
              return true;
            }
            
            // Process the table header
            const headerParts = lineText.split('|');
            const columnCount = headerParts.length;
            
            // Create table open token
            let token = state.push('table_open', 'table', 1);
            token.map = [startLine, 0]; // Will be updated at table close
            
            // Create thead open token
            token = state.push('thead_open', 'thead', 1);
            token.map = [startLine, startLine + 1];
            
            // Create tr open token
            token = state.push('tr_open', 'tr', 1);
            token.map = [startLine, startLine + 1];
            
            // Process header cells
            for (let i = 0; i < headerParts.length; i++) {
              let cellContent = headerParts[i].trim();
              if (cellContent.length > 0 || (i > 0 && i < headerParts.length - 1)) {
                token = state.push('th_open', 'th', 1);
                
                // Determine alignment from separator row
                if (i < sepParts.length) {
                  const sepCell = sepParts[i].trim();
                  let align = '';
                  
                  if (sepCell.startsWith(':') && sepCell.endsWith(':')) {
                    align = 'center';
                  } else if (sepCell.startsWith(':')) {
                    align = 'left';
                  } else if (sepCell.endsWith(':')) {
                    align = 'right';
                  }
                  
                  if (align) {
                    token.attrs = [['style', `text-align:${align}`]];
                  }
                }
                
                // Process cell content with inline rules
                token = state.push('inline', '', 0);
                token.content = cellContent;
                token.map = [startLine, startLine + 1];
                token.children = [];
                
                token = state.push('th_close', 'th', -1);
              }
            }
            
            token = state.push('tr_close', 'tr', -1);
            token = state.push('thead_close', 'thead', -1);
            
            // Create tbody
            token = state.push('tbody_open', 'tbody', 1);
            token.map = [startLine + 2, 0]; // Updated at tbody close
            
            // Process body rows
            let rowLine = startLine + 2;
            let tableOpen = true;
            
            while (rowLine < endLine) {
              const rowStart = state.bMarks[rowLine] + state.tShift[rowLine];
              const rowEnd = state.eMarks[rowLine];
              const rowText = state.src.slice(rowStart, rowEnd);
              
              // Exit if line doesn't look like a table row (no pipe)
              if (rowText.indexOf('|') === -1) {
                break;
              }
              
              // Create row
              token = state.push('tr_open', 'tr', 1);
              token.map = [rowLine, rowLine + 1];
              
              const rowParts = rowText.split('|');
              
              // Process cells
              for (let i = 0; i < rowParts.length; i++) {
                let cellContent = rowParts[i].trim();
                if (cellContent.length > 0 || (i > 0 && i < rowParts.length - 1)) {
                  token = state.push('td_open', 'td', 1);
                  
                  // Apply alignment from header
                  if (i < sepParts.length) {
                    const sepCell = sepParts[i].trim();
                    let align = '';
                    
                    if (sepCell.startsWith(':') && sepCell.endsWith(':')) {
                      align = 'center';
                    } else if (sepCell.startsWith(':')) {
                      align = 'left';
                    } else if (sepCell.endsWith(':')) {
                      align = 'right';
                    }
                    
                    if (align) {
                      token.attrs = [['style', `text-align:${align}`]];
                    }
                  }
                  
                  // Process cell content
                  token = state.push('inline', '', 0);
                  token.content = cellContent;
                  token.map = [rowLine, rowLine + 1];
                  token.children = [];
                  
                  token = state.push('td_close', 'td', -1);
                }
              }
              
              token = state.push('tr_close', 'tr', -1);
              rowLine++;
            }
            
            token = state.push('tbody_close', 'tbody', -1);
            token = state.push('table_close', 'table', -1);
            
            // Update table mapping to include all rows - safely find the tokens to update
            // Find the table_open and tbody_open tokens and update their maps
            for (let i = state.tokens.length - 1; i >= 0; i--) {
              if (state.tokens[i].type === 'table_open' && state.tokens[i].map) {
                state.tokens[i].map[1] = rowLine;
              }
              if (state.tokens[i].type === 'tbody_open' && state.tokens[i].map) {
                state.tokens[i].map[1] = rowLine;
              }
              // Only look back at the most recent tokens for this table
              if (i < state.tokens.length - 30) break;
            }
            
            state.line = rowLine;
            return true;
          }, { alt: ['paragraph', 'reference'] });
        }
      });
      
      // Add basic extensions - these are implemented directly rather than using external plugins
      
      // Sub - Subscript
      md.use(function(md) {
        md.inline.ruler.after('emphasis', 'sub', function(state, silent) {
          if (silent) return false;
          const start = state.pos;
          const max = state.posMax;
          
          if (state.src.charCodeAt(start) !== 0x7E/* ~ */) return false;
          if (start + 2 >= max) return false;
          if (state.src.charCodeAt(start + 1) === 0x7E/* ~ */) return false;
          
          state.pos = start + 1;
          let found = false;
          
          while (state.pos < max) {
            if (state.src.charCodeAt(state.pos) === 0x7E/* ~ */) {
              found = true;
              break;
            }
            state.md.inline.skipToken(state);
          }
          
          if (!found || start + 1 === state.pos) {
            state.pos = start;
            return false;
          }
          
          const content = state.src.slice(start + 1, state.pos);
          let token = state.push('sub_open', 'sub', 1);
          token.markup = '~';
          
          token = state.push('text', '', 0);
          token.content = content;
          
          token = state.push('sub_close', 'sub', -1);
          token.markup = '~';
          
          state.pos++;
          return true;
        });
      });
      
      // Sup - Superscript
      md.use(function(md) {
        md.inline.ruler.after('emphasis', 'sup', function(state, silent) {
          if (silent) return false;
          const start = state.pos;
          const max = state.posMax;
          
          if (state.src.charCodeAt(start) !== 0x5E/* ^ */) return false;
          if (start + 2 >= max) return false;
          
          state.pos = start + 1;
          let found = false;
          
          while (state.pos < max) {
            if (state.src.charCodeAt(state.pos) === 0x5E/* ^ */) {
              found = true;
              break;
            }
            state.md.inline.skipToken(state);
          }
          
          if (!found || start + 1 === state.pos) {
            state.pos = start;
            return false;
          }
          
          const content = state.src.slice(start + 1, state.pos);
          let token = state.push('sup_open', 'sup', 1);
          token.markup = '^';
          
          token = state.push('text', '', 0);
          token.content = content;
          
          token = state.push('sup_close', 'sup', -1);
          token.markup = '^';
          
          state.pos++;
          return true;
        });
      });
      
      // Task lists
      md.use(function(md) {
        md.renderer.rules.list_item_open = function(tokens, idx) {
          if (tokens[idx + 2] && 
              tokens[idx + 2].type === 'paragraph_open' && 
              tokens[idx + 3] && 
              tokens[idx + 3].type === 'inline' && 
              tokens[idx + 3].content.startsWith('[ ] ')) {
            tokens[idx + 3].content = tokens[idx + 3].content.substring(4);
            return '<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox" disabled>';
          }
          if (tokens[idx + 2] && 
              tokens[idx + 2].type === 'paragraph_open' && 
              tokens[idx + 3] && 
              tokens[idx + 3].type === 'inline' && 
              tokens[idx + 3].content.startsWith('[x] ')) {
            tokens[idx + 3].content = tokens[idx + 3].content.substring(4);
            return '<li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox" checked disabled>';
          }
          return '<li>';
        };
      });
      
      // Mark - Highlighted text
      md.use(function(md) {
        md.inline.ruler.before('emphasis', 'mark', function(state, silent) {
          if (silent) return false;
          const start = state.pos;
          const marker = state.src.charCodeAt(start);
          
          if (marker !== 0x3D/* = */) return false;
          if (state.src.charCodeAt(start + 1) !== 0x3D/* = */) return false;
          
          const pos = state.pos + 2;
          const max = state.posMax;
          
          // Search for end "=="
          let found = false;
          for (let i = pos; i < max - 1; i++) {
            if (state.src.charCodeAt(i) === 0x3D/* = */ && 
                state.src.charCodeAt(i + 1) === 0x3D/* = */) {
              found = true;
              state.pos = i + 2;
              break;
            }
          }
          
          if (!found) {
            state.pos = start;
            return false;
          }
          
          const content = state.src.slice(pos, state.pos - 2);
          
          // Don't allow unescaped spaces/newlines inside
          if (content.match(/(^|[^\\])(\\\\)*\s/)) {
            state.pos = start;
            return false;
          }
          
          state.posMax = state.pos;
          state.pos = start + 2;
          
          // Push mark token
          let token = state.push('mark_open', 'mark', 1);
          token.markup = '==';
          
          // Parse content inside
          state.md.inline.tokenize(state);
          
          token = state.push('mark_close', 'mark', -1);
          token.markup = '==';
          
          state.pos = state.posMax;
          state.posMax = max;
          
          return true;
        });
      });
      
      // Container for warnings and tips
      md.use(function(md) {
        function validateContainer(params, type) {
          return params.trim().toLowerCase() === type;
        }
        
        function renderContainer(tokens, idx, type, className) {
          if (tokens[idx].nesting === 1) {
            return `<blockquote class="${className}">\n`;
          } else {
            return '</blockquote>\n';
          }
        }
        
        md.block.ruler.before('fence', 'container_warning', function(state, startLine, endLine, silent) {
          const start = state.bMarks[startLine] + state.tShift[startLine];
          const max = state.eMarks[startLine];
          
          if (max - start < 5) return false; // :::\s
          if (state.src.charCodeAt(start) !== 0x3A/* : */) return false;
          if (state.src.charCodeAt(start + 1) !== 0x3A/* : */) return false;
          if (state.src.charCodeAt(start + 2) !== 0x3A/* : */) return false;
          
          if (silent) return true;
          
          const params = state.src.slice(start + 3, max).trim();
          const containerType = params.toLowerCase();
          
          let nextLine = startLine;
          let autoClosed = false;
          
          // Search for the end of the block
          for (;;) {
            nextLine++;
            if (nextLine >= endLine) break;
            
            const start = state.bMarks[nextLine] + state.tShift[nextLine];
            const max = state.eMarks[nextLine];
            
            if (max - start < 3) continue;
            
            if (state.src.charCodeAt(start) !== 0x3A/* : */) continue;
            if (state.src.charCodeAt(start + 1) !== 0x3A/* : */) continue;
            if (state.src.charCodeAt(start + 2) !== 0x3A/* : */) continue;
            
            // Found the closing marker
            autoClosed = true;
            break;
          }
          
          // Generate tokens
          let token = state.push('container_' + containerType + '_open', 'div', 1);
          token.markup = ':::';
          token.block = true;
          token.info = params;
          token.map = [startLine, nextLine];
          
          state.md.block.tokenize(state, startLine + 1, autoClosed ? nextLine : endLine);
          
          token = state.push('container_' + containerType + '_close', 'div', -1);
          token.markup = ':::';
          token.block = true;
          
          state.line = autoClosed ? nextLine + 1 : nextLine;
          return true;
        });
        
        // Add renderer rules
        md.renderer.rules['container_warning_open'] = function(tokens, idx) {
          return renderContainer(tokens, idx, 'warning', 'warning');
        };
        md.renderer.rules['container_warning_close'] = function(tokens, idx) {
          return renderContainer(tokens, idx, 'warning', 'warning');
        };
        
        md.renderer.rules['container_tips_open'] = function(tokens, idx) {
          return renderContainer(tokens, idx, 'tips', 'tip');
        };
        md.renderer.rules['container_tips_close'] = function(tokens, idx) {
          return renderContainer(tokens, idx, 'tips', 'tip');
        };
      });
      
      // Custom KaTeX rendering
      md.use(function(md) {
        // Define delimiters
        const inlineDelimiter = '$';
        const blockDelimiter = '$$';
        
        // Helper to check if a string contains Chinese characters
        function containsChinese(str) {
          return /[\u4e00-\u9fff]/.test(str);
        }
        
        // Helper to auto-wrap Chinese characters in \text{} for KaTeX
        function wrapChineseWithText(content) {
          // Replace any Chinese character sequences with \text{...}
          return content.replace(/([\u4e00-\u9fff，：；？！（）]+)/g, '\\text{$1}');
        }
        
        // New helper function to find $$ with leading spaces
        function findBlockDelimWithSpaces(line) {
          // Find $$ position, accounting for leading spaces
          const trimmedPos = line.trim().indexOf(blockDelimiter);
          if (trimmedPos === 0) {
            // The $$ is at the start of the trimmed line, find its actual position
            return line.indexOf(blockDelimiter);
          }
          return -1;
        }
        
        // Inline math rule
        function mathInline(state, silent) {
          // Skip if not starting with $
          if (state.src[state.pos] !== inlineDelimiter) {
            return false;
          }
          
          // Don't parse $ as math if it's part of a string like $100
          const prevChar = state.pos > 0 ? state.src[state.pos - 1] : null;
          if (prevChar && /[0-9a-zA-Z]/.test(prevChar)) {
            return false;
          }
          
          // Look for the closing $
          let pos = state.pos + 1;
          let found = false;
          
          while (pos < state.posMax) {
            if (state.src[pos] === inlineDelimiter) {
              // Don't count $ if it's preceded by a backslash (escape)
              if (state.src[pos - 1] !== '\\') {
                // Make sure this isn't part of $$
                if (state.src[pos + 1] !== inlineDelimiter) {
                  found = true;
                  break;
                }
              }
            }
            pos++;
          }
          
          // No closing delimiter found
          if (!found) {
            if (!silent) {
              state.pending += inlineDelimiter;
            }
            state.pos += 1;
            return true;
          }
          
          // Don't allow empty math expressions
          if (pos === state.pos + 1) {
            if (!silent) {
              state.pending += inlineDelimiter + inlineDelimiter;
            }
            state.pos += 2;
            return true;
          }
          
          if (!silent) {
            const mathContent = state.src.slice(state.pos + 1, pos);
            
            const token = state.push('math_inline', 'math', 0);
            token.content = mathContent;
            token.markup = inlineDelimiter;
          }
          
          state.pos = pos + 1;
          return true;
        }
        
        // Block math rule - simplified
        function mathBlock(state, startLine, endLine, silent) {
          let pos = state.bMarks[startLine] + state.tShift[startLine];
          let max = state.eMarks[startLine];

          // Check if line starts with $$ (allowing for spaces handled by tShift)
          if (state.src.slice(pos, pos + 2) !== blockDelimiter) {
            return false;
          }

          if (silent) {
            return true;
          }

          // Search for the end $$ on subsequent lines
          let nextLine = startLine;
          let found = false;
          let content = '';

          // Check if the formula is on a single line: $$ formula $$
          const singleLineMatch = state.src.slice(pos + 2, max).match(/(.*?)\$\$\s*$/);
          if (singleLineMatch) {
            content = singleLineMatch[1];
            found = true;
            nextLine = startLine; // Formula ends on the same line
          } else {
            // Multi-line formula
            content = state.src.slice(pos + 2, max); // Content from the first line
            for (nextLine = startLine + 1; nextLine < endLine; nextLine++) {
              if (state.tShift[nextLine] < state.blkIndent) { break; } // Line is less indented, so it's not part of the block
              
              let lineStart = state.bMarks[nextLine] + state.tShift[nextLine];
              let lineMax = state.eMarks[nextLine];
              let lineText = state.src.slice(lineStart, lineMax);

              if (lineText.endsWith(blockDelimiter)) {
                content += '\n' + lineText.slice(0, -2); // Add content before $$, removing $$ itself
                found = true;
                break;
              }
              content += '\n' + lineText;
            }
          }

          if (!found) {
            return false; // No closing delimiter found
          }

          state.line = nextLine + 1;

          const token = state.push('math_block', 'math', 0);
          token.block = true;
          token.content = content.trim();
          token.markup = blockDelimiter;
          token.map = [startLine, state.line];

          return true;
        }
        
        // Register our rules
        md.inline.ruler.after('escape', 'math_inline', mathInline);
        md.block.ruler.before('fence', 'math_block', mathBlock);
        
        // Render inline math
        md.renderer.rules.math_inline = function(tokens, idx) {
          try {
            // Process content to handle Chinese characters
            let content = tokens[idx].content;
            if (containsChinese(content)) {
              content = wrapChineseWithText(content);
            }
            
            return katex.renderToString(content, { 
              displayMode: false, 
              throwOnError: false,
              strict: false,
              trust: true,
              macros: {
                "\\RR": "\\mathbb{R}"
              }
            });
          } catch (err) {
            console.error("KaTeX error (inline):", err);
            return `<span class="katex-error" title="${err}">${md.utils.escapeHtml(tokens[idx].content)}</span>`;
          }
        };
        
        // Render block math
        md.renderer.rules.math_block = function(tokens, idx) {
          try {
            // Process content to handle Chinese characters
            let content = tokens[idx].content;
            if (containsChinese(content)) {
              content = wrapChineseWithText(content);
            }
            
            // Always use displayMode: true for block math
            return `<div class="katex-block-wrapper"><div class="katex-display">${katex.renderToString(content, { 
              displayMode: true, 
              throwOnError: false,
              strict: false,
              trust: true,
              macros: {
                "\\RR": "\\mathbb{R}"
              }
            })}</div></div>`;
          } catch (err) {
            console.error("KaTeX error (block):", err, tokens[idx].content);
            return `<div class="katex-display katex-error" title="${err}">${md.utils.escapeHtml(tokens[idx].content)}</div>`;
          }
        };
      });
      
      // Custom plugin for Table of Contents (TOC)
      md.use(function(md) {
        const tocRegexp = /^\[\[toc\]\]/im;
        
        function generateToc(tokens, idx) {
          let tocHTML = '<div class="toc-container"><ul class="toc-list">';
          const headings = [];
          
          // Find all headings
          for (let i = 0; i < tokens.length; i++) {
            if (tokens[i].type === 'heading_open' && tokens[i].tag.match(/^h[2-3]$/)) {
              const level = parseInt(tokens[i].tag.substring(1)) - 1;
              const content = tokens[i + 1].content;
              const id = content.toLowerCase().replace(/[^\w]+/g, '-');
              
              tokens[i].attrs = tokens[i].attrs || [];
              tokens[i].attrs.push(['id', id]);
              
              headings.push({
                level: level,
                content: content,
                id: id
              });
            }
          }
          
          // Generate TOC HTML
          let lastLevel = 0;
          
          headings.forEach(heading => {
            if (heading.level > lastLevel) {
              tocHTML += '<ul class="toc-sublist">';
            } else if (heading.level < lastLevel) {
              tocHTML += '</ul>';
            }
            
            tocHTML += `<li class="toc-item toc-level-${heading.level}">
              <a class="toc-link" href="#${heading.id}">${heading.content}</a>
            </li>`;
            
            lastLevel = heading.level;
          });
          
          // Close any open lists
          while (lastLevel > 0) {
            tocHTML += '</ul>';
            lastLevel--;
          }
          
          tocHTML += '</ul></div>';
          return tocHTML;
        }
        
        // Define renderer rule for [[toc]] marker
        md.inline.ruler.before('emphasis', 'toc', function(state, silent) {
          if (silent) return false;
          
          const start = state.pos;
          const max = state.posMax;
          
          // Check for [[toc]] marker
          if (max - start < 7) return false; // Minimum "[[toc]]"
          
          if (state.src.slice(start, start + 7).toLowerCase() !== '[[toc]]') {
            return false;
          }
          
          // Token for TOC
          const token = state.push('toc', '', 0);
          
          state.pos = start + 7;
          return true;
        });
        
        md.renderer.rules.toc = function(tokens, idx) {
          return generateToc(tokens, idx);
        };
      });
      
      // Custom plugin for Mermaid diagrams
      md.use(function(md) {
        const defaultRender = md.renderer.rules.fence.bind(md.renderer.rules);
        md.renderer.rules.fence = function(tokens, idx, options, env, self) {
          const token = tokens[idx];
          const code = token.content.trim();
          if (token.info === 'mermaid') {
            return `<div class="mermaid">${code}</div>`;
          }
          return defaultRender(tokens, idx, options, env, self);
        };
      });
      
      // Render markdown function
      function renderMarkdown() {
        let markdown = markdownInput.value;

        // Pre-process block math formulas to ensure they have proper spacing
        markdown = preprocessBlockMath(markdown);

        markdownOutput.innerHTML = md.render(markdown);

        // Process mermaid diagrams
        mermaid.init(undefined, document.querySelectorAll('.mermaid'));

        // Add event listeners to copy buttons
        document.querySelectorAll('.copy-button').forEach(button => {
          button.addEventListener('click', function() {
            const codeBlock = this.parentNode.querySelector('code');
            navigator.clipboard.writeText(codeBlock.textContent).then(() => {
              const originalText = this.textContent;
              this.textContent = 'Copied!';
              setTimeout(() => {
                this.textContent = originalText;
              }, 2000);
            }).catch(err => {
              console.error('Could not copy text: ', err);
            });
          });
        });
      }
      
      // Pre-process block math formulas to ensure they have proper spacing
      function preprocessBlockMath(markdown) {
        // 逐个匹配 $$...$$，每个都前后加空行，避免合并
        return markdown.replace(/(^|[^\S\r\n])(\$\$[\s\S]*?\$\$)(?=[^\S\r\n]|$)/gm, function(match, before, mathBlock) {
          let result = '';
          // 保证 mathBlock 前后有两个换行
          if (!before.endsWith('\n\n')) result += '\n\n';
          result += mathBlock;
          result += '\n\n';
          return result;
        });
      }
      
      // Toggle dark mode
      function toggleDarkMode() {
        document.body.classList.toggle('dark-theme');
        const isDarkMode = document.body.classList.contains('dark-theme');
        
        // Update highlight.js theme
        darkHighlightTheme.disabled = !isDarkMode;
        
        // Update mermaid theme
        mermaid.initialize({
          theme: isDarkMode ? 'dark' : 'default'
        });
        
        // Re-render to update diagrams with the new theme
        renderMarkdown();
      }
      
      // Example markdown
      function loadExample() {
        markdownInput.value = `# 🚀 Markdown Live Preview Demo

[[toc]]

## ✨ Text Formatting

This is **bold text**, *italic text*, and ~~strikethrough~~.

You can also use ==highlighted text== and combine **_bold italic_** formatting.

## 🔗 Links and References

Visit [GitHub](https://github.com/) or check out [Google](https://google.com).

## 📝 Lists and Tasks

### Unordered List
- 🎯 Important item
- 📚 Learning resources
  - 📖 Documentation
  - 🎥 Video tutorials
  - 💡 Examples
- ✅ Completed items

### Task List
- [x] ✅ Set up the project
- [x] ✅ Write documentation
- [ ] 🔄 Add more features
- [ ] 🚀 Deploy to production

## 💻 Code Examples

Inline code: \`const greeting = "Hello World!"\`

### JavaScript
\`\`\`javascript
// Modern JavaScript example
const fetchData = async (url) => {
  try {
    const response = await fetch(url);
    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error:', error);
  }
};
\`\`\`

### Python
\`\`\`python
# Python data analysis example
import pandas as pd
import numpy as np

def analyze_data(df):
    """Analyze dataset and return summary statistics."""
    return {
        'mean': df.mean(),
        'std': df.std(),
        'count': len(df)
    }
\`\`\`

## 📊 Tables

| Feature | Status | Priority | Notes |
|---------|--------|----------|-------|
| Live Preview | ✅ Complete | High | Real-time rendering |
| Math Support | ✅ Complete | High | KaTeX integration |
| Diagrams | ✅ Complete | Medium | Mermaid support |
| Export | ✅ Complete | Low | HTML/Print |

## 🧮 Mathematical Formulas

### Inline Math
The famous equation $E = mc^2$ shows mass-energy equivalence.

The quadratic formula is $x = \\frac{-b \\pm \\sqrt{b^2-4ac}}{2a}$.

### Block Math
$$
\\int_{-\\infty}^{\\infty} e^{-x^2} dx = \\sqrt{\\pi}
$$

$$
\\sum_{n=1}^{\\infty} \\frac{1}{n^2} = \\frac{\\pi^2}{6}
$$

Complex formula with Chinese text:
$$
\\text{综合评分} = \\alpha \\cdot \\text{准确率} + \\beta \\cdot \\frac{1}{\\text{推理时间}} + \\gamma \\cdot \\frac{1}{\\text{资源消耗}}
$$

## 📈 Diagrams

### Flowchart
\`\`\`mermaid
graph TD
    A[📝 Write Markdown] --> B{🔍 Valid Syntax?}
    B -->|✅ Yes| C[🎨 Render Preview]
    B -->|❌ No| D[⚠️ Show Error]
    C --> E[👀 Review Output]
    D --> A
    E --> F{😊 Satisfied?}
    F -->|✅ Yes| G[💾 Export/Save]
    F -->|❌ No| A
\`\`\`

### Sequence Diagram
\`\`\`mermaid
sequenceDiagram
    participant U as 👤 User
    participant E as 📝 Editor
    participant P as 🎨 Preview
    participant R as ⚙️ Renderer

    U->>E: Type markdown
    E->>P: Input change event
    P->>R: Process markdown
    R->>P: Return HTML
    P->>U: Display result

    Note over U,R: Live preview updates automatically
\`\`\`

## 💡 Special Blocks

::: warning
⚠️ **Warning**: This is a warning block for important notices.
:::

::: tips
💡 **Tip**: Use the toolbar buttons to quickly access common functions!
:::

## 🔬 Advanced Features

### Subscript and Superscript
- Chemical formula: H~2~O + CO~2~
- Mathematical notation: x^2^ + y^2^ = r^2^

### Blockquotes
> 💭 "The best way to predict the future is to create it."
>
> — Peter Drucker

> 🎯 **Pro Tip**: You can nest blockquotes
> > Like this inner quote
> > > And even deeper!

## 🎉 Conclusion

This markdown renderer supports:
- ✅ Real-time preview
- ✅ Syntax highlighting
- ✅ Mathematical formulas (KaTeX)
- ✅ Mermaid diagrams
- ✅ Tables and lists
- ✅ Dark/light themes
- ✅ Export functionality

Happy writing! 🚀
`;
        renderMarkdown();
      }
      
      // 通知函数
      function showNotification(message, duration = 3000) {
        const notification = document.getElementById('notification');
        notification.textContent = message;
        notification.classList.add('show');
        
        setTimeout(() => {
          notification.classList.remove('show');
        }, duration);
      }
      
      // Auto-render on input with debouncing
      let renderTimeout;
      markdownInput.addEventListener('input', function() {
        clearTimeout(renderTimeout);
        renderTimeout = setTimeout(() => {
          renderMarkdown();
        }, 300); // 300ms debounce
      });

      // Initial render
      renderMarkdown();

      // Event handlers
      themeToggle.addEventListener('click', function() {
        toggleDarkMode();
        const theme = document.body.classList.contains('dark-theme') ? 'dark' : 'light';
        showNotification(`Theme switched to ${theme} mode`);
      });

      exampleButton.addEventListener('click', function() {
        loadExample();
        showNotification('Example loaded!');
      });
      
      // 生成PDF的实际函数
      function generatePDF(filename, includeHeaderFooter, includeTimestamp) {
        // 创建加载指示器
        const loadingElement = document.createElement('div');
        loadingElement.className = 'pdf-loading';
        loadingElement.innerHTML = `
          <div class="spinner"></div>
          <div>Generating PDF...</div>
          <div style="font-size: 14px; margin-top: 10px;">This may take a few moments</div>
        `;
        document.body.appendChild(loadingElement);
        
        // 等待一小段时间让UI更新
        setTimeout(() => {
          // 创建一个临时容器来保存要打印的内容
          const printContainer = document.createElement('div');
          printContainer.innerHTML = markdownOutput.innerHTML;
          printContainer.style.maxWidth = '180mm'; // 避免超宽
          printContainer.style.width = '100%';
          printContainer.style.padding = '15mm'; // 页边距
          printContainer.style.backgroundColor = 'white'; // 确保背景为白色
          printContainer.style.color = 'black'; // 确保文本为黑色
          printContainer.style.position = 'absolute';
          printContainer.style.left = '-9999px';
          printContainer.style.top = '0';
          printContainer.style.zIndex = '-1';
          document.body.appendChild(printContainer);
          
          // 添加页眉和页脚（如果需要）
          if (includeHeaderFooter) {
            let documentTitle = 'Markdown Document';
            const h1Element = printContainer.querySelector('h1');
            if (h1Element) {
              documentTitle = h1Element.textContent;
            }
            const header = document.createElement('div');
            header.style.textAlign = 'center';
            header.style.borderBottom = '1px solid #ddd';
            header.style.padding = '5px 0';
            header.style.marginBottom = '15px';
            header.style.fontSize = '9pt';
            header.style.color = '#666';
            header.textContent = documentTitle;
            printContainer.insertBefore(header, printContainer.firstChild);
            const footer = document.createElement('div');
            footer.style.textAlign = 'center';
            footer.style.borderTop = '1px solid #ddd';
            footer.style.padding = '5px 0';
            footer.style.marginTop = '15px';
            footer.style.fontSize = '8pt';
            footer.style.color = '#666';
            if (includeTimestamp) {
              const now = new Date();
              const dateStr = now.toLocaleDateString();
              const timeStr = now.toLocaleTimeString();
              footer.textContent = `Generated on ${dateStr} at ${timeStr}`;
            } else {
              footer.textContent = 'Page {page} of {pages}';
            }
            printContainer.appendChild(footer);
          }
          
          // 对所有链接进行处理，确保它们在PDF中可见
          const links = printContainer.querySelectorAll('a');
          links.forEach(link => {
            if (link.href) {
              link.textContent = `${link.textContent} (${link.href})`;
              link.style.color = 'blue';
              link.style.textDecoration = 'underline';
            }
          });
          
          // 修复代码块的样式问题
          const codeBlocks = printContainer.querySelectorAll('pre code');
          codeBlocks.forEach(block => {
            const copyButton = block.parentElement.querySelector('.copy-button');
            if (copyButton) {
              copyButton.remove();
            }
            block.style.fontFamily = 'Courier, monospace';
            block.style.fontSize = '10pt';
            block.style.whiteSpace = 'pre-wrap';
            block.style.wordWrap = 'break-word';
            block.style.border = '1px solid #ddd';
            block.style.padding = '10px';
            const lineNumbers = block.querySelectorAll('.hljs-ln-numbers');
            if (lineNumbers.length > 0) {
              lineNumbers.forEach(ln => {
                ln.style.paddingRight = '10px';
              });
            }
          });
          
          // 样式调整，确保PDF中内容完整且美观
          const styles = document.createElement('style');
          styles.textContent = `
            body { font-family: Arial, sans-serif; color: black; }
            pre { page-break-inside: avoid; white-space: pre-wrap; border: 1px solid #ddd; border-radius: 3px; background-color: #f8f8f8; margin: 1em 0; }
            code { font-family: Courier, monospace; font-size: 10pt; }
            table { page-break-inside: avoid; width: 100%; border-collapse: collapse; margin: 1em 0; }
            th, td { border: 1px solid #ddd; padding: 8px; }
            th { background-color: #f2f2f2; }
            img, svg { max-width: 100%; }
            .katex-display { overflow: hidden; max-width: 100%; page-break-inside: avoid; }
            .mermaid { max-width: 100%; text-align: center; margin: 1em 0; page-break-inside: avoid; }
            h1, h2, h3 { page-break-after: avoid; }
            ul, ol { page-break-before: avoid; }
            blockquote { border-left: 4px solid #ddd; padding-left: 1em; margin-left: 0; }
            a { color: blue; text-decoration: underline; }
            @page { margin: 15mm; }
          `;
          printContainer.appendChild(styles);
          
          // 修复可能的mermaid图表显示问题
          const mermaidDiagrams = printContainer.querySelectorAll('.mermaid');
          if (mermaidDiagrams.length > 0) {
            mermaidDiagrams.forEach(diagram => {
              const renderedSvg = diagram.querySelector('svg');
              if (renderedSvg) {
                const clonedSvg = renderedSvg.cloneNode(true);
                clonedSvg.setAttribute('width', '100%');
                clonedSvg.setAttribute('height', 'auto');
                clonedSvg.style.maxWidth = '180mm';
                diagram.innerHTML = clonedSvg.outerHTML;
              }
            });
          }
          
          // 设置KaTeX公式的样式
          const katexElements = printContainer.querySelectorAll('.katex-display, .katex-inline');
          katexElements.forEach(elem => {
            elem.style.pageBreakInside = 'avoid';
            elem.style.maxWidth = '100%';
          });
          
          // 配置html2pdf选项
          const opt = {
            margin: [15, 15, 15, 15],
            filename: filename,
            image: { type: 'jpeg', quality: 0.98 },
            html2canvas: { 
              scale: 2,
              useCORS: true,
              letterRendering: true,
              logging: false
            },
            jsPDF: { 
              unit: 'mm', 
              format: 'a4', 
              orientation: 'portrait'
            },
            pagebreak: { mode: ['avoid-all', 'css', 'legacy'] }
          };
          
          // 等待异步渲染完成
          setTimeout(() => {
            html2pdf().from(printContainer).set(opt).save()
              .then(() => {
                document.body.removeChild(printContainer);
                document.body.removeChild(loadingElement);
                showNotification(`PDF "${filename}" downloaded successfully!`, 3000);
              })
              .catch(err => {
                document.body.removeChild(printContainer);
                document.body.removeChild(loadingElement);
                console.error('PDF generation error:', err);
                showNotification('Error generating PDF. See console for details.', 5000);
              });
          }, 500); // 适当延迟，确保渲染完成
        }, 100);
      }
      
      // 新增HTML导出功能
      exportHtmlButton.addEventListener('click', function() {
        // 确保内容已渲染
        renderMarkdown();
        
        // 创建一个对话框获取文件名
        const overlay = document.createElement('div');
        overlay.className = 'pdf-overlay';
        
        const dialog = document.createElement('div');
        dialog.className = 'pdf-filename-dialog';
        dialog.innerHTML = `
          <h3>Export HTML</h3>
          <div class="form-group">
            <label for="html-filename">Filename:</label>
            <input type="text" id="html-filename" value="markdown-content.html" placeholder="Enter filename (e.g., my-document.html)">
          </div>
          <div class="dialog-buttons">
            <button id="cancel-html-export">Cancel</button>
            <button id="confirm-html-export">Export</button>
          </div>
        `;
        
        document.body.appendChild(overlay);
        document.body.appendChild(dialog);
        
        // 文件名输入框焦点
        setTimeout(() => {
          document.getElementById('html-filename').focus();
          document.getElementById('html-filename').select();
        }, 100);
        
        // 取消按钮
        document.getElementById('cancel-html-export').addEventListener('click', function() {
          document.body.removeChild(overlay);
          document.body.removeChild(dialog);
        });
        
        // 确认导出按钮
        document.getElementById('confirm-html-export').addEventListener('click', function() {
          // 获取文件名
          let filename = document.getElementById('html-filename').value.trim();
          if (!filename) {
            filename = 'markdown-content.html';
          } else if (!filename.toLowerCase().endsWith('.html')) {
            filename += '.html';
          }
          
          // 移除对话框
          document.body.removeChild(overlay);
          document.body.removeChild(dialog);
          
          // 创建完整HTML文档
          exportToHTML(filename);
        });
        
        // 处理回车键提交
        document.getElementById('html-filename').addEventListener('keyup', function(event) {
          if (event.key === 'Enter') {
            document.getElementById('confirm-html-export').click();
          }
        });
      });
      
      // 导出HTML的实际函数
      function exportToHTML(filename) {
        showNotification('Preparing HTML export...', 2000);
        
        // 获取文档的标题（第一个h1标签的内容）
        let documentTitle = 'Markdown Document';
        const h1Element = markdownOutput.querySelector('h1');
        if (h1Element) {
          documentTitle = h1Element.textContent;
        }
        
        // 创建一个完整的HTML文档结构
        const htmlTemplate = `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>${documentTitle}</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      line-height: 1.6;
      color: #333;
      max-width: 900px;
      margin: 0 auto;
      padding: 20px;
    }
    pre, code {
      background-color: #f6f8fa;
      border-radius: 3px;
      font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
    }
    pre {
      padding: 16px;
      overflow: auto;
      line-height: 1.45;
    }
    code {
      padding: 0.2em 0.4em;
      font-size: 85%;
    }
    pre code {
      padding: 0;
      background-color: transparent;
    }
    blockquote {
      padding: 0 1em;
      color: #6a737d;
      border-left: 0.25em solid #dfe2e5;
      margin: 0 0 16px 0;
    }
    img, svg {
      max-width: 100%;
      height: auto;
    }
    table {
      border-collapse: collapse;
      width: 100%;
      margin-bottom: 16px;
    }
    table th, table td {
      padding: 6px 13px;
      border: 1px solid #dfe2e5;
    }
    table th {
      background-color: #f6f8fa;
      font-weight: 600;
    }
    table tr {
      background-color: #fff;
      border-top: 1px solid #c6cbd1;
    }
    table tr:nth-child(2n) {
      background-color: #f6f8fa;
    }
    h1, h2, h3, h4, h5, h6 {
      margin-top: 24px;
      margin-bottom: 16px;
      font-weight: 600;
      line-height: 1.25;
    }
    h1 {
      font-size: 2em;
      border-bottom: 1px solid #eaecef;
      padding-bottom: 0.3em;
    }
    h2 {
      font-size: 1.5em;
      border-bottom: 1px solid #eaecef;
      padding-bottom: 0.3em;
    }
    a {
      color: #0366d6;
      text-decoration: none;
    }
    a:hover {
      text-decoration: underline;
    }
    .katex-display {
      overflow-x: auto;
      overflow-y: hidden;
      margin: 1em 0;
      padding: 0.5em 0;
    }
    /* KaTeX样式 */
    .katex-display > .katex {
      display: block;
      text-align: center;
    }
    .mermaid {
      text-align: center;
      margin: 1em 0;
    }
    .hljs {
      display: block;
      overflow-x: auto;
      padding: 0.5em;
      color: #333;
      background: #f8f8f8;
    }
  </style>
  <!-- 添加KaTeX的CSS，确保公式正确显示 -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.16.9/dist/katex.min.css">
</head>
<body>
  <article class="markdown-body">
    ${markdownOutput.innerHTML}
  </article>
</body>
</html>`;

        // 处理KaTeX公式
        let processedHTML = htmlTemplate;
        
        // 处理Mermaid图表，确保它们已经被渲染为SVG并包含在导出中
        // 创建一个副本用于处理
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = processedHTML;
        
        // 查找所有已渲染的mermaid图表
        const mermaidDiagrams = tempDiv.querySelectorAll('.mermaid');
        mermaidDiagrams.forEach(diagram => {
          // 如果存在SVG内容，确保它被正确导出
          const svgElement = diagram.querySelector('svg');
          if (svgElement) {
            // 确保SVG有正确的命名空间
            if (!svgElement.getAttribute('xmlns')) {
              svgElement.setAttribute('xmlns', 'http://www.w3.org/2000/svg');
            }
            
            // 克隆并设置合适的尺寸
            const clonedSvg = svgElement.cloneNode(true);
            clonedSvg.setAttribute('width', '100%');
            clonedSvg.setAttribute('height', 'auto');
            
            // 将原始图表替换为处理过的SVG
            diagram.innerHTML = clonedSvg.outerHTML;
          }
        });
        
        // 更新处理后的HTML
        processedHTML = tempDiv.innerHTML;
        
        // 提取内容并重新组装HTML文档
        const bodyContent = tempDiv.querySelector('.markdown-body');
        if (bodyContent) {
          const htmlContent = `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>${documentTitle}</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      line-height: 1.6;
      color: #333;
      max-width: 900px;
      margin: 0 auto;
      padding: 20px;
    }
    pre, code {
      background-color: #f6f8fa;
      border-radius: 3px;
      font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
    }
    pre {
      padding: 16px;
      overflow: auto;
      line-height: 1.45;
    }
    code {
      padding: 0.2em 0.4em;
      font-size: 85%;
    }
    pre code {
      padding: 0;
      background-color: transparent;
    }
    blockquote {
      padding: 0 1em;
      color: #6a737d;
      border-left: 0.25em solid #dfe2e5;
      margin: 0 0 16px 0;
    }
    img, svg {
      max-width: 100%;
      height: auto;
    }
    table {
      border-collapse: collapse;
      width: 100%;
      margin-bottom: 16px;
    }
    table th, table td {
      padding: 6px 13px;
      border: 1px solid #dfe2e5;
    }
    table th {
      background-color: #f6f8fa;
      font-weight: 600;
    }
    table tr {
      background-color: #fff;
      border-top: 1px solid #c6cbd1;
    }
    table tr:nth-child(2n) {
      background-color: #f6f8fa;
    }
    h1, h2, h3, h4, h5, h6 {
      margin-top: 24px;
      margin-bottom: 16px;
      font-weight: 600;
      line-height: 1.25;
    }
    h1 {
      font-size: 2em;
      border-bottom: 1px solid #eaecef;
      padding-bottom: 0.3em;
    }
    h2 {
      font-size: 1.5em;
      border-bottom: 1px solid #eaecef;
      padding-bottom: 0.3em;
    }
    a {
      color: #0366d6;
      text-decoration: none;
    }
    a:hover {
      text-decoration: underline;
    }
    .katex-display {
      overflow-x: auto;
      overflow-y: hidden;
      margin: 1em 0;
      padding: 0.5em 0;
    }
    /* KaTeX样式 */
    .katex-display > .katex {
      display: block;
      text-align: center;
    }
    .mermaid {
      text-align: center;
      margin: 1em 0;
    }
    .hljs {
      display: block;
      overflow-x: auto;
      padding: 0.5em;
      color: #333;
      background: #f8f8f8;
    }
  </style>
  <!-- 添加KaTeX的CSS，确保公式正确显示 -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.16.9/dist/katex.min.css">
</head>
<body>
  <article class="markdown-body">
    ${markdownOutput.innerHTML}
  </article>
</body>
</html>`;

          // 创建Blob对象并下载
          const blob = new Blob([htmlContent], { type: 'text/html' });
          const url = URL.createObjectURL(blob);
          
          const a = document.createElement('a');
          a.href = url;
          a.download = filename;
          document.body.appendChild(a);
          a.click();
          
          // 清理
          setTimeout(() => {
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            showNotification(`HTML file "${filename}" downloaded successfully!`, 3000);
          }, 100);
        } else {
          showNotification('Error creating HTML content', 3000);
        }
      }
      
      // 打印功能
      printButton.addEventListener('click', function() {
        // 确保内容已经渲染
        renderMarkdown();
        
        // 显示通知
        showNotification('Preparing document for printing...', 2000);
        
        // 创建打印友好的样式
        const style = document.createElement('style');
        style.id = 'print-styles';
        style.textContent = `
          @media print {
            body * {
              visibility: hidden;
            }
            .container, .container * {
              visibility: hidden;
            }
            #markdown-output, #markdown-output * {
              visibility: visible;
            }
            #markdown-output {
              position: absolute;
              left: 0;
              top: 0;
              width: 100%;
              padding: 15mm;
              box-sizing: border-box;
            }
            .preview-container {
              border: none;
              overflow: visible;
            }
            pre, code, table, .mermaid {
              page-break-inside: avoid;
              white-space: pre-wrap;
            }
            img, svg {
              max-width: 100% !important;
              page-break-inside: avoid;
            }
            h1, h2, h3 {
              page-break-after: avoid;
            }
            ul, ol, li {
              page-break-inside: avoid;
            }
            .katex-display {
              page-break-inside: avoid;
            }
            a {
              text-decoration: underline;
            }
            a[href]:after {
              content: " (" attr(href) ")";
              font-size: 90%;
              color: #0366d6;
            }
          }
        `;
        document.head.appendChild(style);
        
        // 确保所有内容都加载完成，特别是图表和公式
        setTimeout(() => {
          window.print();
          
          // 打印对话框关闭后移除打印样式
          setTimeout(() => {
            const printStyles = document.getElementById('print-styles');
            if (printStyles) {
              document.head.removeChild(printStyles);
            }
          }, 1000);
        }, 300);
      });
      
      // Initial load with sample text
      markdownInput.value = `# 🚀 Welcome to Markdown Live Preview

Start typing your markdown here and watch the **live preview** update automatically!

## ✨ Quick Start
- Type markdown in the left panel
- See instant preview on the right
- Click **📝 Load Example** for a full feature demo

## 🎯 Features
- **Real-time rendering** as you type
- **Math formulas** with KaTeX: $E = mc^2$
- **Mermaid diagrams** support
- **Syntax highlighting** for code
- **Dark/Light themes** 🌓
- **Export to HTML** 💾

Happy writing! ✍️`;
      renderMarkdown();
    });
  </script>
 </body>
 </html>