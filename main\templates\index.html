<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI助教 - 登录</title>
    <link rel="stylesheet" href="{{ url_for('main.static', filename='css/style.css') }}">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- 滚动文字 -->
    <div class="scrolling-text-container">
        <div class="scrolling-text">
            🕒 网页开放时间：9:00 - 02:00 &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
            🕒 网页开放时间：9:00 - 02:00 &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
            🕒 网页开放时间：9:00 - 02:00 &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
        </div>
    </div>

    <div class="container">
        <div class="auth-container">
            <!-- 认证选项 -->
            <div class="auth-options">
                <button class="auth-option active" data-tab="login">登录</button>
                <button class="auth-option" data-tab="register">注册</button>
                <button class="auth-option" data-tab="reset">找回密码</button>
            </div>

            <!-- 登录表单 -->
            <div id="login-tab" class="auth-tab active">
                <div class="login-title-container">
                    <h3 class="login-title">
                        <span>✨欢迎登录✨</span>
                    </h3>
                </div>
                <form id="login-form" class="auth-form">
                    <div class="form-group">
                        <input type="text" id="login-username" placeholder="请输入用户名" required>
                    </div>
                    <div class="form-group">
                        <input type="password" id="login-password" placeholder="请输入密码" required>
                    </div>
                    <button type="submit" class="submit-btn">登 录</button>
                </form>
            </div>

            <!-- 注册表单 -->
            <div id="register-tab" class="auth-tab">
                <h3 class="form-title">新用户注册</h3>
                <form id="register-form" class="auth-form">
                    <div class="form-group">
                        <input type="text" id="register-username" placeholder="设置用户名(至少3位，可包含汉字、字母、数字和下划线)" required>
                        <div class="validation-message" id="username-validation"></div>
                    </div>
                    <div class="form-group">
                        <input type="password" id="register-password" placeholder="设置密码" required>
                    </div>
                    <div class="form-group">
                        <input type="password" id="register-password-repeat" placeholder="再次输入密码" required>
                    </div>
                    <div class="form-group">
                        <input type="email" id="register-email" placeholder="请输入有效邮箱" required>
                        <div class="validation-message" id="email-validation"></div>
                    </div>
                    <button type="submit" class="submit-btn" id="send-code-btn">发送验证码</button>
                </form>

                <!-- 验证码表单 -->
                <div id="verification-form" class="auth-form" style="display: none;">
                    <h3 class="form-title">邮箱验证</h3>
                    <div class="form-group">
                        <input type="text" id="verification-code" placeholder="请输入收到的验证码" required>
                    </div>
                    <button type="button" class="submit-btn" id="verify-code-btn">完成注册</button>
                </div>
            </div>

            <!-- 找回密码表单 -->
            <div id="reset-tab" class="auth-tab">
                <!-- 第一步：输入邮箱 -->
                <div id="reset-email-form" class="auth-form">
                    <h3 class="form-title">找回密码</h3>
                    <form id="reset-form">
                        <div class="form-group">
                            <input type="email" id="reset-email" placeholder="请输入注册时使用的邮箱" required>
                        </div>
                        <button type="submit" class="submit-btn">查找用户</button>
                    </form>
                </div>

                <!-- 第二步：显示用户名并输入验证码和新密码 -->
                <div id="reset-verify-form" class="auth-form" style="display: none;">
                    <h3 class="form-title">重置密码</h3>
                    <div class="form-group">
                        <label>找到的用户名：</label>
                        <div id="found-username" style="font-weight: bold; color: #007bff; margin-bottom: 10px;"></div>
                    </div>
                    <div class="form-group">
                        <input type="text" id="reset-verification-code" placeholder="请输入邮箱收到的验证码" required>
                    </div>
                    <div class="form-group">
                        <input type="password" id="new-password" placeholder="请输入新密码（至少6位）" required>
                    </div>
                    <div class="form-group">
                        <input type="password" id="confirm-password" placeholder="请确认新密码" required>
                    </div>
                    <button type="button" class="submit-btn" id="reset-password-btn">重置密码</button>
                    <button type="button" class="submit-btn" id="back-to-email-btn" style="background-color: #6c757d; margin-top: 10px;">返回上一步</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 消息提示 -->
    <div id="message-container" class="message-container"></div>

    <script src="{{ url_for('main.static', filename='js/main.js') }}"></script>
    <script>
        // 标签切换功能
        document.querySelectorAll('.auth-option').forEach(button => {
            button.addEventListener('click', function() {
                const tabName = this.dataset.tab;
                
                // 移除所有活动状态
                document.querySelectorAll('.auth-option').forEach(btn => btn.classList.remove('active'));
                document.querySelectorAll('.auth-tab').forEach(tab => tab.classList.remove('active'));
                
                // 添加活动状态
                this.classList.add('active');
                document.getElementById(tabName + '-tab').classList.add('active');
            });
        });

        // 登录表单处理
        document.getElementById('login-form').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const username = document.getElementById('login-username').value;
            const password = document.getElementById('login-password').value;
            
            if (!username || !password) {
                showMessage('请输入用户名和密码', 'error');
                return;
            }
            
            try {
                const response = await fetch('/main/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ username, password })
                });

                const result = await response.json();

                if (result.success) {
                    showMessage('登录成功！', 'success');
                    setTimeout(() => {
                        window.location.href = '/main/dashboard';
                    }, 1000);
                } else {
                    showMessage(result.message, 'error');
                }
            } catch (error) {
                showMessage('登录失败，请重试', 'error');
            }
        });

        // 注册表单处理
        document.getElementById('register-form').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const username = document.getElementById('register-username').value;
            const password = document.getElementById('register-password').value;
            const passwordRepeat = document.getElementById('register-password-repeat').value;
            const email = document.getElementById('register-email').value;
            
            // 用户名验证
            if (!isValidUsername(username)) {
                showMessage('用户名格式不正确', 'error');
                return;
            }
            
            if (!username || !password || !passwordRepeat || !email) {
                showMessage('请填写所有必填项', 'error');
                return;
            }
            
            if (password !== passwordRepeat) {
                showMessage('两次输入的密码不匹配', 'error');
                return;
            }
            
            if (password.length < 6) {
                showMessage('密码长度至少为6位', 'error');
                return;
            }
            
            try {
                const response = await fetch('/main/register', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'send_code',
                        username,
                        password,
                        email
                    })
                });

                const result = await response.json();

                if (result.success) {
                    showMessage(result.message, 'success');
                    document.getElementById('register-form').style.display = 'none';
                    document.getElementById('verification-form').style.display = 'block';
                } else {
                    showMessage(result.message, 'error');
                }
            } catch (error) {
                showMessage('发送验证码失败，请重试', 'error');
            }
        });

        // 添加用户名实时验证
        document.getElementById('register-username').addEventListener('input', function() {
            const username = this.value;
            const validationMessage = document.getElementById('username-validation');
            
            if (username === '') {
                validationMessage.textContent = '';
                return;
            }
            
            if (!isValidUsername(username)) {
                validationMessage.textContent = '用户名至少3位，只能包含汉字、字母、数字和下划线';
                validationMessage.style.color = '#ff4444';
            } else {
                validationMessage.textContent = '✓ 用户名格式正确';
                validationMessage.style.color = '#4CAF50';
            }
        });

        // 用户名验证函数
        function isValidUsername(username) {
            // 匹配汉字、字母、数字、下划线，且长度至少为3
            const usernameRegex = /^[\u4e00-\u9fa5a-zA-Z0-9_]{3,}$/;
            return usernameRegex.test(username);
        }

        // 验证码验证
        document.getElementById('verify-code-btn').addEventListener('click', async function() {
            const code = document.getElementById('verification-code').value;
            
            if (!code) {
                showMessage('请输入验证码', 'error');
                return;
            }
            
            try {
                const response = await fetch('/main/register', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'verify_code',
                        code
                    })
                });

                const result = await response.json();

                if (result.success) {
                    showMessage(result.message, 'success');
                    setTimeout(() => {
                        // 切换到登录标签
                        document.querySelector('[data-tab="login"]').click();
                        document.getElementById('verification-form').style.display = 'none';
                        document.getElementById('register-form').style.display = 'block';
                    }, 1500);
                } else {
                    showMessage(result.message, 'error');
                }
            } catch (error) {
                showMessage('验证失败，请重试', 'error');
            }
        });

        // 找回密码表单处理 - 第一步：查找用户
        document.getElementById('reset-form').addEventListener('submit', async function(e) {
            e.preventDefault();

            const email = document.getElementById('reset-email').value;

            if (!email) {
                showMessage('请输入邮箱地址', 'error');
                return;
            }

            try {
                const response = await fetch('/main/reset-password', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'find_user',
                        email: email
                    })
                });

                const result = await response.json();

                if (result.success) {
                    showMessage(result.message, 'success');
                    // 显示找到的用户名
                    document.getElementById('found-username').textContent = result.username;
                    // 切换到验证码和密码重置表单
                    document.getElementById('reset-email-form').style.display = 'none';
                    document.getElementById('reset-verify-form').style.display = 'block';
                } else {
                    showMessage(result.message, 'error');
                }
            } catch (error) {
                showMessage('请求失败，请重试', 'error');
            }
        });

        // 重置密码按钮处理 - 第二步：验证码和密码重置
        document.getElementById('reset-password-btn').addEventListener('click', async function() {
            const code = document.getElementById('reset-verification-code').value;
            const newPassword = document.getElementById('new-password').value;
            const confirmPassword = document.getElementById('confirm-password').value;

            if (!code || !newPassword || !confirmPassword) {
                showMessage('请填写所有字段', 'error');
                return;
            }

            if (newPassword !== confirmPassword) {
                showMessage('两次输入的密码不一致', 'error');
                return;
            }

            if (newPassword.length < 6) {
                showMessage('密码长度至少为6位', 'error');
                return;
            }

            try {
                const response = await fetch('/main/reset-password', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'verify_and_reset',
                        code: code,
                        new_password: newPassword
                    })
                });

                const result = await response.json();

                if (result.success) {
                    showMessage(result.message, 'success');
                    setTimeout(() => {
                        // 重置表单并切换到登录标签
                        document.getElementById('reset-verify-form').style.display = 'none';
                        document.getElementById('reset-email-form').style.display = 'block';
                        document.getElementById('reset-form').reset();
                        document.getElementById('reset-verification-code').value = '';
                        document.getElementById('new-password').value = '';
                        document.getElementById('confirm-password').value = '';
                        document.querySelector('[data-tab="login"]').click();
                    }, 2000);
                } else {
                    showMessage(result.message, 'error');
                }
            } catch (error) {
                showMessage('请求失败，请重试', 'error');
            }
        });

        // 返回上一步按钮处理
        document.getElementById('back-to-email-btn').addEventListener('click', function() {
            document.getElementById('reset-verify-form').style.display = 'none';
            document.getElementById('reset-email-form').style.display = 'block';
            // 清空验证码和密码字段
            document.getElementById('reset-verification-code').value = '';
            document.getElementById('new-password').value = '';
            document.getElementById('confirm-password').value = '';
        });

        // 消息显示函数
        function showMessage(message, type) {
            const container = document.getElementById('message-container');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            messageDiv.textContent = message;
            
            container.appendChild(messageDiv);
            
            setTimeout(() => {
                messageDiv.classList.add('show');
            }, 100);
            
            setTimeout(() => {
                messageDiv.classList.remove('show');
                setTimeout(() => {
                    container.removeChild(messageDiv);
                }, 300);
            }, 3000);
        }
    </script>
</body>
</html>
