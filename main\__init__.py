from flask import Flask
from werkzeug.middleware.proxy_fix import ProxyFix
from flask_cors import CORS

def create_app():
    app = Flask(__name__)
    app.wsgi_app = ProxyFix(app.wsgi_app, x_for=1, x_proto=1, x_host=1)
    CORS(app)

    # 导入并注册蓝图
    from app import main_bp, init_app
    
    # 初始化主应用
    init_app(app)
    
    # 注册主蓝图
    app.register_blueprint(main_bp, url_prefix='/main')
    
    return app 