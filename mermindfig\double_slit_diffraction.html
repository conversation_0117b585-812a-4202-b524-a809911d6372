<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>双缝衍射演示</title>
  <style>
    body {
      margin: 0;
      padding: 0;
      background: #001133;
      font-family: Arial, sans-serif;
      overflow: hidden;
    }
    
    #controls {
      position: fixed;
      top: 10px;
      left: 10px;
      background: rgba(255, 255, 255, 0.95);
      padding: 15px;
      border-radius: 10px;
      box-shadow: 0 4px 15px rgba(0,0,0,0.3);
      z-index: 100;
      min-width: 280px;
    }
    
    #controls h3 {
      margin: 0 0 15px 0;
      color: #333;
      text-align: center;
    }
    
    .control-group {
      margin: 10px 0;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
    
    .control-group label {
      font-weight: bold;
      color: #333;
      min-width: 80px;
    }
    
    .control-group input[type="range"] {
      flex: 1;
      margin: 0 10px;
    }
    
    .value-display {
      min-width: 60px;
      text-align: right;
      font-weight: bold;
      color: #666;
    }
    
    button {
      background: linear-gradient(45deg, #4ECDC4, #44A08D);
      color: white;
      border: none;
      padding: 8px 15px;
      border-radius: 5px;
      cursor: pointer;
      margin: 5px;
      font-weight: bold;
    }
    
    button:hover {
      background: linear-gradient(45deg, #45B7B8, #3D8B7A);
    }
    
    #info {
      position: fixed;
      bottom: 10px;
      left: 10px;
      background: rgba(255, 255, 255, 0.9);
      padding: 10px;
      border-radius: 8px;
      max-width: 300px;
      font-size: 12px;
      color: #333;
    }
    
    #canvas {
      display: block;
    }
    
    .parameter-display {
      position: fixed;
      top: 10px;
      right: 10px;
      background: rgba(255, 255, 255, 0.9);
      padding: 15px;
      border-radius: 10px;
      font-family: monospace;
      font-size: 14px;
      color: #333;
    }
  </style>
</head>
<body>
  <div id="controls">
    <h3>🔀 双缝衍射演示</h3>
    <div class="control-group">
      <label>波长 λ:</label>
      <input id="wavelength" type="range" min="20" max="80" value="40">
      <span id="wavelengthVal" class="value-display">40 px</span>
    </div>
    <div class="control-group">
      <label>缝宽 a:</label>
      <input id="slitWidth" type="range" min="10" max="100" value="30">
      <span id="slitWidthVal" class="value-display">30 px</span>
    </div>
    <div class="control-group">
      <label>缝间距 d:</label>
      <input id="slitSeparation" type="range" min="50" max="200" value="100">
      <span id="slitSeparationVal" class="value-display">100 px</span>
    </div>
    <div class="control-group">
      <label>振幅:</label>
      <input id="amplitude" type="range" min="50" max="150" value="100">
      <span id="amplitudeVal" class="value-display">100</span>
    </div>
    <div class="control-group">
      <label>频率:</label>
      <input id="frequency" type="range" min="0.5" max="3" step="0.1" value="1.5">
      <span id="frequencyVal" class="value-display">1.5 Hz</span>
    </div>
    <div style="margin-top: 15px; border-top: 1px solid #ddd; padding-top: 10px;">
      <button onclick="resetDemo()">🔄 重置</button>
      <button onclick="toggleAnimation()">⏯️ 暂停/播放</button>
    </div>
  </div>
  
  <div class="parameter-display">
    <div><strong>双缝衍射参数:</strong></div>
    <div id="ratioDisplay">λ/a = 1.33</div>
    <div id="conditionDisplay">衍射条件: 满足</div>
    <div id="interferenceDisplay">干涉第一暗纹: 计算中...</div>
    <div id="separationRatio">d/λ = 2.5</div>
  </div>
  
  <div id="info">
    💡 <strong>双缝衍射说明:</strong><br>
    • 调节波长和缝宽观察衍射效果<br>
    • 缝间距影响干涉条纹间距<br>
    • 当λ/a ≈ 1时衍射效应最明显<br>
    • 蓝色=无波动，红色=最大强度<br>
    • 可拖动波源改变入射角度
  </div>
  
  <canvas id="canvas"></canvas>

  <script>
    const canvas = document.getElementById('canvas');
    const ctx = canvas.getContext('2d');
    let width = window.innerWidth;
    let height = window.innerHeight;
    canvas.width = width;
    canvas.height = height;

    // 控件
    const wavelengthSlider = document.getElementById('wavelength');
    const slitWidthSlider = document.getElementById('slitWidth');
    const slitSeparationSlider = document.getElementById('slitSeparation');
    const amplitudeSlider = document.getElementById('amplitude');
    const frequencySlider = document.getElementById('frequency');
    
    const wavelengthVal = document.getElementById('wavelengthVal');
    const slitWidthVal = document.getElementById('slitWidthVal');
    const slitSeparationVal = document.getElementById('slitSeparationVal');
    const amplitudeVal = document.getElementById('amplitudeVal');
    const frequencyVal = document.getElementById('frequencyVal');

    // 参数
    let wavelength = parseFloat(wavelengthSlider.value);
    let slitWidth = parseFloat(slitWidthSlider.value);
    let slitSeparation = parseFloat(slitSeparationSlider.value);
    let amplitude = parseFloat(amplitudeSlider.value);
    let frequency = parseFloat(frequencySlider.value);
    let waveSpeed = 200; // 固定波速

    // 计算波数
    let k = 2 * Math.PI / wavelength;

    // 波源位置（可拖动）
    let waveSource = {
      x: width * 0.2,
      y: height * 0.5,
      dragging: false,
      dragOffset: {x: 0, y: 0}
    };

    // 双缝位置
    let slitCenter = {
      x: width * 0.5,
      y: height * 0.5
    };

    let animationRunning = true;
    let startTime = performance.now() / 1000;

    // 控件监听器
    wavelengthSlider.oninput = () => {
      wavelength = parseFloat(wavelengthSlider.value);
      wavelengthVal.textContent = wavelength + ' px';
      k = 2 * Math.PI / wavelength; // 重新计算波数
      updateParameterDisplay();
    };

    slitWidthSlider.oninput = () => {
      slitWidth = parseFloat(slitWidthSlider.value);
      slitWidthVal.textContent = slitWidth + ' px';
      updateParameterDisplay();
    };

    slitSeparationSlider.oninput = () => {
      slitSeparation = parseFloat(slitSeparationSlider.value);
      slitSeparationVal.textContent = slitSeparation + ' px';
      updateParameterDisplay();
    };

    amplitudeSlider.oninput = () => {
      amplitude = parseFloat(amplitudeSlider.value);
      amplitudeVal.textContent = amplitude;
    };

    frequencySlider.oninput = () => {
      frequency = parseFloat(frequencySlider.value);
      frequencyVal.textContent = frequency.toFixed(1) + ' Hz';
    };

    // 更新参数显示
    function updateParameterDisplay() {
      const ratio = (wavelength / slitWidth).toFixed(2);
      document.getElementById('ratioDisplay').textContent = `λ/a = ${ratio}`;

      const condition = ratio > 0.1 ? '满足' : '不明显';
      document.getElementById('conditionDisplay').textContent = `衍射条件: ${condition}`;

      // 对于双缝衍射，计算干涉第一级暗纹角度 sin(θ) = λ/(2d)
      // 这是双缝干涉的第一个暗纹（两波相消干涉）
      const interferenceRatio = wavelength / (2 * slitSeparation);
      if (interferenceRatio <= 1) {
        const interferenceAngle = Math.asin(interferenceRatio) * 180 / Math.PI;
        document.getElementById('interferenceDisplay').textContent = `干涉第一暗纹: ${interferenceAngle.toFixed(1)}°`;
      } else {
        document.getElementById('interferenceDisplay').textContent = `干涉第一暗纹: 无暗纹`;
      }

      // 显示缝间距与波长的比值
      const separationRatio = (slitSeparation / wavelength).toFixed(1);
      document.getElementById('separationRatio').textContent = `d/λ = ${separationRatio}`;
    }

    // 初始化参数显示
    updateParameterDisplay();

    // 颜色映射函数
    function amplitudeToColor(amplitude, maxAmplitude) {
      let intensity = Math.abs(amplitude) / maxAmplitude;
      intensity = Math.max(0, Math.min(1, intensity));

      const bgR = 0, bgG = 17, bgB = 51; // #001133

      let r, g, b;

      if (intensity < 0.15) {
        let t = intensity / 0.15;
        r = bgR + t * (0 - bgR);
        g = bgG + t * (80 - bgG);
        b = bgB + t * (200 - bgB);
      } else if (intensity < 0.35) {
        let t = (intensity - 0.15) / 0.2;
        r = 0 + t * (0 - 0);
        g = 80 + t * (180 - 80);
        b = 200 + t * (255 - 200);
      } else if (intensity < 0.55) {
        let t = (intensity - 0.35) / 0.2;
        r = 0 + t * (100 - 0);
        g = 180 + t * (255 - 180);
        b = 255 + t * (100 - 255);
      } else if (intensity < 0.75) {
        let t = (intensity - 0.55) / 0.2;
        r = 100 + t * (255 - 100);
        g = 255 + t * (200 - 255);
        b = 100 + t * (0 - 100);
      } else {
        let t = (intensity - 0.75) / 0.25;
        r = 255 + t * (255 - 255);
        g = 200 + t * (50 - 200);
        b = 0 + t * (0 - 0);
      }

      return [Math.round(r), Math.round(g), Math.round(b)];
    }

    // 计算双缝衍射的振幅（基于惠更斯原理）
    function calculateDoubleslitAmplitude(x, y, time) {
      // 双缝位置
      const slit1Y = slitCenter.y - slitSeparation / 2;
      const slit2Y = slitCenter.y + slitSeparation / 2;

      // 如果在缝的左侧，计算入射波
      if (x < slitCenter.x) {
        let dx = x - waveSource.x;
        let dy = y - waveSource.y;
        let distance = Math.hypot(dx, dy);

        // 检查波是否已经传播到这个位置
        let waveSpeed = wavelength * frequency;
        let travelTime = distance / waveSpeed;

        if (time > travelTime) {
          let omega = 2 * Math.PI * frequency;
          let phase = k * distance - omega * time;

          let attenuation = 1 / (1 + distance / 100);
          let amplitudeFactor = Math.pow(amplitude / 100, 1.2);
          return amplitude * amplitudeFactor * attenuation * Math.sin(phase);
        }

        return 0;
      }

      // 如果在缝的右侧，计算衍射波
      if (x > slitCenter.x) {
        let totalAmplitude = 0;
        let omega = 2 * Math.PI * frequency;

        // 使用惠更斯原理计算双缝衍射 - 从每个缝内的多个点计算贡献
        let numPoints = Math.max(6, Math.floor(slitWidth / 6)); // 每个缝内的采样点数

        // 缝1的贡献
        let slit1Top = slit1Y - slitWidth / 2;
        let slit1Bottom = slit1Y + slitWidth / 2;

        for (let i = 0; i < numPoints; i++) {
          let slitY = slit1Top + (slit1Bottom - slit1Top) * i / (numPoints - 1);

          // 从波源到缝内该点的距离
          let d1 = Math.hypot(slitCenter.x - waveSource.x, slitY - waveSource.y);
          // 从缝内该点到目标点的距离
          let d2 = Math.hypot(x - slitCenter.x, y - slitY);

          let totalDistance = d1 + d2;

          // 检查波是否已经传播到这个位置
          let waveSpeed = wavelength * frequency;
          let travelTime = totalDistance / waveSpeed;

          if (time > travelTime) {
            let phase = k * totalDistance - omega * time;

            // 衰减因子
            let attenuation = 1 / (1 + totalDistance / 100);

            // 每个缝内点的贡献
            totalAmplitude += (amplitude / (2 * numPoints)) * attenuation * Math.sin(phase);
          }
        }

        // 缝2的贡献
        let slit2Top = slit2Y - slitWidth / 2;
        let slit2Bottom = slit2Y + slitWidth / 2;

        for (let i = 0; i < numPoints; i++) {
          let slitY = slit2Top + (slit2Bottom - slit2Top) * i / (numPoints - 1);

          // 从波源到缝内该点的距离
          let d1 = Math.hypot(slitCenter.x - waveSource.x, slitY - waveSource.y);
          // 从缝内该点到目标点的距离
          let d2 = Math.hypot(x - slitCenter.x, y - slitY);

          let totalDistance = d1 + d2;

          // 检查波是否已经传播到这个位置
          let waveSpeed = wavelength * frequency;
          let travelTime = totalDistance / waveSpeed;

          if (time > travelTime) {
            let phase = k * totalDistance - omega * time;

            // 衰减因子
            let attenuation = 1 / (1 + totalDistance / 100);

            // 每个缝内点的贡献
            totalAmplitude += (amplitude / (2 * numPoints)) * attenuation * Math.sin(phase);
          }
        }

        // 确保振幅参数的影响可见
        let amplitudeFactor = Math.pow(amplitude / 100, 1.2);
        totalAmplitude *= amplitudeFactor;

        return totalAmplitude;
      }

      return 0;
    }

    // 鼠标事件处理
    canvas.addEventListener('mousedown', e => {
      const rect = canvas.getBoundingClientRect();
      const mx = e.clientX - rect.left;
      const my = e.clientY - rect.top;

      // 检查是否点击在波源上
      if (Math.hypot(mx - waveSource.x, my - waveSource.y) < 20) {
        waveSource.dragging = true;
        waveSource.dragOffset.x = mx - waveSource.x;
        waveSource.dragOffset.y = my - waveSource.y;
        canvas.style.cursor = 'grabbing';
      }
    });

    window.addEventListener('mousemove', e => {
      const rect = canvas.getBoundingClientRect();
      const mx = e.clientX - rect.left;
      const my = e.clientY - rect.top;

      if (waveSource.dragging) {
        waveSource.x = Math.max(20, Math.min(slitCenter.x - 50, mx - waveSource.dragOffset.x));
        waveSource.y = Math.max(20, Math.min(height - 20, my - waveSource.dragOffset.y));
        canvas.style.cursor = 'grabbing';
      } else if (Math.hypot(mx - waveSource.x, my - waveSource.y) < 20) {
        canvas.style.cursor = 'grab';
      } else {
        canvas.style.cursor = 'default';
      }
    });

    window.addEventListener('mouseup', e => {
      if (waveSource.dragging) {
        waveSource.dragging = false;
        canvas.style.cursor = 'default';
        startTime = performance.now() / 1000; // 重置时间
      }
    });

    // 控制函数
    function resetDemo() {
      waveSource.x = width * 0.2;
      waveSource.y = height * 0.5;

      // 重置所有参数
      wavelength = 40;
      slitWidth = 30;
      slitSeparation = 100;
      amplitude = 100;
      frequency = 1.5;

      // 重新计算波数
      k = 2 * Math.PI / wavelength;

      // 更新滑块值
      wavelengthSlider.value = wavelength;
      slitWidthSlider.value = slitWidth;
      slitSeparationSlider.value = slitSeparation;
      amplitudeSlider.value = amplitude;
      frequencySlider.value = frequency;

      // 更新显示值
      wavelengthVal.textContent = wavelength + ' px';
      slitWidthVal.textContent = slitWidth + ' px';
      slitSeparationVal.textContent = slitSeparation + ' px';
      amplitudeVal.textContent = amplitude;
      frequencyVal.textContent = frequency.toFixed(1) + ' Hz';

      updateParameterDisplay();
    }

    function toggleAnimation() {
      animationRunning = !animationRunning;
      if (animationRunning) {
        draw();
      }
    }

    // 窗口自适应
    window.addEventListener('resize', () => {
      width = window.innerWidth;
      height = window.innerHeight;
      canvas.width = width;
      canvas.height = height;

      // 重新调整位置
      waveSource.x = Math.min(waveSource.x, width * 0.4);
      waveSource.y = Math.min(waveSource.y, height - 20);
      slitCenter.x = width * 0.5;
      slitCenter.y = height * 0.5;
    });

    // 主绘制函数
    function draw() {
      if (!animationRunning) {
        requestAnimationFrame(draw);
        return;
      }

      let now = performance.now() / 1000;

      // 清空画布
      ctx.fillStyle = '#001133';
      ctx.fillRect(0, 0, width, height);

      // 创建图像数据
      let imageData = ctx.createImageData(width, height);
      let data = imageData.data;

      let step = 3; // 采样步长，提高性能

      for (let y = 0; y < height; y += step) {
        for (let x = 0; x < width; x += step) {
          let waveAmplitude = calculateDoubleslitAmplitude(x, y, now);
          let color = amplitudeToColor(waveAmplitude, 150); // 使用固定的最大振幅进行归一化

          // 填充像素
          for (let dy = 0; dy < step && y + dy < height; dy++) {
            for (let dx = 0; dx < step && x + dx < width; dx++) {
              let idx = ((y + dy) * width + (x + dx)) * 4;
              data[idx] = color[0];
              data[idx + 1] = color[1];
              data[idx + 2] = color[2];
              data[idx + 3] = 255;
            }
          }
        }
      }

      ctx.putImageData(imageData, 0, 0);

      // 绘制双缝障碍物
      ctx.fillStyle = 'white';
      // 上方障碍物
      ctx.fillRect(slitCenter.x - 2, 0, 4, slitCenter.y - slitSeparation/2 - slitWidth/2);
      // 中间障碍物
      ctx.fillRect(slitCenter.x - 2, slitCenter.y - slitSeparation/2 + slitWidth/2, 4, slitSeparation - slitWidth);
      // 下方障碍物
      ctx.fillRect(slitCenter.x - 2, slitCenter.y + slitSeparation/2 + slitWidth/2, 4, height - (slitCenter.y + slitSeparation/2 + slitWidth/2));

      // 绘制缝的边界线
      ctx.strokeStyle = '#FFD700';
      ctx.lineWidth = 2;
      ctx.beginPath();
      // 缝1边界
      ctx.moveTo(slitCenter.x, slitCenter.y - slitSeparation/2 - slitWidth/2);
      ctx.lineTo(slitCenter.x, slitCenter.y - slitSeparation/2 + slitWidth/2);
      // 缝2边界
      ctx.moveTo(slitCenter.x, slitCenter.y + slitSeparation/2 - slitWidth/2);
      ctx.lineTo(slitCenter.x, slitCenter.y + slitSeparation/2 + slitWidth/2);
      ctx.stroke();

      // 绘制波源
      ctx.beginPath();
      ctx.arc(waveSource.x, waveSource.y, 20, 0, 2 * Math.PI);
      ctx.fillStyle = 'white';
      ctx.fill();
      ctx.strokeStyle = waveSource.dragging ? '#FF6B6B' : '#4ECDC4';
      ctx.lineWidth = 4;
      ctx.stroke();

      // 波源中心
      ctx.beginPath();
      ctx.arc(waveSource.x, waveSource.y, 8, 0, 2 * Math.PI);
      ctx.fillStyle = waveSource.dragging ? '#FF6B6B' : '#4ECDC4';
      ctx.fill();

      requestAnimationFrame(draw);
    }

    // 启动动画
    draw();
  </script>
</body>
</html>
