<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>分屏滚动条测试</title>
    <style>
        :root {
            --primary-color: #667eea;
            --secondary-color: #764ba2;
            --bg-primary: #ffffff;
            --bg-secondary: #f8fafc;
            --bg-tertiary: #f1f5f9;
            --text-primary: #1e293b;
            --text-secondary: #64748b;
            --border-color: #e2e8f0;
            --border-radius: 8px;
        }

        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: var(--bg-secondary);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .tabs {
            display: flex;
            margin-bottom: 20px;
            border-bottom: 2px solid var(--border-color);
        }

        .tab {
            padding: 12px 24px;
            background: var(--bg-secondary);
            border: none;
            cursor: pointer;
            border-radius: 8px 8px 0 0;
            margin-right: 4px;
            font-weight: 500;
        }

        .tab.active {
            background: var(--primary-color);
            color: white;
        }

        .editor-content {
            display: flex;
            min-height: 600px;
            background: var(--bg-primary);
            border-radius: var(--border-radius);
            overflow: hidden;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        .editor-panel {
            flex: 1;
            padding: 25px;
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .preview-panel {
            flex: 1;
            padding: 25px;
            background: var(--bg-secondary);
            border-left: 1px solid var(--border-color);
            overflow-y: auto;
        }

        /* 分屏模式样式 */
        .editor-content.split-view .editor-panel,
        .editor-content.split-view .preview-panel {
            flex: 1;
            height: 600px;
            max-height: 600px;
        }

        .editor-content.split-view .preview-panel {
            border-left: 1px solid var(--border-color);
            overflow-y: auto;
        }

        .editor-content.split-view .editor-panel {
            overflow-y: auto;
        }

        /* 分屏模式下的滚动条样式 */
        .editor-content.split-view .preview-panel::-webkit-scrollbar,
        .editor-content.split-view .editor-panel::-webkit-scrollbar {
            width: 8px;
        }

        .editor-content.split-view .preview-panel::-webkit-scrollbar-track,
        .editor-content.split-view .editor-panel::-webkit-scrollbar-track {
            background: var(--bg-secondary);
            border-radius: 4px;
        }

        .editor-content.split-view .preview-panel::-webkit-scrollbar-thumb,
        .editor-content.split-view .editor-panel::-webkit-scrollbar-thumb {
            background: var(--primary-color);
            border-radius: 4px;
            opacity: 0.7;
        }

        .editor-content.split-view .preview-panel::-webkit-scrollbar-thumb:hover,
        .editor-content.split-view .editor-panel::-webkit-scrollbar-thumb:hover {
            background: var(--secondary-color);
            opacity: 1;
        }

        #content {
            width: 100%;
            height: 500px;
            padding: 20px;
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            font-family: 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.6;
            resize: none;
            overflow-y: auto;
        }

        #compiledContent {
            height: 100%;
            max-height: 550px;
            overflow-y: auto;
            padding: 20px;
            background: var(--bg-primary);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            line-height: 1.8;
        }

        #compiledContent::-webkit-scrollbar {
            width: 8px;
        }

        #compiledContent::-webkit-scrollbar-track {
            background: var(--bg-secondary);
            border-radius: 4px;
        }

        #compiledContent::-webkit-scrollbar-thumb {
            background: var(--primary-color);
            border-radius: 4px;
            opacity: 0.7;
        }

        #compiledContent::-webkit-scrollbar-thumb:hover {
            background: var(--secondary-color);
            opacity: 1;
        }

        .long-content {
            height: 2000px;
            background: linear-gradient(to bottom, #f0f0f0, #e0e0e0);
            padding: 20px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>分屏滚动条测试</h1>
        
        <div class="tabs">
            <button class="tab" onclick="switchMode('edit')">编辑</button>
            <button class="tab" onclick="switchMode('preview')">预览</button>
            <button class="tab active" onclick="switchMode('split')">分屏</button>
        </div>

        <div class="editor-content split-view" id="editorContent">
            <div class="editor-panel" id="editorPanel">
                <h3>编辑区域（左侧）</h3>
                <textarea id="content" placeholder="输入长文本内容来测试滚动条...">这是一个测试文本。

请输入大量内容来测试滚动条的效果。

1. 第一行内容
2. 第二行内容
3. 第三行内容
4. 第四行内容
5. 第五行内容
6. 第六行内容
7. 第七行内容
8. 第八行内容
9. 第九行内容
10. 第十行内容

继续添加更多内容...

11. 第十一行内容
12. 第十二行内容
13. 第十三行内容
14. 第十四行内容
15. 第十五行内容
16. 第十六行内容
17. 第十七行内容
18. 第十八行内容
19. 第十九行内容
20. 第二十行内容

更多内容...

21. 第二十一行内容
22. 第二十二行内容
23. 第二十三行内容
24. 第二十四行内容
25. 第二十五行内容
26. 第二十六行内容
27. 第二十七行内容
28. 第二十八行内容
29. 第二十九行内容
30. 第三十行内容

继续...

31. 第三十一行内容
32. 第三十二行内容
33. 第三十三行内容
34. 第三十四行内容
35. 第三十五行内容
36. 第三十六行内容
37. 第三十七行内容
38. 第三十八行内容
39. 第三十九行内容
40. 第四十行内容

最后一些内容...

41. 第四十一行内容
42. 第四十二行内容
43. 第四十三行内容
44. 第四十四行内容
45. 第四十五行内容
46. 第四十六行内容
47. 第四十七行内容
48. 第四十八行内容
49. 第四十九行内容
50. 第五十行内容

结束。</textarea>
            </div>
            
            <div class="preview-panel" id="previewPanel">
                <h3>预览区域（右侧）</h3>
                <div id="compiledContent">
                    <h1>预览内容</h1>
                    <p>这里是预览区域，应该有独立的滚动条。</p>
                    
                    <div class="long-content">
                        <h2>长内容区域 1</h2>
                        <p>这是一个很长的内容区域，用来测试滚动条的效果。</p>
                        <p>内容继续...</p>
                        <p>更多内容...</p>
                        <p>继续添加内容...</p>
                        <p>测试滚动效果...</p>
                    </div>
                    
                    <div class="long-content">
                        <h2>长内容区域 2</h2>
                        <p>另一个长内容区域。</p>
                        <p>更多测试内容...</p>
                        <p>继续测试...</p>
                        <p>滚动条测试...</p>
                        <p>独立滚动测试...</p>
                    </div>
                    
                    <div class="long-content">
                        <h2>长内容区域 3</h2>
                        <p>第三个长内容区域。</p>
                        <p>测试分屏滚动...</p>
                        <p>左右独立滚动...</p>
                        <p>滚动条样式测试...</p>
                        <p>最终测试内容...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function switchMode(mode) {
            const editorContent = document.getElementById('editorContent');
            const editorPanel = document.getElementById('editorPanel');
            const previewPanel = document.getElementById('previewPanel');
            const tabs = document.querySelectorAll('.tab');
            
            // 清除所有活动状态
            tabs.forEach(tab => tab.classList.remove('active'));
            
            switch(mode) {
                case 'edit':
                    event.target.classList.add('active');
                    editorContent.className = 'editor-content';
                    editorPanel.style.display = 'flex';
                    previewPanel.style.display = 'none';
                    break;
                case 'preview':
                    event.target.classList.add('active');
                    editorContent.className = 'editor-content';
                    editorPanel.style.display = 'none';
                    previewPanel.style.display = 'block';
                    break;
                case 'split':
                    event.target.classList.add('active');
                    editorContent.className = 'editor-content split-view';
                    editorPanel.style.display = 'flex';
                    previewPanel.style.display = 'block';
                    break;
            }
        }
    </script>
</body>
</html>
