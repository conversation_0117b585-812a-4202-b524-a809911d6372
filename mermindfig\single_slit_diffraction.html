<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <title>单缝衍射演示</title>
  <style>
    body {
      margin: 0;
      background: #001133;
      overflow: hidden;
      user-select: none;
      font-family: 'Microsoft YaHei', <PERSON><PERSON>, sans-serif;
    }
    #controls {
      position: absolute;
      top: 15px;
      left: 15px;
      background: rgba(255,255,255,0.95);
      border-radius: 12px;
      padding: 15px 20px;
      z-index: 10;
      box-shadow: 0 4px 12px rgba(0,0,0,0.3);
      min-width: 300px;
    }
    #controls h3 {
      margin: 0 0 12px 0;
      color: #333;
      font-size: 16px;
    }
    #controls .control-group {
      margin-bottom: 12px;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
    #controls label {
      font-weight: 500;
      color: #555;
      min-width: 80px;
    }
    #controls input[type=range] {
      width: 120px;
      margin: 0 8px;
    }
    #controls .value-display {
      min-width: 70px;
      text-align: right;
      font-weight: bold;
      color: #2196F3;
    }
    #controls button {
      background: #4ECDC4;
      color: white;
      border: none;
      padding: 8px 16px;
      border-radius: 6px;
      cursor: pointer;
      margin: 4px;
      font-size: 14px;
    }
    #controls button:hover {
      background: #45B7B8;
    }
    #slit-controls {
      border-top: 1px solid #ddd;
      padding-top: 12px;
      margin-top: 12px;
    }
    #info {
      position: absolute;
      bottom: 15px;
      left: 15px;
      background: rgba(255,255,255,0.9);
      border-radius: 8px;
      padding: 10px 15px;
      z-index: 10;
      font-size: 14px;
      color: #666;
      max-width: 350px;
    }
    #theory-info {
      position: absolute;
      top: 15px;
      right: 15px;
      background: rgba(255,255,255,0.9);
      border-radius: 8px;
      padding: 10px 15px;
      z-index: 10;
      font-size: 13px;
      color: #666;
      max-width: 250px;
    }
  </style>
</head>
<body>
  <div id="controls">
    <h3>🌊 单缝衍射演示</h3>
    <div class="control-group">
      <label>波长 λ:</label>
      <input id="wavelength" type="range" min="20" max="100" value="50">
      <span id="wavelengthval" class="value-display">50 px</span>
    </div>
    <div class="control-group">
      <label>振幅:</label>
      <input id="amplitude" type="range" min="30" max="120" value="80">
      <span id="ampval" class="value-display">80</span>
    </div>
    <div class="control-group">
      <label>频率:</label>
      <input id="frequency" type="range" min="0.5" max="3" step="0.1" value="1.2">
      <span id="freqval" class="value-display">1.2 Hz</span>
    </div>
    
    <div id="slit-controls">
      <div class="control-group">
        <label>缝宽 a:</label>
        <input id="slitwidth" type="range" min="10" max="200" value="60">
        <span id="slitwidthval" class="value-display">60 px</span>
      </div>
      <div class="control-group">
        <label>缝位置:</label>
        <input id="slitposition" type="range" min="100" max="800" value="400">
        <span id="slitposval" class="value-display">400 px</span>
      </div>
      <button onclick="toggleSlit()">🚪 显示/隐藏缝</button>
      <button onclick="resetDemo()">🔄 重置</button>
    </div>
  </div>
  
  <div id="theory-info">
    <strong>衍射理论:</strong><br>
    <div id="diffraction-info">
      <div>λ/a = <span id="ratio-display">0.83</span></div>
      <div>衍射条件: <span id="condition-display">满足</span></div>
      <div>第一暗纹角度: <span id="angle-display">56.4°</span></div>
    </div>
  </div>
  
  <div id="info">
    💡 <strong>操作提示:</strong><br>
    • 调节波长和缝宽观察衍射现象<br>
    • 当 λ ≈ a 时衍射最明显<br>
    • 拖动波源改变入射角度<br>
    • 蓝色=无波动，红色=最大强度<br>
    • 白色竖线表示单缝障碍物
  </div>
  
  <canvas id="wavecanvas"></canvas>

  <script>
    const canvas = document.getElementById('wavecanvas');
    const ctx = canvas.getContext('2d');
    let width = window.innerWidth;
    let height = window.innerHeight;
    canvas.width = width;
    canvas.height = height;

    // 控件
    const wavelengthSlider = document.getElementById('wavelength');
    const ampSlider = document.getElementById('amplitude');
    const freqSlider = document.getElementById('frequency');
    const slitWidthSlider = document.getElementById('slitwidth');
    const slitPosSlider = document.getElementById('slitposition');
    
    const wavelengthVal = document.getElementById('wavelengthval');
    const ampVal = document.getElementById('ampval');
    const freqVal = document.getElementById('freqval');
    const slitWidthVal = document.getElementById('slitwidthval');
    const slitPosVal = document.getElementById('slitposval');

    // 波参数
    let wavelength = parseFloat(wavelengthSlider.value);
    let amplitude = parseFloat(ampSlider.value);
    let frequency = parseFloat(freqSlider.value);
    let slitWidth = parseFloat(slitWidthSlider.value);
    let slitPosition = parseFloat(slitPosSlider.value);
    let waveSpeed = 200; // 固定波速
    let showSlit = true;

    // 波源
    let waveSource = {
      x: 100,
      y: height / 2,
      dragging: false,
      dragOffset: {x: 0, y: 0}
    };

    // 颜色映射函数
    function amplitudeToColor(amplitude, maxAmplitude) {
      let intensity = Math.abs(amplitude) / maxAmplitude;
      intensity = Math.max(0, Math.min(1, intensity));
      
      const bgR = 0, bgG = 17, bgB = 51;
      
      let r, g, b;
      
      if (intensity < 0.2) {
        let t = intensity / 0.2;
        r = bgR + t * (0 - bgR);
        g = bgG + t * (100 - bgG);
        b = bgB + t * (255 - bgB);
      } else if (intensity < 0.4) {
        let t = (intensity - 0.2) / 0.2;
        r = 0;
        g = 100 + t * (200 - 100);
        b = 255;
      } else if (intensity < 0.6) {
        let t = (intensity - 0.4) / 0.2;
        r = 0 + t * (150 - 0);
        g = 200 + t * (255 - 200);
        b = 255 + t * (100 - 255);
      } else if (intensity < 0.8) {
        let t = (intensity - 0.6) / 0.2;
        r = 150 + t * (255 - 150);
        g = 255 + t * (200 - 255);
        b = 100 + t * (0 - 100);
      } else {
        let t = (intensity - 0.8) / 0.2;
        r = 255;
        g = 200 + t * (0 - 200);
        b = 0;
      }
      
      return [Math.round(r), Math.round(g), Math.round(b)];
    }

    // 控件监听器
    wavelengthSlider.oninput = () => {
      wavelength = parseFloat(wavelengthSlider.value);
      wavelengthVal.textContent = wavelength + ' px';
      updateDiffractionInfo();
    };
    ampSlider.oninput = () => {
      amplitude = parseFloat(ampSlider.value);
      ampVal.textContent = amplitude;
    };
    freqSlider.oninput = () => {
      frequency = parseFloat(freqSlider.value);
      freqVal.textContent = frequency.toFixed(1) + ' Hz';
    };
    slitWidthSlider.oninput = () => {
      slitWidth = parseFloat(slitWidthSlider.value);
      slitWidthVal.textContent = slitWidth + ' px';
      updateDiffractionInfo();
    };
    slitPosSlider.oninput = () => {
      slitPosition = parseFloat(slitPosSlider.value);
      slitPosVal.textContent = slitPosition + ' px';
    };

    // 更新衍射信息显示
    function updateDiffractionInfo() {
      let ratio = wavelength / slitWidth;
      document.getElementById('ratio-display').textContent = ratio.toFixed(2);
      
      let condition = ratio > 0.1 ? '满足' : '不明显';
      document.getElementById('condition-display').textContent = condition;
      document.getElementById('condition-display').style.color = ratio > 0.1 ? 'green' : 'red';
      
      // 第一暗纹角度 sin(θ) = λ/a
      if (ratio <= 1) {
        let angle = Math.asin(ratio) * 180 / Math.PI;
        document.getElementById('angle-display').textContent = angle.toFixed(1) + '°';
      } else {
        document.getElementById('angle-display').textContent = '无暗纹';
      }
    }

    // 切换缝的显示
    function toggleSlit() {
      showSlit = !showSlit;
    }

    // 重置演示
    function resetDemo() {
      waveSource.x = 100;
      waveSource.y = height / 2;
      wavelength = 50;
      amplitude = 80;
      frequency = 1.2;
      slitWidth = 60;
      slitPosition = 400;
      
      wavelengthSlider.value = wavelength;
      ampSlider.value = amplitude;
      freqSlider.value = frequency;
      slitWidthSlider.value = slitWidth;
      slitPosSlider.value = slitPosition;
      
      wavelengthVal.textContent = wavelength + ' px';
      ampVal.textContent = amplitude;
      freqVal.textContent = frequency.toFixed(1) + ' Hz';
      slitWidthVal.textContent = slitWidth + ' px';
      slitPosVal.textContent = slitPosition + ' px';
      
      updateDiffractionInfo();
    }

    // 初始化
    updateDiffractionInfo();

    // 鼠标事件处理
    canvas.addEventListener('mousedown', e => {
      const rect = canvas.getBoundingClientRect();
      const mx = e.clientX - rect.left;
      const my = e.clientY - rect.top;

      if (Math.hypot(mx - waveSource.x, my - waveSource.y) < 25) {
        waveSource.dragging = true;
        waveSource.dragOffset.x = mx - waveSource.x;
        waveSource.dragOffset.y = my - waveSource.y;
        canvas.style.cursor = 'grabbing';
      }
    });

    window.addEventListener('mousemove', e => {
      if (waveSource.dragging) {
        const rect = canvas.getBoundingClientRect();
        const mx = e.clientX - rect.left;
        const my = e.clientY - rect.top;
        waveSource.x = Math.max(30, Math.min(slitPosition - 50, mx - waveSource.dragOffset.x));
        waveSource.y = Math.max(30, Math.min(height - 30, my - waveSource.dragOffset.y));
      } else {
        const rect = canvas.getBoundingClientRect();
        const mx = e.clientX - rect.left;
        const my = e.clientY - rect.top;
        if (Math.hypot(mx - waveSource.x, my - waveSource.y) < 25) {
          canvas.style.cursor = 'grab';
        } else {
          canvas.style.cursor = 'default';
        }
      }
    });

    window.addEventListener('mouseup', () => {
      if (waveSource.dragging) {
        waveSource.dragging = false;
        canvas.style.cursor = 'default';
      }
    });

    // 计算单缝衍射的波动
    function calculateDiffractionAmplitude(x, y, time) {
      // 如果在缝的左侧，计算入射波
      if (x < slitPosition) {
        let dx = x - waveSource.x;
        let dy = y - waveSource.y;
        let distance = Math.hypot(dx, dy);

        let k = 2 * Math.PI / wavelength;
        let omega = 2 * Math.PI * frequency;
        let phase = k * distance - omega * time;

        let attenuation = 1 / (1 + distance / 100);
        let amplitudeFactor = Math.pow(amplitude / 80, 1.2); // 增强振幅效果
        return amplitude * amplitudeFactor * attenuation * Math.sin(phase);
      }

      // 如果在缝的右侧，计算衍射波
      if (x > slitPosition && showSlit) {
        // 检查是否在缝的开口范围内能接收到波
        let slitTop = height / 2 - slitWidth / 2;
        let slitBottom = height / 2 + slitWidth / 2;

        // 计算从缝中各点到目标点的贡献（惠更斯原理）
        let totalAmplitude = 0;
        let numPoints = Math.max(10, Math.floor(slitWidth / 5)); // 缝内采样点数

        for (let i = 0; i < numPoints; i++) {
          let slitY = slitTop + (slitBottom - slitTop) * i / (numPoints - 1);

          // 从波源到缝内该点的距离
          let d1 = Math.hypot(slitPosition - waveSource.x, slitY - waveSource.y);
          // 从缝内该点到目标点的距离
          let d2 = Math.hypot(x - slitPosition, y - slitY);

          let totalDistance = d1 + d2;
          let k = 2 * Math.PI / wavelength;
          let omega = 2 * Math.PI * frequency;
          let phase = k * totalDistance - omega * time;

          // 衰减因子
          let attenuation = 1 / (1 + totalDistance / 100);

          // 每个缝内点的贡献
          totalAmplitude += (amplitude / numPoints) * attenuation * Math.sin(phase);
        }

        // 增强振幅效果
        let amplitudeFactor = Math.pow(amplitude / 80, 1.2);
        return totalAmplitude * amplitudeFactor;
      }

      return 0;
    }

    // 窗口自适应
    window.addEventListener('resize', () => {
      width = window.innerWidth;
      height = window.innerHeight;
      canvas.width = width;
      canvas.height = height;

      // 重新调整滑块范围
      slitPosSlider.max = width - 100;
      if (slitPosition > width - 100) {
        slitPosition = width - 100;
        slitPosSlider.value = slitPosition;
        slitPosVal.textContent = slitPosition + ' px';
      }
    });

    // 主绘制函数
    function draw() {
      let now = performance.now() / 1000;

      // 清空画布
      ctx.fillStyle = '#001133';
      ctx.fillRect(0, 0, width, height);

      // 创建图像数据
      let imageData = ctx.createImageData(width, height);
      let data = imageData.data;

      let step = 3; // 采样步长，提高性能

      for (let y = 0; y < height; y += step) {
        for (let x = 0; x < width; x += step) {
          let waveAmplitude = calculateDiffractionAmplitude(x, y, now);
          let color = amplitudeToColor(waveAmplitude, 120); // 使用固定的最大振幅进行归一化

          // 填充像素
          for (let dy = 0; dy < step && y + dy < height; dy++) {
            for (let dx = 0; dx < step && x + dx < width; dx++) {
              let idx = ((y + dy) * width + (x + dx)) * 4;
              data[idx] = color[0];
              data[idx + 1] = color[1];
              data[idx + 2] = color[2];
              data[idx + 3] = 255;
            }
          }
        }
      }

      ctx.putImageData(imageData, 0, 0);

      // 绘制单缝障碍物
      if (showSlit) {
        ctx.fillStyle = 'white';
        ctx.fillRect(slitPosition - 2, 0, 4, height / 2 - slitWidth / 2);
        ctx.fillRect(slitPosition - 2, height / 2 + slitWidth / 2, 4, height / 2 - slitWidth / 2);

        // 绘制缝的边界线
        ctx.strokeStyle = '#FFD700';
        ctx.lineWidth = 2;
        ctx.beginPath();
        ctx.moveTo(slitPosition, height / 2 - slitWidth / 2);
        ctx.lineTo(slitPosition, height / 2 + slitWidth / 2);
        ctx.stroke();
      }

      // 绘制波源
      ctx.beginPath();
      ctx.arc(waveSource.x, waveSource.y, 20, 0, 2 * Math.PI);
      ctx.fillStyle = 'white';
      ctx.fill();
      ctx.strokeStyle = waveSource.dragging ? '#FF6B6B' : '#4ECDC4';
      ctx.lineWidth = 4;
      ctx.stroke();

      // 波源中心
      ctx.beginPath();
      ctx.arc(waveSource.x, waveSource.y, 8, 0, 2 * Math.PI);
      ctx.fillStyle = waveSource.dragging ? '#FF6B6B' : '#4ECDC4';
      ctx.fill();

      requestAnimationFrame(draw);
    }

    // 启动动画
    draw();
  </script>
</body>
</html>
