<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mermaid 中文符号转换测试</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@11.6.0/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .mermaid-container {
            margin: 20px 0;
            text-align: center;
        }
        .code-block {
            background: #f8f8f8;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            margin: 10px 0;
            white-space: pre-wrap;
        }
        .test-button {
            background: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #45a049;
        }
    </style>
</head>
<body>
    <h1>Mermaid 中文符号转换测试</h1>
    
    <div class="test-container">
        <h2>测试1：包含中文冒号的序列图</h2>
        <div class="code-block" id="original-code-1">
sequenceDiagram
    participant 光源
    participant 准直镜
    participant 测试区
    participant 聚焦镜
    participant 相机
    光源->>准直镜: 平行光生成
    准直镜->>测试区：入射 
    测试区->>聚焦镜：：折射偏移 
    聚焦镜->>相机：：形成密度梯度图像
        </div>
        
        <button class="test-button" onclick="testConversion(1)">测试符号转换</button>
        <button class="test-button" onclick="renderMermaid(1)">渲染图表</button>
        
        <div class="code-block" id="converted-code-1" style="display:none;">
            <h3>转换后的代码：</h3>
            <div id="converted-result-1"></div>
        </div>
        
        <div class="mermaid-container">
            <div id="mermaid-1"></div>
        </div>
    </div>

    <div class="test-container">
        <h2>测试2：包含多种中文符号的流程图</h2>
        <div class="code-block" id="original-code-2">
graph TD
    A["开始"] --> B{"判断条件？"}
    B -->|是| C["执行操作１"]
    B -->|否| D["执行操作２"]
    C --> E["结束。"]
    D --> E
        </div>
        
        <button class="test-button" onclick="testConversion(2)">测试符号转换</button>
        <button class="test-button" onclick="renderMermaid(2)">渲染图表</button>
        
        <div class="code-block" id="converted-code-2" style="display:none;">
            <h3>转换后的代码：</h3>
            <div id="converted-result-2"></div>
        </div>
        
        <div class="mermaid-container">
            <div id="mermaid-2"></div>
        </div>
    </div>

    <script>
        // 从原文件中复制的符号转换函数
        function convertChineseSymbolsToEnglish(code) {
          if (!code || typeof code !== 'string') {
            return code;
          }
          
          // 定义中文符号到英文符号的映射
          const symbolMap = {
            '：': ':',    // 中文冒号 -> 英文冒号
            '；': ';',    // 中文分号 -> 英文分号
            '，': ',',    // 中文逗号 -> 英文逗号
            '。': '.',    // 中文句号 -> 英文句号
            '？': '?',    // 中文问号 -> 英文问号
            '！': '!',    // 中文感叹号 -> 英文感叹号
            '（': '(',    // 中文左括号 -> 英文左括号
            '）': ')',    // 中文右括号 -> 英文右括号
            '【': '[',    // 中文左方括号 -> 英文左方括号
            '】': ']',    // 中文右方括号 -> 英文右方括号
            '｛': '{',    // 全角左大括号 -> 英文左大括号
            '｝': '}',    // 全角右大括号 -> 英文右大括号
            '"': '"',    // 中文左双引号 -> 英文双引号
            '"': '"',    // 中文右双引号 -> 英文双引号
            "'": "'",    // 中文左单引号 -> 英文单引号
            "'": "'",    // 中文右单引号 -> 英文单引号
            '－': '-',    // 全角连字符 -> 英文连字符
            '＝': '=',    // 全角等号 -> 英文等号
            '＋': '+',    // 全角加号 -> 英文加号
            '＊': '*',    // 全角星号 -> 英文星号
            '／': '/',    // 全角斜杠 -> 英文斜杠
            '＼': '\\',   // 全角反斜杠 -> 英文反斜杠
            '｜': '|',    // 全角竖线 -> 英文竖线
            '＆': '&',    // 全角和号 -> 英文和号
            '％': '%',    // 全角百分号 -> 英文百分号
            '＃': '#',    // 全角井号 -> 英文井号
            '＠': '@',    // 全角at符号 -> 英文at符号
            '＄': '$',    // 全角美元符号 -> 英文美元符号
            '＾': '^',    // 全角脱字符 -> 英文脱字符
            '～': '~',    // 全角波浪号 -> 英文波浪号
            '｀': '`',    // 全角反引号 -> 英文反引号
            '　': ' '     // 全角空格 -> 英文空格
          };
          
          // 逐个替换中文符号
          let result = code;
          for (const [chineseSymbol, englishSymbol] of Object.entries(symbolMap)) {
            result = result.replace(new RegExp(chineseSymbol, 'g'), englishSymbol);
          }
          
          return result;
        }

        function testConversion(testNum) {
            const originalCode = document.getElementById(`original-code-${testNum}`).textContent.trim();
            const convertedCode = convertChineseSymbolsToEnglish(originalCode);
            
            document.getElementById(`converted-result-${testNum}`).textContent = convertedCode;
            document.getElementById(`converted-code-${testNum}`).style.display = 'block';
            
            console.log(`测试 ${testNum} - 原始代码:`, originalCode);
            console.log(`测试 ${testNum} - 转换后代码:`, convertedCode);
        }

        function renderMermaid(testNum) {
            const originalCode = document.getElementById(`original-code-${testNum}`).textContent.trim();
            const convertedCode = convertChineseSymbolsToEnglish(originalCode);
            const container = document.getElementById(`mermaid-${testNum}`);
            
            // 清空容器
            container.innerHTML = '';
            
            // 初始化 Mermaid
            mermaid.initialize({
                startOnLoad: false,
                theme: 'default',
                securityLevel: 'loose'
            });
            
            // 渲染图表
            try {
                container.innerHTML = convertedCode;
                mermaid.init(undefined, container);
            } catch (error) {
                container.innerHTML = `<div style="color: red; padding: 10px; border: 1px solid red; border-radius: 4px;">
                    渲染错误: ${error.message}<br>
                    <pre style="margin-top: 10px; background: #f8f8f8; padding: 5px;">${convertedCode}</pre>
                </div>`;
            }
        }

        // 页面加载完成后初始化 Mermaid
        document.addEventListener('DOMContentLoaded', function() {
            mermaid.initialize({
                startOnLoad: false,
                theme: 'default',
                securityLevel: 'loose'
            });
        });
    </script>
</body>
</html>
