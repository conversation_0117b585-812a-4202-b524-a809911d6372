import json
import os

def convert_to_srt(json_data):
    srt_content = ""
    for i, (start, end, text) in enumerate(json_data, start=1):
        start_time = format_time(start)
        end_time = format_time(end)
        srt_content += f"{i}\n{start_time} --> {end_time}\n{text.strip()}\n\n"
    return srt_content

def format_time(seconds):
    hours = int(seconds // 3600)
    minutes = int((seconds % 3600) // 60)
    seconds = int(seconds % 60)
    milliseconds = int((seconds % 1) * 1000)
    return f"{hours:02}:{minutes:02}:{seconds:02},{milliseconds:03}"

def json_to_srt(json_file_path):
    # 获取SRT文件路径
    srt_file_path = os.path.splitext(json_file_path)[0] + '.srt'

    # 读取JSON文件
    with open(json_file_path, 'r', encoding='utf-8') as file:
        json_data = json.load(file)

    # 转换为SRT格式
    srt_content = convert_to_srt(json_data)

    # 保存为SRT文件
    with open(srt_file_path, 'w', encoding='utf-8') as file:
        file.write(srt_content)

    print(f"转换完成，已保存为 {srt_file_path}")


if __name__ == "__main__":
    # 示例调用
    json_to_srt(r'D:\github\ragproj\prj_web\video\AQM21_20151216.json')
