<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>视频播放器</title>
    <link rel="stylesheet" href="https://cdn.plyr.io/3.6.8/plyr.css" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/highlight.js@11.7.0/styles/github.min.css">
    <script src="https://cdn.jsdelivr.net/npm/highlight.js@11.7.0/highlight.min.js"></script>
    
    <!-- 加载MathJax -->
    <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.9/MathJax.js?config=TeX-AMS-MML_HTMLorMML"></script>
    <script type="text/x-mathjax-config">
    MathJax.Hub.Config({
        tex2jax: {
            inlineMath: [['$', '$'], ['\\(', '\\)']],
            displayMath: [['$$', '$$'], ['\\[', '\\]']],
            processEscapes: true
        },
        "HTML-CSS": { 
            fonts: ["TeX"],
            scale: 100,
            linebreaks: { automatic: true },
            availableFonts: ["TeX"],
            preferredFont: "TeX",
            webFont: "TeX",
            imageFont: null,
            undefinedFamily: "STIXGeneral,'Arial Unicode MS',serif",
            mtextFontInherit: false,
            EqnChunk: 50,
            EqnChunkFactor: 1.5,
            EqnChunkDelay: 100,
            matchFontHeight: true,
            noReflows: true,
            styles: {
                ".MathJax_Display": {
                    "overflow": "visible !important",
                    "max-width": "100%"
                },
                ".MathJax": {
                    "overflow": "visible !important",
                    "max-width": "100%"
                },
                ".MathJax_SVG_Display": {
                    "overflow": "visible !important",
                    "max-width": "100%"
                },
                ".MathJax_SVG": {
                    "overflow": "visible !important",
                    "max-width": "100%"
                },
                ".MathJax_CHTML": {
                    "overflow": "visible !important",
                    "max-width": "100%"
                }
            }
        },
        messageStyle: "none",
        showProcessingMessages: false,
        showMathMenu: false,
        showMathMenuMSIE: false
    });
    </script>
    <style>
        body, html {
            margin: 0;
            padding: 0;
            height: 100%;
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            overflow-x: hidden;
        }

        .container {
            display: flex;
            width: 100%;
            height: 100vh;
            position: relative;
        }

        .pdf-container {
            width: 40%;
            height: 100%;
            border-right: 2px solid rgba(255, 255, 255, 0.1);
            background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
            position: relative;
            display: none;
            box-shadow: 2px 0 20px rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(10px);
        }

        #pdfViewer {
            width: 100%;
            height: 100%;
            border: none;
            border-radius: 8px;
        }

        .video-container {
            flex: 1;
            height: 100vh;
            background: linear-gradient(135deg, #000000 0%, #1a1a2e 100%);
            position: relative;
            overflow: hidden;
        }

        .video-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at center, transparent 0%, rgba(0, 0, 0, 0.3) 100%);
            pointer-events: none;
            z-index: 1;
        }

        .plyr {
            width: 100%;
            max-width: 1280px;
            margin: 0 auto;
            position: relative;
            z-index: 2;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
        }
        .subtitle-container {
            position: absolute;
            bottom: 30px;
            left: 0;
            right: 0;
            text-align: center;
            color: white;
            z-index: 10;
            padding: 0 30px;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 12px;
            pointer-events: none;
        }

        .subtitle-english, .subtitle-chinese {
            font-size: 28px;
            font-weight: 500;
            padding: 8px 16px;
            background: linear-gradient(135deg, rgba(0, 0, 0, 0.85) 0%, rgba(20, 20, 40, 0.9) 100%);
            border-radius: 12px;
            max-width: 85%;
            margin: 0;
            display: block;
            white-space: pre-wrap;
            text-shadow: 0 2px 8px rgba(0, 0, 0, 0.8);
            pointer-events: none;
            backdrop-filter: blur(8px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
            line-height: 1.4;
        }

        .subtitle-english {
            color: #ffffff;
            font-family: 'Segoe UI', system-ui, sans-serif;
        }

        .subtitle-chinese {
            color: #f0f8ff;
            font-family: 'Microsoft YaHei', 'PingFang SC', 'Hiragino Sans GB', sans-serif;
        }

        #subtitleControls {
            position: absolute;
            top: 20px;
            right: 20px;
            z-index: 1001;
            background: linear-gradient(135deg, rgba(0, 0, 0, 0.7) 0%, rgba(20, 20, 40, 0.8) 100%);
            padding: 8px 12px;
            border-radius: 12px;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
        }

        #subtitleControls:hover {
            background: linear-gradient(135deg, rgba(0, 0, 0, 0.8) 0%, rgba(20, 20, 40, 0.9) 100%);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4);
        }

        #subtitleControls button {
            background: transparent;
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: #ffffff;
            padding: 6px 12px;
            margin: 0 3px;
            cursor: pointer;
            border-radius: 8px;
            transition: all 0.3s ease;
            font-size: 12px;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        #subtitleControls button:hover {
            background: rgba(255, 255, 255, 0.15);
            border-color: rgba(255, 255, 255, 0.5);
            transform: scale(1.05);
        }

        #subtitleControls button.active {
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            color: white;
            border-color: transparent;
            box-shadow: 0 2px 8px rgba(79, 70, 229, 0.4);
        }
        .plyr--fullscreen .subtitle-container {
            bottom: 60px;
            z-index: 10000;
        }
        .plyr--fullscreen .subtitle-english,
        .plyr--fullscreen .subtitle-chinese {
            font-size: 42px;
            padding: 6px 16px;
        }
        :fullscreen .subtitle-container,
        :-webkit-full-screen .subtitle-container,
        :-moz-full-screen .subtitle-container {
            position: absolute;
            bottom: 60px;
            z-index: 10000;
        }
        
        /* 数学公式样式 */
        .math-display {
            display: block;
            margin: 1em 0;
            text-align: center;
            overflow-x: auto;
            max-width: 100%;
            padding: 0.5em 0;
            background-color: #f8f8f8;
            border-radius: 4px;
        }
        .math-inline {
            display: inline-block;
            vertical-align: middle;
            margin: 0 0.1em;
            padding: 0 0.2em;
        }
        /* 确保MathJax生成的元素可见 */
        .MathJax {
            display: inline-block !important;
        }
        .MJX-TEX {
            font-size: 120% !important;
        }
        .calculation-step {
            margin-bottom: 1em;
            padding: 0.5em;
            background-color: #f9f9f9;
            border-radius: 4px;
        }
        .calculation-step:nth-child(odd) {
            background-color: #f0f0f0;
        }
        .question-explanation {
            padding: 1em;
            background-color: #f5f5f5;
            border-radius: 4px;
            margin-top: 0.5em;
        }
        
        /* 响应式设计 */
        @media (max-width: 1200px) {
            .pdf-list-container {
                width: 260px;
                padding: 16px;
            }

            #chatWindow {
                width: 350px;
                height: 480px;
            }
        }

        @media (max-width: 768px) {
            .container {
                flex-direction: column;
            }

            .pdf-list-container {
                width: 100%;
                height: 200px;
                order: 2;
            }

            .video-container {
                order: 1;
                height: 60vh;
            }

            .subtitle-english, .subtitle-chinese {
                font-size: 20px;
                padding: 6px 12px;
            }

            .plyr--fullscreen .subtitle-english,
            .plyr--fullscreen .subtitle-chinese {
                font-size: 28px;
            }

            #chatWindow {
                width: 90vw;
                height: 70vh;
                right: 5vw;
                top: 10px;
            }

            #toggleChat {
                right: 10px;
                top: 80px;
                padding: 10px 16px;
                font-size: 12px;
            }

            #subtitleControls {
                top: 10px;
                right: 10px;
                padding: 6px 8px;
            }

            #subtitleControls button {
                padding: 4px 8px;
                font-size: 10px;
                margin: 0 2px;
            }
        }

        @media (max-width: 480px) {
            .pdf-list-container {
                padding: 12px;
            }

            .pdf-list-header {
                font-size: 16px;
                padding: 8px;
            }

            .question-item {
                padding: 16px;
                margin-bottom: 16px;
            }

            .question-content {
                font-size: 15px;
            }

            .modal-content {
                width: 95%;
                margin: 2% auto;
                padding: 20px;
            }
        }

        /* 加载动画 */
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        @keyframes bounce {
            0%, 20%, 53%, 80%, 100% { transform: translate3d(0,0,0); }
            40%, 43% { transform: translate3d(0,-8px,0); }
            70% { transform: translate3d(0,-4px,0); }
            90% { transform: translate3d(0,-2px,0); }
        }

        .loading {
            animation: pulse 1.5s ease-in-out infinite;
        }

        .bounce-animation {
            animation: bounce 1s ease-in-out;
        }

        /* 聊天窗口样式 */
        #chatWindow {
            position: fixed;
            right: 20px;
            top: 20px;
            width: 380px;
            height: 520px;
            background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
            border-radius: 20px;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(255, 255, 255, 0.1);
            display: flex;
            flex-direction: column;
            z-index: 1000;
            resize: none;
            overflow: hidden;
            min-width: 320px;
            min-height: 420px;
            max-width: 80vw;
            transition: all 0.3s ease;
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        #chatWindow:hover {
            box-shadow: 0 15px 50px rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(255, 255, 255, 0.15);
            transform: translateY(-2px);
        }

        /* 创建自定义拖拽手柄 */
        #chatResizeHandle {
            position: absolute;
            left: 0;
            bottom: 0;
            width: 20px;
            height: 20px;
            cursor: nw-resize;
            background: linear-gradient(
                225deg,
                transparent 0%,
                transparent 45%,
                rgba(99, 102, 241, 0.6) 50%,
                rgba(99, 102, 241, 0.8) 55%,
                transparent 60%,
                transparent 100%
            );
            border-bottom-left-radius: 20px;
            opacity: 0.6;
            transition: all 0.3s ease;
        }

        #chatResizeHandle:hover {
            opacity: 1;
            background: linear-gradient(
                225deg,
                transparent 0%,
                transparent 45%,
                rgba(99, 102, 241, 0.8) 50%,
                rgba(99, 102, 241, 1) 55%,
                transparent 60%,
                transparent 100%
            );
        }

        #chatHeader {
            padding: 16px 20px;
            background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
            color: white;
            border-radius: 20px 20px 0 0;
            cursor: move;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-weight: 600;
            font-size: 16px;
            box-shadow: 0 2px 10px rgba(99, 102, 241, 0.2);
        }

        #minimizeChat:hover {
            background: rgba(255, 255, 255, 0.2) !important;
            transform: scale(1.1);
        }

        #chatHistory {
            flex-grow: 1;
            overflow-y: auto;
            padding: 20px;
            background: linear-gradient(145deg, #fafbff 0%, #f1f5f9 100%);
            scrollbar-width: thin;
            scrollbar-color: rgba(99, 102, 241, 0.3) transparent;
        }

        #chatHistory::-webkit-scrollbar {
            width: 6px;
        }

        #chatHistory::-webkit-scrollbar-track {
            background: transparent;
        }

        #chatHistory::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, rgba(99, 102, 241, 0.3), rgba(139, 92, 246, 0.3));
            border-radius: 3px;
        }

        #chatHistory::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, rgba(99, 102, 241, 0.5), rgba(139, 92, 246, 0.5));
        }

        .chat-input-container {
            padding: 16px 20px;
            border-top: 1px solid rgba(99, 102, 241, 0.1);
            display: flex;
            gap: 12px;
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(10px);
        }

        #userInput {
            flex-grow: 1;
            padding: 12px 16px;
            border: 2px solid rgba(99, 102, 241, 0.2);
            border-radius: 12px;
            resize: none;
            font-size: 14px;
            background: rgba(255, 255, 255, 0.9);
            transition: all 0.3s ease;
            outline: none;
        }

        #userInput:focus {
            border-color: #6366f1;
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
        }

        #sendButton {
            padding: 12px 20px;
            background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
            color: white;
            border: none;
            border-radius: 12px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(99, 102, 241, 0.3);
        }

        #sendButton:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(99, 102, 241, 0.4);
        }

        #sendButton:active {
            transform: translateY(0);
        }

        .message {
            margin-bottom: 16px;
            padding: 14px 18px;
            border-radius: 16px;
            max-width: 85%;
            font-size: 14px;
            line-height: 1.5;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
        }

        .message:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
        }

        .user-message {
            background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
            color: white;
            margin-left: auto;
            border-bottom-right-radius: 4px;
        }

        .ai-message {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            color: #334155;
            margin-right: auto;
            border-bottom-left-radius: 4px;
            border: 1px solid rgba(99, 102, 241, 0.1);
        }

        .system-message {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            color: #92400e;
            margin: 0 auto;
            text-align: center;
            font-size: 13px;
            border-radius: 20px;
            padding: 8px 16px;
            max-width: 90%;
        }

        #toggleChat {
            position: fixed;
            right: 20px;
            top: 120px;
            z-index: 1001;
            padding: 12px 20px;
            background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
            border: none;
            border-radius: 12px;
            cursor: pointer;
            color: white;
            font-weight: 600;
            font-size: 14px;
            box-shadow: 0 4px 16px rgba(99, 102, 241, 0.3);
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        #toggleChat:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(99, 102, 241, 0.4);
        }

        /* 添加PDF控制按钮样式 */
        #pdfControls {
            position: absolute;
            top: 10px;
            right: 10px;
            z-index: 1001;
            background: rgba(0,0,0,0.5);
            padding: 5px;
            border-radius: 4px;
        }

        #pdfControls button {
            background: transparent;
            border: 1px solid #333;
            color: #333;
            padding: 5px 10px;
            margin: 0 2px;
            cursor: pointer;
            border-radius: 3px;
        }

        /* 更新PDF相关样式 */
        .pdf-list-container {
            width: 280px;
            background: linear-gradient(145deg, #f8fafc 0%, #e2e8f0 100%);
            padding: 20px;
            border-right: 2px solid rgba(99, 102, 241, 0.1);
            display: flex;
            flex-direction: column;
            box-shadow: inset -2px 0 10px rgba(0, 0, 0, 0.05);
        }

        .pdf-list-header {
            font-size: 18px;
            font-weight: 700;
            margin-bottom: 20px;
            color: #1e293b;
            text-align: center;
            padding: 12px;
            background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
            color: white;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
        }

        .pdf-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .pdf-item {
            padding: 12px 16px;
            margin-bottom: 12px;
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            border: 2px solid rgba(99, 102, 241, 0.1);
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
            color: #475569;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }

        .pdf-item:hover {
            background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
            color: white;
            border-color: transparent;
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(99, 102, 241, 0.3);
        }

        .pdf-viewer-container {
            display: none;
            position: fixed;
            width: 850px;
            height: 650px;
            background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
            border: 2px solid rgba(99, 102, 241, 0.2);
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
            overflow: hidden;
            resize: both;
            z-index: 1000;
            border-radius: 16px;
            backdrop-filter: blur(20px);
        }

        #pdfViewer {
            width: 100%;
            height: calc(100% - 40px);
            border: none;
            margin-top: 40px;
            pointer-events: auto;
        }

        .pdf-viewer-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px 20px;
            background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
            border-bottom: 2px solid rgba(255, 255, 255, 0.1);
            border-radius: 16px 16px 0 0;
            cursor: move;
            user-select: none;
            color: white;
        }

        .pdf-viewer-title {
            font-weight: 700;
            color: white;
            font-size: 16px;
        }

        .pdf-controls {
            display: flex;
            gap: 8px;
        }

        .pdf-control-btn {
            padding: 8px 12px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 12px;
            font-weight: 600;
            backdrop-filter: blur(10px);
        }

        .pdf-control-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.5);
            transform: scale(1.05);
        }

        /* 更新调整手柄样式，增加可点击区域 */
        .pdf-resize-handle {
            position: absolute;
            width: 20px;
            height: 20px;
            bottom: 0;
            right: 0;
            cursor: se-resize;
            z-index: 1002; /* 确保手柄在最上层 */
        }

        /* 添加视觉指示器 */
        .pdf-resize-handle::after {
            content: '';
            position: absolute;
            right: 4px;
            bottom: 4px;
            width: 10px;
            height: 10px;
            background: linear-gradient(
                135deg,
                transparent 0%,
                transparent 50%,
                #666 50%,
                #666 60%,
                transparent 60%,
                transparent 100%
            );
        }

        /* 调整手柄悬停效果 */
        .pdf-resize-handle:hover::after {
            opacity: 0.8;
        }

        .knowledge-graph-container {
            margin-top: 20px;
            text-align: center;
        }

        #showKnowledgeGraphBtn {
            width: 100%;
            padding: 14px 16px;
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
            border: none;
            border-radius: 12px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        #showKnowledgeGraphBtn:hover {
            background: linear-gradient(135deg, #059669 0%, #047857 100%);
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(16, 185, 129, 0.4);
        }

        #showKnowledgeGraphBtn:active {
            transform: translateY(0);
        }
        
        /* 模态窗口样式 */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            overflow: auto;
            background: rgba(0, 0, 0, 0.6);
            backdrop-filter: blur(8px);
            animation: fadeIn 0.3s ease;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(-30px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        /* 知识图谱模态窗口样式增强 */
        .modal-content {
            background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
            margin: 3% auto;
            padding: 30px;
            border: 2px solid rgba(99, 102, 241, 0.1);
            width: 90%;
            max-width: 1200px;
            border-radius: 20px;
            transition: all 0.3s ease;
            position: relative;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
            animation: slideIn 0.4s ease;
        }
        
        .modal-header {
            padding: 0 0 20px 0;
            border-bottom: 2px solid rgba(99, 102, 241, 0.1);
            position: relative;
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .modal-header h2 {
            margin: 0;
            color: #1e293b;
            font-size: 24px;
            font-weight: 700;
            background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .close-modal {
            color: #64748b;
            font-size: 32px;
            font-weight: bold;
            cursor: pointer;
            margin-left: 20px;
            transition: all 0.3s ease;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            background: rgba(99, 102, 241, 0.1);
        }

        .close-modal:hover,
        .close-modal:focus {
            color: #ef4444;
            background: rgba(239, 68, 68, 0.1);
            transform: scale(1.1);
        }
        
        /* 修改模态窗口主体样式，添加滚动条 */
        .modal-body {
            padding: 20px 0;
            height: 600px;
            transition: height 0.3s ease;
            overflow: auto; /* 添加滚动条 */
        }
        
        /* 添加最大化按钮样式 */
        .graph-controls {
            display: flex;
            gap: 10px;
            align-items: center; /* 垂直居中对齐 */
            margin-left: 90px; /* 整体向左移动 */
        }
        
        .graph-control-btn {
            padding: 5px 10px;
            background-color: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s ease;
            position: relative;
            top: -2px; /* 向上微调位置 */
        }
        
        .graph-control-btn:hover {
            background-color: #e9ecef;
        }
        
        /* 下载按钮特殊样式 */
        .download-btn {
            background-color: #28a745;
            color: white;
            border-color: #28a745;
        }
        
        .download-btn:hover {
            background-color: #218838;
            border-color: #1e7e34;
        }
        
        /* 最大化/还原按钮特殊样式 */
        #maximizeGraphBtn {
            background-color: #007bff;
            color: white;
            border-color: #007bff;
            font-weight: 500;
        }
        
        #maximizeGraphBtn:hover {
            background-color: #0069d9;
            border-color: #0062cc;
        }
        
        /* 最大化状态样式 */
        .modal-maximized .modal-content {
            width: 98%;
            max-width: 98%;
            height: 95vh;
            margin: 1vh auto;
        }
        
        .modal-maximized .modal-body {
            height: calc(95vh - 120px);
        }

        /* 添加知识图谱编辑区域的样式 */
        .graph-editor-container {
            margin-top: 15px;
            display: none;
        }

        #graphTextEditor {
            width: 100%;
            height: 200px;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            resize: vertical;
            margin-bottom: 15px;
        }

        /* 按钮容器 */
        .graph-buttons-container {
            display: flex;
            justify-content: space-between;
            margin-bottom: 15px;
        }

        #updateGraphBtn {
            width: 45%;
            padding: 10px;
            background-color: #2196F3;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: background-color 0.3s;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        #updateGraphBtn:hover {
            background-color: #0b7dda;
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        
        #deleteGraphBtn {
            width: 45%;
            padding: 10px;
            background-color: #f44336;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: background-color 0.3s;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        #deleteGraphBtn:hover {
            background-color: #d32f2f;
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }

        .editor-status {
            margin-top: 10px;
            font-size: 13px;
            color: #666;
            text-align: center;
        }

        /* 添加全局样式，确保数学公式正确显示 */
        .MathJax {
            overflow-x: auto;
            max-width: 100%;
        }
        /* 确保块级公式居中 */
        .math-display {
            display: block;
            text-align: center;
            margin: 1em auto;
            overflow-x: auto;
            max-width: 100%;
        }
        /* 修复行内公式样式 */
        .math-inline {
            display: inline-block;
            vertical-align: middle;
        }
        .message .MathJax {
            overflow-x: auto;
            max-width: 100%;
        }

        /* 添加题目相关样式 */
        .questions-container {
            margin-top: 20px;
            text-align: center;
        }

        #showQuestionsBtn {
            width: 100%;
            padding: 14px 16px;
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            color: white;
            border: none;
            border-radius: 12px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        #showQuestionsBtn:hover {
            background: linear-gradient(135deg, #d97706 0%, #b45309 100%);
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(245, 158, 11, 0.4);
        }

        #showQuestionsBtn:active {
            transform: translateY(0);
        }
        
        .questions-controls {
            display: flex;
            gap: 10px;
            align-items: center;
            margin-left: 90px;
        }
        
        .questions-control-select {
            padding: 5px 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .questions-control-btn {
            padding: 5px 10px;
            background-color: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s ease;
        }
        
        .questions-control-btn:hover {
            background-color: #e9ecef;
        }
        
        /* 为题目容器添加特定样式 */
        #questionsContainer {
            max-height: 500px; /* 设置最大高度 */
            overflow-y: auto; /* 添加垂直滚动条 */
            padding: 0 20px; /* 添加左右内边距 */
        }
        
        /* 确保题目状态信息显示在底部 */
        .questions-status {
            margin-top: 15px;
            padding: 0 20px;
            font-style: italic;
            color: #666;
        }
        
        /* 题目样式 */
        .question-item {
            background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
            border: 2px solid rgba(99, 102, 241, 0.1);
            border-radius: 16px;
            padding: 24px;
            margin-bottom: 24px;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .question-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
        }

        .question-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
            border-color: rgba(99, 102, 241, 0.2);
        }

        .question-content {
            font-size: 17px;
            margin-bottom: 20px;
            line-height: 1.6;
            color: #1e293b;
            font-weight: 500;
        }

        .question-options {
            margin-bottom: 20px;
        }

        .question-option {
            padding: 12px 16px;
            margin-bottom: 12px;
            background: linear-gradient(135deg, #ffffff 0%, #f1f5f9 100%);
            border: 2px solid rgba(99, 102, 241, 0.1);
            border-radius: 12px;
            transition: all 0.3s ease;
            cursor: pointer;
            font-weight: 500;
            color: #475569;
        }

        .question-option:hover {
            background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
            color: white;
            border-color: transparent;
            transform: translateX(4px);
        }

        .show-explanation-btn {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 12px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
        }

        .show-explanation-btn:hover {
            background: linear-gradient(135deg, #059669 0%, #047857 100%);
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(16, 185, 129, 0.4);
        }

        .question-explanation {
            display: none;
            margin-top: 20px;
            padding: 20px;
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
            border-radius: 12px;
            border-left: 4px solid #10b981;
            line-height: 1.7;
            color: #0f172a;
            box-shadow: 0 2px 8px rgba(16, 185, 129, 0.1);
        }

        .calculation-step {
            margin-bottom: 12px;
            padding-bottom: 12px;
            border-bottom: 1px dashed #ddd;
        }

        .calculation-step:last-child {
            border-bottom: none;
        }

        .final-answer {
            font-weight: bold;
            margin: 15px 0;
            padding: 10px;
            background-color: #e8f5e9;
            border-radius: 4px;
        }

        .key-points {
            margin-top: 15px;
            padding: 10px;
            background-color: #fff3e0;
            border-radius: 4px;
        }

        /* 数学公式样式 */
        .katex {
            font-size: 1.1em;
        }

        /* 题目控制区域样式 */
        .questions-controls {
            display: flex;
            gap: 10px;
            align-items: center;
            margin-left: 20px;
        }

        .questions-control-select {
            padding: 5px 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: #f8f9fa;
            font-size: 14px;
        }

        .questions-count-container {
            display: flex;
            align-items: center;
            gap: 5px;
            font-size: 14px;
        }

        .questions-count-container label {
            white-space: nowrap;
            color: #555;
        }

        .questions-control-btn {
            padding: 8px 16px;
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
        }

        .questions-control-btn:hover {
            background: linear-gradient(135deg, #059669 0%, #047857 100%);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
        }

        /* 编辑题目的样式 */
        .edit-question-btn {
            background-color: #2196F3;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 5px 10px;
            margin-left: 10px;
            cursor: pointer;
            font-size: 12px;
        }

        .edit-question-btn:hover {
            background-color: #0b7dda;
        }

        #editQuestionModal {
            display: none;
            position: fixed;
            z-index: 1001;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            overflow: auto;
            background-color: rgba(0,0,0,0.7);
        }

        .edit-modal-content {
            background-color: #fefefe;
            margin: 5% auto;
            padding: 20px;
            border: 1px solid #888;
            width: 80%;
            max-width: 800px;
            max-height: 80vh;
            overflow-y: auto;
            border-radius: 5px;
        }

        .edit-modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            border-bottom: 1px solid #ddd;
            padding-bottom: 10px;
        }

        .close-edit-modal {
            color: #aaa;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .close-edit-modal:hover {
            color: #333;
        }

        #questionJsonEditor {
            width: 100%;
            height: 400px;
            font-family: monospace;
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 4px;
            margin-bottom: 15px;
            white-space: pre;
            overflow: auto;
            font-size: 14px;
            line-height: 1.5;
        }

        .edit-modal-buttons {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
        }

        .edit-modal-buttons button {
            padding: 8px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
        }

        .save-btn {
            background-color: #4CAF50;
            color: white;
        }

        .save-btn:hover {
            background-color: #45a049;
        }

        .cancel-btn {
            background-color: #f44336;
            color: white;
        }

        .cancel-btn:hover {
            background-color: #d32f2f;
        }

        .validate-btn {
            background-color: #17a2b8;
            color: white;
        }

        .validate-btn:hover {
            background-color: #138496;
        }

        .debug-btn {
            background-color: #6c757d;
            color: white;
        }

        .debug-btn:hover {
            background-color: #5a6268;
        }

        /* 题目状态样式 */
        .questions-status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .questions-status.idle {
            background-color: #f8f9fa;
            color: #6c757d;
        }

        .questions-status.loading,
        .questions-status.generating,
        .questions-status.updating,
        .questions-status.deleting {
            background-color: #cce5ff;
            color: #0066cc;
            animation: pulse 1.5s infinite;
        }

        .questions-status.error {
            background-color: #f8d7da;
            color: #721c24;
        }

        .questions-status.success {
            background-color: #d4edda;
            color: #155724;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }

        /* 无题目状态 */
        .no-questions {
            text-align: center;
            padding: 40px 20px;
            color: #6c757d;
            font-style: italic;
        }

        /* 错误消息样式 */
        .error-message {
            background-color: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            border: 1px solid #f5c6cb;
        }

        /* 加载动画 */
        .loading-questions {
            text-align: center;
            padding: 40px 20px;
            color: #0066cc;
        }

        .loading-questions::after {
            content: '';
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #0066cc;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-left: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 全局滚动条样式 */
        * {
            scrollbar-width: thin;
            scrollbar-color: rgba(99, 102, 241, 0.3) transparent;
        }

        *::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }

        *::-webkit-scrollbar-track {
            background: rgba(0, 0, 0, 0.05);
            border-radius: 4px;
        }

        *::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, rgba(99, 102, 241, 0.4), rgba(139, 92, 246, 0.4));
            border-radius: 4px;
            transition: all 0.3s ease;
        }

        *::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, rgba(99, 102, 241, 0.6), rgba(139, 92, 246, 0.6));
        }

        /* 解决公式滑动条问题的样式 */
        .MathJax_Display {
            overflow: visible !important;
            margin: 1em 0;
            padding: 0.5em 0;
            max-width: 100%;
        }
        .MathJax {
            display: inline-block !important;
            overflow: visible !important;
            max-width: 100%;
        }
        .MathJax_SVG_Display {
            overflow: visible !important;
            max-width: 100%;
        }
        .MathJax_SVG {
            display: inline-block !important;
            overflow: visible !important;
            max-width: 100%;
        }
        .MathJax_CHTML {
            max-width: 100%;
            overflow: visible !important;
        }
        /* 确保公式容器足够宽 */
        .math-display {
            width: 100%;
            overflow: visible !important;
            margin: 1em 0;
            padding: 0.5em 0;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border-radius: 8px;
            border: 1px solid rgba(99, 102, 241, 0.1);
        }
        .math-inline {
            display: inline-block;
            max-width: 100%;
            overflow: visible !important;
            vertical-align: middle;
            padding: 0 0.4em;
            background: rgba(99, 102, 241, 0.05);
            border-radius: 4px;
            margin: 0 2px;
        }

        /* 添加一些微交互效果 */
        .plyr {
            transition: all 0.3s ease;
        }

        .plyr:hover {
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.5);
        }

        /* 优化按钮焦点状态 */
        button:focus {
            outline: 2px solid rgba(99, 102, 241, 0.5);
            outline-offset: 2px;
        }

        /* 添加选择文本的样式 */
        ::selection {
            background: rgba(99, 102, 241, 0.2);
            color: #1e293b;
        }

        ::-moz-selection {
            background: rgba(99, 102, 241, 0.2);
            color: #1e293b;
        }
    </style>
    <!-- 添加 Markdown 支持 -->
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    
    <!-- 添加 KaTeX 支持 -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.16.9/dist/katex.min.css">
    <script src="https://cdn.jsdelivr.net/npm/katex@0.16.9/dist/katex.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/katex@0.16.9/dist/contrib/auto-render.min.js"></script>

    <!-- 添加代码高亮支持 -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/highlight.js@11.7.0/styles/github.min.css">
    <script src="https://cdn.jsdelivr.net/npm/highlight.js@11.7.0/highlight.min.js"></script>
</head>
<body>
    <div class="container">
        <div class="pdf-list-container">
            <div class="pdf-list-header">参考资料</div>
            <ul class="pdf-list" id="pdfList"></ul>
            <!-- 添加知识图谱按钮 -->
            <div class="knowledge-graph-container">
                <button id="showKnowledgeGraphBtn" class="btn btn-primary">AI生成知识图谱</button>
                <!-- 添加知识图谱编辑区域 -->
                <div class="graph-editor-container" id="graphEditorContainer">
                    <textarea id="graphTextEditor" placeholder="知识图谱关系数据将显示在这里..."></textarea>
                    <div class="graph-buttons-container">
                        <button id="updateGraphBtn" style="display: none;">更新知识图谱</button>
                        <button id="deleteGraphBtn" style="display: none;">删除知识图谱</button>
                    </div>
                    <div class="editor-status" id="editorStatus"></div>
                </div>
            </div>
            <!-- 添加题目生成按钮和容器 -->
            <div class="questions-container">
                <button id="showQuestionsBtn">AI生成题目</button>
            </div>
        </div>

        <!-- 添加题目模态窗口 -->
        <div id="questionsModal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2>课程题目</h2>
                    <div class="questions-controls">
                        <select id="questionType" class="questions-control-select">
                            <option value="choice">选择题</option>
                            <option value="calculation">计算题</option>
                        </select>
                        <div class="questions-count-container">
                            <label for="questionCount">题目数量:</label>
                            <select id="questionCount" class="questions-control-select">
                                <option value="1">1题</option>
                                <option value="2">2题</option>
                                <option value="3" selected>3题</option>
                                <option value="5">5题</option>
                                <option value="10">10题</option>
                            </select>
                        </div>
                        <button id="generateQuestionsBtn" class="questions-control-btn">生成题目</button>
                        <button id="updateQuestionsBtn" class="questions-control-btn" style="display: none;">更新题目</button>
                        <button id="deleteQuestionsBtn" class="questions-control-btn" style="display: none;">删除题目</button>
                        <span class="close-modal">&times;</span>
                    </div>
                </div>
                <div class="modal-body">
                    <div id="questionsContainer"></div>
                    <div id="questionsStatus" class="questions-status"></div>
                </div>
            </div>
        </div>

        <div class="video-container">
            <video id="player" playsinline>
                <source src="" type="video/mp4">
            </video>
            <div id="subtitle" class="subtitle-container"></div>
            <div id="subtitleControls">
                <button id="subtitleOff">Off</button>
                <button id="subtitleEnglish">English</button>
                <button id="subtitleChinese">中文</button>
                <button id="subtitleBoth" class="active">Both</button>
            </div>
        </div>
    </div>

    <button id="toggleChat">显示对话</button>
    <div id="chatWindow" style="display: none;">
        <div id="chatHeader">
            <span>视频对话助手</span>
            <button id="minimizeChat" style="background: none; border: none; color: white; cursor: pointer; font-size: 18px; font-weight: bold; padding: 4px 8px; border-radius: 4px; transition: all 0.3s ease;">—</button>
        </div>
        <div id="chatHistory"></div>
        <div class="chat-input-container">
            <textarea id="userInput" placeholder="输入你的问题..." rows="2"></textarea>
            <button id="sendButton">发送</button>
        </div>
        <div id="chatResizeHandle"></div>
    </div>

    <div class="pdf-viewer-container" id="pdfViewerContainer">
        <div class="pdf-viewer-header" id="pdfViewerHeader">
            <span class="pdf-viewer-title" id="pdfViewerTitle"></span>
            <div class="pdf-controls">
                <button class="pdf-control-btn" id="maximizePdf">最大化</button>
                <button class="pdf-control-btn" id="minimizePdf">最小化</button>
                <button class="pdf-control-btn" id="closePdf">关闭</button>
            </div>
        </div>
        <iframe id="pdfViewer"></iframe>
        <div class="pdf-resize-handle" id="pdfResizeHandle"></div>
    </div>

    <!-- 知识图谱弹出窗口 -->
    <div id="knowledgeGraphModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>知识图谱</h2>
                <div class="graph-controls">
                    <button id="downloadGraphBtn" class="graph-control-btn download-btn">下载图片</button>
                    <button id="maximizeGraphBtn" class="graph-control-btn">最大化</button>
                    <span class="close-modal">&times;</span>
                </div>
            </div>
            <div class="modal-body">
                <div id="knowledgeGraphContainer" style="width: 100%; height: 100%;"></div>
            </div>
        </div>
    </div>

    <!-- 题目编辑模态框 -->
    <div id="editQuestionModal" class="modal">
        <div class="edit-modal-content">
            <div class="edit-modal-header">
                <h3>编辑题目</h3>
                <span class="close-edit-modal">&times;</span>
            </div>
            <p>直接修改下方的JSON内容，点击保存即可更新题目</p>
            <div id="jsonValidationStatus" style="margin-bottom: 10px; padding: 5px; border-radius: 3px; display: none;"></div>
            <textarea id="questionJsonEditor"></textarea>
            <div class="edit-modal-buttons">
                <button class="validate-btn" id="validateJsonBtn">验证JSON</button>
                <button class="save-btn" id="saveQuestionBtn">保存</button>
                <button class="cancel-btn" id="cancelEditBtn">取消</button>
                <button class="debug-btn" id="debugJsonBtn" style="background-color: #6c757d; color: white;">调试信息</button>
            </div>
        </div>
    </div>

    <script src="https://cdn.plyr.io/3.6.8/plyr.polyfilled.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <script>
        // 获取 URL 参数
        const urlParams = new URLSearchParams(window.location.search);
        const user = urlParams.get('user');
        const key = urlParams.get('key');
        const token = urlParams.get('token');
        const videoName = urlParams.get('filename');
        const folder = urlParams.get('folder');
        
        // 全局变量
        var doc_id = '';
        var dataset_id = '';
        var if_vip_user = false; // 默认为false
        var conversationId = '1'; // 默认对话ID
        var messageHistory = []; // 消息历史
        
        // 确保全局变量在window对象上也可用
        window.doc_id = doc_id;
        window.dataset_id = dataset_id;
        window.if_vip_user = if_vip_user;
        window.conversationId = conversationId;
        window.messageHistory = messageHistory;
        
        if (!videoName) {
            console.error('No filename provided');
        }
        
        function getUser() {
            return user || '';
        }
        
        function getKey() {
            return key || '';
        }
        
        function getToken() {
            return token || '';
        }
        
        function getFolder() {
            return folder || '';
        }
      
        function getFilename() {
            return videoName || '';
        }
        
        // 构建视频路径
        const videoPath = folder ? `${folder}/${videoName}` : videoName;
        
        // 构建带认证参数的 URL
        const authParams = new URLSearchParams({
            user: user,
            key: key,
            token: token
        }).toString();
        
        // 构建最终的 URL
        const videoUrl = `/video/${encodeURIComponent(videoPath)}?${authParams}`;
        const subtitleUrl = `/subtitles/${encodeURIComponent(videoPath)}?${authParams}`;

        // 设置视频源
        document.addEventListener('DOMContentLoaded', () => {
            const videoSource = document.querySelector('#player source');
            if (videoSource) {
                videoSource.src = videoUrl;
                console.log('Video URL:', videoUrl);
                
                // 重新加载视频元素
                const videoElement = document.getElementById('player');
                if (videoElement) {
                    videoElement.load();
                }
            }
            
            // 添加这行代码来初始化聊天功能
            initializeVideoChat().then(() => {
                // 在初始化完成后检查VIP状态并显示/隐藏按钮
                console.log("初始化完成后检查if_vip_user:", if_vip_user);
                appendMessage('ai', '你好！我是视频对话助手，请问有什么可以帮助你的吗？');
                checkVipUserStatus();
            });
            
            // 加载PDF匹配信息
            loadPdfMatch();

            // 确保聊天按钮事件监听器始终有效
            setupChatToggleButton();
        });

        // 设置聊天切换按钮的函数
        function setupChatToggleButton() {
            const toggleChat = document.getElementById('toggleChat');
            const chatWindow = document.getElementById('chatWindow');
            
            // 移除可能存在的旧事件监听器，防止重复绑定
            toggleChat.removeEventListener('click', toggleChatWindow);
            
            // 添加新的事件监听器
            toggleChat.addEventListener('click', toggleChatWindow);
            
            // 切换聊天窗口显示/隐藏的函数
            function toggleChatWindow() {
                console.log('Toggle chat button clicked');
                if (chatWindow.style.display === 'none' || !chatWindow.style.display) {
                    chatWindow.style.display = 'flex';
                    toggleChat.textContent = '隐藏对话';
                    console.log('Chat window displayed');
                } else {
                    chatWindow.style.display = 'none';
                    toggleChat.textContent = '显示对话';
                    console.log('Chat window hidden');
                }
            }
            
            // 同样确保最小化按钮也能正常工作
            const minimizeChat = document.getElementById('minimizeChat');
            minimizeChat.removeEventListener('click', minimizeChatWindow);
            minimizeChat.addEventListener('click', minimizeChatWindow);
            
            function minimizeChatWindow() {
                chatWindow.style.display = 'none';
                toggleChat.textContent = '显示对话';
                console.log('Chat window minimized');
            }
        }

        // 检查VIP用户状态并显示/隐藏按钮
        function checkVipUserStatus() {
            console.log("检查VIP用户状态:", if_vip_user);
            var updateGraphBtn = document.getElementById('updateGraphBtn');
            if (updateGraphBtn) {
                if (if_vip_user) {
                    console.log("用户是VIP，显示更新按钮");
                    updateGraphBtn.style.display = 'block';
                } else {
                    console.log("用户不是VIP，隐藏更新按钮");
                    updateGraphBtn.style.display = 'none';
                }
            } else {
                console.error("找不到更新按钮元素!");
            }
        }

        // 初始化时获取 doc_id 和其他必要信息
        async function initializeVideoChat() {
            const MAX_RETRIES = 3;
            let retryCount = 0;
            // appendMessage('ai', '你好！我是视频对话助手，请问有什么可以帮助你的吗？');
            async function tryInitialize() {
                try {
                    // 打印初始化参数，方便调试
                    console.log('Initialization parameters:', {
                        filename: videoName,
                        user: user,
                        key: key,
                        token: token?.substring(0, 10) + '...', // 只显示token的前10个字符
                        folder: folder
                    });

                    // 确保所有参数都有值
                    if (!videoName || !user || !key || !token) {
                        const missingParams = [];
                        if (!videoName) missingParams.push('filename');
                        if (!user) missingParams.push('user');
                        if (!key) missingParams.push('key');
                        if (!token) missingParams.push('token');
                        
                        const errorMsg = `错误: 缺少必要的参数 (${missingParams.join(', ')})`;
                        console.error(errorMsg);
                        appendMessage('system', errorMsg);
                        disableChat();
                        return;
                    }

                    // 构建最简单的请求数据结构，确保所有值都是字符串类型
                    const requestData = {
                        filename: String(videoName),
                        user: String(user),
                        key: String(key),
                        token: String(token)
                    };

                    // 只在folder存在且不为空时添加
                    if (folder && folder.trim()) {
                        requestData.folder = String(folder.trim());
                    }

                    console.log('Sending request with data:', {
                        ...requestData,
                        token: requestData.token.substring(0, 10) + '...' // 日志中隐藏完整token
                    });
                    
                    // 发送初始化请求
                    const response = await fetch('/api/init_video_chat', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Accept': 'application/json'
                        },
                        body: JSON.stringify(requestData)
                    });

                    // 获取响应内容
                    let responseText = await response.text();
                    let responseData;
                    
                    try {
                        responseData = JSON.parse(responseText);
                    } catch (e) {
                        console.error('Failed to parse response as JSON:', responseText);
                        throw new Error('服务器返回了无效的JSON数据');
                    }

                    if (!response.ok) {
                        console.error('Server error response:', {
                            status: response.status,
                            statusText: response.statusText,
                            data: responseData
                        });
                        
                        // 特殊处理500错误
                        if (response.status === 500) {
                            // 如果是Invalid argument错误，可能是参数格式问题
                            if (responseText.includes('Invalid argument') || responseText.includes('Errno 22')) {
                                console.log('检测到Invalid argument错误，尝试使用简化参数...');
                                
                                // 尝试使用更简化的请求数据
                                const simpleRequestData = {
                                    filename: String(videoName),
                                    user: String(user),
                                    key: String(key),
                                    token: String(token)
                                };
                                
                                // 不添加folder参数，除非绝对必要
                                if (folder && typeof folder === 'string' && folder.trim() && 
                                    !folder.includes('/') && !folder.includes('\\')) {
                                    simpleRequestData.folder = folder.trim();
                                }
                                
                                console.log('使用简化参数重试:', {
                                    ...simpleRequestData,
                                    token: simpleRequestData.token.substring(0, 10) + '...'
                                });
                                
                                // 使用简化参数重试
                                const retryResponse = await fetch('/api/init_video_chat', {
                                    method: 'POST',
                                    headers: {
                                        'Content-Type': 'application/json',
                                        'Accept': 'application/json'
                                    },
                                    body: JSON.stringify(simpleRequestData)
                                });
                                
                                if (retryResponse.ok) {
                                    const retryData = await retryResponse.json();
                                    if (retryData.success) {
                                        console.log('使用简化参数成功初始化:', retryData);
                                        
                                        // 更新doc_id和dataset_id
                                        doc_id = retryData.doc_id ? String(retryData.doc_id) : '';
                                        dataset_id = retryData.dataset_id ? String(retryData.dataset_id) : '';
                                        if_vip_user = Boolean(retryData.if_vip_user);
                                        
                                        initializeChat();
                                        return;
                                    }
                                }
                            }
                        }
                        
                        throw new Error(
                            typeof responseData === 'object' && responseData.message
                                ? responseData.message
                                : `服务器错误 (${response.status})`
                        );
                    }

                    if (!responseData.success) {
                        throw new Error(responseData.message || '初始化失败，服务器未返回成功状态');
                    }

                    // 成功初始化
                    console.log('Chat initialization successful:', {
                        doc_id: responseData.doc_id,
                        dataset_id: responseData.dataset_id,
                        if_vip_user: responseData.if_vip_user
                    });

                    // 确保doc_id和dataset_id是字符串类型
                    doc_id = responseData.doc_id ? String(responseData.doc_id) : '';
                    dataset_id = responseData.dataset_id ? String(responseData.dataset_id) : '';
                    if_vip_user = Boolean(responseData.if_vip_user);
                    
                    // 验证doc_id是否有效
                    if (!doc_id) {
                        console.warn('Warning: doc_id is empty or invalid');
                        appendMessage('system', '警告: 文档ID无效，聊天功能可能受限');
                    }
                    
                    console.log('初始化完成，全局变量状态:', {
                        doc_id: doc_id,
                        dataset_id: dataset_id,
                        if_vip_user: if_vip_user
                    });
                    
                    // 确保全局变量已正确设置
                    window.doc_id = doc_id;
                    window.dataset_id = dataset_id;
                    window.if_vip_user = if_vip_user;
                    
                    initializeChat();

                } catch (error) {
                    retryCount++;
                    if (retryCount < MAX_RETRIES) {
                        console.log(`Initialization attempt ${retryCount} failed, retrying...`);
                        
                        // 如果是Invalid argument错误，可能需要更长的延迟
                        const delay = error.message && 
                                     (error.message.includes('Invalid argument') || 
                                      error.message.includes('Errno 22'))
                                     ? 2000 * retryCount  // 更长的延迟
                                     : 1000 * retryCount; // 标准延迟
                        
                        await new Promise(resolve => setTimeout(resolve, delay));
                        
                        // 如果是第二次重试，尝试清除可能的缓存问题
                        if (retryCount === 2) {
                            console.log('尝试清除可能的缓存问题...');
                            // 清空消息历史
                            messageHistory = [];
                        }
                        
                        return tryInitialize();
                    }
                    
                    console.error('Chat initialization failed after all retries:', error);
                    
                    // 特殊处理Invalid argument错误
                    if (error.message && (error.message.includes('Invalid argument') || error.message.includes('Errno 22'))) {
                        appendMessage('system', '正在准备对话资源，请稍候...');
                        
                        // 设置一个定时器，显示加载状态
                        let loadingDots = 0;
                        const loadingInterval = setInterval(() => {
                            loadingDots = (loadingDots + 1) % 4;
                            const dots = '.'.repeat(loadingDots);
                            const lastMessage = document.querySelector('.message.system:last-child');
                            if (lastMessage) {
                                lastMessage.textContent = `正在准备对话资源，请稍候${dots}`;
                            }
                        }, 500);
                        
                        // 延迟后自动重试
                        setTimeout(() => {
                            clearInterval(loadingInterval);
                            appendMessage('system', '正在重新连接...');
                            
                            // 尝试设置一个默认的doc_id，以便用户可以尝试发送消息
                            if (!doc_id) {
                                doc_id = 'default_doc_id';
                                console.log('设置默认doc_id以允许用户尝试发送消息');
                            }
                            
                            // 不完全禁用聊天，让用户可以尝试发送消息
                            document.getElementById('userInput').disabled = false;
                            document.getElementById('sendButton').disabled = false;
                            
                            // 提示用户可以尝试发送消息
                            appendMessage('system', '您可以尝试发送一条消息，通常第一次发送后系统会自动连接');
                        }, 3000);
                    } else {
                        appendMessage('system', `初始化失败: ${error.message}`);
                        disableChat();
                    }
                }
            }
            
            return tryInitialize();
        }

        // 禁用聊天功能
        function disableChat() {
            document.getElementById('userInput').disabled = true;
            document.getElementById('sendButton').disabled = true;
        }

        // 发送消息到服务器
        async function sendMessage() {
            const userInput = document.getElementById('userInput');
            const message = userInput.value.trim();
            const sendButton = document.getElementById('sendButton');
            
            if (!message) return;
            
            console.log('开始发送消息:', message);
            console.log('当前用户信息:', {
                user: getUser(),
                key: getKey() ? '***' : null,
                token: getToken() ? '***' : null,
                doc_id: doc_id,
                dataset_id: dataset_id
            });
            
            // 禁用发送按钮
            if (sendButton) {
                sendButton.disabled = true;
                sendButton.textContent = '发送中...';
            }
            
            // 检查系统是否已准备好
            if (!isSystemReady()) {
                console.log('系统未准备好，尝试初始化...');
                appendMessage('system', '系统正在准备中，请稍候...');
                appendMessage('system', '正在加载视频资源，这可能需要几秒钟时间');
                
                // 尝试重新初始化
                try {
                    await initializeVideoChat();
                    appendMessage('system', '系统已准备就绪，请重新发送您的问题');
                } catch (err) {
                    console.error('初始化失败:', err);
                    appendMessage('system', '初始化失败，请刷新页面重试');
                }
                
                // 恢复发送按钮状态
                if (sendButton) {
                    sendButton.disabled = false;
                    sendButton.textContent = '发送';
                }
                
                return;
            }
            
            // 清空输入框
            userInput.value = '';
            
            // 添加用户消息到历史
            // 检查是否已经存在相同内容的最近消息，避免重复添加
            const lastMessage = messageHistory.length > 0 ? messageHistory[messageHistory.length - 1] : null;
            if (!lastMessage || lastMessage.sender !== 'user' || lastMessage.content !== message) {
                messageHistory.push({
                    sender: 'user',
                    content: message,
                    timestamp: new Date().toISOString()
                });
            }
            
            // 显示用户消息
            appendMessage('user', message);
            
            // 显示加载中消息
            const loadingMessage = appendMessage('system', '正在思考中...');
            
            // 准备请求数据
            const requestData = {
                user: getUser(),
                key: getKey(),
                token: getToken(),
                message: message,
                filename: getFilename(),
                conversationId: conversationId,
                history: messageHistory.filter(msg => msg.sender !== 'system'),
                doc_id: String(window.doc_id || doc_id || ''),
                dataset_id: String(window.dataset_id || dataset_id || ''),
                folder: getFolder()
            };
            
            console.log('Sending request with data:', {
                ...requestData,
                token: '***',
                key: '***',
                doc_id: requestData.doc_id,
                dataset_id: requestData.dataset_id
            });
            
            // 重试计数器
            let retryCount = 0;
            const MAX_RETRIES = 3;
            
            // 尝试发送消息的函数
            async function attemptSendMessage() {
                const sendButton = document.getElementById('sendButton');
                
                try {
                    console.log('准备发送请求，数据:', {
                        ...requestData,
                        token: requestData.token ? '***' : null,
                        key: requestData.key ? '***' : null
                    });
                    
                    // 发送请求到服务器
                    console.log('发送请求到:', '/api/chat');
                    const response = await fetch('/api/chat', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(requestData)
                    });
                    
                    console.log('收到响应:', {
                        status: response.status,
                        statusText: response.statusText,
                        ok: response.ok
                    });

                    // 检查响应状态
                    if (!response.ok) {
                        let errorText;
                        try {
                            errorText = await response.text();
                        } catch (e) {
                            errorText = '无法获取错误详情';
                        }
                        
                        console.error('Server error response:', {
                            status: response.status,
                            statusText: response.statusText,
                            errorText: errorText
                        });
                        
                        // 如果是500错误且包含"Invalid argument"，尝试重试
                        if (response.status === 500 && 
                            (errorText.includes('Invalid argument') || errorText.includes('Errno 22'))) {
                            
                            if (retryCount < MAX_RETRIES) {
                                retryCount++;
                                console.log(`尝试第${retryCount}次重新发送消息...`);
                                
                                // 移除之前的加载消息
                                if (loadingMessage && loadingMessage.parentNode) {
                                    loadingMessage.parentNode.removeChild(loadingMessage);
                                }
                                
                                // 显示正在重试的消息
                                appendMessage('system', `正在重新连接，请稍候...(${retryCount}/${MAX_RETRIES})`);
                                
                                // 如果是第一次重试，尝试重新初始化
                                if (retryCount === 1) {
                                    console.log('尝试重新初始化聊天...');
                                    await initializeVideoChat();
                                    // 更新doc_id和dataset_id
                                    requestData.doc_id = String(doc_id || '');
                                    requestData.dataset_id = String(dataset_id || '');
                                }
                                
                                // 延迟后重试
                                await new Promise(resolve => setTimeout(resolve, 1500));
                                return attemptSendMessage();
                            } else {
                                // 达到最大重试次数，显示友好的错误信息
                                // 恢复发送按钮状态
                                if (sendButton) {
                                    sendButton.disabled = false;
                                    sendButton.textContent = '发送';
                                }
                                throw new Error('连接暂时不可用，请稍后再试。您可以刷新页面或者尝试其他视频。');
                            }
                        }
                        
                        // 恢复发送按钮状态
                        if (sendButton) {
                            sendButton.disabled = false;
                            sendButton.textContent = '发送';
                        }
                        
                        throw new Error(`服务器错误 (${response.status}): ${errorText}`);
                    }

                    // 解析响应JSON
                    let data;
                    try {
                        data = await response.json();
                        
                        // 立即删除"正在思考中..."的消息
                        removeThinkingMessages();
                        
                    } catch (e) {
                        console.error('Failed to parse response as JSON:', e);
                        // 恢复发送按钮状态
                        if (sendButton) {
                            sendButton.disabled = false;
                            sendButton.textContent = '发送';
                        }
                        
                        // 确保删除"正在思考中..."的消息
                        removeThinkingMessages();
                        
                        throw new Error('服务器返回了无效的JSON数据');
                    }

                    // 无论响应是成功还是失败，都立即删除加载消息
                    if (loadingMessage && loadingMessage.parentNode) {
                        loadingMessage.parentNode.removeChild(loadingMessage);
                    } else {
                        // 如果loadingMessage不存在或已被删除，尝试查找并删除所有"正在思考中..."的消息
                        removeThinkingMessages();
                    }

                    if (data.success) {
                        // 检查响应是否包含错误信息
                        const response = data.response || '';
                        
                        // 检查是否是错误消息
                        if (response.startsWith('抱歉，处理您的问题时出现错误:')) {
                            console.error('后端处理错误:', response);
                            
                            // 提取错误信息
                            const errorDetail = response.replace('抱歉，处理您的问题时出现错误:', '').trim();
                            
                            // 移除加载中消息已在上面处理，这里不再重复
                            
                            // 显示友好的错误消息
                            appendMessage('system', '系统暂时无法处理您的请求，请稍后再试');
                            
                            // 如果是初始化相关错误，显示更具体的提示
                            if (errorDetail.includes('dataset_ids') || errorDetail.includes('document_ids') || 
                                errorDetail.includes('无法找到相关内容') || errorDetail.includes('Invalid argument')) {
                                appendMessage('system', '正在准备对话资源，请稍候片刻再发送消息...');
                                
                                // 尝试重新初始化
                                console.log('尝试重新初始化聊天...');
                                setTimeout(() => {
                                    initializeVideoChat().then(() => {
                                        appendMessage('system', '系统已准备就绪，请重新发送您的问题');
                                        
                                        // 恢复发送按钮
                                        if (sendButton) {
                                            sendButton.disabled = false;
                                            sendButton.textContent = '发送';
                                        }
                                    }).catch(err => {
                                        console.error('重新初始化失败:', err);
                                        appendMessage('system', '初始化失败，请刷新页面重试');
                                        
                                        // 恢复发送按钮
                                        if (sendButton) {
                                            sendButton.disabled = false;
                                            sendButton.textContent = '发送';
                                        }
                                    });
                                }, 2000);
                            } else {
                                // 恢复发送按钮
                                if (sendButton) {
                                    sendButton.disabled = false;
                                    sendButton.textContent = '发送';
                                }
                            }
                            
                            return; // 提前返回，不继续处理
                        } else {
                            // 移除加载中消息
                            if (loadingMessage && loadingMessage.parentNode) {
                                loadingMessage.parentNode.removeChild(loadingMessage);
                            }
                            
                            // 正常响应，添加 AI 响应到历史
                            // 检查是否已经存在相同内容的最近消息，避免重复添加
                            const lastAIMessage = messageHistory.length > 0 ? 
                                messageHistory.filter(msg => msg.sender === 'AI').pop() : null;
                            if (!lastAIMessage || lastAIMessage.content !== response) {
                                messageHistory.push({
                                    sender: 'AI',
                                    content: response,
                                    timestamp: new Date().toISOString()                        
                                });
                            }

                            // 再次管理历史记录大小
                            messageHistory = manageMessageHistory(messageHistory);
                            
                            // 显示 AI 响应
                            appendMessage('ai', response);
                            
                            // 恢复发送按钮
                            if (sendButton) {
                                sendButton.disabled = false;
                                sendButton.textContent = '发送';
                            }
                        }
                        
                        // 更新剩余对话次数
                        if (data.remainingChats !== undefined) {
                            updateRemainingChats(data.remainingChats);
                        }
                    } else {
                        // 移除加载中消息
                        if (loadingMessage && loadingMessage.parentNode) {
                            loadingMessage.parentNode.removeChild(loadingMessage);
                        }
                        
                        const errorMessage = data.message || '服务器响应错误';
                        appendMessage('system', '错误: ' + errorMessage);
                        
                        // 特殊错误处理
                        if (errorMessage.includes('Daily chat limit exceeded')) {
                            appendMessage('system', '今日对话次数已达上限，请明天再试');
                        } else if (errorMessage.includes('Invalid token')) {
                            appendMessage('system', '认证已过期，请刷新页面重试');
                        }
                        
                        // 恢复发送按钮
                        if (sendButton) {
                            sendButton.disabled = false;
                            sendButton.textContent = '发送';
                        }
                    }
                } catch (error) {
                    // 如果已经重试过但仍然失败，抛出错误
                    if (retryCount >= MAX_RETRIES) {
                        // 确保删除"正在思考中..."的消息
                        removeThinkingMessages();
                        throw error;
                    }
                    
                    // 移除加载中消息
                    if (loadingMessage && loadingMessage.parentNode) {
                        loadingMessage.parentNode.removeChild(loadingMessage);
                    } else {
                        // 如果loadingMessage不存在或已被删除，尝试查找并删除所有"正在思考中..."的消息
                        removeThinkingMessages();
                    }
                    
                    // 否则尝试重试
                    retryCount++;
                    console.log(`发送消息失败，尝试第${retryCount}次重试...`);
                    // appendMessage('system', `连接失败，正在第${retryCount}次重试...`);
                    await new Promise(resolve => setTimeout(resolve, 1000));
                    return attemptSendMessage();
                }
            }
            
            // 开始尝试发送消息
            try {
                await attemptSendMessage();
            } catch (error) {
                console.error('Error:', error);
                
                // 处理特定类型的错误，显示更友好的提示
                if (error.message.includes('Invalid argument') || error.message.includes('Errno 22')) {
                    // 移除加载中消息
                    if (loadingMessage && loadingMessage.parentNode) {
                        loadingMessage.parentNode.removeChild(loadingMessage);
                    }
                    
                    appendMessage('system', '正在准备对话资源，请稍候...');
                    
                    // 延迟后自动重试
                    setTimeout(() => {
                        appendMessage('system', '正在重新连接...');
                        // 重新发送消息
                        const userInput = document.getElementById('userInput');
                        if (!userInput.value.trim()) {
                            // 如果用户已经清空了输入框，尝试恢复之前的消息
                            let lastUserMessage = null;
                            // 从后向前查找最后一条用户消息
                            for (let i = messageHistory.length - 1; i >= 0; i--) {
                                if (messageHistory[i].sender === 'user') {
                                    lastUserMessage = messageHistory[i];
                                    break;
                                }
                            }
                            if (lastUserMessage) {
                                userInput.value = lastUserMessage.content;
                            }
                        }
                        sendMessage();
                    }, 2000);
                    return;
                }
                // 处理其他常见错误
                else if (error.message.includes('network') || error.message.includes('Failed to fetch')) {
                    appendMessage('system', '网络连接不稳定，请检查网络后重试');
                }
                else {
                    appendMessage('system', `发送消息时发生错误: ${error.message}`);
                }
            } finally {
                // 最后确保删除所有"正在思考中..."的消息
                removeThinkingMessages();
                
                // 移除加载中消息
                if (loadingMessage && loadingMessage.parentNode) {
                    loadingMessage.parentNode.removeChild(loadingMessage);
                }
                
                // 恢复发送按钮
                if (sendButton) {
                    sendButton.disabled = false;
                    sendButton.textContent = '发送';
                }
            }
        }

        // 添加一个函数来删除"正在思考中..."的消息
        function removeThinkingMessages() {
            const chatHistory = document.getElementById('chatHistory');
            if (chatHistory) {
                const systemMessages = chatHistory.querySelectorAll('.system-message');
                systemMessages.forEach(msg => {
                    if (msg.textContent && msg.textContent.includes('正在思考中')) {
                        try {
                            chatHistory.removeChild(msg);
                        } catch (e) {
                            console.error('Error removing thinking message:', e);
                        }
                    }
                });
            }
        }

        // 初始化聊天界面
        function initializeChat() {
            if (!user || !key || !token) {
                appendMessage('system', '请先登录后再使用对话功能');
                disableChat();
                return;
            }
            
            // 确保消息历史是一个数组
            if (!Array.isArray(messageHistory)) {
                console.log('重置消息历史为空数组');
                messageHistory = [];
                window.messageHistory = messageHistory;
            }

            // 检查 MathJax 是否已初始化
            const checkMathJaxReady = () => {
                if (typeof MathJax === 'undefined' || !MathJax.Hub.Queue) {
                    console.log('MathJax not ready yet, waiting...');
                    setTimeout(checkMathJaxReady, 300);
                    return;
                }
                
                console.log('MathJax is ready, showing welcome message');
                
            // 显示初始欢迎消息
                try {
                    // 使用安全的消息显示方法           
            // 启用聊天输入
            document.getElementById('userInput').disabled = false;
            document.getElementById('sendButton').disabled = false;
                } catch (error) {
                    console.error('Error showing welcome message:', error);
                    // 如果显示欢迎消息失败，至少启用输入
                    document.getElementById('userInput').disabled = false;
                    document.getElementById('sendButton').disabled = false;
                }
            };
            
            // 开始检查 MathJax 是否就绪
            checkMathJaxReady();
        }

        // 安全的消息添加函数，用于欢迎消息
        function safeAppendMessage(type, content) {
            if (!content) {
                console.warn('Empty content passed to safeAppendMessage');
                content = '';
            }
            
            // 确保content是字符串类型
            content = String(content);
            
            const chatHistory = document.getElementById('chatHistory');
            if (!chatHistory) {
                console.error('Chat history element not found');
                return;
            }
            
            // 直接使用appendMessage函数，但添加错误处理
            try {
                appendMessage(type, content);
            } catch (error) {
                console.error('Error in safeAppendMessage:', error);
                
                // 如果appendMessage失败，使用简单的文本显示作为备份
                try {
                    const messageDiv = document.createElement('div');
                    messageDiv.className = `message ${type}-message`;
                    messageDiv.textContent = content;
                    chatHistory.appendChild(messageDiv);
                    chatHistory.scrollTop = chatHistory.scrollHeight;
                } catch (finalError) {
                    console.error('Critical error in message display:', finalError);
                }
            }
        }

        const player = new Plyr('#player', {
            controls: [
                'play-large', // 大播放按钮
                'play', // 播放/暂停按钮
                'progress', // 进度条
                'current-time', // 当前时间
                'duration', // 总时长
                'mute', // 静音按钮
                'volume', // 音量控制
                'settings', // 设置按钮
                'fullscreen' // 全屏按钮
            ],
            settings: ['quality', 'speed']
        });

        let subtitles = [];
        let subtitleMode = 'both';

        // 修改全屏变化处理函数
        function onFullscreenChange() {
            const subtitleDiv = document.getElementById('subtitle');
            const isFullscreen = document.fullscreenElement || 
                               document.webkitFullscreenElement || 
                               document.mozFullScreenElement;
            
            if (isFullscreen) {
                subtitleDiv.style.zIndex = '10000';
            }
        }

        player.on('ready', function() {
            const plyrContainer = document.querySelector('.plyr');
            const subtitleDiv = document.getElementById('subtitle');
            const subtitleControls = document.getElementById('subtitleControls');
            
            if (plyrContainer && subtitleDiv) {
                plyrContainer.appendChild(subtitleDiv);
            }
            
            if (plyrContainer && subtitleControls) {
                plyrContainer.appendChild(subtitleControls);
            }
            
            onFullscreenChange();
        });

        document.addEventListener('fullscreenchange', onFullscreenChange);
        document.addEventListener('webkitfullscreenchange', onFullscreenChange);
        document.addEventListener('mozfullscreenchange', onFullscreenChange);

        window.addEventListener('resize', onFullscreenChange);

        // 加载字幕
        fetch(subtitleUrl, {
            headers: {
                'User-Agent': 'Mozilla/5.0'
            },
            timeout: 10000
        })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                subtitles = data;
                console.log('Subtitles loaded successfully');
            })
            .catch(error => {
                console.warn('Error loading subtitles:', error);
                subtitles = [];
            });

        // 使用二分查找更新字幕
        function findSubtitle(currentTime) {
            let left = 0;
            let right = subtitles.length - 1;
            
            while (left <= right) {
                const mid = Math.floor((left + right) / 2);
                const [start, end, text] = subtitles[mid];
                
                if (currentTime >= start && currentTime <= end) {
                    return text;
                } else if (currentTime < start) {
                    right = mid - 1;
                } else {
                    left = mid + 1;
                }
            }
            return "";
        }

        function updateSubtitle(text) {
            if (subtitleMode === 'off') {
                document.getElementById('subtitle').innerHTML = '';
                return;
            }

            // 使用正则表达式分割文本，处理一个或多个换行符的情况
            const parts = text.split(/\n+/).map(t => t.trim()).filter(t => t);
            const englishText = parts[0] || '';
            const chineseText = parts[1] || '';
            
            let html = '';
            if ((subtitleMode === 'english' || subtitleMode === 'both') && englishText) {
                html += `<div class="subtitle-english">${englishText}</div>`;
            }
            if ((subtitleMode === 'chinese' || subtitleMode === 'both') && chineseText) {
                html += `<div class="subtitle-chinese">${chineseText}</div>`;
            }

            document.getElementById('subtitle').innerHTML = html;
        }

        player.on('timeupdate', () => {
            const currentTime = player.currentTime;
            const currentSubtitle = findSubtitle(currentTime);
            updateSubtitle(currentSubtitle);
        });

        // 字幕控制按钮事件
        document.querySelectorAll('#subtitleControls button').forEach(button => {
            button.addEventListener('click', () => {
                const mode = button.id.replace('subtitle', '').toLowerCase();
                subtitleMode = mode;
                document.querySelectorAll('#subtitleControls button').forEach(btn => {
                    btn.classList.remove('active');
                });
                button.classList.add('active');
            });
        });

        // 错误处理和日志
        player.on('error', (e) => {
            console.error('Video error:', e);
        });

        player.on('loadeddata', () => {
            console.log('Video loaded successfully');
        });

        // 添加消息历史数组 - 已在全局范围内定义
        // let messageHistory = [];
        const MAX_WORDS = 20000;

        // 计算文本中的汉字和英文单词数
        function countWords(text) {
            const chineseChars = text.match(/[\u4e00-\u9fa5]/g) || [];
            const englishWords = text.match(/[a-zA-Z]+(-[a-zA-Z]+)*|\d+/g) || [];
            return {
                total: chineseChars.length + englishWords.length,
                chinese: chineseChars.length,
                english: englishWords.length
            };
        }

        // 管理消息历史
        function manageMessageHistory(history) {
            if (!Array.isArray(history)) {
                console.error('Invalid history format, expected array');
                return [];
            }
            
            let totalWords = 0;
            let managedHistory = [];
            
            try {
                for (let i = history.length - 1; i >= 0; i--) {
                    const message = history[i];
                    
                    // 验证消息格式
                    if (!message || typeof message !== 'object') {
                        console.warn('Skipping invalid message:', message);
                        continue;
                    }
                    
                    // 确保所有字段都是字符串类型
                    const sender = message.sender ? String(message.sender) : 'Unknown';
                    const content = message.content ? String(message.content) : '';
                    const timestamp = message.timestamp ? String(message.timestamp) : new Date().toISOString();
                    
                    // 创建安全的消息对象
                    const safeMessage = {
                        sender: sender,
                        content: content,
                        timestamp: timestamp
                    };
                    
                    const messageText = `${sender}: ${content}\n`;
                    const wordCount = countWords(messageText);
                    
                    if (totalWords + wordCount.total <= MAX_WORDS) {
                        managedHistory.unshift(safeMessage);
                        totalWords += wordCount.total;
                    } else {
                        if (managedHistory.length === 0) {
                            managedHistory.unshift(safeMessage);
                        }
                        break;
                    }
                }
            } catch (error) {
                console.error('Error managing message history:', error);
                // 如果处理过程中出错，返回空数组以避免进一步的错误
                return [];
            }
            
            return managedHistory;
        }

        // 更新剩余对话次数显示
        function updateRemainingChats(remainingChats) {
            const chatHeader = document.getElementById('chatHeader');
            const remainingText = document.createElement('span');
            remainingText.style.fontSize = '12px';
            remainingText.style.marginLeft = '10px';
            remainingText.textContent = `剩余对话次数: ${remainingChats}`;
            
            // 移除旧的剩余次数显示（如果存在）
            const oldRemaining = chatHeader.querySelector('.remaining-chats');
            if (oldRemaining) {
                oldRemaining.remove();
            }
            
            remainingText.className = 'remaining-chats';
            chatHeader.appendChild(remainingText);
        }

        // 改进的消息显示函数
        function appendMessage(type, content) {
            if (!content) {
                console.warn('Empty content passed to appendMessage');
                content = '';
            }
            
            // 确保content是字符串类型
            content = String(content);
            
            const chatHistory = document.getElementById('chatHistory');
            if (!chatHistory) {
                console.error('Chat history element not found');
                return;
            }
            
            // 如果是系统消息，先删除之前的系统消息
            if (type === 'system') {
                try {
                    // 只删除临时状态消息，而不是所有系统消息
                    const tempStatusMessages = [
                        '正在思考中...',
                        '正在重新连接...',
                        '正在准备对话资源，请稍候...',
                        '系统正在准备中，请稍候...',
                        '正在加载视频资源，这可能需要几秒钟时间'
                    ];
                    
                    // 检查当前消息是否是临时状态消息
                    const isStatusMessage = tempStatusMessages.some(msg => content.includes(msg));
                    
                    // 如果当前消息是临时状态消息，或者内容包含"正在"、"请稍候"等关键词
                    if (isStatusMessage || content.includes('正在') || content.includes('请稍候')) {
                        const systemMessages = chatHistory.querySelectorAll('.system-message');
                        systemMessages.forEach(msg => {
                            try {
                                // 只删除临时状态消息
                                const msgText = msg.textContent || '';
                                const shouldRemove = tempStatusMessages.some(statusMsg => 
                                    msgText.includes(statusMsg)
                                ) || msgText.includes('正在') || msgText.includes('请稍候');
                                
                                if (shouldRemove && msg && msg.parentNode) {
                                    chatHistory.removeChild(msg);
                                }
                            } catch (e) {
                                console.error('Error removing system message:', e);
                            }
                        });
                    }
                } catch (e) {
                    console.error('Error handling system messages:', e);
                }
            }
            
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}-message`;
            messageDiv.setAttribute('data-processed', 'false'); // 添加标记，表示尚未处理过公式
            
            try {
                // 配置 marked 选项
                if (typeof marked !== 'undefined') {
                    marked.setOptions({
                        breaks: true,
                        gfm: true
                    });
                } else {
                    console.warn('Marked library not available, falling back to plain text');
                    messageDiv.textContent = content;
                    chatHistory.appendChild(messageDiv);
                    chatHistory.scrollTop = chatHistory.scrollHeight;
                    return;
                }

                // 处理数学公式
                try {
                    // 1. 预处理内容，保护数学公式
                    let processedContent = content;
                    const mathPlaceholders = [];
                    let mathCounter = 0;
                    
                    // 预处理函数：处理LaTeX中的反斜杠问题
                    function preprocessLatexFormula(formula) {
                        // 新规则：将双反斜杠转换为单反斜杠
                        // 首先将双反斜杠替换为临时标记
                        let processed = formula.replace(/\\\\/g, '___SINGLE_BACKSLASH___');
                        
                        // 处理其他可能的转义序列
                        processed = processed.replace(/\\([^a-zA-Z0-9])/g, '___ESCAPED_$1___');
                        
                        return processed;
                    }
                    
                    // 还原LaTeX公式中的特殊标记
                    function restoreLatexFormula(formula) {
                        // 还原双反斜杠为单反斜杠（新规则）
                        let restored = formula.replace(/___SINGLE_BACKSLASH___/g, '\\');
                        
                        // 还原其他转义序列
                        restored = restored.replace(/___ESCAPED_([^a-zA-Z0-9])___/g, '\\$1');
                        
                        return restored;
                    }
                    
                    // 处理块级公式 $$ ... $$
                    processedContent = processedContent.replace(/\$\$([\s\S]*?)\$\$/g, function(match, formula) {
                        const preprocessedFormula = preprocessLatexFormula(formula);
                        const placeholder = `MATH_BLOCK_${mathCounter}`;
                        mathPlaceholders.push({
                            placeholder: placeholder,
                            formula: preprocessedFormula.trim(),
                            type: 'block',
                            needsRestore: true
                        });
                        mathCounter++;
                        return placeholder;
                    });
                    
                    // 处理行内公式 \( ... \)
                    processedContent = processedContent.replace(/\\\(([\s\S]*?)\\\)/g, function(match, formula) {
                        const preprocessedFormula = preprocessLatexFormula(formula);
                        const placeholder = `MATH_INLINE_${mathCounter}`;
                        mathPlaceholders.push({
                            placeholder: placeholder,
                            formula: preprocessedFormula.trim(),
                            type: 'inline',
                            delimiter: '\\(',
                            needsRestore: true
                        });
                        mathCounter++;
                        return placeholder;
                    });
                    
                    // 处理行内公式 $ ... $
                    processedContent = processedContent.replace(/\$([^$\n]+?)\$/g, function(match, formula) {
                        const preprocessedFormula = preprocessLatexFormula(formula);
                        const placeholder = `MATH_INLINE_${mathCounter}`;
                        mathPlaceholders.push({
                            placeholder: placeholder,
                            formula: preprocessedFormula.trim(),
                            type: 'inline',
                            delimiter: '$',
                            needsRestore: true
                        });
                        mathCounter++;
                        return placeholder;
                    });
                    
                    // 2. 使用 marked 处理 Markdown
                    let renderedContent = marked.parse(processedContent);
                    
                    // 3. 恢复数学公式
                    mathPlaceholders.forEach(item => {
                        const uniqueId = `math-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
                        
                        // 还原公式中的特殊标记
                        let formula = item.formula;
                        if (item.needsRestore) {
                            formula = restoreLatexFormula(formula);
                        }
                        
                        if (item.type === 'block') {
                            renderedContent = renderedContent.replace(
                                item.placeholder, 
                                `<div class="math-display" id="${uniqueId}">\\[${formula}\\]</div>`
                            );
                        } else {
                            renderedContent = renderedContent.replace(
                                item.placeholder, 
                                `<span class="math-inline" id="${uniqueId}">\\(${formula}\\)</span>`
                            );
                        }
                    });
                    
                    // 4. 设置内容
                    messageDiv.innerHTML = renderedContent;
                    
                    // 5. 添加到聊天历史
                    chatHistory.appendChild(messageDiv);
                    
                    // 6. 渲染新消息中的公式
                    if (mathPlaceholders.length > 0) {
                        console.log(`检测到${mathPlaceholders.length}个公式，开始渲染`);
                        
                        // 立即渲染
                        renderMathJaxForElement(messageDiv);
                        
                        // 延迟100ms后再次尝试渲染，确保所有公式都被处理
                        setTimeout(() => {
                            console.log('延迟渲染检查');
                            if (messageDiv.getAttribute('data-processed') !== 'true') {
                                console.log('发现未处理的元素，再次尝试渲染');
                                renderMathJaxForElement(messageDiv);
                            }
                            
                            // 强制触发全局MathJax渲染
                            if (typeof MathJax !== 'undefined') {
                                if (typeof MathJax.typeset === 'function') {
                                    try {
                                        MathJax.typeset();
                                        console.log('触发全局MathJax渲染');
                                    } catch (e) {
                                        console.error('全局MathJax渲染错误:', e);
                                    }
                                } else if (MathJax.Hub && typeof MathJax.Hub.Queue === 'function') {
                                    try {
                                        MathJax.Hub.Queue(["Typeset", MathJax.Hub]);
                                        console.log('触发全局MathJax 2.x渲染');
                                    } catch (e) {
                                        console.error('全局MathJax 2.x渲染错误:', e);
                                    }
                                }
                            }
                        }, 100);
                    }
                    
                    // 7. 滚动到底部
                    chatHistory.scrollTop = chatHistory.scrollHeight;
                    
                } catch (formulaError) {
                    console.error('Error processing formulas:', formulaError);
                    messageDiv.textContent = content;  // 降级为纯文本
                    chatHistory.appendChild(messageDiv);
                    chatHistory.scrollTop = chatHistory.scrollHeight;
                }
                
            } catch (err) {
                console.error('Rendering error:', err);
                messageDiv.textContent = content;  // 降级为纯文本
                chatHistory.appendChild(messageDiv);
                chatHistory.scrollTop = chatHistory.scrollHeight;
            }

            // 应用代码高亮
            try {
                if (window.hljs) {
                    messageDiv.querySelectorAll('pre code').forEach((block) => {
                        hljs.highlightBlock(block);
                    });
                }
            } catch (highlightError) {
                console.error('Error applying code highlighting:', highlightError);
            }
        }

        // 备用数学公式渲染函数
        function applyFallbackMathRendering(element) {
            if (!element) return;
            
            // 处理所有MATH_BLOCK_占位符
            const blockRegex = /MATH_BLOCK_(\d+)/g;
            let blockMatch;
            while ((blockMatch = blockRegex.exec(element.innerHTML)) !== null) {
                const blockId = blockMatch[1];
                const blockPlaceholder = `MATH_BLOCK_${blockId}`;
                
                // 从原始内容中提取公式
                const originalContent = element.textContent;
                const blockFormulas = originalContent.match(/\$\$([\s\S]*?)\$\$/g);
                
                if (blockFormulas && blockFormulas[blockId]) {
                    let formula = blockFormulas[blockId].replace(/\$\$/g, '').trim();
                    // 应用新规则：将双反斜杠转换为单反斜杠
                    formula = formula.replace(/\\\\/g, '\\');
                    element.innerHTML = element.innerHTML.replace(
                        blockPlaceholder,
                        `<div class="math-display">\\[${formula}\\]</div>`
                    );
                }
            }
            
            // 处理所有MATH_INLINE_占位符
            const inlineRegex = /MATH_INLINE_(\d+)/g;
            let inlineMatch;
            while ((inlineMatch = inlineRegex.exec(element.innerHTML)) !== null) {
                const inlineId = inlineMatch[1];
                const inlinePlaceholder = `MATH_INLINE_${inlineId}`;
                
                // 从原始内容中提取公式
                const originalContent = element.textContent;
                // 先尝试匹配 \( ... \) 格式
                let inlineFormulas = originalContent.match(/\\\(([\s\S]*?)\\\)/g);
                
                // 如果没有找到，尝试匹配 $ ... $ 格式
                if (!inlineFormulas || inlineFormulas.length <= inlineId) {
                    inlineFormulas = originalContent.match(/\$([^$\n]+?)\$/g);
                }
                
                if (inlineFormulas && inlineFormulas[inlineId]) {
                    let formula = inlineFormulas[inlineId];
                    // 移除分隔符
                    formula = formula.replace(/\\\(|\\\)|\$/g, '').trim();
                    // 应用新规则：将双反斜杠转换为单反斜杠
                    formula = formula.replace(/\\\\/g, '\\');
                    
                    element.innerHTML = element.innerHTML.replace(
                        inlinePlaceholder,
                        `<span class="math-inline">\\(${formula}\\)</span>`
                    );
                }
            }
            
            // 尝试再次渲染数学公式
            if (typeof MathJax !== 'undefined' && MathJax.typesetPromise) {
                try {
                    MathJax.typesetPromise([element]).catch(err => {
                        console.error('Fallback MathJax rendering failed:', err);
                    });
                } catch (e) {
                    console.error('Error in fallback MathJax rendering:', e);
                }
            }
        }

        // 添加聊天窗口拖动功能
        const chatWindow = document.getElementById('chatWindow');
        const chatHeader = document.getElementById('chatHeader');
        const toggleChat = document.getElementById('toggleChat');
        const minimizeChat = document.getElementById('minimizeChat');

        let isDragging = false;
        let currentX;
        let currentY;
        let initialX;
        let initialY;
        let xOffset = 0;
        let yOffset = 0;

        chatHeader.addEventListener('mousedown', dragStart);
        document.addEventListener('mousemove', drag);
        document.addEventListener('mouseup', dragEnd);

        function dragStart(e) {
            initialX = e.clientX - xOffset;
            initialY = e.clientY - yOffset;

            if (e.target === chatHeader) {
                isDragging = true;
            }
        }

        function drag(e) {
            if (isDragging) {
                e.preventDefault();
                currentX = e.clientX - initialX;
                currentY = e.clientY - initialY;

                xOffset = currentX;
                yOffset = currentY;

                setTranslate(currentX, currentY, chatWindow);
            }
        }

        function setTranslate(xPos, yPos, el) {
            el.style.transform = `translate3d(${xPos}px, ${yPos}px, 0)`;
        }

        function dragEnd() {
            initialX = currentX;
            initialY = currentY;
            isDragging = false;
        }

        // 切换聊天窗口显示/隐藏
        document.addEventListener('DOMContentLoaded', function() {
            const toggleChat = document.getElementById('toggleChat');
            const chatWindow = document.getElementById('chatWindow');
            const minimizeChat = document.getElementById('minimizeChat');
            
            // 确保初始状态正确
            if (chatWindow) {
                // 默认显示聊天窗口
                chatWindow.style.display = 'flex';
                if (toggleChat) toggleChat.textContent = '隐藏对话';
            }
            
            // 重新绑定切换按钮事件
            if (toggleChat) {
                // 移除所有现有的点击事件监听器
                const newToggleBtn = toggleChat.cloneNode(true);
                if (toggleChat.parentNode) {
                    toggleChat.parentNode.replaceChild(newToggleBtn, toggleChat);
                }
                
                // 添加新的事件监听器
                newToggleBtn.addEventListener('click', function() {
                    console.log('Toggle chat button clicked');
                    if (chatWindow.style.display === 'none') {
                        chatWindow.style.display = 'flex';
                        newToggleBtn.textContent = '隐藏对话';
                        console.log('Chat window displayed');
                    } else {
                        chatWindow.style.display = 'none';
                        newToggleBtn.textContent = '显示对话';
                        console.log('Chat window hidden');
                    }
                });
            }
            
            // 重新绑定最小化按钮事件
            if (minimizeChat) {
                // 移除所有现有的点击事件监听器
                const newMinimizeBtn = minimizeChat.cloneNode(true);
                if (minimizeChat.parentNode) {
                    minimizeChat.parentNode.replaceChild(newMinimizeBtn, minimizeChat);
                }
                
                // 添加新的事件监听器
                newMinimizeBtn.addEventListener('click', function() {
                    chatWindow.style.display = 'none';
                    if (toggleChat) toggleChat.textContent = '显示对话';
                    console.log('Chat window minimized');
                });
            }
        });

        // 注释掉之前的事件绑定代码
        /*
        toggleChat.addEventListener('click', () => {
            const chatWindow = document.getElementById('chatWindow');
            if (chatWindow.style.display === 'none') {
                chatWindow.style.display = 'flex';
                toggleChat.textContent = '隐藏对话';
            } else {
                chatWindow.style.display = 'none';
                toggleChat.textContent = '显示对话';
            }
        });

        minimizeChat.addEventListener('click', () => {
            chatWindow.style.display = 'none';
            toggleChat.textContent = '显示对话';
        });
        */

        // 聊天功能
        const sendButton = document.getElementById('sendButton');
        const userInput = document.getElementById('userInput');

        sendButton.addEventListener('click', sendMessage);
        userInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });

        document.addEventListener('DOMContentLoaded', function() {
            const chatWindow = document.getElementById('chatWindow');
            const resizeHandle = document.getElementById('chatResizeHandle');
            let isResizing = false;
            let originalWidth;
            let originalX;

            resizeHandle.addEventListener('mousedown', function(e) {
                isResizing = true;
                originalWidth = chatWindow.offsetWidth;
                originalX = e.clientX;
                
                // 添加临时事件监听器
                document.addEventListener('mousemove', handleMouseMove);
                document.addEventListener('mouseup', handleMouseUp);
                
                // 防止文本选择
                e.preventDefault();
            });

            function handleMouseMove(e) {
                if (!isResizing) return;
                
                const deltaX = originalX - e.clientX;
                const newWidth = Math.min(
                    Math.max(originalWidth + deltaX, 300), // 最小宽度 300px
                    window.innerWidth * 0.8  // 最大宽度为窗口的 80%
                );
                
                chatWindow.style.width = newWidth + 'px';
            }

            function handleMouseUp() {
                isResizing = false;
                // 移除临时事件监听器
                document.removeEventListener('mousemove', handleMouseMove);
                document.removeEventListener('mouseup', handleMouseUp);
            }
        });

        // 加载PDF匹配信息并显示PDF列表
        async function loadPdfMatch() {
            try {
                const authParams = new URLSearchParams({
                    user: user,
                    key: key,
                    token: token
                }).toString();

                const response = await fetch(`/api/videopdf/matches?${authParams}`);
                const matchData = await response.json();

                const videoBaseName = videoName.replace(/\.[^/.]+$/, "");
                const folderMatches = matchData[folder];
                
                if (folderMatches) {
                    const videoMatch = folderMatches.find(match => 
                        match.video_json.replace(/\.[^/.]+$/, "") === videoBaseName
                    );

                    if (videoMatch && videoMatch.matching_pdfs.length > 0) {
                        const pdfList = document.getElementById('pdfList');
                        pdfList.innerHTML = '';

                        videoMatch.matching_pdfs.forEach(pdfInfo => {
                            const li = document.createElement('li');
                            li.className = 'pdf-item';
                            li.textContent = pdfInfo.file_name;
                            li.onclick = () => openPdf(pdfInfo.file_name);
                            pdfList.appendChild(li);
                        });
                    }
                }
            } catch (error) {
                console.error('Error loading PDF matches:', error);
            }
        }

        // 优化PDF查看器初始化
        function initializePdfViewer() {
            const container = document.getElementById('pdfViewerContainer');
            const header = document.getElementById('pdfViewerHeader');
            const maximizeBtn = document.getElementById('maximizePdf');
            const minimizeBtn = document.getElementById('minimizePdf');
            const closeBtn = document.getElementById('closePdf');
            const resizeHandle = document.getElementById('pdfResizeHandle');
            const pdfViewer = document.getElementById('pdfViewer');
            
            let isResizing = false;
            let originalWidth, originalHeight, originalX, originalY;
            let originalLeft, originalTop;
            let originalState = null;
            let isDragging = false; // 添加拖动状态变量

            // 初始化调整大小
            function initResize(e) {
                if (e.target !== resizeHandle || e.button !== 0) return;
                if (originalState) return;
                
                isResizing = true;
                
                // 记录初始状态
                const rect = container.getBoundingClientRect();
                originalWidth = rect.width;
                originalHeight = rect.height;
                originalX = e.clientX;
                originalY = e.clientY;
                originalLeft = rect.left;
                originalTop = rect.top;

                // 在容器和iframe上添加事件监听
                container.addEventListener('mousemove', handleResize);
                pdfViewer.addEventListener('mousemove', handleResize);
                document.addEventListener('mousemove', handleResize);
                
                container.addEventListener('mouseup', stopResize);
                pdfViewer.addEventListener('mouseup', stopResize);
                document.addEventListener('mouseup', stopResize);
                
                // 防止iframe内的内容干扰拖动
                if (pdfViewer.style.pointerEvents !== 'none') {
                    pdfViewer.style.pointerEvents = 'none';
                }
                
                e.preventDefault();
                e.stopPropagation();
            }

            // 处理调整大小
            function handleResize(e) {
                if (!isResizing) return;
                
                // 计算鼠标移动的距离
                const dx = e.clientX - originalX;
                const dy = e.clientY - originalY;

                // 计算新的尺寸
                const newWidth = Math.max(400, Math.min(originalWidth + dx, window.innerWidth - originalLeft));
                const newHeight = Math.max(300, Math.min(originalHeight + dy, window.innerHeight - originalTop));

                // 直接更新容器大小
                container.style.width = newWidth + 'px';
                container.style.width = `${newWidth}px`;
                container.style.height = `${newHeight}px`;

                e.preventDefault();
                e.stopPropagation();
            }

            // 停止调整大小
            function stopResize(e) {
                if (!isResizing) return;
                
                isResizing = false;
                
                // 移除所有事件监听
                container.removeEventListener('mousemove', handleResize);
                pdfViewer.removeEventListener('mousemove', handleResize);
                document.removeEventListener('mousemove', handleResize);
                
                container.removeEventListener('mouseup', stopResize);
                pdfViewer.removeEventListener('mouseup', stopResize);
                document.removeEventListener('mouseup', stopResize);
                
                // 恢复iframe的事件处理
                pdfViewer.style.pointerEvents = 'auto';
                
                e.preventDefault();
                e.stopPropagation();
            }

            // 只在调整手柄上添加mousedown事件监听
            resizeHandle.addEventListener('mousedown', initResize);

            // 添加窗口失焦事件监听
            window.addEventListener('blur', () => {
                if (isResizing) {
                    isResizing = false;
                    stopResize({ preventDefault: () => {}, stopPropagation: () => {} });
                }
            });

            // 最大化按钮
            maximizeBtn.onclick = () => {
                if (!originalState) {
                    // 保存当前状态
                    originalState = {
                        width: container.style.width,
                        height: container.style.height,
                        left: container.style.left,
                        top: container.style.top,
                        transform: container.style.transform
                    };

                    // 设置最大化状态
                    container.style.width = '95vw';
                    container.style.height = '95vh';
                    container.style.left = '50%';
                    container.style.top = '50%';
                    container.style.transform = 'translate(-50%, -50%)';
                    maximizeBtn.textContent = '还原';
                } else {
                    // 还原原始状态
                    Object.assign(container.style, originalState);
                    originalState = null;
                    maximizeBtn.textContent = '最大化';
                }
            };

            // 最小化按钮
            minimizeBtn.onclick = () => {
                container.style.width = '400px';
                container.style.height = '300px';
                if (originalState) {
                    originalState = null;
                    maximizeBtn.textContent = '最大化';
                }
            };

            // 关闭按钮
            closeBtn.onclick = function(event) {
                console.log("关闭按钮被点击");
                event.stopPropagation();  // 阻止事件冒泡
                container.style.display = "none";
                // 重置PDF查看器
                pdfViewer.src = "";
                if (originalState) {
                    originalState = null;
                    maximizeBtn.textContent = '最大化';
                }
            };

            // 拖动功能
            header.onmousedown = (e) => {
                if (e.target.closest('.pdf-controls')) return; // 如果点击的是控制按钮，不启动拖动

                isDragging = true;
                const rect = container.getBoundingClientRect();
                
                // 计算鼠标在容器内的相对位置
                originalX = e.clientX - rect.left;
                originalY = e.clientY - rect.top;

                // 防止iframe内的内容干扰拖动
                pdfViewer.style.pointerEvents = 'none';

                // 添加临时事件监听器
                document.addEventListener('mousemove', handleDrag);
                document.addEventListener('mouseup', stopDrag);
            };

            function handleDrag(e) {
                if (!isDragging) return;
                e.preventDefault();

                // 计算新位置
                const newLeft = e.clientX - originalX;
                const newTop = e.clientY - originalY;

                // 确保窗口不会被拖出视口
                const maxX = window.innerWidth - container.offsetWidth;
                const maxY = window.innerHeight - container.offsetHeight;

                container.style.left = `${Math.min(Math.max(0, newLeft), maxX)}px`;
                container.style.top = `${Math.min(Math.max(0, newTop), maxY)}px`;
                container.style.transform = 'none'; // 移除transform以便直接使用left/top定位
            }

            function stopDrag() {
                isDragging = false;
                
                // 恢复iframe的事件处理
                pdfViewer.style.pointerEvents = 'auto';
                
                document.removeEventListener('mousemove', handleDrag);
                document.removeEventListener('mouseup', stopDrag);
            }
        }

        // 优化打开PDF函数
        function openPdf(filename) {
            const authParams = new URLSearchParams({
                user: user,
                key: key,
                token: token
            }).toString();

            const pdfUrl = `/api/videopdf/${encodeURIComponent(folder)}/ragfiles/${encodeURIComponent(filename)}?${authParams}`;
            
            const container = document.getElementById('pdfViewerContainer');
            const viewer = document.getElementById('pdfViewer');
            const title = document.getElementById('pdfViewerTitle');
            
            // 如果容器已经显示，只更新内容
            if (container.style.display === 'block') {
                viewer.src = pdfUrl;
                title.textContent = filename;
            } else {
                // 首次显示，设置初始位置和大小
                container.style.display = 'block';
                container.style.width = '800px';
                container.style.height = '600px';
                container.style.left = '50%';
                container.style.top = '50%';
                container.style.transform = 'translate(-50%, -50%)';
                container.style.zIndex = '1000'; // 确保显示在最上层
                viewer.src = pdfUrl;
                title.textContent = filename;
            }
        }

        // 在文档加载完成后初始化
        document.addEventListener('DOMContentLoaded', () => {
            loadPdfMatch();
            initializePdfViewer();
        });

        // 在文档加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            // 获取模态窗口和按钮元素
            var modal = document.getElementById('knowledgeGraphModal');
            var btn = document.getElementById('showKnowledgeGraphBtn');
            var closeBtn = modal.querySelector('.close-modal');  // 修改这里，直接从modal中获取关闭按钮
            var maximizeBtn = document.getElementById('maximizeGraphBtn');
            var downloadBtn = document.getElementById('downloadGraphBtn');
            var graphEditorContainer = document.getElementById('graphEditorContainer');
            var graphTextEditor = document.getElementById('graphTextEditor');
            var updateGraphBtn = document.getElementById('updateGraphBtn');
            var deleteGraphBtn = document.getElementById('deleteGraphBtn');
            var editorStatus = document.getElementById('editorStatus');
            
            // 全局变量，用于存储VIP状态和图表最大化状态
            var userIsVip = false;
            var isGraphMaximized = false;
            
            // 初始化ECharts图表
            var graphChart = null;
            
            // 为关闭按钮添加点击事件
            if (closeBtn) {
                closeBtn.addEventListener('click', function(event) {
                    console.log("关闭按钮被点击");
                    event.stopPropagation();  // 阻止事件冒泡
                    modal.style.display = "none";
                    // 如果图表存在，销毁它以便下次重新创建
                    if (graphChart) {
                        graphChart.dispose();
                        graphChart = null;
                    }
                });
            }
            
            // 添加知识图谱所需的辅助函数
            function getRandomColor() {
                return '#' + Math.floor(Math.random()*16777215).toString(16).padStart(6, '0');
            }

            function getComplementaryColor(hex) {
                // 将十六进制颜色转换为RGB
                var r = parseInt(hex.slice(1, 3), 16);
                var g = parseInt(hex.slice(3, 5), 16);
                var b = parseInt(hex.slice(5, 7), 16);

                // 计算补色
                r = 255 - r;
                g = 255 - g;
                b = 255 - b;

                // 转换回十六进制
                return '#' + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1);
            }

            function getDarkerColor(color) {
                // 将颜色转换为RGB
                var r = parseInt(color.slice(1, 3), 16);
                var g = parseInt(color.slice(3, 5), 16);
                var b = parseInt(color.slice(5, 7), 16);
                
                // 使颜色变深（这里我们减少了亮度）
                r = Math.max(0, r - 50);
                g = Math.max(0, g - 50);
                b = Math.max(0, b - 50);
                
                // 转换回十六进制
                return '#' + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1);
            }

            // 添加关系到图表数据的函数
            function addRelation(source, target, value, data, links) {
                function addOrUpdateNode(name) {
                    var node = data.find(item => item.name === name);
                    if (!node) {
                        var backgroundColor = getRandomColor();
                        var textColor = getComplementaryColor(backgroundColor);
                        node = {
                            name: name, 
                            symbolSize: 50,  // 基础大小保持不变
                            itemStyle: {
                                color: backgroundColor,
                                borderColor: getDarkerColor(backgroundColor),
                                borderWidth: 2
                            },
                            label: {
                                color: textColor,
                                fontSize: 12
                            },
                            x: Math.random() * graphChart.getWidth(),
                            y: Math.random() * graphChart.getHeight()
                        };
                        data.push(node);
                    }
                    // 降低增长速度，减小系数和乘数
                    var connectionCount = links.filter(link => 
                        link.source === name || link.target === name).length;
                    node.symbolSize = Math.min(100, 50 + 8 * Math.log(1 + connectionCount*2));
                    return node;
                }
                
                addOrUpdateNode(source);
                addOrUpdateNode(target);

                // 定义连线颜色
                var linkColors = ['#8B0000', '#006400', '#00008B', '#8B4513', '#2F4F4F'];
                var existingLinkColor = links.find(link => link.value === value)?.lineStyle?.color;
                var linkColor = existingLinkColor || getDarkerColor(linkColors[links.length % linkColors.length]);

                links.push({
                    source: source,
                    target: target,
                    value: value,
                    lineStyle: {
                        color: linkColor,
                        width: 2
                    },
                    label: {
                        show: true,
                        formatter: function(params) {
                            var text = params.data.value;
                            var lines = [];
                            var maxLineLength = 20; // 每行最大字符数
                            for (var i = 0; i < text.length; i += maxLineLength) {
                                lines.push(text.substr(i, maxLineLength));
                            }
                            return lines.join('\n');
                        },
                        fontSize: 12,
                        color: '#000',
                        backgroundColor: 'rgba(255, 255, 255, 0.7)',
                        padding: [4, 8],
                        borderRadius: 4
                    }
                });
            }
            
            // 当用户点击按钮时，打开模态窗口并加载知识图谱
            btn.onclick = function() {
                console.log("知识图谱按钮被点击");
                modal.style.display = "block";
                
                // 检查VIP状态并显示/隐藏按钮
                console.log("点击按钮时检查if_vip_user:", if_vip_user);
                if (if_vip_user) {
                    updateGraphBtn.style.display = 'block';
                    deleteGraphBtn.style.display = 'block'; // 显示删除按钮
                } else {
                    updateGraphBtn.style.display = 'none';
                    deleteGraphBtn.style.display = 'none'; // 隐藏删除按钮
                }
                
                // 显示编辑区域
                graphEditorContainer.style.display = "block";
                
                // 如果图表已初始化，先销毁它
                if (graphChart) {
                    graphChart.dispose();
                    graphChart = null;
                }
                
                // 延迟初始化图表，确保DOM已完全渲染
                setTimeout(function() {
                    try {
                        graphChart = echarts.init(document.getElementById('knowledgeGraphContainer'), 'white', {renderer: 'canvas'});
                        console.log("图表初始化成功");
                        
                        // 设置加载中的状态
                        graphChart.showLoading({
                            text: '正在生成知识图谱...',
                            color: '#4CAF50',
                            textColor: '#000',
                            maskColor: 'rgba(255, 255, 255, 0.8)',
                        });
                        
                        // 调用后端API生成知识图谱
                        generateKnowledgeGraph();
                    } catch (error) {
                        console.error("图表初始化失败:", error);
                        alert("图表初始化失败: " + error.message);
                    }
                }, 300); // 添加300ms延迟确保DOM已渲染
                
                // 确保图表大小适应容器
                window.addEventListener('resize', function() {
                    if (graphChart) graphChart.resize();
                });
            };
            
            // 当用户点击关闭按钮时，关闭模态窗口
            closeBtn.onclick = function(event) {
                console.log("关闭按钮被点击");
                event.stopPropagation();  // 阻止事件冒泡
                modal.style.display = "none";
                // 如果图表存在，销毁它以便下次重新创建
                if (graphChart) {
                    graphChart.dispose();
                    graphChart = null;
                }
            };
            
            // 当用户点击模态窗口外部时，关闭模态窗口
            document.addEventListener('click', function(event) {
                if (event.target === modal) {
                    console.log("模态窗口外部被点击");
                    modal.style.display = "none";
                    // 如果图表存在，销毁它以便下次重新创建
                    if (graphChart) {
                        graphChart.dispose();
                        graphChart = null;
                    }
                }
            });
            
            // 阻止模态内容区域的点击事件冒泡
            const modalContent = modal.querySelector('.modal-content');
            if (modalContent) {
                modalContent.onclick = function(event) {
                    event.stopPropagation();
                };
            }
            
            // 更新图谱按钮点击事件
            updateGraphBtn.addEventListener('click', function() {
                updateKnowledgeGraph();
            });
            
            // 删除图谱按钮点击事件
            deleteGraphBtn.addEventListener('click', function() {
                // 弹出确认对话框
                if (confirm("确定要删除这个知识图谱吗？此操作不可恢复！")) {
                    deleteKnowledgeGraph();
                }
            });
            
            // 修改生成知识图谱的函数
            function generateKnowledgeGraph() {
                // 获取当前视频的信息
                var currentVideo = getCurrentVideo();
                console.log("当前视频信息:", currentVideo);
                
                if (!currentVideo.filename && !currentVideo.doc_id) {
                    alert('无法获取当前视频信息');
                    return;
                }
                
                // 确保图表容器已正确初始化尺寸
                if (graphChart) {
                    graphChart.resize();
                }
                
                // 准备请求参数
                var requestData = {
                    user: user,
                    key: key,
                    token: token,
                    filename: currentVideo.filename,
                    doc_id: currentVideo.doc_id,
                    dataset_id: currentVideo.dataset_id,
                    folder: currentVideo.folder
                };
                
                console.log("发送知识图谱请求:", {
                    ...requestData,
                    token: requestData.token ? (requestData.token.substring(0, 10) + '...') : null
                });
                
                // 更新加载状态
                graphChart.showLoading({
                    text: '正在生成知识图谱...',
                    color: '#4CAF50',
                    textColor: '#000',
                    maskColor: 'rgba(255, 255, 255, 0.8)',
                });
                
                // 更新状态信息
                var editorStatus = document.getElementById('editorStatus');
                editorStatus.textContent = "正在生成知识图谱...";
                
                // 禁用按钮，防止重复操作
                var updateGraphBtn = document.getElementById('updateGraphBtn');
                var deleteGraphBtn = document.getElementById('deleteGraphBtn');
                if (updateGraphBtn) updateGraphBtn.disabled = true;
                if (deleteGraphBtn) deleteGraphBtn.disabled = true;
                
                // 添加重试计数器
                let retryCount = 0;
                const maxRetries = 2;
                
                function attemptGeneration() {
                    // 调用后端API
                    fetch('/api/gen_graph', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Accept': 'application/json'
                        },
                        body: JSON.stringify(requestData)
                    })
                    .then(response => {
                        if (!response.ok) {
                            // 如果是服务器错误且还有重试次数，则重试
                            if (response.status >= 500 && retryCount < maxRetries) {
                                retryCount++;
                                console.log(`服务器错误，正在进行第${retryCount}次重试...`);
                                editorStatus.textContent = `服务器错误，正在进行第${retryCount}次重试...`;
                                
                                // 延迟3秒后重试
                                setTimeout(attemptGeneration, 3000);
                                return Promise.reject(new Error(`服务器错误 (${response.status})，正在重试...`));
                            }
                            
                            throw new Error(`网络响应不正常: ${response.status}`);
                        }
                        return response.json();
                    })
                    .then(data => {
                        console.log("知识图谱API响应:", data);
                        
                        if (!data.success) {
                            throw new Error(data.message || '生成知识图谱失败');
                        }
                        
                        // 隐藏加载状态
                        graphChart.hideLoading();
                        
                        // 解析图谱数据
                        var graphData = data.graph;
                        var graphNodes = [];
                        var graphLinks = [];
                        
                        // 填充文本编辑区域
                        var graphTextEditor = document.getElementById('graphTextEditor');
                        graphTextEditor.value = graphData || '';
                        
                        // 处理图谱数据
                        if (graphData) {
                            var lines = graphData.split('\n');
                            console.log("解析图谱数据，行数:", lines.length);
                            
                            for (var i = 0; i < lines.length; i++) {
                                var line = lines[i].trim();
                                if (!line) continue;
                                
                                var parts = line.split(',', 3);
                                if (parts.length === 3) {
                                    var source = parts[0].trim();
                                    var target = parts[1].trim();
                                    var relation = parts[2].trim();
                                    
                                    // 提取关系文本（去除花括号）
                                    relation = relation.replace(/^\{|\}$/g, '');
                                    
                                    console.log("添加关系:", source, "->", target, ":", relation);
                                    addRelation(source, target, relation, graphNodes, graphLinks);
                                }
                            }
                        }
                        
                        // 设置图表选项
                        var option = {
                            backgroundColor: '#FFFAFA',
                            series: [{
                                type: 'graph',
                                layout: 'force',
                                force: {
                                    repulsion: [450, 680],  // 增加斥力
                                    edgeLength: 200,  // 进一步增加边长度
                                    gravity: 0.05,    // 减小重力，让节点更分散
                                    layoutAnimation: true,
                                },
                                symbolSize: 50,      // 减小默认节点大小
                                roam: true,
                                draggable: true,
                                focusNodeAdjacency: true,
                                label: {
                                    show: true,
                                    position: 'inside',
                                    fontSize: 14     // 稍微减小字体大小
                                },
                                edgeLabel: {
                                    show: true,
                                    formatter: '{c}',
                                    fontSize: 12,
                                    color: '#000',
                                    backgroundColor: 'rgba(255, 255, 255, 0.7)',
                                    padding: [4, 8],
                                    borderRadius: 4
                                },
                                data: graphNodes,
                                links: graphLinks,
                                lineStyle: {
                                    curveness: 0.1
                                }
                            }]
                        };
                        
                        // 应用图表选项
                        graphChart.setOption(option);
                        
                        // 存储原始图谱数据
                        originalGraphData = graphData || '';
                        
                        // 更新状态信息
                        editorStatus.textContent = "知识图谱已成功生成";
                        
                        // 如果是最大化状态，确保图表大小正确
                        if (isGraphMaximized) {
                            setTimeout(function() {
                                graphChart.resize();
                            }, 100);
                        }
                    })
                    .catch(error => {
                        console.error("生成知识图谱失败:", error);
                        
                        // 如果是重试中的错误，不显示失败UI
                        if (error.message && error.message.includes('正在重试')) {
                            return;
                        }
                        
                        graphChart.hideLoading();
                        graphChart.setOption({
                            title: {
                                text: '生成知识图谱失败',
                                subtext: error.message,
                                left: 'center',
                                top: 'center',
                                textStyle: {
                                    fontSize: 18,
                                    color: '#e74c3c'
                                },
                                subtextStyle: {
                                    fontSize: 14,
                                    color: '#7f8c8d'
                                }
                            }
                        });
                        
                        // 更新状态信息
                        editorStatus.textContent = "生成知识图谱失败: " + error.message;
                        
                        // 如果是服务器错误，提供重试选项
                        if (error.message && error.message.includes('500')) {
                            if (confirm("服务器暂时无法处理请求。是否稍后重试？")) {
                                setTimeout(function() {
                                    // 重置重试计数器
                                    retryCount = 0;
                                    attemptGeneration();
                                }, 5000);
                            }
                        }
                    })
                    .finally(() => {
                        // 恢复按钮状态
                        if (updateGraphBtn) updateGraphBtn.disabled = false;
                        if (deleteGraphBtn) deleteGraphBtn.disabled = false;
                    });
                }
                
                // 开始第一次尝试
                attemptGeneration();
            }
            
            // 更新知识图谱的函数
            function updateKnowledgeGraph() {
                var updatedGraphData = document.getElementById('graphTextEditor').value.trim();
                
                // 如果数据没有变化，不需要更新
                if (updatedGraphData === originalGraphData) {
                    editorStatus.textContent = "数据未变更，无需更新";
                    setTimeout(() => { editorStatus.textContent = ""; }, 3000);
                    return;
                }
                
                // 获取当前视频的信息
                var currentVideo = getCurrentVideo();
                
                // 准备请求参数
                var requestData = {
                    user: user,
                    key: key,
                    token: token,
                    filename: currentVideo.filename,
                    folder: currentVideo.folder,
                    graph_data: updatedGraphData
                };
                
                // 显示更新中状态
                editorStatus.textContent = "正在更新...";
                updateGraphBtn.disabled = true;
                
                // 调用后端API更新图谱
                fetch('/api/update_graph', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify(requestData)
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error('网络响应不正常: ' + response.status);
                    }
                    return response.json();
                })
                .then(data => {
                    if (!data.success) {
                        throw new Error(data.message || '更新知识图谱失败');
                    }
                    
                    // 更新成功
                    editorStatus.textContent = "更新成功！";
                    originalGraphData = updatedGraphData; // 更新原始数据
                    
                    // 如果服务器返回了VIP状态，更新全局变量
                    if (data.if_vip_user !== undefined) {
                        if_vip_user = data.if_vip_user;
                        console.log("更新后的if_vip_user:", if_vip_user);
                        if (if_vip_user) {
                            updateGraphBtn.style.display = 'block';
                            deleteGraphBtn.style.display = 'block'; // 显示删除按钮
                    } else {
                            updateGraphBtn.style.display = 'none';
                            deleteGraphBtn.style.display = 'none'; // 隐藏删除按钮
                        }
                    }

                    // 重新加载图表
                    modal.style.display = "none";
                    setTimeout(() => {
                        modal.style.display = "block";
                        if (graphChart) {
                            graphChart.dispose();
                            graphChart = null;
                        }
                        graphChart = echarts.init(document.getElementById('knowledgeGraphContainer'), 'white', {renderer: 'canvas'});
                        graphChart.showLoading({
                            text: '正在重新加载知识图谱...',
                            color: '#4CAF50',
                            textColor: '#000',
                            maskColor: 'rgba(255, 255, 255, 0.8)',
                        });
                        generateKnowledgeGraph();
                    }, 500);
                })
                .catch(error => {
                    console.error("更新知识图谱失败:", error);
                    editorStatus.textContent = "更新失败: " + error.message;
                })
                .finally(() => {
                    // 恢复按钮状态
                    updateGraphBtn.disabled = false;
                    setTimeout(() => { 
                        if (editorStatus.textContent === "正在更新...") {
                            editorStatus.textContent = "";
                        }
                    }, 3000);
                });
                
            }
            
            // 获取当前视频信息的辅助函数
            function getCurrentVideo() {
                // 使用与initializeVideoChat函数相同的参数
                return {
                    filename: videoName || '',
                    doc_id: doc_id || '',
                    dataset_id: dataset_id || '',
                    folder: folder || ''
                };
            }

            // 添加删除知识图谱的函数
            function deleteKnowledgeGraph() {
                // 获取当前视频的信息
                var currentVideo = getCurrentVideo();
                console.log("删除知识图谱 - 当前视频信息:", currentVideo);
                
                if (!currentVideo.filename) {
                    alert('无法获取当前视频信息');
                    return;
                }
                
                // 准备请求参数
                var requestData = {
                    user: user,
                    key: key,
                    token: token,
                    filename: currentVideo.filename,
                    folder: currentVideo.folder
                };
                
                console.log("发送删除知识图谱请求:", {
                    ...requestData,
                    token: requestData.token ? (requestData.token.substring(0, 10) + '...') : null
                });
                
                // 显示状态信息
                var editorStatus = document.getElementById('editorStatus');
                editorStatus.textContent = "正在删除知识图谱...";
                
                // 禁用按钮，防止重复操作
                var deleteGraphBtn = document.getElementById('deleteGraphBtn');
                var updateGraphBtn = document.getElementById('updateGraphBtn');
                var showKnowledgeGraphBtn = document.getElementById('showKnowledgeGraphBtn');
                
                if (deleteGraphBtn) deleteGraphBtn.disabled = true;
                if (updateGraphBtn) updateGraphBtn.disabled = true;
                
                // 调用后端API
                fetch('/api/delete_graph', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify(requestData)
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error('网络响应不正常: ' + response.status);
                    }
                    return response.json();
                })
                .then(data => {
                    console.log("删除知识图谱API响应:", data);
                    
                    if (!data.success) {
                        throw new Error(data.message || '删除知识图谱失败');
                    }
                    
                    // 清空编辑区域
                    var graphTextEditor = document.getElementById('graphTextEditor');
                    graphTextEditor.value = '';
                    
                    // 清空图表
                    if (graphChart) {
                        graphChart.clear();
                        // 重置图表
                        graphChart.setOption({
                            backgroundColor: '#FFFAFA',
                            series: [{
                                type: 'graph',
                                layout: 'force',
                                data: [],
                                links: []
                            }]
                        });
                    }
                    
                    // 重置原始图谱数据
                    originalGraphData = '';
                    
                    // 更新状态信息
                    editorStatus.textContent = "知识图谱已删除";
                    
                    // 显示成功消息并询问是否要重新生成
                    if (confirm("知识图谱已成功删除！是否要立即重新生成？")) {
                        // 关闭当前模态窗口
                        var modal = document.getElementById('knowledgeGraphModal');
                        modal.style.display = "none";
                        
                        // 延迟2秒后重新打开并生成
                        setTimeout(function() {
                            // 重新初始化图表
                            if (graphChart) {
                                graphChart.dispose();
                                graphChart = null;
                            }
                            
                            // 模拟点击生成按钮
                            if (showKnowledgeGraphBtn) {
                                showKnowledgeGraphBtn.click();
                            }
                        }, 2000);
                    } else {
                        // 关闭模态窗口
                        var modal = document.getElementById('knowledgeGraphModal');
                        modal.style.display = "none";
                    }
                })
                .catch(error => {
                    console.error("删除知识图谱失败:", error);
                    editorStatus.textContent = "删除失败: " + error.message;
                    alert("删除知识图谱失败: " + error.message);
                })
                .finally(() => {
                    // 恢复按钮状态
                    if (deleteGraphBtn) deleteGraphBtn.disabled = false;
                    if (updateGraphBtn) updateGraphBtn.disabled = false;
                });
            }

            // 添加最大化按钮功能
            maximizeBtn.addEventListener('click', function() {
                isGraphMaximized = !isGraphMaximized;
                
                if (isGraphMaximized) {
                    // 最大化
                    modal.classList.add('modal-maximized');
                    maximizeBtn.textContent = '还原';
                } else {
                    // 还原
                    modal.classList.remove('modal-maximized');
                    maximizeBtn.textContent = '最大化';
                }
                
                // 延迟一点时间后调整图表大小，确保DOM已更新
                setTimeout(function() {
                    if (graphChart) {
                        graphChart.resize();
                    }
                }, 300);
            });
            
            // 添加下载图片按钮功能
            downloadBtn.addEventListener('click', function() {
                if (!graphChart) {
                    alert('图表尚未加载完成，请稍后再试');
                    return;
                }
                
                try {
                    // 获取当前视频文件名作为图片文件名的一部分
                    var currentVideo = getCurrentVideo();
                    var filename = currentVideo.filename || 'knowledge_graph';
                    // 移除文件扩展名
                    filename = filename.replace(/\.[^/.]+$/, "");
                    // 添加时间戳
                    var timestamp = new Date().toISOString().replace(/[:.]/g, '-').substring(0, 19);
                    var imgName = `${filename}_graph_${timestamp}.png`;
                    
                    // 获取图表的数据URL
                    var url = graphChart.getDataURL({
                        type: 'png',
                        pixelRatio: 2, // 使用2倍像素比以获得更高质量的图像
                        backgroundColor: '#fff'
                    });
                    
                    // 创建下载链接
                    var link = document.createElement('a');
                    link.download = imgName;
                    link.href = url;
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                    
                    // 显示成功消息
                    editorStatus.textContent = "图片已成功下载";
                    setTimeout(() => {
                        if (editorStatus.textContent === "图片已成功下载") {
                            editorStatus.textContent = "";
                        }
                    }, 3000);
                } catch (error) {
                    console.error('下载图片失败:', error);
                    alert('下载图片失败: ' + error.message);
                }
            });
        });

        // 添加题目生成相关代码
        document.addEventListener('DOMContentLoaded', function() {
            const questionsModal = document.getElementById('questionsModal');
            const showQuestionsBtn = document.getElementById('showQuestionsBtn');
            const closeQuestionsBtn = questionsModal.querySelector('.close-modal');
            const generateQuestionsBtn = document.getElementById('generateQuestionsBtn');
            const updateQuestionsBtn = document.getElementById('updateQuestionsBtn');
            const deleteQuestionsBtn = document.getElementById('deleteQuestionsBtn');
            const questionTypeSelect = document.getElementById('questionType');
            const questionsContainer = document.getElementById('questionsContainer');
            const questionsStatus = document.getElementById('questionsStatus');

            // 显示题目模态窗口
            showQuestionsBtn.onclick = async function() {
                questionsModal.style.display = "block";
                questionsStatus.textContent = "正在初始化...";
                
                try {
                    // 确保所有参数都正确编码和格式化
                    const requestData = {
                        filename: String(videoName || ''),
                        folder: String(folder || ''),
                        user: String(user || ''),
                        key: String(key || ''),
                        token: String(token || ''),
                        question_type: String(document.getElementById('questionType').value || 'choice')
                    };

                    // 打印初始化参数用于调试
                    console.log('Initializing with params:', {
                        filename: requestData.filename,
                        folder: requestData.folder,
                        user: requestData.user,
                        key: '***', // 隐藏密钥
                        token: requestData.token?.substring(0, 10) + '...', // 隐藏完整token
                        question_type: requestData.question_type
                    });

                    // 检查必要参数
                    if (!requestData.filename || !requestData.user || !requestData.key || !requestData.token) {
                        throw new Error('缺少必要参数，请刷新页面重试');
                    }

                    // 发送初始化请求
                    const response = await fetch('/api/init_questions', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(requestData)
                    });

                    // 获取响应文本，无论成功与否
                    const responseText = await response.text();
                    console.log('Raw response:', responseText);
                    
                    // 尝试解析JSON
                    let data;
                    try {
                        data = JSON.parse(responseText);
                    } catch (e) {
                        console.error('Failed to parse response as JSON:', e);
                        throw new Error(`服务器返回了无效的响应: ${responseText.substring(0, 100)}...`);
                    }
                    
                    console.log('Parsed response:', data);

                    if (!response.ok) {
                        // 如果是字幕文件不存在的错误，提供更友好的提示
                        if (data.message && data.message.includes('字幕文件不存在')) {
                            throw new Error('字幕文件不存在，请确保视频已经生成字幕');
                        } else {
                            throw new Error(data.message || `服务器错误 (${response.status}): ${responseText.substring(0, 100)}...`);
                        }
                    }

                    if (data.success) {
                        // 更新全局变量
                        doc_id = data.doc_id || '';
                        dataset_id = data.dataset_id || '';
                        if_vip_user = Boolean(data.if_vip_user);

                        console.log('Updated globals:', { doc_id, dataset_id, if_vip_user });

                        // 根据VIP状态显示/隐藏按钮
                        updateQuestionsBtn.style.display = if_vip_user ? 'block' : 'none';
                        deleteQuestionsBtn.style.display = if_vip_user ? 'block' : 'none';

                        // 如果题目已存在，直接加载
                        if (data.questions_exist) {
                            await loadQuestions();
                        } else {
                            questionsStatus.textContent = "准备就绪，请点击生成题目按钮";
                        }
                    } else {
                        throw new Error(data.message || '初始化失败，未知错误');
                    }
                } catch (error) {
                    console.error('初始化题目失败:', error);
                    
                    // 处理特定错误类型，显示更友好的错误信息
                    let errorMessage = error.message;
                    
                    // 处理Invalid argument错误
                    if (errorMessage.includes('Invalid argument') || errorMessage.includes('Errno 22')) {
                        errorMessage = '正在准备题目资源，请稍候...';
                        questionsStatus.textContent = errorMessage;
                        
                        // 自动重试
                        setTimeout(() => {
                            questionsStatus.textContent = '正在重新连接...';
                            this.onclick();
                        }, 2000);
                        return;
                    }
                    
                    // 处理其他常见错误
                    if (errorMessage.includes('doc_id')) {
                        errorMessage = '视频内容正在准备中，请稍后再试';
                    } else if (errorMessage.includes('network') || errorMessage.includes('Failed to fetch')) {
                        errorMessage = '网络连接不稳定，请检查网络后重试';
                    }
                    
                    questionsStatus.textContent = `初始化失败: ${errorMessage}`;
                    
                    // 如果是网络错误，提供重试选项
                    if (error.name === 'TypeError' && error.message.includes('network')) {
                        if (confirm('网络连接失败，是否重试？')) {
                            this.onclick();
                        }
                    }
                }
            };

            // 关闭模态窗口
            closeQuestionsBtn.onclick = function(event) {
                event.stopPropagation();  // 阻止事件冒泡
                questionsModal.style.display = "none";
            };

            // 点击模态窗口外部关闭
            questionsModal.addEventListener('click', function(event) {
                if (event.target === this) {
                    questionsModal.style.display = "none";
                }
            });

            // 阻止模态内容区域的点击事件冒泡
            const questionsModalContent = questionsModal.querySelector('.modal-content');
            if (questionsModalContent) {
                questionsModalContent.onclick = function(event) {
                    event.stopPropagation();
                };
            }

            // 生成题目
            generateQuestionsBtn.onclick = async function() {
                questionsStatus.textContent = "正在生成题目...";
                generateQuestionsBtn.disabled = true;
                
                try {
                    // 获取题目类型和数量
                    const questionType = document.getElementById('questionType');
                    const num_questions = parseInt(questionCount.value) || (questionType.value === 'choice' ? 5 : 3);
                    
                    console.log('Generating questions with params:', {
                        user: user,
                        key: key,
                        token: token?.substring(0, 10) + '...',
                        filename: videoName,
                        doc_id: doc_id,
                        dataset_id: dataset_id,
                        folder: folder,
                        question_type: questionType.value,
                        num_questions: num_questions
                    });
                    
                    const response = await fetch('/api/gen_questions', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            user: user,
                            key: key,
                            token: token,
                            filename: videoName,
                            doc_id: doc_id,
                            dataset_id: dataset_id,
                            folder: folder,
                            question_type: questionType.value,
                            num_questions: num_questions
                        })
                    });
                    
                    const data = await response.json();
                    console.log('Generation response:', data);
                    
                    if (!response.ok) {
                        throw new Error(data.message || '生成题目失败');
                    }
                    
                    if (data.success && data.questions) {
                        // 验证和标准化数据
                        const validatedData = validateAndNormalizeQuestionsData(data.questions, questionType.value);
                        if (!validatedData) {
                            throw new Error('生成的题目数据格式不正确');
                        }

                        // 保存题目数据到全局变量
                        currentQuestionsData = validatedData;

                        displayQuestions(validatedData);

                        // 计算题目数量
                        const questionCount = questionType.value === 'choice' ?
                            (validatedData.questions ? validatedData.questions.length : 0) :
                            (validatedData.calculation_questions ? validatedData.calculation_questions.length : 0);

                        questionsStatus.textContent = `成功生成 ${questionCount} 道${questionType.value === 'choice' ? '选择题' : '计算题'}！`;

                        // 显示token使用信息
                        if (data.questions.tokens_used) {
                            console.log(`本次生成使用了 ${data.questions.tokens_used} tokens`);
                        }

                        // 显示更新和删除按钮（如果是VIP用户）
                        if (if_vip_user) {
                            updateQuestionsBtn.style.display = 'block';
                            deleteQuestionsBtn.style.display = 'block';
                        }
                    } else {
                        throw new Error(data.message || '生成题目失败');
                    }
                } catch (error) {
                    console.error('生成题目失败:', error);
                    
                    // 处理特定错误类型
                    let errorMessage = error.message;
                    var shouldRetry = false;
                    
                    // 处理Invalid argument错误
                    if (errorMessage.includes('Invalid argument') || errorMessage.includes('Errno 22')) {
                        errorMessage = '正在准备生成题目，请稍候...';
                        questionsStatus.textContent = errorMessage;
                        shouldRetry = true;
                    } 
                    // 处理其他常见错误
                    else if (errorMessage.includes('network') || errorMessage.includes('Failed to fetch')) {
                        errorMessage = '网络连接不稳定，请检查网络后重试';
                        shouldRetry = true;
                    }
                    else {
                        questionsStatus.textContent = `生成失败: ${errorMessage}`;
                    }
                    
                    // 自动重试
                    if (shouldRetry) {
                        let retryCount = 0;
                        const maxRetries = 2;
                        
                        const retryGeneration = () => {
                            if (retryCount < maxRetries) {
                                retryCount++;
                                questionsStatus.textContent = `正在重新尝试生成题目...(${retryCount}/${maxRetries})`;
                                
                                setTimeout(() => {
                                    // 重新调用生成函数
                                    this.onclick();
                                }, 2000);
                            } else {
                                questionsStatus.textContent = '生成题目暂时不可用，请稍后再试';
                                generateQuestionsBtn.disabled = false;
                            }
                        };
                        
                        retryGeneration();
                        return;
                    }
                } finally {
                    if (!shouldRetry) {
                        generateQuestionsBtn.disabled = false;
                    }
                }
            };

            // 更新题目
            updateQuestionsBtn.onclick = async function() {
                if (!if_vip_user) {
                    alert('只有VIP用户才能更新题目');
                    return;
                }

                if (confirm('确定要更新题目吗？这将重新生成所有题目。')) {
                    generateQuestionsBtn.click();
                }
            };

            // 删除题目
            deleteQuestionsBtn.onclick = async function() {
                if (!if_vip_user) {
                    alert('只有VIP用户才能删除题目');
                    return;
                }

                if (!confirm('确定要删除这些题目吗？此操作不可恢复！')) {
                    return;
                }

                questionsStatus.textContent = "正在删除题目...";
                deleteQuestionsBtn.disabled = true;

                try {
                    const response = await fetch('/api/delete_questions', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            user: user,
                            key: key,
                            token: token,
                            filename: videoName,
                            folder: folder,
                            question_type: document.getElementById('questionType').value
                        })
                    });

                    const data = await response.json();
                    
                    if (!response.ok) {
                        throw new Error(data.message || '删除题目失败');
                    }

                    if (data.success) {
                        questionsContainer.innerHTML = '';
                        questionsStatus.textContent = "题目已删除";
                        
                        // 询问是否要重新生成
                        if (confirm('题目已成功删除！是否要立即重新生成？')) {
                            generateQuestionsBtn.click();
                        }
                    } else {
                        throw new Error(data.message || '删除题目失败');
                    }
                } catch (error) {
                    console.error('删除题目失败:', error);
                    questionsStatus.textContent = `删除失败: ${error.message}`;
                } finally {
                    deleteQuestionsBtn.disabled = false;
                }
            };

            // 加载已存在的题目
            async function loadQuestions() {
                questionsStatus.textContent = "正在加载题目...";
                
                try {
                    const response = await fetch('/api/gen_questions', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            user: user,
                            key: key,
                            token: token,
                            filename: videoName,
                            doc_id: doc_id,
                            dataset_id: dataset_id,
                            folder: folder,
                            question_type: document.getElementById('questionType').value
                        })
                    });
                    
                    const data = await response.json();
                    
                    if (!response.ok) {
                        throw new Error(data.message || '加载题目失败');
                    }
                    
                    if (data.success && data.questions) {
                        // 验证和标准化数据
                        const questionType = document.getElementById('questionType').value;
                        const validatedData = validateAndNormalizeQuestionsData(data.questions, questionType);
                        if (!validatedData) {
                            throw new Error('加载的题目数据格式不正确');
                        }

                        // 保存题目数据到全局变量
                        currentQuestionsData = validatedData;

                        displayQuestions(validatedData);

                        // 计算题目数量
                        const questionCount = questionType === 'choice' ?
                            (validatedData.questions ? validatedData.questions.length : 0) :
                            (validatedData.calculation_questions ? validatedData.calculation_questions.length : 0);

                        questionsStatus.textContent = `成功加载 ${questionCount} 道${questionType === 'choice' ? '选择题' : '计算题'}！`;

                        // 显示更新和删除按钮（如果是VIP用户）
                        if (if_vip_user) {
                            updateQuestionsBtn.style.display = 'block';
                            deleteQuestionsBtn.style.display = 'block';
                        }
                    } else {
                        throw new Error(data.message || '加载题目失败');
                    }
                } catch (error) {
                    console.error('加载题目失败:', error);
                    
                    // 处理特定错误类型
                    let errorMessage = error.message;
                    let shouldRetry = false;
                    
                    // 处理Invalid argument错误
                    if (errorMessage.includes('Invalid argument') || errorMessage.includes('Errno 22')) {
                        errorMessage = '正在准备题目数据，请稍候...';
                        questionsStatus.textContent = errorMessage;
                        shouldRetry = true;
                    } 
                    // 处理其他常见错误
                    else if (errorMessage.includes('network') || errorMessage.includes('Failed to fetch')) {
                        errorMessage = '网络连接不稳定，请检查网络后重试';
                        shouldRetry = true;
                    }
                    else {
                        questionsStatus.textContent = `加载失败: ${errorMessage}`;
                    }
                    
                    // 自动重试
                    if (shouldRetry) {
                        setTimeout(() => {
                            questionsStatus.textContent = '正在重新连接...';
                            loadQuestions();
                        }, 2000);
                    }
                }
            }

            // displayQuestions 函数已移到全局作用域

            // 题目类型切换时重新加载题目
            document.getElementById('questionType').onchange = function() {
                loadQuestions();
            };
        });

        // 初始化题目
        function initQuestions() {
            console.log("Initializing questions...");
            const filename = encodeURIComponent(getFilename());
            const folder = encodeURIComponent(getFolder());
            const user = encodeURIComponent(getUser());
            const key = encodeURIComponent(getKey());
            const token = encodeURIComponent(getToken());
            const questionType = document.getElementById('question-type-select').value;
            
            console.log(`Parameters: filename=${filename}, folder=${folder}, user=${user}, key=${key}, token=${token}, question_type=${questionType}`);
            
            fetch(`/api/init_questions?filename=${filename}&folder=${folder}&user=${user}&key=${key}&token=${token}&question_type=${questionType}`)
                .then(response => {
                    console.log("Init questions response status:", response.status);
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    console.log("Init questions response data:", data);
                    if (data.success) {
                        document.getElementById('questions-doc-id').value = data.doc_id;
                        document.getElementById('questions-dataset-id').value = data.dataset_id;
                        
                        if (data.questions_exist) {
                            loadQuestions();
                        } else {
                            document.getElementById('questions-container').innerHTML = '<div class="no-questions">暂无题目，请点击"生成题目"按钮生成题目。</div>';
                        }
                    } else {
                        console.error("Init questions failed:", data.message);
                        document.getElementById('questions-container').innerHTML = `<div class="error-message">初始化失败：${data.message}</div>`;
                    }
                })
                .catch(error => {
                    console.error("Error initializing questions:", error);
                    document.getElementById('questions-container').innerHTML = '<div class="error-message">初始化失败，请稍后重试</div>';
                });
        }

        // 生成题目
        function generateQuestions() {
            console.log("Generating questions...");
            const docId = document.getElementById('questions-doc-id').value;
            const datasetId = document.getElementById('questions-dataset-id').value;
            const user = encodeURIComponent(getUser());
            const key = encodeURIComponent(getKey());
            const token = encodeURIComponent(getToken());
            const questionType = document.getElementById('question-type-select').value;
            const questionCount = document.getElementById('question-count-select').value;
            
            console.log(`Parameters: doc_id=${docId}, dataset_id=${datasetId}, user=${user}, key=${key}, token=${token}, question_type=${questionType}, question_count=${questionCount}`);
            
            document.getElementById('questions-container').innerHTML = '<div class="loading-questions">正在生成题目，请稍候...</div>';
            
            fetch(`/api/gen_questions?doc_id=${docId}&dataset_id=${datasetId}&user=${user}&key=${key}&token=${token}&question_type=${questionType}&question_count=${questionCount}`)
                .then(response => {
                    console.log("Generate questions response status:", response.status);
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    console.log("Generate questions response data:", data);
                    if (data.success) {
                        loadQuestions();
                    } else {
                        console.error("Generate questions failed:", data.message);
                        document.getElementById('questions-container').innerHTML = `<div class="error-message">生成题目失败：${data.message}</div>`;
                    }
                })
                .catch(error => {
                    console.error("Error generating questions:", error);
                    document.getElementById('questions-container').innerHTML = '<div class="error-message">生成题目失败，请稍后重试</div>';
                });
        }

        // 全局变量，用于存储当前编辑的题目数据
        let currentQuestionsData = null;
        let currentEditIndex = -1;
        let currentEditType = '';

        // 题目状态管理
        const QuestionState = {
            IDLE: 'idle',
            LOADING: 'loading',
            GENERATING: 'generating',
            UPDATING: 'updating',
            DELETING: 'deleting',
            ERROR: 'error'
        };

        let currentState = QuestionState.IDLE;

        // 统一的状态更新函数
        function updateQuestionStatus(state, message = '') {
            const questionsStatus = document.getElementById('questionsStatus');
            const generateBtn = document.getElementById('generateQuestionsBtn');
            const updateBtn = document.getElementById('updateQuestionsBtn');
            const deleteBtn = document.getElementById('deleteQuestionsBtn');

            currentState = state;

            // 更新状态文本
            if (questionsStatus) {
                questionsStatus.textContent = message;
                questionsStatus.className = `questions-status ${state}`;
            }

            // 更新按钮状态
            const isWorking = [QuestionState.LOADING, QuestionState.GENERATING, QuestionState.UPDATING, QuestionState.DELETING].includes(state);

            if (generateBtn) generateBtn.disabled = isWorking;
            if (updateBtn) updateBtn.disabled = isWorking;
            if (deleteBtn) deleteBtn.disabled = isWorking;
        }

        // 统一的错误处理函数
        function handleQuestionError(error, operation = '操作') {
            console.error(`${operation}失败:`, error);

            let errorMessage = error.message || '未知错误';
            let shouldRetry = false;

            // 处理特定错误类型
            if (errorMessage.includes('Invalid argument') || errorMessage.includes('Errno 22')) {
                errorMessage = '正在准备数据，请稍候...';
                shouldRetry = true;
            } else if (errorMessage.includes('network') || errorMessage.includes('Failed to fetch')) {
                errorMessage = '网络连接不稳定，请检查网络后重试';
                shouldRetry = true;
            } else if (errorMessage.includes('401')) {
                errorMessage = '身份验证失败，请刷新页面重新登录';
            } else if (errorMessage.includes('403')) {
                errorMessage = '权限不足，该操作需要VIP权限';
            } else if (errorMessage.includes('500')) {
                errorMessage = '服务器内部错误，请稍后重试';
                shouldRetry = true;
            }

            updateQuestionStatus(QuestionState.ERROR, `${operation}失败: ${errorMessage}`);

            return { shouldRetry, errorMessage };
        }

        // 数据验证和标准化函数
        function validateAndNormalizeQuestionsData(questionsData, questionType) {
            if (!questionsData || typeof questionsData !== 'object') {
                console.error('Invalid questions data:', questionsData);
                return null;
            }

            if (questionType === 'choice') {
                return validateChoiceQuestions(questionsData);
            } else if (questionType === 'calculation') {
                return validateCalculationQuestions(questionsData);
            }

            return questionsData;
        }

        // 检查是否包含占位符文本
        function isPlaceholderText(text) {
            const placeholders = [
                '题目内容', '选项A', '选项B', '选项C', '选项D',
                '详细解析', '解析内容', '解题步骤说明', '关键知识点',
                'question content', 'option a', 'option b', 'explanation'
            ];

            const lowerText = text.toLowerCase();
            return placeholders.some(placeholder => lowerText.includes(placeholder.toLowerCase()));
        }

        function validateChoiceQuestions(questionsData) {
            if (!questionsData.questions || !Array.isArray(questionsData.questions)) {
                console.error('Invalid choice questions data');
                return { questions: [] };
            }

            const validatedQuestions = questionsData.questions.map((question, index) => {
                return {
                    id: question.id || (index + 1).toString(),
                    question: question.question || '',
                    options: Array.isArray(question.options) ? question.options : [],
                    correct_answer: question.correct_answer || 'A',
                    explanation: question.explanation || ''
                };
            }).filter(q => {
                // 过滤空题目和占位符题目
                if (!q.question.trim()) return false;
                if (isPlaceholderText(q.question)) {
                    console.warn('过滤占位符题目:', q.question);
                    return false;
                }

                // 检查选项是否包含占位符
                const hasPlaceholderOptions = q.options.some(option => isPlaceholderText(option));
                if (hasPlaceholderOptions) {
                    console.warn('过滤包含占位符选项的题目:', q.question);
                    return false;
                }

                return true;
            });

            return { ...questionsData, questions: validatedQuestions };
        }

        function validateCalculationQuestions(questionsData) {
            if (!questionsData.calculation_questions || !Array.isArray(questionsData.calculation_questions)) {
                console.error('Invalid calculation questions data');
                return { calculation_questions: [] };
            }

            const validatedQuestions = questionsData.calculation_questions.map((question, index) => {
                // 处理solution_steps
                let solutionSteps = question.solution_steps || [];
                if (typeof solutionSteps === 'string') {
                    try {
                        solutionSteps = JSON.parse(solutionSteps);
                    } catch (e) {
                        console.error('Failed to parse solution_steps:', e);
                        solutionSteps = [];
                    }
                }

                if (!Array.isArray(solutionSteps)) {
                    console.error('solution_steps is not an array:', solutionSteps);
                    solutionSteps = [];
                }

                // 处理key_points
                let keyPoints = question.key_points || [];
                if (typeof keyPoints === 'string') {
                    try {
                        keyPoints = JSON.parse(keyPoints);
                    } catch (e) {
                        keyPoints = [keyPoints];
                    }
                }

                if (!Array.isArray(keyPoints)) {
                    keyPoints = [String(keyPoints)];
                }

                return {
                    id: question.id || (index + 1).toString(),
                    question: question.question || '',
                    solution_steps: solutionSteps,
                    final_answer: question.final_answer || { value: '', unit: '' },
                    key_points: keyPoints
                };
            }).filter(q => {
                // 过滤空题目和占位符题目
                if (!q.question.trim()) return false;
                if (isPlaceholderText(q.question)) {
                    console.warn('过滤占位符计算题:', q.question);
                    return false;
                }

                // 检查解题步骤是否包含占位符
                const hasPlaceholderSteps = q.solution_steps.some(step =>
                    isPlaceholderText(step.description || '') ||
                    isPlaceholderText(step.formula || '') ||
                    isPlaceholderText(step.calculation || '')
                );
                if (hasPlaceholderSteps) {
                    console.warn('过滤包含占位符步骤的计算题:', q.question);
                    return false;
                }

                // 检查最终答案是否包含占位符
                let finalAnswerText = '';
                if (typeof q.final_answer === 'string') {
                    // 新的简化格式
                    finalAnswerText = q.final_answer;
                } else if (typeof q.final_answer === 'object' && q.final_answer) {
                    // 兼容旧格式
                    finalAnswerText = (q.final_answer.value || '') + ' ' + (q.final_answer.unit || '');
                }

                if (isPlaceholderText(finalAnswerText)) {
                    console.warn('过滤包含占位符答案的计算题:', q.question);
                    return false;
                }

                // 检查关键知识点是否包含占位符
                const hasPlaceholderKeyPoints = q.key_points.some(point => isPlaceholderText(point || ''));
                if (hasPlaceholderKeyPoints) {
                    console.warn('过滤包含占位符知识点的计算题:', q.question);
                    return false;
                }

                return true;
            });

            return { ...questionsData, calculation_questions: validatedQuestions };
        }

        // 渲染选择题
        function renderChoiceQuestions(questions, questionsContainer) {
            questions.forEach((question, index) => {
                const questionDiv = document.createElement('div');
                questionDiv.className = 'question-item';

                const optionsHtml = question.options.map(option => {
                    return `<div class="question-option">${option}</div>`;
                }).join('');

                const editButtonHtml = if_vip_user ?
                    `<button class="edit-question-btn" data-index="${index}" data-type="choice">编辑题目</button>` : '';

                questionDiv.innerHTML = `
                    <div class="question-content">${index + 1}. ${question.question}</div>
                    <div class="question-options">
                        ${optionsHtml}
                    </div>
                    <div class="question-actions">
                        <button class="show-explanation-btn">查看解析</button>
                        ${editButtonHtml}
                    </div>
                    <div class="question-explanation" style="display: none;">
                        <strong>正确答案：</strong>${question.correct_answer}<br><br>
                        <strong>解析：</strong><br>${question.explanation}
                    </div>
                `;

                addExplanationToggle(questionDiv);
                questionsContainer.appendChild(questionDiv);
            });
        }

        // 渲染计算题
        function renderCalculationQuestions(questions, questionsContainer) {
            questions.forEach((question, index) => {
                const questionDiv = document.createElement('div');
                questionDiv.className = 'question-item';

                const stepsHtml = question.solution_steps.map(step => {
                    const description = step.description || '';
                    const formula = step.formula || '';  // 兼容旧格式
                    const calculation = step.calculation || '';

                    return `
                        <div class="calculation-step">
                            <strong>步骤 ${step.step_number}：</strong><br>
                            ${description}<br>
                            ${formula ? `使用公式：${formula}<br>` : ''}
                            ${calculation ? `计算过程：${calculation}<br>` : ''}
                        </div>
                    `;
                }).join('');

                const keyPointsHtml = question.key_points.map(point => {
                    return `• ${String(point)}`;
                }).join('<br>');

                const editButtonHtml = if_vip_user ?
                    `<button class="edit-question-btn" data-index="${index}" data-type="calculation">编辑题目</button>` : '';

                questionDiv.innerHTML = `
                    <div class="question-content">${index + 1}. ${question.question}</div>
                    <div class="question-actions">
                        <button class="show-explanation-btn">查看解析</button>
                        ${editButtonHtml}
                    </div>
                    <div class="question-explanation" style="display: none;">
                        <div class="calculation-steps">
                            ${stepsHtml}
                        </div>
                        <div class="final-answer">
                            最终答案：${typeof question.final_answer === 'string' ? question.final_answer : (question.final_answer.value + ' ' + question.final_answer.unit)}
                        </div>
                        <div class="key-points">
                            <strong>关键知识点：</strong><br>
                            ${keyPointsHtml}
                        </div>
                    </div>
                `;

                addExplanationToggle(questionDiv);
                questionsContainer.appendChild(questionDiv);
            });
        }

        // 添加解析切换功能
        function addExplanationToggle(questionDiv) {
            const showExplanationBtn = questionDiv.querySelector('.show-explanation-btn');
            const explanationDiv = questionDiv.querySelector('.question-explanation');

            showExplanationBtn.onclick = function() {
                if (explanationDiv.style.display === 'none') {
                    explanationDiv.style.display = 'block';
                    showExplanationBtn.textContent = '隐藏解析';
                } else {
                    explanationDiv.style.display = 'none';
                    showExplanationBtn.textContent = '查看解析';
                }
            };
        }

        // 渲染数学公式
        function renderMathFormulas() {
            if (window.MathJax) {
                try {
                    console.log('开始渲染数学公式...');
                    setTimeout(function() {
                        renderMathJax();
                        document.dispatchEvent(new CustomEvent('questionsDisplayed'));
                    }, 100);
                } catch (e) {
                    console.error('渲染数学公式时出错:', e);
                }
            } else {
                console.warn('MathJax未加载，无法渲染数学公式');
                loadMathJax();
            }
        }

        // 动态加载MathJax
        function loadMathJax() {
            if (!document.getElementById('MathJax-script')) {
                console.log('尝试动态加载MathJax...');
                const script = document.createElement('script');
                script.id = 'MathJax-script';
                script.src = 'https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js';
                script.async = true;
                script.onload = function() {
                    console.log('MathJax加载成功，开始渲染...');
                    setTimeout(function() {
                        renderMathJax();
                        document.dispatchEvent(new CustomEvent('questionsDisplayed'));
                    }, 500);
                };
                document.head.appendChild(script);
            }
        }

        // 主显示函数 - 优化后的版本
        function displayQuestions(questionsData) {
            const questionsContainer = document.getElementById('questionsContainer');
            if (!questionsContainer) {
                console.error('Questions container not found');
                return;
            }

            questionsContainer.innerHTML = '';

            if (!questionsData) {
                console.error('No questions data provided');
                return;
            }

            const questionType = document.getElementById('questionType') ?
                document.getElementById('questionType').value : 'choice';

            // 验证和标准化数据
            const validatedData = validateAndNormalizeQuestionsData(questionsData, questionType);
            if (!validatedData) {
                console.error('Failed to validate questions data');
                return;
            }

            // 根据题目类型渲染
            if (questionType === 'choice' && validatedData.questions) {
                renderChoiceQuestions(validatedData.questions, questionsContainer);
            } else if (questionType === 'calculation' && validatedData.calculation_questions) {
                renderCalculationQuestions(validatedData.calculation_questions, questionsContainer);
            } else {
                console.error('Invalid question type or missing questions data');
                questionsContainer.innerHTML = '<div class="no-questions">没有找到有效的题目数据</div>';
                return;
            }

            // 添加编辑按钮的事件监听
            document.querySelectorAll('.edit-question-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    const index = parseInt(this.getAttribute('data-index'));
                    const type = this.getAttribute('data-type');
                    openEditModal(index, type);
                });
            });

            // 渲染数学公式
            renderMathFormulas();
        }

        // 打开编辑模态框
        function openEditModal(index, type) {
            currentEditIndex = index;
            currentEditType = type;
            
            const editModal = document.getElementById('editQuestionModal');
            const jsonEditor = document.getElementById('questionJsonEditor');
            
            // 获取当前题目的JSON数据
            let questionData = null;
            if (type === 'choice' && currentQuestionsData && currentQuestionsData.questions) {
                questionData = JSON.parse(JSON.stringify(currentQuestionsData.questions[index]));
            } else if (type === 'calculation' && currentQuestionsData && currentQuestionsData.calculation_questions) {
                questionData = JSON.parse(JSON.stringify(currentQuestionsData.calculation_questions[index]));
                
                // 处理solution_steps，确保它是数组
                if (questionData.solution_steps) {
                    if (typeof questionData.solution_steps === 'string') {
                        try {
                            questionData.solution_steps = JSON.parse(questionData.solution_steps);
                        } catch (e) {
                            console.error('解析solution_steps失败:', e);
                            // 保持原样，让用户在编辑时修复
                        }
                    }
                }
            }
            
            // 设置编辑器内容
            if (questionData) {
                jsonEditor.value = JSON.stringify(questionData, null, 2);
            } else {
                jsonEditor.value = '';
            }
            
            // 显示模态框
            editModal.style.display = 'block';
        }

        // 保存编辑后的题目
        async function saveEditedQuestion() {
            const questionsStatus = document.getElementById('questionsStatus');
            try {
                const jsonEditor = document.getElementById('questionJsonEditor');
                const editedJson = jsonEditor.value.trim();

                console.log('开始保存编辑的题目...');
                console.log('编辑的JSON内容:', editedJson);

                // 检查JSON是否为空
                if (!editedJson) {
                    questionsStatus.textContent = '更新失败: JSON内容不能为空';
                    return;
                }

                // 解析JSON
                let editedQuestion;
                try {
                    editedQuestion = JSON.parse(editedJson);
                    console.log('JSON解析成功:', editedQuestion);
                } catch (parseError) {
                    console.error('JSON解析失败:', parseError);
                    questionsStatus.textContent = `更新失败: JSON格式错误 - ${parseError.message}`;
                    return;
                }

                // 处理计算题的solution_steps，确保它是数组
                if (currentEditType === 'calculation' && editedQuestion.solution_steps) {
                    console.log('处理计算题的solution_steps:', typeof editedQuestion.solution_steps, editedQuestion.solution_steps);

                    // 如果solution_steps是字符串，尝试解析为数组
                    if (typeof editedQuestion.solution_steps === 'string') {
                        try {
                            editedQuestion.solution_steps = JSON.parse(editedQuestion.solution_steps);
                            console.log('solution_steps字符串解析成功:', editedQuestion.solution_steps);
                        } catch (e) {
                            console.error('解析solution_steps失败:', e);
                            questionsStatus.textContent = `更新失败: solution_steps格式不正确，请确保它是有效的JSON数组 - ${e.message}`;
                            return;
                        }
                    }

                    // 确保solution_steps是数组
                    if (!Array.isArray(editedQuestion.solution_steps)) {
                        console.error('solution_steps不是数组:', typeof editedQuestion.solution_steps, editedQuestion.solution_steps);
                        questionsStatus.textContent = `更新失败: solution_steps必须是数组，当前类型: ${typeof editedQuestion.solution_steps}`;
                        return;
                    }

                    console.log('solution_steps验证通过，数组长度:', editedQuestion.solution_steps.length);
                }

                // 更新当前题目数据
                if (currentEditType === 'choice' && currentQuestionsData && currentQuestionsData.questions) {
                    currentQuestionsData.questions[currentEditIndex] = editedQuestion;
                    console.log('更新选择题数据');
                } else if (currentEditType === 'calculation' && currentQuestionsData && currentQuestionsData.calculation_questions) {
                    currentQuestionsData.calculation_questions[currentEditIndex] = editedQuestion;
                    console.log('更新计算题数据');
                }

                // 准备发送的数据
                const requestData = {
                    user: user,
                    key: key,
                    token: token,
                    filename: videoName,
                    folder: folder,
                    question_type: document.getElementById('questionType').value,
                    questions_data: currentQuestionsData
                };

                console.log('准备发送更新请求:', {
                    ...requestData,
                    questions_data: '...' // 避免日志过长
                });

                // 发送更新请求
                const response = await fetch('/api/update_questions', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestData)
                });

                console.log('收到响应状态:', response.status);

                const data = await response.json();
                console.log('响应数据:', data);

                if (data.success) {
                    // 关闭模态框
                    document.getElementById('editQuestionModal').style.display = 'none';

                    // 重新显示题目
                    displayQuestions(currentQuestionsData);

                    // 显示成功消息
                    questionsStatus.textContent = "题目更新成功！";
                    console.log('题目更新成功');
                } else {
                    questionsStatus.textContent = `更新失败: ${data.message}`;
                    console.error('更新失败:', data.message);
                }
            } catch (error) {
                console.error('更新题目失败:', error);
                questionsStatus.textContent = `更新失败: ${error.message || '未知错误'}`;
            }
        }

        // JSON验证函数
        function validateQuestionJson() {
            const jsonEditor = document.getElementById('questionJsonEditor');
            const validationStatus = document.getElementById('jsonValidationStatus');
            const editedJson = jsonEditor.value.trim();

            if (!editedJson) {
                validationStatus.style.display = 'block';
                validationStatus.style.backgroundColor = '#f8d7da';
                validationStatus.style.color = '#721c24';
                validationStatus.textContent = 'JSON内容不能为空';
                return false;
            }

            try {
                const parsedJson = JSON.parse(editedJson);

                // 如果是计算题，验证solution_steps
                if (currentEditType === 'calculation' && parsedJson.solution_steps) {
                    if (typeof parsedJson.solution_steps === 'string') {
                        try {
                            JSON.parse(parsedJson.solution_steps);
                        } catch (e) {
                            validationStatus.style.display = 'block';
                            validationStatus.style.backgroundColor = '#f8d7da';
                            validationStatus.style.color = '#721c24';
                            validationStatus.textContent = `solution_steps字符串格式错误: ${e.message}`;
                            return false;
                        }
                    } else if (!Array.isArray(parsedJson.solution_steps)) {
                        validationStatus.style.display = 'block';
                        validationStatus.style.backgroundColor = '#f8d7da';
                        validationStatus.style.color = '#721c24';
                        validationStatus.textContent = `solution_steps必须是数组，当前类型: ${typeof parsedJson.solution_steps}`;
                        return false;
                    }
                }

                validationStatus.style.display = 'block';
                validationStatus.style.backgroundColor = '#d4edda';
                validationStatus.style.color = '#155724';
                validationStatus.textContent = 'JSON格式验证通过！';
                return true;
            } catch (error) {
                validationStatus.style.display = 'block';
                validationStatus.style.backgroundColor = '#f8d7da';
                validationStatus.style.color = '#721c24';
                validationStatus.textContent = `JSON格式错误: ${error.message}`;
                return false;
            }
        }

        // 调试信息函数
        function showDebugInfo() {
            const jsonEditor = document.getElementById('questionJsonEditor');
            const editedJson = jsonEditor.value.trim();

            let debugInfo = '=== 调试信息 ===\n';
            debugInfo += `当前编辑类型: ${currentEditType}\n`;
            debugInfo += `JSON长度: ${editedJson.length}\n`;
            debugInfo += `JSON前100字符: ${editedJson.substring(0, 100)}\n`;

            try {
                const parsedJson = JSON.parse(editedJson);
                debugInfo += `JSON解析: 成功\n`;
                debugInfo += `题目ID: ${parsedJson.id || '无'}\n`;
                debugInfo += `题目类型: ${typeof parsedJson}\n`;

                if (parsedJson.solution_steps) {
                    debugInfo += `solution_steps类型: ${typeof parsedJson.solution_steps}\n`;
                    debugInfo += `solution_steps是数组: ${Array.isArray(parsedJson.solution_steps)}\n`;
                    if (Array.isArray(parsedJson.solution_steps)) {
                        debugInfo += `solution_steps长度: ${parsedJson.solution_steps.length}\n`;
                    } else if (typeof parsedJson.solution_steps === 'string') {
                        debugInfo += `solution_steps字符串长度: ${parsedJson.solution_steps.length}\n`;
                        debugInfo += `solution_steps前50字符: ${parsedJson.solution_steps.substring(0, 50)}\n`;
                    }
                } else {
                    debugInfo += `solution_steps: 不存在\n`;
                }

                // 检查JSON序列化后的结果
                const serialized = JSON.stringify(parsedJson);
                debugInfo += `重新序列化长度: ${serialized.length}\n`;

            } catch (error) {
                debugInfo += `JSON解析: 失败 - ${error.message}\n`;
            }

            // 显示调试信息
            alert(debugInfo);
            console.log(debugInfo);
        }

        // 初始化编辑模态框的事件监听
        document.addEventListener('DOMContentLoaded', function() {
            const editModal = document.getElementById('editQuestionModal');
            const closeEditModalBtn = document.querySelector('.close-edit-modal');
            const saveQuestionBtn = document.getElementById('saveQuestionBtn');
            const cancelEditBtn = document.getElementById('cancelEditBtn');
            const validateJsonBtn = document.getElementById('validateJsonBtn');
            const debugJsonBtn = document.getElementById('debugJsonBtn');

            // 关闭按钮事件
            closeEditModalBtn.addEventListener('click', function() {
                editModal.style.display = 'none';
            });

            // 验证JSON按钮事件
            if (validateJsonBtn) {
                validateJsonBtn.addEventListener('click', validateQuestionJson);
            }

            // 调试按钮事件
            if (debugJsonBtn) {
                debugJsonBtn.addEventListener('click', showDebugInfo);
            }

            // 保存按钮事件
            saveQuestionBtn.addEventListener('click', saveEditedQuestion);

            // 取消按钮事件
            cancelEditBtn.addEventListener('click', function() {
                editModal.style.display = 'none';
            });

            // 点击模态框外部关闭
            window.addEventListener('click', function(event) {
                if (event.target === editModal) {
                    editModal.style.display = 'none';
                }
            });
        });

        // 添加一个辅助函数，用于手动触发MathJax渲染
        function renderMathJax() {
            if (window.MathJax) {
                try {
                    if (typeof MathJax.typeset === 'function') {
                        // MathJax 3.x
                        MathJax.typeset();
                    } else if (MathJax.Hub && typeof MathJax.Hub.Queue === 'function') {
                        // MathJax 2.x
                        MathJax.Hub.Queue(["Typeset", MathJax.Hub]);
                        
                        // 修复公式容器的宽度问题
                        setTimeout(function() {
                            // 处理所有公式容器
                            const mathElements = document.querySelectorAll('.MathJax_Display, .MathJax');
                            mathElements.forEach(function(element) {
                                // 移除滚动条
                                element.style.overflow = 'visible';
                                element.style.maxWidth = '100%';
                                
                                // 如果公式容器有父元素，确保父元素也能正确显示
                                if (element.parentElement) {
                                    element.parentElement.style.overflow = 'visible';
                                    element.parentElement.style.maxWidth = '100%';
                                }
                            });
                        }, 500);
                    }
                } catch (e) {
                    // 静默处理错误
                }
            } else {
                // 尝试加载MathJax
                if (!document.getElementById('MathJax-script')) {
                    const script = document.createElement('script');
                    script.id = 'MathJax-script';
                    script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.9/MathJax.js?config=TeX-AMS-MML_HTMLorMML';
                    script.async = true;
                    script.onload = function() {
                        // 配置 MathJax
                        window.MathJax.Hub.Config({
                            tex2jax: {
                                inlineMath: [['$', '$']],
                                displayMath: [['$$', '$$']],
                                processEscapes: true
                            },
                            "HTML-CSS": { 
                                fonts: ["TeX"],
                                scale: 100,
                                linebreaks: { automatic: true },
                                availableFonts: ["TeX"],
                                preferredFont: "TeX",
                                webFont: "TeX",
                                imageFont: null,
                                matchFontHeight: true,
                                noReflows: true,
                                styles: {
                                    ".MathJax_Display": {
                                        "overflow": "visible !important",
                                        "max-width": "100%"
                                    },
                                    ".MathJax": {
                                        "overflow": "visible !important",
                                        "max-width": "100%"
                                    }
                                }
                            },
                            messageStyle: "none",
                            showProcessingMessages: false,
                            showMathMenu: false
                        });
                        
                        // 延迟渲染
                        setTimeout(function() {
                            renderMathJax();
                        }, 500);
                    };
                    document.head.appendChild(script);
                }
            }
        }
        
        // 在页面加载完成后触发MathJax渲染
        document.addEventListener('DOMContentLoaded', function() {
            // 延迟一段时间后触发渲染，确保MathJax已加载
            setTimeout(renderMathJax, 1000);
        });
        
        // 在窗口大小改变时重新渲染公式
        window.addEventListener('resize', function() {
            // 使用防抖动处理，避免频繁触发
            clearTimeout(window.resizeTimer);
            window.resizeTimer = setTimeout(renderMathJax, 500);
        });

        // 添加一个直接处理公式文本的函数，用于测试和调试
        function renderFormula(formula, isBlock = true) {
            console.log('渲染公式:', formula);
            
            // 创建一个容器
            const container = document.createElement('div');
            container.className = isBlock ? 'math-display' : 'math-inline';
            
            // 设置公式内容，使用原始的$和$$分隔符
            container.innerHTML = isBlock ? `$$${formula}$$` : `$${formula}$`;
            
            // 添加到页面
            document.body.appendChild(container);
            
            // 渲染公式
            if (window.MathJax) {
                try {
                    if (typeof MathJax.typeset === 'function') {
                        MathJax.typeset([container]);
                    } else if (MathJax.Hub && typeof MathJax.Hub.Queue === 'function') {
                        MathJax.Hub.Queue(["Typeset", MathJax.Hub, container]);
                    }
                } catch (e) {
                    console.error('渲染公式出错:', e);
                }
            }
            
            return container;
        }

        // // 添加一个测试按钮
        // document.addEventListener('DOMContentLoaded', function() {
        //     const testButton = document.createElement('button');
        //     testButton.textContent = '测试公式渲染';
        //     testButton.style.position = 'fixed';
        //     testButton.style.bottom = '10px';
        //     testButton.style.right = '10px';
        //     testButton.style.zIndex = '9999';
            
        //     testButton.onclick = function() {
        //         // 测试公式
        //         const formula = 'E = \\frac{1}{4 \\pi \\times 8.85 \\times 10^{-12}} \\frac{5 \\times 10^{-6}}{2^2}';
        //         const container = renderFormula(formula, true);
                
        //         // 显示在弹窗中
        //         const modal = document.createElement('div');
        //         modal.style.position = 'fixed';
        //         modal.style.top = '0';
        //         modal.style.left = '0';
        //         modal.style.width = '100%';
        //         modal.style.height = '100%';
        //         modal.style.backgroundColor = 'rgba(0,0,0,0.7)';
        //         modal.style.display = 'flex';
        //         modal.style.justifyContent = 'center';
        //         modal.style.alignItems = 'center';
        //         modal.style.zIndex = '10000';
                
        //         const content = document.createElement('div');
        //         content.style.backgroundColor = 'white';
        //         content.style.padding = '20px';
        //         content.style.borderRadius = '5px';
        //         content.style.maxWidth = '80%';
        //         content.style.maxHeight = '80%';
        //         content.style.overflow = 'auto';
                
        //         const closeButton = document.createElement('button');
        //         closeButton.textContent = '关闭';
        //         closeButton.style.marginTop = '10px';
        //         closeButton.onclick = function() {
        //             document.body.removeChild(modal);
        //         };
                
        //         content.appendChild(container);
        //         content.appendChild(document.createElement('br'));
        //         content.appendChild(closeButton);
        //         modal.appendChild(content);
                
        //         document.body.appendChild(modal);
        //     };
            
        //     document.body.appendChild(testButton);
        // });

        // 检查系统是否已准备好
        function isSystemReady() {
            // 使用全局变量或window对象获取doc_id和dataset_id
            const currentDocId = window.doc_id || doc_id;
            const currentDatasetId = window.dataset_id || dataset_id;
            
            console.log('检查系统是否准备好:', {
                currentDocId: currentDocId,
                currentDatasetId: currentDatasetId,
                doc_id: doc_id,
                dataset_id: dataset_id,
                window_doc_id: window.doc_id,
                window_dataset_id: window.dataset_id
            });
            
            // 检查doc_id和dataset_id是否已设置
            if ((!currentDocId || currentDocId === 'undefined' || currentDocId === '') && 
                (!currentDatasetId || currentDatasetId === 'undefined' || currentDatasetId === '')) {
                console.warn('系统未准备好: doc_id和dataset_id均未设置');
                return false;
            }
            
            return true;
        }

        // 页面加载完成后自动初始化视频聊天
        document.addEventListener('DOMContentLoaded', async function() {
            console.log('页面加载完成，准备初始化视频聊天...');
            try {
                await initializeVideoChat();
                console.log('视频聊天初始化成功');
            } catch (error) {
                console.error('视频聊天初始化失败:', error);
                // 延迟后自动重试一次
                setTimeout(async () => {
                    console.log('尝试重新初始化视频聊天...');
                    try {
                        await initializeVideoChat();
                        console.log('视频聊天重新初始化成功');
                    } catch (retryError) {
                        console.error('视频聊天重新初始化失败:', retryError);
                        appendMessage('system', '初始化失败，请刷新页面或尝试发送一条消息');
                    }
                }, 3000);
            }
        });

        // 为单个元素渲染MathJax的函数
        function renderMathJaxForElement(element) {
            if (!element || element.getAttribute('data-processed') === 'true') {
                return;
            }

            if (typeof MathJax !== 'undefined') {
                try {
                    console.log('开始渲染公式...');
                    
                    // 强制触发渲染
                    if (typeof MathJax.typeset === 'function') {
                        // MathJax 3.x
                        console.log('使用MathJax 3.x渲染');
                        try {
                            MathJax.typeset([element]);
                            
                            // 检查是否成功渲染
                            setTimeout(() => {
                                const mathElements = element.querySelectorAll('.math-inline, .math-display');
                                const renderedElements = element.querySelectorAll('.MathJax, .MJX-TEX');
                                
                                console.log(`检测到${mathElements.length}个公式元素，${renderedElements.length}个已渲染元素`);
                                
                                // 如果没有成功渲染，尝试再次渲染
                                if (mathElements.length > 0 && renderedElements.length < mathElements.length) {
                                    console.log('检测到未渲染的公式，尝试再次渲染');
                                    MathJax.typeset([element]);
                                }
                            }, 100);
                        } catch (e) {
                            console.error('MathJax 3.x渲染错误:', e);
                        }
                    } else if (MathJax.Hub && typeof MathJax.Hub.Queue === 'function') {
                        // MathJax 2.x
                        console.log('使用MathJax 2.x渲染');
                        try {
                            // 直接处理，不使用队列
                            MathJax.Hub.Process(element);
                            
                            // 再次使用队列确保渲染
                            MathJax.Hub.Queue(["Typeset", MathJax.Hub, element]);
                            
                            // 检查渲染状态
                            MathJax.Hub.Queue(function() {
                                const mathElements = element.querySelectorAll('.math-inline, .math-display');
                                const renderedElements = element.querySelectorAll('.MathJax');
                                
                                console.log(`MathJax 2.x: 检测到${mathElements.length}个公式元素，${renderedElements.length}个已渲染元素`);
                            });
                        } catch (e) {
                            console.error('MathJax 2.x渲染错误:', e);
                        }
                    }
                    
                    element.setAttribute('data-processed', 'true');
                    console.log('公式渲染完成，元素已标记为已处理');
                } catch (e) {
                    console.error('MathJax渲染错误:', e);
                }
            } else {
                console.warn('MathJax未定义，无法渲染公式');
            }
        }

        // 修改renderMathJax函数，用于渲染所有未处理的公式
        function renderMathJax() {
            const chatHistory = document.getElementById('chatHistory');
            if (!chatHistory) return;

            // 获取所有未处理的消息
            const unprocessedMessages = chatHistory.querySelectorAll('.message[data-processed="false"]');
            
            unprocessedMessages.forEach(message => {
                renderMathJaxForElement(message);
            });
        }

        // 添加一个函数，用于在页面加载和视频播放器交互时触发公式渲染
        function ensureMathRendering() {
            console.log('确保所有公式渲染');
            renderMathJax();
            
            // 强制触发全局MathJax渲染
            if (typeof MathJax !== 'undefined') {
                if (typeof MathJax.typeset === 'function') {
                    try {
                        MathJax.typeset();
                    } catch (e) {
                        console.error('全局MathJax渲染错误:', e);
                    }
                } else if (MathJax.Hub && typeof MathJax.Hub.Queue === 'function') {
                    try {
                        MathJax.Hub.Queue(["Typeset", MathJax.Hub]);
                    } catch (e) {
                        console.error('全局MathJax 2.x渲染错误:', e);
                    }
                }
            }
        }

        // 在页面加载完成后设置事件监听
        document.addEventListener('DOMContentLoaded', function() {
            // 添加事件监听，确保在视频播放器交互时触发公式渲染
            const videoPlayer = document.getElementById('videoPlayer');
            const chatHistory = document.getElementById('chatHistory');
            
            if (videoPlayer) {
                videoPlayer.addEventListener('click', function() {
                    console.log('视频播放器点击，触发公式渲染');
                    setTimeout(ensureMathRendering, 100);
                });
            }
            
            if (chatHistory) {
                chatHistory.addEventListener('click', function() {
                    console.log('聊天历史点击，触发公式渲染');
                    setTimeout(ensureMathRendering, 100);
                });
            }
            
            // 初始渲染
            setTimeout(ensureMathRendering, 500);
        });

        // 在窗口大小改变时重新渲染未处理的公式
        window.addEventListener('resize', function() {
            clearTimeout(window.resizeTimer);
            window.resizeTimer = setTimeout(ensureMathRendering, 500);
        });

        // 在页面滚动时检查并渲染可见区域内的公式
        window.addEventListener('scroll', function() {
            clearTimeout(window.scrollTimer);
            window.scrollTimer = setTimeout(ensureMathRendering, 300);
        });
    </script>
    
    <script>
        // 页面加载完成后自动渲染公式
        document.addEventListener('DOMContentLoaded', function() {
            // 页面加载完成后自动渲染公式，不输出日志
            
            // 定义一个函数来渲染所有公式
            function renderAllFormulas() {
                console.log('开始渲染数学公式...');
                if (window.MathJax && window.MathJax.Hub) {
                    MathJax.Hub.Queue(["Typeset", MathJax.Hub]);
                    
                    // 修复公式容器样式
                    setTimeout(fixMathJaxContainers, 1000);
                } else {
                    setTimeout(renderAllFormulas, 1000);
                }
            }
            
            // 修复公式容器样式的函数
            function fixMathJaxContainers() {
                // 静默处理所有公式容器，不输出日志
                const mathElements = document.querySelectorAll('.MathJax_Display, .MathJax');
                
                mathElements.forEach(function(element) {
                    // 直接设置所有元素的样式，不检查是否需要修复
                    element.style.overflow = 'visible';
                    element.style.maxWidth = '100%';
                    
                    // 如果公式容器有父元素，确保父元素也能正确显示
                    if (element.parentElement) {
                        element.parentElement.style.overflow = 'visible';
                        element.parentElement.style.maxWidth = '100%';
                    }
                });
            }
            
            // 首次尝试渲染
            setTimeout(renderAllFormulas, 500);
            
            // 在问题显示后再次渲染
            document.addEventListener('questionsDisplayed', function() {
                // 问题已显示，重新渲染公式，不输出日志
                setTimeout(renderAllFormulas, 100);
            });
            
            // 移除定时器，改为只在特定事件触发时修复
            // setInterval(fixMathJaxContainers, 3000);
        });
    </script>
</body>
</html>