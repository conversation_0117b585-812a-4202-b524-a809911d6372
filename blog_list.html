<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" href="data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 100 100%22><text y=%22.9em%22 font-size=%2290%22>📚</text></svg>">
    <title>AI 助教平台 - 博客列表</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #667eea;
            --secondary-color: #764ba2;
            --accent-color: #f093fb;
            --text-primary: #2d3748;
            --text-secondary: #4a5568;
            --text-muted: #718096;
            --bg-primary: #f7fafc;
            --bg-secondary: #ffffff;
            --border-color: #e2e8f0;
            --shadow-light: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-medium: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-heavy: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            --gradient-primary: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            --gradient-accent: linear-gradient(135deg, var(--accent-color) 0%, var(--primary-color) 100%);
        }

        * {
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding-top: 80px;
            line-height: 1.6;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
            pointer-events: none;
            z-index: -1;
        }

        #blogList {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
            gap: 30px;
            margin-top: 30px;
        }

        .blog-item {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            padding: 25px;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            min-height: 400px;
            display: flex;
            flex-direction: column;
        }

        .blog-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--gradient-primary);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .blog-item:hover::before {
            transform: scaleX(1);
        }

        .blog-item:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: var(--shadow-heavy);
            background: rgba(255, 255, 255, 0.98);
        }

        .blog-info {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .blog-info h2 {
            margin: 0 0 15px 0;
            font-size: 1.5em;
            font-weight: 700;
            color: var(--text-primary);
            line-height: 1.3;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .blog-author {
            font-weight: 600;
            color: var(--primary-color);
            font-size: 0.95em;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
        }

        .blog-author::before {
            content: '👤';
            margin-right: 8px;
        }

        .blog-date {
            color: var(--text-muted);
            font-size: 0.9em;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
        }

        .blog-date::before {
            content: '📅';
            margin-right: 8px;
        }

        .blog-activity {
            color: var(--text-muted);
            font-size: 0.85em;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }

        .blog-activity::before {
            content: '⚡';
            margin-right: 8px;
        }

        .blog-thumbnail {
            width: 100%;
            height: 180px;
            object-fit: cover;
            border-radius: 12px;
            margin: 15px 0;
            box-shadow: var(--shadow-light);
            transition: transform 0.3s ease;
        }

        .blog-item:hover .blog-thumbnail {
            transform: scale(1.05);
        }

        .blog-stats {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: auto;
            padding-top: 15px;
            border-top: 1px solid var(--border-color);
            font-size: 0.85em;
            color: var(--text-muted);
            flex-wrap: wrap;
            gap: 10px;
        }

        .blog-stats span {
            display: flex;
            align-items: center;
            gap: 4px;
            padding: 4px 8px;
            background: rgba(102, 126, 234, 0.1);
            border-radius: 12px;
            font-weight: 500;
        }
        .blog-list-header {
            text-align: center;
            margin: 20px 0 40px;
            position: relative;
            padding: 40px 20px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: 25px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .blog-list-title {
            font-family: 'Inter', sans-serif;
            font-size: 3.5rem;
            font-weight: 800;
            background: linear-gradient(135deg, #ffffff 0%, rgba(255, 255, 255, 0.8) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin: 0;
            letter-spacing: -0.02em;
            text-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .blog-list-subtitle {
            font-family: 'Inter', sans-serif;
            font-size: 1.25rem;
            color: rgba(255, 255, 255, 0.9);
            margin-top: 15px;
            font-weight: 400;
            letter-spacing: 0.5px;
        }

        .blog-list-header::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 120px;
            height: 4px;
            background: var(--gradient-accent);
            border-radius: 2px;
            box-shadow: 0 2px 8px rgba(240, 147, 251, 0.4);
        }
        .header-container {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            padding: 15px 30px;
            z-index: 1000;
            display: flex;
            justify-content: flex-end;
            align-items: center;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }

        .search-container {
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 30px 0;
            gap: 15px;
            flex-wrap: wrap;
        }

        #backButton {
            padding: 15px 30px;
            font-size: 16px;
            font-weight: 600;
            background: var(--gradient-accent);
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: var(--shadow-light);
            display: none;
        }

        #backButton:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-medium);
        }

        #backButton:active {
            transform: translateY(0);
        }

        .search-status {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: 15px;
            padding: 10px 20px;
            color: rgba(255, 255, 255, 0.9);
            font-weight: 500;
            border: 1px solid rgba(255, 255, 255, 0.2);
            display: none;
        }

        #searchInput {
            padding: 15px 20px;
            font-size: 16px;
            border: none;
            border-radius: 25px;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            box-shadow: var(--shadow-light);
            outline: none;
            transition: all 0.3s ease;
            min-width: 300px;
            color: var(--text-primary);
        }

        #searchInput::placeholder {
            color: var(--text-muted);
        }

        #searchInput:focus {
            background: rgba(255, 255, 255, 0.95);
            box-shadow: var(--shadow-medium);
            transform: translateY(-2px);
        }

        #searchButton {
            padding: 15px 30px;
            font-size: 16px;
            font-weight: 600;
            background: var(--gradient-primary);
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: var(--shadow-light);
        }

        #searchButton:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-medium);
        }

        #searchButton:active {
            transform: translateY(0);
        }

        #errorMessage {
            color: #e53e3e;
            text-align: center;
            margin: 20px 0;
            padding: 15px;
            background: rgba(229, 62, 62, 0.1);
            border-radius: 12px;
            border: 1px solid rgba(229, 62, 62, 0.2);
            font-weight: 500;
        }

        .user-info {
            font-size: 16px;
            color: rgba(255, 255, 255, 0.9);
        }

        #userDisplay {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            font-weight: 600;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .login-button {
            background: var(--gradient-accent);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: var(--shadow-light);
        }

        .login-button:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-medium);
        }
        .category-tag {
            display: inline-block;
            padding: 6px 12px;
            background: var(--gradient-primary);
            color: white;
            border-radius: 15px;
            font-size: 0.8em;
            font-weight: 600;
            margin: 5px 0;
            box-shadow: var(--shadow-light);
            transition: all 0.3s ease;
        }

        .category-tag:hover {
            transform: translateY(-1px);
            box-shadow: var(--shadow-medium);
        }

        .category-filter-container {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin: 30px 0;
            justify-content: center;
            padding: 20px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .category-filter-btn {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            background: rgba(255, 255, 255, 0.2);
            color: rgba(255, 255, 255, 0.9);
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 600;
            font-size: 0.95em;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .category-filter-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
            box-shadow: var(--shadow-light);
        }

        .category-filter-btn.active {
            background: var(--gradient-accent);
            color: white;
            box-shadow: var(--shadow-medium);
            transform: translateY(-2px);
        }

        .score-tag {
            display: inline-flex;
            align-items: center;
            gap: 4px;
            padding: 4px 10px;
            border-radius: 12px;
            font-weight: 600;
            font-size: 0.8em;
            transition: all 0.3s ease;
        }

        .score-tag:hover {
            transform: scale(1.05);
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            body {
                padding: 15px;
                padding-top: 70px;
            }

            .header-container {
                padding: 12px 20px;
            }

            .user-info {
                font-size: 14px;
            }

            #userDisplay, .login-button {
                padding: 8px 16px;
                font-size: 14px;
            }

            .blog-list-title {
                font-size: 2.5rem;
            }

            .blog-list-subtitle {
                font-size: 1.1rem;
            }

            #blogList {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .search-container {
                flex-direction: column;
                align-items: center;
                gap: 15px;
            }

            #searchInput {
                min-width: 280px;
                width: 100%;
                max-width: 400px;
            }

            #backButton, #searchButton {
                padding: 12px 24px;
                font-size: 15px;
            }

            .search-status {
                text-align: center;
                font-size: 14px;
            }

            .category-filter-container {
                padding: 15px;
            }

            .category-filter-btn {
                padding: 10px 20px;
                font-size: 0.9em;
            }
        }

        @media (max-width: 480px) {
            .blog-list-title {
                font-size: 2rem;
            }

            .blog-item {
                padding: 20px;
                min-height: 350px;
            }

            .blog-info h2 {
                font-size: 1.3em;
            }
        }
    </style>
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/katex@0.13.11/dist/katex.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/katex@0.13.11/dist/contrib/auto-render.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@11.3.0/dist/mermaid.min.js"></script>
</head>
<body>
    <div class="header-container">
        <div class="user-info">
            <span id="userDisplay"></span>
            <a id="loginButton" href="#" class="login-button" style="display: none;">登录</a>
        </div>
    </div>
    <header class="blog-list-header">
        <h1 class="blog-list-title">博客列表</h1>
        <p class="blog-list-subtitle">探索精彩内容，分享你的想法</p>
    </header>
    <div class="search-container">
        <button id="backButton">← 返回全部博客</button>
        <div class="search-status" id="searchStatus"></div>
        <input type="text" id="searchInput" placeholder="搜索博客...">
        <button id="searchButton">🔍 搜索</button>
    </div>
    <div class="category-filter-container">
        <button class="category-filter-btn active" data-category="all">全部</button>
    </div>

    <div id="errorMessage"></div>
    <div id="blogList"></div>

    <script>
        // 获取 URL 参数
        const urlParams = new URLSearchParams(window.location.search);
        const user = urlParams.get('user');
        const key = urlParams.get('key');
        const token = urlParams.get('token');
        const searchTerm = urlParams.get('search') || '';
        let currentBlogs = []; // 存储当前的博客列表
        let currentStats = {}; // 存储当前的统计信息

        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleString('zh-CN');
        }

        function openBlog(filename) {
            window.open(`/api/view_blog/?filename=${filename}&user=${user}&key=${key}&token=${token}`, '_blank');
        }

        // 获取博客统计信息
        function getBlogStats() {
            return fetch('/api/get_blog_stats', {
                method: 'GET',
                headers: {
                    'Cache-Control': 'no-cache, no-store, must-revalidate',
                    'Pragma': 'no-cache',
                    'Expires': '0'
                },
                cache: 'no-store'
            })
            .then(response => response.json())
            .then(data => {
                console.log('Received blog stats:', data);
                return data.success ? data.stats : {};
            })
            .catch(error => {
                console.error('Error fetching blog stats:', error);
                return {};
            });
        }

        // 获取并渲染博客列表
        function fetchBlogsAndRender() {
            const searchTerm = urlParams.get('search') || '';
            let url = `/api/search_blogs?search=${encodeURIComponent(searchTerm)}`;   
            if (user && key) {
                url += `&user=${encodeURIComponent(user)}&key=${encodeURIComponent(key)}&token=${encodeURIComponent(token)}`;
            }
            Promise.all([
                fetch(url).then(response => response.json()),
                fetch('/api/get_blog_stats', {
                    method: 'GET',
                    headers: {
                        'Cache-Control': 'no-cache, no-store, must-revalidate',
                        'Pragma': 'no-cache',
                        'Expires': '0'
                    },
                    cache: 'no-store'
                }).then(response => response.json()),
                fetch('/api/get_scores').then(response => response.json()) // 添加获取分数的请求
            ])
            .then(([searchData, statsData, scoresData]) => {
                if (searchData.success) {                  
                    currentBlogs = searchData.blogs;
                    currentStats = statsData.stats;
                    updateCategoryButtons(searchData.blogs);            
                    renderBlogs(searchData.blogs, statsData.stats, scoresData.scores);
                } else {
                    document.getElementById('errorMessage').textContent = searchData.message;
                }
            })
            .catch(error => {
                console.error('Error:', error);
                document.getElementById('errorMessage').textContent = 
                    '加载博客列表时出错。请检查网络连接或联系管理员。';
            });
        }

        // 渲染博客列表
        function renderBlogs(blogs, stats, scores) {
            console.log('Rendering blogs with stats:', stats);
            const blogListElement = document.getElementById('blogList');
            blogListElement.innerHTML = '';
            if (!Array.isArray(blogs) || blogs.length === 0) {
                blogListElement.innerHTML = '<p>没有找到匹配的博客。</p>';
                return;
            }
            blogs.forEach(blog => {
                // 检查是否为电磁学作业分类且用户名与文件名匹配
                const blogAuthor = blog.filename.split('_')[0]; // 获取文件名前缀
                if (!blog || (blog.category.endsWith('作业') && blogAuthor !== user)) return; // 排除不符合条件的博客
                
                const blogItem = document.createElement('div');
                blogItem.className = 'blog-item';
                blogItem.onclick = () => openBlog(blog.filename);
                
                const blogStats = stats[blog.filename] || { views: 0, comments: 0 };
                const score = scores ? scores[blog.filename] : null;
                
                let blogContent = `
                    <div class="blog-content-wrapper">
                        <div class="blog-info">
                            <h2>${blog.title || '无标题'}</h2>
                            <p class="blog-author">作者: ${blog.user || '未知'}</p>
                            <p class="blog-date">创建于 ${blog.created_at ? formatDate(blog.created_at) : '未知日期'}</p>
                            <p class="blog-activity">最新活动: ${formatDate(blog.latest_activity)}</p>
                            <div class="blog-meta">
                                <span class="category-tag">${blog.category || '未分类'}</span>
                                <div class="blog-stats">
                                    <span>👁️ ${blogStats.views} 阅读</span>
                                    <span>💬 ${blogStats.comments} 评论</span>
                                    ${blog.category && blog.category.endsWith('作业') ? 
                                        `<span class="score-tag" style="
                                            margin-left: 10px;
                                            padding: 2px 8px;
                                            border-radius: 4px;
                                            font-weight: bold;
                                            background-color: ${score >= 90 ? '#e8f5e9' : score >= 60 ? '#fff3e0' : '#f5f5f5'};
                                            color: ${score >= 90 ? '#4CAF50' : score >= 60 ? '#FF9800' : '#757575'};
                                        ">
                                            ${score !== null ? `分数: ${score}` : '未评分'}
                                        </span>` 
                                        : ''}
                                </div>
                            </div>
                        </div>
                        ${blog.first_image && blog.first_image.src ? 
                            `<div class="blog-image-wrapper">
                                <img src="${blog.first_image.src}" alt="${blog.first_image.alt || ''}" class="blog-thumbnail">
                            </div>` : 
                            ''
                        }
                    </div>
                `;
                
                blogItem.innerHTML = blogContent;
                blogListElement.appendChild(blogItem);
            });
        }

        // 更新分类按钮
        function updateCategoryButtons(blogs) {
            const categories = new Set(['all']);
            blogs.forEach(blog => {
                if (blog.category) {  // 移除了 !blog.category.endsWith('作业') 的条件
                    categories.add(blog.category);
                }
            });

            const containerElement = document.querySelector('.category-filter-container');
            containerElement.innerHTML = ''; // 清空现有按钮

            categories.forEach(category => {
                const button = document.createElement('button');
                button.className = 'category-filter-btn';
                button.textContent = category === 'all' ? '全部' : category;
                button.dataset.category = category;
                if (category === 'all') {
                    button.classList.add('active');
                }
                button.onclick = () => filterByCategory(category);
                containerElement.appendChild(button);
            });
        }

        // 按分类过滤博客
        function filterByCategory(category) {
            // 更新按钮状态
            document.querySelectorAll('.category-filter-btn').forEach(btn => {
                btn.classList.remove('active');
                if (btn.dataset.category === category) {
                    btn.classList.add('active');
                }
            });

            // 过滤博客
            const filteredBlogs = category === 'all' 
                ? currentBlogs 
                : currentBlogs.filter(blog => blog.category === category);

            // 获取最新的分数信息
            fetch('/api/get_scores')
                .then(response => response.json())
                .then(scoresData => {
                    // 渲染过滤后的博客，并传入分数信息
                    renderBlogs(filteredBlogs, currentStats, scoresData.scores);
                })
                .catch(error => {
                    console.error('Error fetching scores:', error);
                    // 如果获取分数失败，仍然渲染博客但不显示分数
                    renderBlogs(filteredBlogs, currentStats);
                });
        }

        // 搜索功能
        function searchBlogs() {
            const searchInput = document.getElementById('searchInput');
            const searchTerm = searchInput.value.trim();
            if (searchTerm) {
                // 构建新的 URL，包含所有现有参数和新的搜索词
                const currentUrl = new URL(window.location.href);
                currentUrl.searchParams.set('search', searchTerm);
                // 重定向到新的 URL
                window.location.href = currentUrl.toString();
            }
        }

        // 返回全部博客功能
        function backToAllBlogs() {
            // 构建新的 URL，移除搜索参数
            const currentUrl = new URL(window.location.href);
            currentUrl.searchParams.delete('search');
            // 重定向到新的 URL
            window.location.href = currentUrl.toString();
        }

        // 更新搜索状态显示
        function updateSearchStatus() {
            const backButton = document.getElementById('backButton');
            const searchStatus = document.getElementById('searchStatus');
            const searchTerm = urlParams.get('search') || '';

            if (searchTerm) {
                // 显示返回按钮和搜索状态
                backButton.style.display = 'block';
                searchStatus.style.display = 'block';
                searchStatus.textContent = `搜索结果: "${searchTerm}"`;
            } else {
                // 隐藏返回按钮和搜索状态
                backButton.style.display = 'none';
                searchStatus.style.display = 'none';
            }
        }

        // 显示用户名或登录按钮
        function displayUserInfo() {
            const userDisplay = document.getElementById('userDisplay');
            const loginButton = document.getElementById('loginButton');

            if (user && key) {
                userDisplay.textContent = `欢迎，${user}`;
                userDisplay.style.display = 'inline-block';
                loginButton.style.display = 'none';
            } else {
                userDisplay.style.display = 'none';
                loginButton.style.display = 'inline-block';
            }
        }

        // 获取当前页面的主域名
        function getMainDomain() {
            return window.location.protocol + '//' + window.location.hostname;
        }
        // 初始加载
        document.addEventListener('DOMContentLoaded', function() {
            displayUserInfo();
            updateSearchStatus(); // 更新搜索状态显示
            fetchBlogsAndRender();

            const loginButton = document.getElementById('loginButton');
            if (loginButton) {
                loginButton.href = getMainDomain();
            }

            // 如果有搜索关键词，将其填入搜索框
            if (searchTerm) {
                document.getElementById('searchInput').value = searchTerm;
            }

            // 添加搜索按钮事件监听器
            document.getElementById('searchButton').addEventListener('click', searchBlogs);

            // 添加返回按钮事件监听器
            document.getElementById('backButton').addEventListener('click', backToAllBlogs);

            // 添加搜索输入框的回车键事件监听器
            document.getElementById('searchInput').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    searchBlogs();
                }
            });
        });
    </script>
</body>
</html>