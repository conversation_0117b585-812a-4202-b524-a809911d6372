// 主要JavaScript功能文件

// 全局变量
let currentUser = null;
let apiKeys = {
    deepseek: '',
    siliconflow: '',
    qwen: ''
};

// 工具函数
function showMessage(message, type = 'info', duration = 3000) {
    const container = document.getElementById('message-container') || createMessageContainer();
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${type}`;
    messageDiv.textContent = message;
    
    container.appendChild(messageDiv);
    
    // 显示动画
    setTimeout(() => {
        messageDiv.classList.add('show');
    }, 100);
    
    // 自动隐藏
    setTimeout(() => {
        messageDiv.classList.remove('show');
        setTimeout(() => {
            if (container.contains(messageDiv)) {
                container.removeChild(messageDiv);
            }
        }, 300);
    }, duration);
}

function createMessageContainer() {
    const container = document.createElement('div');
    container.id = 'message-container';
    container.className = 'message-container';
    document.body.appendChild(container);
    return container;
}

// API请求封装
async function apiRequest(url, options = {}) {
    const defaultOptions = {
        headers: {
            'Content-Type': 'application/json',
        },
        ...options
    };

    try {
        const response = await fetch(url, defaultOptions);
        const data = await response.json();
        return { success: response.ok, data, status: response.status };
    } catch (error) {
        console.error('API请求失败:', error);
        return { success: false, error: error.message };
    }
}

// 表单验证
function validateEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

function validatePassword(password) {
    return password && password.length >= 6;
}

function validateUsername(username) {
    return username && username.length >= 3 && /^[\u4e00-\u9fa5a-zA-Z0-9_]+$/.test(username);
}

// 用户认证相关
class AuthManager {
    constructor() {
        this.initializeEventListeners();
    }

    initializeEventListeners() {
        // 登录表单
        const loginForm = document.getElementById('login-form');
        if (loginForm) {
            loginForm.addEventListener('submit', this.handleLogin.bind(this));
        }

        // 注册表单
        const registerForm = document.getElementById('register-form');
        if (registerForm) {
            registerForm.addEventListener('submit', this.handleRegister.bind(this));
        }

        // 验证码验证
        const verifyBtn = document.getElementById('verify-code-btn');
        if (verifyBtn) {
            verifyBtn.addEventListener('click', this.handleVerifyCode.bind(this));
        }

        // 找回密码表单
        const resetForm = document.getElementById('reset-form');
        if (resetForm) {
            resetForm.addEventListener('submit', this.handleResetPassword.bind(this));
        }

        // 实时验证
        this.setupRealTimeValidation();
    }

    setupRealTimeValidation() {
        const usernameInput = document.getElementById('register-username');
        const emailInput = document.getElementById('register-email');

        if (usernameInput) {
            usernameInput.addEventListener('blur', this.validateUsernameField.bind(this));
            usernameInput.addEventListener('input', this.validateUsernameField.bind(this));
        }

        if (emailInput) {
            emailInput.addEventListener('blur', this.validateEmailField.bind(this));
        }
    }

    async validateUsernameField() {
        const username = document.getElementById('register-username').value;
        const validationDiv = document.getElementById('username-validation');

        if (username === '') {
            validationDiv.textContent = '';
            return;
        }

        if (!validateUsername(username)) {
            validationDiv.textContent = '用户名至少3位，可包含汉字、字母、数字和下划线';
            validationDiv.style.color = '#ff4444';
        } else {
            validationDiv.textContent = '✓ 用户名格式正确';
            validationDiv.style.color = '#4CAF50';
        }

        // 检查用户名是否已存在
        const result = await apiRequest('/main/check-username', {
            method: 'POST',
            body: JSON.stringify({ username })
        });

        if (result.success) {
            if (result.data.available) {
                validationDiv.textContent = '用户名可用';
                validationDiv.className = 'validation-message success';
            } else {
                validationDiv.textContent = '该用户名已被使用';
                validationDiv.className = 'validation-message error';
            }
        }
    }

    async validateEmailField() {
        const email = document.getElementById('register-email').value;
        const validationDiv = document.getElementById('email-validation');

        if (!email) {
            validationDiv.textContent = '';
            return;
        }

        if (!validateEmail(email)) {
            validationDiv.textContent = '请输入有效的邮箱地址';
            validationDiv.className = 'validation-message error';
            return;
        }

        // 检查邮箱是否已注册
        const result = await apiRequest('/main/check-email', {
            method: 'POST',
            body: JSON.stringify({ email })
        });

        if (result.success) {
            if (result.data.available) {
                validationDiv.textContent = '邮箱可用';
                validationDiv.className = 'validation-message success';
            } else {
                validationDiv.textContent = '该邮箱已被注册';
                validationDiv.className = 'validation-message error';
            }
        }
    }

    async handleLogin(e) {
        e.preventDefault();
        
        const username = document.getElementById('login-username').value.trim();
        const password = document.getElementById('login-password').value;

        if (!username || !password) {
            showMessage('请输入用户名和密码', 'error');
            return;
        }

        const submitBtn = e.target.querySelector('button[type="submit"]');
        const originalText = submitBtn.textContent;
        submitBtn.textContent = '登录中...';
        submitBtn.disabled = true;

        try {
            const result = await apiRequest('/main/login', {
                method: 'POST',
                body: JSON.stringify({ username, password })
            });

            if (result.success && result.data.success) {
                showMessage('登录成功！', 'success');
                setTimeout(() => {
                    window.location.href = '/main/dashboard';
                }, 1000);
            } else {
                showMessage(result.data.message || '登录失败', 'error');
            }
        } catch (error) {
            showMessage('登录失败，请重试', 'error');
        } finally {
            submitBtn.textContent = originalText;
            submitBtn.disabled = false;
        }
    }

    async handleRegister(e) {
        e.preventDefault();
        
        const username = document.getElementById('register-username').value.trim();
        const password = document.getElementById('register-password').value;
        const passwordRepeat = document.getElementById('register-password-repeat').value;
        const email = document.getElementById('register-email').value.trim();

        // 验证输入
        if (!username || !password || !passwordRepeat || !email) {
            showMessage('请填写所有必填项', 'error');
            return;
        }

        if (!validateUsername(username)) {
            showMessage('用户名格式不正确', 'error');
            return;
        }

        if (!validateEmail(email)) {
            showMessage('邮箱格式不正确', 'error');
            return;
        }

        if (!validatePassword(password)) {
            showMessage('密码长度至少为6位', 'error');
            return;
        }

        if (password !== passwordRepeat) {
            showMessage('两次输入的密码不匹配', 'error');
            return;
        }

        const submitBtn = e.target.querySelector('button[type="submit"]');
        const originalText = submitBtn.textContent;
        submitBtn.textContent = '发送中...';
        submitBtn.disabled = true;

        try {
            const result = await apiRequest('/main/register', {
                method: 'POST',
                body: JSON.stringify({
                    action: 'send_code',
                    username,
                    password,
                    email
                })
            });

            if (result.success && result.data.success) {
                showMessage(result.data.message, 'success');
                document.getElementById('register-form').style.display = 'none';
                document.getElementById('verification-form').style.display = 'block';
            } else {
                showMessage(result.data.message || '发送验证码失败', 'error');
            }
        } catch (error) {
            showMessage('发送验证码失败，请重试', 'error');
        } finally {
            submitBtn.textContent = originalText;
            submitBtn.disabled = false;
        }
    }

    async handleVerifyCode() {
        const code = document.getElementById('verification-code').value.trim();

        if (!code) {
            showMessage('请输入验证码', 'error');
            return;
        }

        const verifyBtn = document.getElementById('verify-code-btn');
        const originalText = verifyBtn.textContent;
        verifyBtn.textContent = '验证中...';
        verifyBtn.disabled = true;

        try {
            const result = await apiRequest('/main/register', {
                method: 'POST',
                body: JSON.stringify({
                    action: 'verify_code',
                    code
                })
            });

            if (result.success && result.data.success) {
                showMessage(result.data.message, 'success');
                setTimeout(() => {
                    // 切换到登录标签
                    document.querySelector('[data-tab="login"]').click();
                    document.getElementById('verification-form').style.display = 'none';
                    document.getElementById('register-form').style.display = 'block';
                    // 清空表单
                    document.getElementById('register-form').reset();
                    document.getElementById('verification-code').value = '';
                }, 1500);
            } else {
                showMessage(result.data.message || '验证失败', 'error');
            }
        } catch (error) {
            showMessage('验证失败，请重试', 'error');
        } finally {
            verifyBtn.textContent = originalText;
            verifyBtn.disabled = false;
        }
    }

    async handleResetPassword(e) {
        e.preventDefault();
        
        const email = document.getElementById('reset-email').value.trim();

        if (!email) {
            showMessage('请输入邮箱地址', 'error');
            return;
        }

        if (!validateEmail(email)) {
            showMessage('请输入有效的邮箱地址', 'error');
            return;
        }

        const submitBtn = e.target.querySelector('button[type="submit"]');
        const originalText = submitBtn.textContent;
        submitBtn.textContent = '处理中...';
        submitBtn.disabled = true;

        try {
            const result = await apiRequest('/main/reset-password', {
                method: 'POST',
                body: JSON.stringify({ email })
            });

            if (result.success && result.data.success) {
                showMessage(result.data.message, 'success');
            } else {
                showMessage(result.data.message || '处理失败', 'error');
            }
        } catch (error) {
            showMessage('处理失败，请重试', 'error');
        } finally {
            submitBtn.textContent = originalText;
            submitBtn.disabled = false;
        }
    }
}

// 标签切换功能
function initTabSwitching() {
    document.querySelectorAll('.auth-option').forEach(button => {
        button.addEventListener('click', function() {
            const tabName = this.dataset.tab;
            
            // 移除所有活动状态
            document.querySelectorAll('.auth-option').forEach(btn => btn.classList.remove('active'));
            document.querySelectorAll('.auth-tab').forEach(tab => tab.classList.remove('active'));
            
            // 添加活动状态
            this.classList.add('active');
            document.getElementById(tabName + '-tab').classList.add('active');
            
            // 重置表单
            document.querySelectorAll('.auth-form').forEach(form => {
                if (form.tagName === 'FORM') {
                    form.reset();
                }
            });
            
            // 清空验证消息
            document.querySelectorAll('.validation-message').forEach(msg => {
                msg.textContent = '';
                msg.className = 'validation-message';
            });
        });
    });
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 初始化认证管理器
    new AuthManager();
    
    // 初始化标签切换
    initTabSwitching();
    
    // 其他初始化代码
    console.log('页面初始化完成');
});
