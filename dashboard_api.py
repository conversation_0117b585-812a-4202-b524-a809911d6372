"""
Dashboard API - Flask接口实现
基于ed_web_ragflow.py中的功能，为dashboard.html提供后端API支持
"""

from flask import Flask, request, jsonify, session
import json
import os
import time
import logging
import requests
import yaml
from datetime import datetime
import cv2
import secrets
import random
import sys

SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
from getDocId import upload_or_get_doc_id
from getAnswer import create_session, send_chat_message, retrieve_chunks
from tool.weather.get_weather import get_weather

# 配置常量
BASE_URL = "http://localhost:8080/v1/api"
API_KEY_MODEL = "ragflow-Y1NTkyNDBlZmZhNTExZWY5ZmNhZWUzZD"
API_KEY_DEEPSEEK = "***********************************"
API_KEY_SILICONFLOW = "sk-izwfqiufrwdqfkvqzklcyaashpmraqaaosuchkjnettqpabb"
API_KEY_QWEN = "sk-61010d52e30c4fb096d240ad7fae39df"

DATASET_ID = "a3870650ffa111ef92aa2a5c03e306d6"  # 视频知识库
CHAT_ID = "3b58bf3e8e2611ef98110242ac120006"  # blog_chat
CHAT_ID_TEST = "88a31ba86e1011efa2e10242ac120002"  # test_chat
CONFIG_FILE = 'config.yaml'
# 文件路径配置
USER_FILES_JSON = os.path.join(SCRIPT_DIR, "user/user_files.json")
DOC_IDS_JSON = os.path.join(SCRIPT_DIR, "ragdoc/doc_ids.json")
RAGDOC_DIR = os.path.join(SCRIPT_DIR, "ragdoc")
VIDEO_BASE_PATH = os.path.join(SCRIPT_DIR, "video")
USER_RAG_COUNTS_FILE = os.path.join(SCRIPT_DIR, "user_rag_counts.json")
USER_CONFIG_FILE = os.path.join(SCRIPT_DIR, "user_configs.json")
USER_FEEDBACK_FILE = os.path.join(SCRIPT_DIR, "user_feedback/all_user_feedback.json")

# 上传限制
MAX_RAG_UPLOADS = 2
MAX_VIDEO_UPLOADS = 1
VIP_EXTRA_RAG_UPLOADS = 10
VIP_EXTRA_VIDEO_UPLOADS = 10

# 允许的文件类型
ALLOWED_EXTENSIONS = ['.docx', '.xlsx', '.pptx', '.jpg', '.jpeg', '.png', '.pdf', '.txt', '.md', '.json', '.eml']

def load_config():
    """加载配置文件"""
    config_path = os.path.join(SCRIPT_DIR, "config.yaml")
    if not os.path.exists(config_path):
        return {}
    with open(config_path, 'r', encoding='utf-8') as f:
        return yaml.safe_load(f)

def save_config(config):
    """保存配置文件"""
    config_path = os.path.join(SCRIPT_DIR, "config.yaml")
    with open(config_path, 'w', encoding='utf-8') as f:
        yaml.dump(config, f, default_flow_style=False, allow_unicode=True)

def get_user_upload_limits(username):
    """获取用户的上传限制次数，考虑 VIP 状态"""
    limits = {
        'rag': MAX_RAG_UPLOADS,
        'video': MAX_VIDEO_UPLOADS
    }

    try:
        config = load_config()
        if username == "admin":
            return {
                'rag': 999999,  # 使用大数字代替 float('inf')
                'video': 999999
            }

        if username in config.get('credentials', {}).get('usernames', {}):
            user_info = config['credentials']['usernames'][username]
            if 'vip_expiry' in user_info:
                expiry_timestamp = user_info['vip_expiry']
                if expiry_timestamp > time.time():
                    limits['rag'] += VIP_EXTRA_RAG_UPLOADS
                    limits['video'] += VIP_EXTRA_VIDEO_UPLOADS
    except Exception as e:
        logging.error(f"Error checking VIP status for upload limits: {str(e)}")

    return limits

def manage_user_rag_count(username, increment=False):
    """管理用户RAG上传次数"""
    if not os.path.exists(USER_RAG_COUNTS_FILE):
        with open(USER_RAG_COUNTS_FILE, 'w') as f:
            json.dump({}, f, ensure_ascii=False)
    
    with open(USER_RAG_COUNTS_FILE, 'r') as f:
        counts = json.load(f)
    
    if username not in counts:
        counts[username] = 0
    
    if increment:
        counts[username] += 1
        with open(USER_RAG_COUNTS_FILE, 'w') as f:
            json.dump(counts, f, ensure_ascii=False)
    
    return counts[username]

def update_user_files(username, filename):
    """更新用户文件记录"""
    os.makedirs(os.path.dirname(USER_FILES_JSON), exist_ok=True)
    user_files = {}
    
    if os.path.exists(USER_FILES_JSON):
        try:
            with open(USER_FILES_JSON, 'r', encoding='utf-8') as f:
                content = f.read()
                if content:
                    user_files = json.loads(content)
        except (json.JSONDecodeError, Exception) as e:
            print(f"读取 {USER_FILES_JSON} 时发生错误: {str(e)}")
    
    if username not in user_files:
        user_files[username] = []
    
    if filename not in user_files[username]:
        user_files[username].append(filename)
    
    try:
        with open(USER_FILES_JSON, 'w', encoding='utf-8') as f:
            json.dump(user_files, f, indent=2, ensure_ascii=False)
    except Exception as e:
        print(f"写入 {USER_FILES_JSON} 时发生错误: {str(e)}")

def get_user_files_with_doc_ids(username, limit=200):
    """获取用户文件及其doc_ids"""
    user_files = []
    doc_ids = {}
    files_with_doc_ids = {}

    # 读取 user_files.json
    if os.path.exists(USER_FILES_JSON):
        try:
            with open(USER_FILES_JSON, 'r', encoding='utf-8') as f:
                user_data = json.load(f)
                user_files = user_data.get(username, [])
        except json.JSONDecodeError:
            print(f"警告: {USER_FILES_JSON} 不是有效的 JSON 格式")
        except Exception as e:
            print(f"读取 {USER_FILES_JSON} 时发生错误: {str(e)}")

    if os.path.exists(DOC_IDS_JSON):
        with open(DOC_IDS_JSON, 'r', encoding='utf-8') as f:
             doc_ids = json.load(f)
    
    # 筛选出有 doc_id 的文件
    for file in reversed(user_files):
        if file in doc_ids:
            files_with_doc_ids[file] = doc_ids[file]
            if len(files_with_doc_ids) == limit:
                break
    
    return files_with_doc_ids

def is_allowed_file(filename):
    """检查文件类型是否允许"""
    return os.path.splitext(filename)[1].lower() in ALLOWED_EXTENSIONS

def get_user_token(username, config):
    """获取用户令牌"""
    user_info = config.get('credentials', {}).get('usernames', {}).get(username, {})
    
    if 'token' not in user_info or 'expiry' not in user_info:
        return update_user_token(username, config)
    
    current_time = time.time()
    if current_time > user_info['expiry']:
        return update_user_token(username, config)
    
    return user_info['token']

def get_user_key(username, config):
    """获取用户令牌"""
    user_info = config.get('credentials', {}).get('usernames', {}).get(username, {})
    if 'password' not in user_info:
        return 
    else:
        return user_info['password']



def get_user_vip(username, config):
    is_vip = False
    if username in config['credentials']['usernames']:
        user_info = config['credentials']['usernames'][username]
        if 'vip_expiry' in user_info:
            expiry_timestamp = user_info['vip_expiry']
            if expiry_timestamp > time.time():  # VIP 未过期
                is_vip = True
    return is_vip





def update_user_token(username, config):
    """更新用户令牌"""
    token = secrets.token_urlsafe(32)
    expiry = time.time() + 240 * 3600  # 240小时后过期
    
    if 'credentials' not in config:
        config['credentials'] = {'usernames': {}}
    if 'usernames' not in config['credentials']:
        config['credentials']['usernames'] = {}
    if username not in config['credentials']['usernames']:
        config['credentials']['usernames'][username] = {}
    
    config['credentials']['usernames'][username]['token'] = token
    config['credentials']['usernames'][username]['expiry'] = expiry
    
    save_config(config)
    return token

def save_user_config(username, config_data):
    """保存用户配置"""
    if not os.path.exists(USER_CONFIG_FILE):
        with open(USER_CONFIG_FILE, 'w') as f:
            json.dump({}, f)
    
    with open(USER_CONFIG_FILE, 'r', encoding='utf-8') as f:
        all_configs = json.load(f)
    
    if username not in all_configs:
        all_configs[username] = {}
    all_configs[username].update(config_data)
    
    with open(USER_CONFIG_FILE, 'w', encoding='utf-8') as f:
        json.dump(all_configs, f, indent=4, ensure_ascii=False)

def get_user_config(username):
    """获取用户配置"""
    if not os.path.exists(USER_CONFIG_FILE):
        return {}
    
    try:
        with open(USER_CONFIG_FILE, 'r', encoding='utf-8') as f:
            all_configs = json.load(f)
            return all_configs.get(username, {})
    except Exception as e:
        print(f"读取用户配置时出错: {str(e)}")
        return {}

def get_api_key_for_user(username, api_type="deepseek"):
    """获取用户的API密钥"""
    user_config = get_user_config(username)
    
    # 检查是否是管理员或VIP用户
    config = load_config()
    is_privileged_user = False
    
    if username == "admin":
        is_privileged_user = True
    else:
        try:
            if username in config.get('credentials', {}).get('usernames', {}):
                user_info = config['credentials']['usernames'][username]
                if 'vip_expiry' in user_info:
                    expiry_timestamp = user_info['vip_expiry']
                    if expiry_timestamp > time.time():
                        is_privileged_user = True
        except Exception as e:
            logging.error(f"Error checking VIP status: {str(e)}")
    
    # 特权用户使用预设的API密钥
    if is_privileged_user:
        if api_type == "deepseek":
            return API_KEY_DEEPSEEK
        elif api_type == "qwen":
            return API_KEY_QWEN
        else:
            return API_KEY_SILICONFLOW
    
    # 普通用户使用自己配置的API密钥
    return user_config.get(f'api_key_{api_type}', '')


# 主应用辅助函数
def load_main_config():
    """加载主应用配置文件"""
    if not os.path.exists(CONFIG_FILE):
        return get_default_main_config()
    with open(CONFIG_FILE, 'r', encoding='utf-8') as file:
        return yaml.safe_load(file)
def get_default_main_config():
    """获取默认配置"""
    return {
        'credentials': {
            'usernames': {}
        }
    }

# API路由函数
def register_dashboard_routes(app):
    """注册dashboard相关的API路由"""

    @app.route('/api/weather')
    def api_weather():
        """获取天气信息API - 优化版本，提供更快的响应"""
        start_time = time.time()

        try:
            # 获取客户端IP地址
            client_ip = request.environ.get('HTTP_X_FORWARDED_FOR', request.environ.get('REMOTE_ADDR', '127.0.0.1'))
            if ',' in client_ip:
                client_ip = client_ip.split(',')[0].strip()

            print(f"[天气API] 开始获取天气信息，IP: {client_ip}")

            # 调用天气获取函数
            weather_info = get_weather(ip_address=client_ip)

            if weather_info:
                # 快速处理数据，减少处理时间
                try:
                    # 格式化时间
                    if isinstance(weather_info.get('local_time'), str):
                        try:
                            dt = datetime.fromisoformat(weather_info['local_time'])
                            local_time = dt.strftime('%Y-%m-%d %H:%M')
                        except:
                            local_time = weather_info['local_time']
                    else:
                        local_time = str(weather_info.get('local_time', ''))

                    # 转换可见度为千米
                    visibility = weather_info.get('visibility', 0)
                    visibility_km = round(visibility / 1000, 1) if visibility else 0

                    # 准备城市名称显示
                    city_display = weather_info.get('city', '未知城市')
                    if weather_info.get('city') == "Xi'an" and weather_info.get('is_fallback'):
                        city_display = f"{city_display} (显示默认城市天气)"

                    # 返回处理后的天气数据，确保所有字段都有值
                    processed_weather = {
                        'city': city_display or '未知城市',
                        'country': weather_info.get('country', ''),
                        'local_time': local_time or datetime.now().strftime('%Y-%m-%d %H:%M'),
                        'temperature': round(weather_info.get('temperature', 0)) if weather_info.get('temperature') is not None else 0,
                        'feels_like': round(weather_info.get('feels_like', 0)) if weather_info.get('feels_like') is not None else 0,
                        'humidity': weather_info.get('humidity', 0) if weather_info.get('humidity') is not None else 0,
                        'weather_description': weather_info.get('weather_description', '晴') or '晴',
                        'wind_speed': round(weather_info.get('wind_speed', 0), 1) if weather_info.get('wind_speed') is not None else 0,
                        'wind_deg': weather_info.get('wind_deg', 0) if weather_info.get('wind_deg') is not None else 0,
                        'clouds': weather_info.get('clouds', 0) if weather_info.get('clouds') is not None else 0,
                        'visibility': visibility_km if visibility_km is not None else 0,
                        'precipitation': weather_info.get('precipitation', 0) if weather_info.get('precipitation') is not None else 0,
                        'sunrise': weather_info.get('sunrise', '') or '',
                        'sunset': weather_info.get('sunset', '') or ''
                    }

                    # 验证关键字段
                    if not processed_weather['city'] or not processed_weather['weather_description']:
                        print(f"[天气API] 关键字段缺失: city={processed_weather['city']}, description={processed_weather['weather_description']}")
                        return jsonify({'success': False, 'message': '天气数据不完整'})

                    processing_time = round((time.time() - start_time) * 1000, 2)
                    print(f"[天气API] 成功获取天气信息，处理时间: {processing_time}ms")

                    response = jsonify({'success': True, 'weather_info': processed_weather})
                    # 添加缓存控制头，允许短时间缓存
                    response.headers['Cache-Control'] = 'public, max-age=300'  # 5分钟缓存
                    return response

                except Exception as data_error:
                    print(f"[天气API] 数据处理错误: {str(data_error)}")
                    return jsonify({'success': False, 'message': '天气数据处理失败'})
            else:
                processing_time = round((time.time() - start_time) * 1000, 2)
                print(f"[天气API] 无法获取天气信息，处理时间: {processing_time}ms")
                return jsonify({'success': False, 'message': '暂时无法获取天气信息，请稍后重试'})

        except Exception as e:
            processing_time = round((time.time() - start_time) * 1000, 2)
            print(f"[天气API] 获取天气信息异常: {str(e)}，处理时间: {processing_time}ms")
            return jsonify({'success': False, 'message': '天气服务暂时不可用'})
        




    @app.route('/api/upload-file', methods=['POST'])
    def api_upload_file():
        """文件上传API"""
        if 'username' not in session:
            return jsonify({'success': False, 'message': '请先登录'})

        username = session['username']

        if 'file' not in request.files:
            return jsonify({'success': False, 'message': '没有选择文件'})

        file = request.files['file']
        if file.filename == '':
            return jsonify({'success': False, 'message': '没有选择文件'})

        # 检查文件类型 - 使用第110行定义的文档类型扩展名列表
        file_ext = os.path.splitext(file.filename)[1].lower()
        # 使用文档上传允许的扩展名（第110行定义的ALLOWED_EXTENSIONS）
        DOCUMENT_ALLOWED_EXTENSIONS = ['.docx', '.xlsx', '.pptx', '.jpg', '.jpeg', '.png', '.pdf', '.txt', '.md', '.json', '.eml']
        if file_ext not in DOCUMENT_ALLOWED_EXTENSIONS:
            return jsonify({
                'success': False,
                'message': f'不支持的文件类型: {file_ext}。支持的类型: {", ".join(DOCUMENT_ALLOWED_EXTENSIONS)}'
            })

        # 检查上传次数限制
        current_uploads = manage_user_rag_count(username)
        limits = get_user_upload_limits(username)

        if current_uploads >= limits['rag']:
            return jsonify({'success': False, 'message': '已达到最大上传次数限制'})

        try:
            # 创建目录
            os.makedirs('ragdoc', exist_ok=True)

            # 修改文件名
            original_filename = file.filename
            new_filename = f"{username}_{original_filename}"
            file_path = os.path.join('ragdoc', new_filename)

            # 保存文件
            file.save(file_path)

            # 更新用户文件记录
            update_user_files(username, new_filename)

            # 增加上传次数
            manage_user_rag_count(username, increment=True)

            # 计算剩余次数
            remaining_uploads = limits['rag'] - (current_uploads + 1)
            # 确保管理员用户的剩余次数显示为大数字
            if remaining_uploads < 0 and limits['rag'] >= 999999:
                remaining_uploads = 999999

            return jsonify({
                'success': True,
                'message': f"文件 '{original_filename}' 上传成功",
                'remaining_uploads': remaining_uploads
            })

        except Exception as e:
            return jsonify({'success': False, 'message': f'文件上传失败: {str(e)}'})



    @app.route('/api/get-entrance-data')
    def api_get_entrance_data():
        """获取入口所需的用户数据"""
        if 'username' not in session:
            return jsonify({'success': False, 'message': '请先登录'})

        username = session['username']
        config = load_main_config()
        token = get_user_token(username, config)
        password = config.get('credentials', {}).get('usernames', {}).get(username, {}).get('password', '')
        origin = request.host_url.rstrip('/')

        # 检查VIP状态
        is_vip = False
        if username in config['credentials']['usernames']:
            user_info = config['credentials']['usernames'][username]
            if 'vip_expiry' in user_info:
                expiry_timestamp = user_info['vip_expiry']
                if expiry_timestamp > time.time():
                    is_vip = True

        # 检查是否是管理员
        is_admin = (username == "admin")

        response_data = {
            'username': username,
            'password': password,
            'token': token,
            'origin': origin,
            'is_admin': is_admin,
            'is_vip': is_vip
        }
        return jsonify({
            'success': True,
            'data': response_data
        })





    @app.route('/api/get-api-key', methods=['GET'])
    def api_get_api_key():
        """获取用户API密钥API"""
        if 'username' not in session:
            return jsonify({'success': False, 'message': '请先登录'})

        username = session['username']
        key_type = request.args.get('key_type', 'siliconflow')

        try:
            # 获取用户配置
            user_config = get_user_config(username)
            api_key = user_config.get(f'api_key_{key_type}', '')

            response = jsonify({
                'success': True,
                'api_key': api_key,
                'key_type': key_type
            })

            # 添加禁用缓存的HTTP头
            response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate'
            response.headers['Pragma'] = 'no-cache'
            response.headers['Expires'] = '0'

            return response

        except Exception as e:
            return jsonify({'success': False, 'message': f'获取失败: {str(e)}'})
        

    @app.route('/api/get-api-key-type', methods=['GET'])
    def api_get_api_key_type():
        """获取用户API密钥API"""
        if 'username' not in session:
            return jsonify({'success': False, 'message': '请先登录'})
        username = session['username']
        try:
            # 获取用户配置
            user_config = get_user_config(username)
            api_key_type = user_config.get(f'api_key_type', '')

            response = jsonify({
                'success': True,
                'api_key_type': api_key_type,
            })

            # 添加禁用缓存的HTTP头
            response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate'
            response.headers['Pragma'] = 'no-cache'
            response.headers['Expires'] = '0'

            return response

        except Exception as e:
            return jsonify({'success': False, 'message': f'获取失败: {str(e)}'})






    @app.route('/api/user-config', methods=['GET'])
    def api_user_config():
        """获取用户配置API"""
        if 'username' not in session:
            return jsonify({'success': False, 'message': '请先登录'})

        username = session['username']
        try:
            user_config = get_user_config(username)
            # 提取API密钥信息
            api_keys = {}
            for key, value in user_config.items():
                if key.startswith('api_key_'):
                    api_type = key.replace('api_key_', '')
                    api_keys[api_type] = value

            return jsonify({
                'success': True,
                'data': {
                    'api_keys': api_keys,
                    'config': user_config
                }
            })
        except Exception as e:
            return jsonify({'success': False, 'message': f'获取用户配置失败: {str(e)}'})


    @app.route('/api/save-api-key', methods=['POST'])
    def api_save_api_key():
        """保存API密钥API"""
        if 'username' not in session:
            return jsonify({'success': False, 'message': '请先登录'})

        username = session['username']
        data = request.get_json()
        key_type = data.get('key_type')
        key_value = data.get('key_value')

        if not key_type or not key_value:
            return jsonify({'success': False, 'message': '请提供完整的API密钥信息'})

        try:
            # 保存API密钥
            config_data = {
                f'api_key_{key_type}': key_value,
                'api_key_type': key_type
            }
            save_user_config(username, config_data)

            return jsonify({'success': True, 'message': 'API密钥保存成功'})

        except Exception as e:
            return jsonify({'success': False, 'message': f'保存失败: {str(e)}'})



    @app.route('/api/knowledge-base')
    def api_knowledge_base():
        """知识库问答页面API"""
        if 'username' not in session:
            return jsonify({'success': False, 'message': '请先登录'})

        username = session['username']
        config = load_config()
        token = get_user_token(username, config)
        # 注意：这里需要在登录时将原始密码存储到session中
        password = get_user_key(username, config)
        # 构建知识库URL
        origin = request.host_url.rstrip('/')
        knowledge_base_url = f"{origin}/api/mermindfig/chatwithfiles.html?user={username}&key={password}&token={token}"

        return jsonify({
            'success': True,
            'url': knowledge_base_url
        })






    @app.route('/api/submit-feedback', methods=['POST'])
    def api_submit_feedback():
        """提交用户反馈API"""
        if 'username' not in session:
            return jsonify({'success': False, 'message': '请先登录'})

        username = session['username']
        data = request.get_json()
        feedback = data.get('feedback', '').strip()
        USER_FEEDBACK_FILE = 'user_feedback/all_user_feedback.json'

        if not feedback:
            return jsonify({'success': False, 'message': '反馈内容不能为空'})

        try:
            # 确保反馈文件夹存在
            feedback_dir = os.path.dirname(USER_FEEDBACK_FILE)
            os.makedirs(feedback_dir, exist_ok=True)

            # 读取现有反馈数据
            feedback_data = {}
            if os.path.exists(USER_FEEDBACK_FILE):
                try:
                    with open(USER_FEEDBACK_FILE, 'r', encoding='utf-8') as f:
                        feedback_data = json.load(f)
                except (json.JSONDecodeError, Exception) as e:
                    print(f"读取反馈文件时发生错误: {str(e)}")
                    feedback_data = {}

            # 初始化用户反馈列表
            if username not in feedback_data:
                feedback_data[username] = []

            # 计算新的索引
            user_feedbacks = feedback_data[username]
            new_index = max([item.get('index', 0) for item in user_feedbacks], default=0) + 1

            # 添加新反馈
            new_feedback = {
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "feedback": feedback,
                "index": new_index
            }
            feedback_data[username].append(new_feedback)

            # 保存反馈数据
            with open(USER_FEEDBACK_FILE, 'w', encoding='utf-8') as f:
                json.dump(feedback_data, f, indent=4, ensure_ascii=False)

            return jsonify({
                'success': True,
                'message': '反馈提交成功',
                'feedback_index': new_index
            })

        except Exception as e:
            return jsonify({'success': False, 'message': f'反馈提交失败: {str(e)}'})


    @app.route('/api/get-feedback-history', methods=['GET'])
    def api_get_feedback_history():
        """获取用户反馈历史API"""
        if 'username' not in session:
            return jsonify({'success': False, 'message': '请先登录'})

        username = session['username']
        USER_FEEDBACK_FILE = 'user_feedback/all_user_feedback.json'

        try:
            # 读取反馈数据
            feedback_data = {}
            if os.path.exists(USER_FEEDBACK_FILE):
                try:
                    with open(USER_FEEDBACK_FILE, 'r', encoding='utf-8') as f:
                        feedback_data = json.load(f)
                except (json.JSONDecodeError, Exception) as e:
                    print(f"读取反馈文件时发生错误: {str(e)}")
                    return jsonify({'success': False, 'message': '读取反馈历史失败'})

            # 获取用户的反馈历史
            user_feedbacks = feedback_data.get(username, [])

            # 按时间倒序排列（最新的在前面）
            user_feedbacks.sort(key=lambda x: x.get('timestamp', ''), reverse=True)

            return jsonify({
                'success': True,
                'feedback_list': user_feedbacks,
                'total_count': len(user_feedbacks)
            })

        except Exception as e:
            return jsonify({'success': False, 'message': f'获取反馈历史失败: {str(e)}'})

