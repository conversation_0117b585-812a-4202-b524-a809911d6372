<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI 助教平台 - 查看博客</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.13.11/dist/katex.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.5.1/styles/atom-one-dark.min.css">
    <link rel="icon" href="data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 100 100%22><text y=%22.9em%22 font-size=%2290%22>📚</text></svg>">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=JetBrains+Mono:wght@400;500;600&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #667eea;
            --secondary-color: #764ba2;
            --accent-color: #f093fb;
            --text-primary: #2d3748;
            --text-secondary: #4a5568;
            --text-muted: #718096;
            --bg-primary: #ffffff;
            --bg-secondary: #f7fafc;
            --bg-tertiary: #edf2f7;
            --border-color: #e2e8f0;
            --shadow-light: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-medium: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-heavy: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            --gradient-primary: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            --gradient-accent: linear-gradient(135deg, var(--accent-color) 0%, var(--primary-color) 100%);
            --border-radius: 12px;
            --border-radius-lg: 20px;
        }

        * {
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: var(--bg-secondary);
            color: var(--text-primary);
            line-height: 1.7;
            font-size: 16px;
        }

        img {
            max-width: 100%;
            height: auto;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-light);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        img:hover {
            transform: scale(1.02);
            box-shadow: var(--shadow-medium);
        }

        #content {
            position: relative;
            margin-top: 80px;
            background: var(--bg-primary);
            border-radius: var(--border-radius-lg);
            padding: 40px;
            box-shadow: var(--shadow-light);
            border: 1px solid var(--border-color);
        }
        /* 代码块样式优化 */
        pre {
            background: #1a1b26 !important;
            border-radius: var(--border-radius) !important;
            border: 1px solid #2a2b3d !important;
            margin: 1.5em 0 !important;
            overflow: auto !important;
            position: relative;
            box-shadow: var(--shadow-medium);
            transition: all 0.3s ease;
        }

        pre:hover {
            box-shadow: var(--shadow-heavy);
            transform: translateY(-2px);
        }

        pre code {
            font-size: 14px !important;
            line-height: 1.6 !important;
            font-family: 'JetBrains Mono', 'Fira Code', 'Consolas', 'Monaco', 'Courier New', monospace !important;
            padding: 1.5em !important;
            display: block;
            color: #c9d1d9 !important;
        }

        /* 行内代码样式 */
        code:not(pre code) {
            font-size: 0.9em !important;
            padding: 0.25em 0.5em !important;
            background: var(--bg-tertiary) !important;
            color: var(--primary-color) !important;
            border-radius: 6px !important;
            font-family: 'JetBrains Mono', 'Fira Code', 'Consolas', monospace !important;
            font-weight: 500;
            border: 1px solid var(--border-color);
        }

        /* 代码块复制按钮 */
        pre::before {
            content: 'Code';
            position: absolute;
            top: 12px;
            right: 12px;
            background: rgba(255, 255, 255, 0.1);
            color: #c9d1d9;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-family: 'Inter', sans-serif;
            opacity: 0.7;
            transition: opacity 0.3s ease;
        }

        pre:hover::before {
            opacity: 1;
        }
        .mermaid {
            background-color: transparent; /* 设置为透明背景 */
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        /* 可选：为 Mermaid 图表添加边框 */
        .mermaid svg {
            border: 1px solid #ddd;
        }
        #commentSection {
            margin-top: 40px;
            background: var(--bg-primary);
            border-radius: var(--border-radius-lg);
            padding: 30px;
            box-shadow: var(--shadow-light);
            border: 1px solid var(--border-color);
        }

        #commentSection h3 {
            color: var(--text-primary);
            font-size: 1.5em;
            font-weight: 700;
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 2px solid var(--border-color);
        }

        .comment-input-container {
            position: relative;
            margin-bottom: 25px;
        }

        #commentInput {
            width: 100%;
            height: 120px;
            padding: 15px;
            margin-bottom: 15px;
            resize: vertical;
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius);
            font-family: 'Inter', sans-serif;
            font-size: 15px;
            line-height: 1.6;
            transition: all 0.3s ease;
            background: var(--bg-secondary);
        }

        #commentInput:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
            background: var(--bg-primary);
        }

        .comment-controls {
            display: flex;
            justify-content: flex-start;
            align-items: center;
            gap: 15px;
        }

        #emojiButton, #imageUploadButton {
            font-size: 20px;
            background: var(--bg-tertiary);
            border: 2px solid var(--border-color);
            cursor: pointer;
            padding: 10px 15px;
            border-radius: var(--border-radius);
            transition: all 0.3s ease;
        }

        #emojiButton:hover, #imageUploadButton:hover {
            background: var(--primary-color);
            border-color: var(--primary-color);
            transform: translateY(-2px);
        }

        #submitComment {
            padding: 12px 24px;
            background: var(--gradient-primary);
            color: white;
            border: none;
            cursor: pointer;
            border-radius: 25px;
            font-weight: 600;
            font-size: 15px;
            transition: all 0.3s ease;
            box-shadow: var(--shadow-light);
        }

        #submitComment:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-medium);
        }
        #garps {
            width: 100%;
            height: 60vh;
            position: relative;
        }
        .comment {
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            padding: 20px;
            margin-bottom: 20px;
            background: var(--bg-secondary);
            transition: all 0.3s ease;
            position: relative;
        }

        .comment:hover {
            box-shadow: var(--shadow-light);
            transform: translateY(-2px);
        }

        .comment-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
            padding-bottom: 8px;
            border-bottom: 1px solid var(--border-color);
        }

        .comment-author {
            font-weight: 600;
            color: var(--primary-color);
            font-size: 15px;
            display: flex;
            align-items: center;
        }

        .comment-author::before {
            content: '👤';
            margin-right: 8px;
            font-size: 14px;
        }

        .comment-date {
            font-size: 13px;
            color: var(--text-muted);
            font-weight: 500;
        }

        .comment-content {
            margin-top: 12px;
            line-height: 1.6;
            color: var(--text-primary);
            font-size: 15px;
        }

        .delete-comment {
            background: linear-gradient(135deg, #ff6b6b, #ee5a52);
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 20px;
            cursor: pointer;
            font-size: 12px;
            font-weight: 600;
            margin-top: 10px;
            transition: all 0.3s ease;
            box-shadow: var(--shadow-light);
        }

        .delete-comment:hover {
            transform: translateY(-1px);
            box-shadow: var(--shadow-medium);
        }
        #userInfo {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            padding: 15px 30px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: flex-end;
            align-items: center;
            box-shadow: var(--shadow-light);
        }

        #userInfo span {
            font-size: 15px;
            color: var(--text-primary);
            font-weight: 500;
        }

        #loginLink {
            color: white;
            background: var(--gradient-primary);
            text-decoration: none;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            padding: 10px 20px;
            border: none;
            border-radius: 25px;
            transition: all 0.3s ease;
            box-shadow: var(--shadow-light);
        }

        #loginLink:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-medium);
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            margin-right: 12px;
            background: var(--gradient-primary);
            display: flex;
            justify-content: center;
            align-items: center;
            color: white;
            font-weight: 700;
            font-size: 16px;
            box-shadow: var(--shadow-light);
        }
        .attachment-link {
            display: inline-flex;
            align-items: center;
            padding: 5px 10px;
            background-color: #f0f0f0;
            border: 1px solid #ddd;
            border-radius: 3px;
            text-decoration: none;
            color: #333;
            margin: 5px 0;
        }
        .attachment-link:hover {
            background-color: #e0e0e0;
        }
        .attachment-icon {
            margin-right: 5px;
        }
        .attachment-text {
            font-weight: bold;
        }
        #contentWrapper {
            position: relative;
            padding-top: 80px;
            margin-top: 20px;
        }

        #downloadPdfBtn {
            position: fixed;
            top: 20px;
            right: 30px;
            padding: 12px 24px;
            background: var(--gradient-primary);
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s ease;
            z-index: 999;
            box-shadow: var(--shadow-light);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        #downloadPdfBtn:before {
            content: '🖨️';
            font-size: 16px;
        }

        #downloadPdfBtn:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-medium);
        }

        #downloadPdfBtn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        #loadingIndicator {
            display: none;
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 30px 40px;
            border-radius: var(--border-radius-lg);
            z-index: 2000;
            font-weight: 500;
            box-shadow: var(--shadow-heavy);
            backdrop-filter: blur(10px);
        }

        /* 打印特定的样式 */
        @media print {
            body {
                padding: 20mm;
            }

            #downloadPdfBtn, #commentSection, #userInfo {
                display: none !important;
            }

            #content {
                width: 100% !important;
                margin: 0 !important;
                padding: 0 !important;
            }

            /* 确保数学公式正确显示 */
            .katex-display > .katex {
                display: block !important;
                text-align: center;
                white-space: nowrap;
            }

            /* 确保 Mermaid 图表正确显示 */
            .mermaid svg {
                max-width: 100% !important;
                height: auto !important;
            }

            /* 添加分页制 */
            h1, h2, h3 {
                page-break-after: avoid;
            }

            img, table {
                page-break-inside: avoid;
            }
        }

        .emoji-picker {
            display: none;
            position: absolute;
            bottom: 100%;
            left: 0;
            background-color: white;
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius);
            padding: 15px;
            width: 350px;
            max-height: 300px;
            z-index: 1000;
            box-shadow: var(--shadow-heavy);
            backdrop-filter: blur(10px);
        }

        .emoji-picker .emoji-tabs {
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
            margin-bottom: 10px;
            padding-bottom: 10px;
            border-bottom: 1px solid var(--border-color);
        }

        .emoji-picker .emoji-content {
            max-height: 180px;
            overflow-y: auto;
            scrollbar-width: thin;
            scrollbar-color: var(--primary-color) var(--bg-tertiary);
        }

        .emoji-picker .emoji-content::-webkit-scrollbar {
            width: 6px;
        }

        .emoji-picker .emoji-content::-webkit-scrollbar-track {
            background: var(--bg-tertiary);
            border-radius: 3px;
        }

        .emoji-picker .emoji-content::-webkit-scrollbar-thumb {
            background: var(--primary-color);
            border-radius: 3px;
        }

        .emoji-picker span {
            cursor: pointer;
            font-size: 20px;
            margin: 2px;
            padding: 5px;
            border-radius: 5px;
            transition: all 0.2s ease;
            user-select: none;
        }

        .emoji-picker span:hover {
            background-color: var(--bg-tertiary);
            transform: scale(1.2);
        }

        .toc-list {
            list-style-type: none;
            padding-left: 0;
        }

        .toc-list ul {
            list-style-type: none;
            padding-left: 20px;
        }

        .toc-list li {
            margin-bottom: 5px;
        }

        .toc-link {
            text-decoration: none;
            color: #333;
            transition: color 0.3s;
        }

        .toc-link:hover {
            color: #007bff;
        }

        /* 一级标题 */
        .toc-list > li > a {
            font-size: 18px;
            font-weight: bold;
        }

        /* 二级标题 */
        .toc-list > li > ul > li > a {
            font-size: 16px;
            font-weight: normal;
        }

        /* 三级标题 */
        .toc-list > li > ul > li > ul > li > a {
            font-size: 14px;
            font-weight: normal;
        }
        .toc-link.toc-level-1 {
            font-size: 18px;
            font-weight: bold;
        }

        .toc-link.toc-level-2 {
            font-size: 16px;
            font-weight: normal;
        }

        .toc-link.toc-level-3 {
            font-size: 14px;
            font-weight: normal;
        }

        .toc-divider {
            margin: 20px 0;
            border: 0;
            height: 1px;
            background-image: linear-gradient(to right, rgba(0, 0, 0, 0), rgba(0, 0, 0, 0.75), rgba(0, 0, 0, 0));
        }

        /* 可选：为目录添加一些额外的样式，使其更加突出 */
        .toc-list {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .video-container {
            position: relative;
            width: 100%; /* 默认宽度，可以在内联样式中覆盖 */
            max-width: 100%;
        }
        .video-container::before {
            content: "";
            display: block;
            padding-top: 56.25%; /* 16:9 宽高比 */
        }
        .video-container iframe,
        .video-container video {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }
        .you-message, .ai-message, .system-message {
            padding: 10px;
            margin-bottom: 10px;
            border-radius: 10px;
        }

        .you-message {
            background-color: #e6f3ff;
        }

        .ai-message {
            background-color: #f0f0f0;
        }

        .system-message {
            background-color: #ffe6e6;
        }

        #chatHistory {
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #ccc;
            padding: 10px;
            margin-bottom: 10px;
        }
        #aiChatSection {
            max-width: 100%;
            margin: 40px 0;
            background: var(--bg-primary);
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-light);
            padding: 30px;
            border: 1px solid var(--border-color);
            position: relative;
            overflow: hidden;
        }

        #aiChatSection::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--gradient-primary);
        }

        #aiChatSection h2 {
            color: var(--text-primary);
            border-bottom: 2px solid var(--border-color);
            padding-bottom: 15px;
            margin-bottom: 25px;
            font-size: 1.5em;
            font-weight: 700;
            display: flex;
            align-items: center;
        }

        #aiChatSection h2::before {
            content: '🤖';
            margin-right: 12px;
            font-size: 1.2em;
        }
        #chatPrompt {
            font-size: 16px;
            color: #4CAF50;
            margin-bottom: 15px;
            text-align: center;
            font-weight: bold;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
        #remainingChats {
            font-size: 14px;
            color: #666;
            margin-bottom: 15px;
        }

        #chatCount {
            font-weight: bold;
            color: #4CAF50;
        }

        #chatHistory {
            height: 400px;
            overflow-y: auto;
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius);
            padding: 20px;
            margin-bottom: 25px;
            background: var(--bg-secondary);
            scrollbar-width: thin;
            scrollbar-color: var(--primary-color) var(--bg-tertiary);
        }

        #chatHistory::-webkit-scrollbar {
            width: 8px;
        }

        #chatHistory::-webkit-scrollbar-track {
            background: var(--bg-tertiary);
            border-radius: 4px;
        }

        #chatHistory::-webkit-scrollbar-thumb {
            background: var(--primary-color);
            border-radius: 4px;
        }

        .chat-input-container {
            display: flex;
            gap: 15px;
            align-items: flex-end;
        }

        #userInput {
            flex-grow: 1;
            padding: 15px;
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius);
            font-size: 15px;
            font-family: 'Inter', sans-serif;
            line-height: 1.5;
            transition: all 0.3s ease;
            background: var(--bg-secondary);
            resize: vertical;
            min-height: 50px;
        }

        #userInput:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
            background: var(--bg-primary);
        }

        #sendButton {
            padding: 15px 25px;
            background: var(--gradient-primary);
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 15px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: var(--shadow-light);
            white-space: nowrap;
        }

        #sendButton:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-medium);
        }

        .you-message, .ai-message, .system-message {
            padding: 15px 20px;
            margin-bottom: 15px;
            border-radius: 20px;
            max-width: 75%;
            word-wrap: break-word;
            line-height: 1.6;
            font-size: 15px;
            position: relative;
            box-shadow: var(--shadow-light);
            animation: messageSlideIn 0.3s ease-out;
        }

        @keyframes messageSlideIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .you-message {
            background: var(--gradient-primary);
            color: white;
            margin-left: auto;
            border-bottom-right-radius: 5px;
        }

        .you-message::before {
            content: '👤';
            position: absolute;
            top: -8px;
            right: 15px;
            background: var(--bg-primary);
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            box-shadow: var(--shadow-light);
        }

        .ai-message {
            background: var(--bg-primary);
            color: var(--text-primary);
            margin-right: auto;
            border-bottom-left-radius: 5px;
            border: 1px solid var(--border-color);
        }

        .ai-message::before {
            content: '🤖';
            position: absolute;
            top: -8px;
            left: 15px;
            background: var(--primary-color);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            box-shadow: var(--shadow-light);
        }

        .system-message {
            background: var(--bg-tertiary);
            color: var(--text-muted);
            margin: 15px auto;
            text-align: center;
            font-style: italic;
            font-weight: 500;
            border: 1px solid var(--border-color);
        }
        .math-block {
            display: block;
            margin: 1em 0;
            text-align: left;
        }
        .math-inline {
            font-style: italic;
        }
        /* 标题样式优化 */
        #content h1, #content h2, #content h3, #content h4, #content h5, #content h6 {
            color: var(--text-primary);
            font-weight: 700;
            line-height: 1.3;
            margin-top: 2em;
            margin-bottom: 1em;
        }

        #content h1 {
            font-size: 2.5em;
            border-bottom: 3px solid var(--primary-color);
            padding-bottom: 15px;
            margin-bottom: 30px;
        }

        #content h2 {
            font-size: 2em;
            color: var(--primary-color);
        }

        #content h3 {
            font-size: 1.5em;
            color: var(--secondary-color);
        }

        /* 段落和文本优化 */
        #content p {
            margin-bottom: 1.5em;
            line-height: 1.8;
        }

        #content blockquote {
            border-left: 4px solid var(--primary-color);
            margin: 1.5em 0;
            padding: 1em 1.5em;
            background: var(--bg-tertiary);
            border-radius: 0 var(--border-radius) var(--border-radius) 0;
            font-style: italic;
            color: var(--text-secondary);
        }

        #content ul, #content ol {
            margin-bottom: 1.5em;
            padding-left: 2em;
        }

        #content li {
            margin-bottom: 0.5em;
            line-height: 1.7;
        }

        /* 表格样式 */
        #content table {
            width: 100%;
            border-collapse: collapse;
            margin: 1.5em 0;
            background: var(--bg-primary);
            border-radius: var(--border-radius);
            overflow: hidden;
            box-shadow: var(--shadow-light);
        }

        #content th, #content td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }

        #content th {
            background: var(--bg-tertiary);
            font-weight: 600;
            color: var(--text-primary);
        }

        #content tr:hover {
            background: var(--bg-secondary);
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            body {
                padding: 15px;
            }

            #content {
                padding: 25px;
                margin-top: 60px;
            }

            #content h1 {
                font-size: 2em;
            }

            #content h2 {
                font-size: 1.7em;
            }

            #userInfo {
                padding: 12px 20px;
            }

            #downloadPdfBtn {
                top: 15px;
                right: 20px;
                padding: 10px 20px;
                font-size: 13px;
            }

            #aiChatSection {
                padding: 20px;
                margin: 30px 0;
            }

            #chatHistory {
                height: 300px;
            }

            .chat-input-container {
                flex-direction: column;
                gap: 12px;
            }

            #userInput {
                min-height: 60px;
            }

            .you-message, .ai-message {
                max-width: 90%;
            }

            .score-container {
                flex-direction: column;
                text-align: center;
                gap: 15px;
            }

            .score-value {
                margin: 0;
                font-size: 2.5em;
            }

            .score-input, .submit-button {
                margin: 0;
                width: auto;
            }
        }

        @media (max-width: 480px) {
            #content {
                padding: 20px;
            }

            #content h1 {
                font-size: 1.8em;
            }

            .user-avatar {
                width: 35px;
                height: 35px;
                font-size: 14px;
            }

            #commentInput {
                height: 100px;
            }

            .comment {
                padding: 15px;
            }
        }
        .score-display {
            margin-top: 20px;
            padding: 10px;
            background-color: #f9f9f9; /* 背景颜色 */
            border: 1px solid #ddd; /* 边框 */
            border-radius: 5px; /* 圆角 */
        }

        .score {
            font-size: 1.2em; /* 字体大小 */
            color: #333; /* 字体颜色 */
        }

        .score-input {
            margin-left: 10px;
            padding: 5px;
            border: 1px solid #ccc;
            border-radius: 4px;
        }

        .submit-button {
            margin-left: 10px;
            padding: 5px 10px;
            background-color: #4CAF50; /* 按钮背景颜色 */
            color: white; /* 字体颜色 */
            border: none; /* 无边框 */
            border-radius: 4px; /* 圆角 */
            cursor: pointer; /* 鼠标指针 */
        }

        .submit-button:hover {
            background-color: #45a049; /* 悬停时的背景颜色 */
        }

        .category-display {
            margin-top: 10px; /* 上方间距 */
            font-size: 1em; /* 字体大小 */
            color: #666; /* 字体颜色 */
        }

        .score-display {
            margin-top: 30px;
            background: var(--bg-primary);
            border-radius: var(--border-radius-lg);
            padding: 25px;
            box-shadow: var(--shadow-light);
            border: 1px solid var(--border-color);
        }

        .score-container {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 25px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-medium);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .score-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, transparent 100%);
            pointer-events: none;
        }

        .score-container:hover {
            transform: translateY(-3px);
            box-shadow: var(--shadow-heavy);
        }

        .score-label {
            font-size: 1.3em;
            font-weight: 700;
            color: white;
            margin-right: 20px;
            text-transform: uppercase;
            letter-spacing: 1px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .score-value {
            font-size: 3em;
            font-weight: 800;
            color: white;
            text-shadow: 0 3px 6px rgba(0,0,0,0.3);
            margin-right: 25px;
            min-width: 100px;
            text-align: center;
            position: relative;
        }

        .score-input {
            padding: 12px 16px;
            font-size: 1.1em;
            border: 2px solid rgba(255,255,255,0.3);
            border-radius: var(--border-radius);
            margin-left: 20px;
            width: 120px;
            transition: all 0.3s ease;
            background: rgba(255,255,255,0.9);
            color: var(--text-primary);
            font-weight: 600;
        }

        .score-input:focus {
            outline: none;
            border-color: white;
            box-shadow: 0 0 0 3px rgba(255,255,255,0.3);
            background: white;
        }

        .submit-button {
            padding: 12px 24px;
            font-size: 1.1em;
            background: rgba(255,255,255,0.2);
            color: white;
            border: 2px solid rgba(255,255,255,0.3);
            border-radius: 25px;
            cursor: pointer;
            margin-left: 15px;
            transition: all 0.3s ease;
            font-weight: 600;
            backdrop-filter: blur(10px);
        }

        .submit-button:hover {
            background: rgba(255,255,255,0.3);
            border-color: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }

        .submit-button:active {
            transform: translateY(0);
        }

        .category-display {
            margin-top: 20px;
            padding: 15px 20px;
            background: var(--bg-tertiary);
            border-radius: var(--border-radius);
            border: 1px solid var(--border-color);
            font-size: 1em;
            color: var(--text-secondary);
            font-weight: 500;
            display: flex;
            align-items: center;
        }

        .category-display::before {
            content: '📂';
            margin-right: 10px;
            font-size: 1.2em;
        }
    </style>
    <!-- CDN版本的 marked.js 和备用加载逻辑 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/marked/4.3.0/marked.min.js"></script>
    <script>
        if (typeof marked === 'undefined') {
            console.log("CDN加载失败：marked，尝试加载本地版本");
            document.write('<script src="/api/static/lib/marked.min.js"><\/script>');
            // 内联版本在下面的超时检查中加载
        }
    </script>
    
    <!-- CDN版本的 KaTeX 和备用加载逻辑 -->
    <script src="https://cdn.jsdelivr.net/npm/katex@0.13.11/dist/katex.min.js"></script>
    <script>
        if (typeof katex === 'undefined') {
            console.log("CDN加载失败：katex，尝试加载本地版本");
            document.write('<script src="/api/static/lib/katex.min.js"><\/script>');
        }
    </script>
    
    <!-- CDN版本的 KaTeX auto-render 和备用加载逻辑 -->
    <script src="https://cdn.jsdelivr.net/npm/katex@0.13.11/dist/contrib/auto-render.min.js"></script>
    <script>
        if (typeof renderMathInElement === 'undefined') {
            console.log("CDN加载失败：renderMathInElement，尝试加载本地版本");
            document.write('<script src="/api/static/lib/auto-render.min.js"><\/script>');
        }
    </script>
    
    <!-- CDN版本的 mermaid 和备用加载逻辑 -->
    <script src="https://cdn.jsdelivr.net/npm/mermaid@11.3.0/dist/mermaid.min.js"></script>
    <script>
        if (typeof mermaid === 'undefined') {
            console.log("CDN加载失败：mermaid，尝试加载本地版本");
            document.write('<script src="/api/static/lib/mermaid.min.js"><\/script>');
        }
    </script>
    
    <!-- CDN版本的 highlight.js 和备用加载逻辑 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.5.1/highlight.min.js"></script>
    <script>
        if (typeof hljs === 'undefined') {
            console.log("CDN加载失败：highlight.js，尝试加载本地版本");
            document.write('<script src="/api/static/lib/highlight.min.js"><\/script>');
        }
    </script>
    
    <!-- CDN版本的 html2pdf 和备用加载逻辑 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.10.1/html2pdf.bundle.min.js"></script>
    <script>
        if (typeof html2pdf === 'undefined') {
            console.log("CDN加载失败：html2pdf，尝试加载本地版本");
            document.write('<script src="/api/static/lib/html2pdf.bundle.min.js"><\/script>');
        }
    </script>
    
    <!-- CDN版本的 jspdf 和备用加载逻辑 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script>
        if (typeof window.jspdf === 'undefined') {
            console.log("CDN加载失败：jspdf，尝试加载本地版本");
            document.write('<script src="/api/static/lib/jspdf.umd.min.js"><\/script>');
        }
    </script>
    
    <!-- CDN版本的 pdfmake 和备用加载逻辑 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.2.7/pdfmake.min.js"></script>
    <script>
        if (typeof pdfMake === 'undefined') {
            console.log("CDN加载失败：pdfmake，尝试加载本地版本");
            document.write('<script src="/api/static/lib/pdfmake.min.js"><\/script>');
        }
    </script>
    
    <!-- CDN版本的 vfs_fonts 和备用加载逻辑 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.2.7/vfs_fonts.js"></script>
    <script>
        if (typeof pdfMake !== 'undefined' && typeof pdfMake.vfs === 'undefined') {
            console.log("CDN加载失败：vfs_fonts，尝试加载本地版本");
            document.write('<script src="/api/static/lib/vfs_fonts.js"><\/script>');
        }
    </script>
    
    <!-- CDN版本的 echarts 和备用加载逻辑 -->
    <script src="https://assets.pyecharts.org/assets/echarts.min.js"></script>
    <script>
        if (typeof echarts === 'undefined') {
            console.log("CDN加载失败：echarts，尝试加载本地版本");
            document.write('<script src="/api/static/lib/echarts.min.js"><\/script>');
        }
    </script>
    
    <!-- 内联版本的 marked.js (最后的备用选项) -->
    <script>
        setTimeout(function() {
            if (typeof marked === 'undefined') {
                console.log("本地marked.js加载失败，使用内联版本");
                // 内联marked.js的简化版本
                (function() {
                    // 最小化的marked核心实现
                    window.marked = (function() {
                        var _escape = function(html) {
                            return html
                                .replace(/&/g, '&amp;')
                                .replace(/</g, '&lt;')
                                .replace(/>/g, '&gt;')
                                .replace(/"/g, '&quot;')
                                .replace(/'/g, '&#39;');
                        };
                        
                        var _parseInline = function(text) {
                            // 处理行内格式 (粗体, 斜体, 链接, 行内代码等)
                            return text
                                // 粗体
                                .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                                // 斜体
                                .replace(/\*(.*?)\*/g, '<em>$1</em>')
                                // 行内代码
                                .replace(/`(.*?)`/g, '<code>$1</code>')
                                // 链接 [text](url)
                                .replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2">$1</a>');
                        };
                        
                        var _parseBlock = function(text) {
                            var lines = text.split('\n');
                            var html = '';
                            var i = 0;
                            
                            while (i < lines.length) {
                                var line = lines[i];
                                
                                // 标题 (# 样式)
                                if (/^#{1,6}\s+(.+)$/.test(line)) {
                                    var match = line.match(/^(#{1,6})\s+(.+)$/);
                                    var level = match[1].length;
                                    var content = _parseInline(match[2]);
                                    html += '<h' + level + '>' + content + '</h' + level + '>\n';
                                    i++;
                                    continue;
                                }
                                
                                // 段落
                                if (line.trim().length > 0) {
                                    var paragraph = line;
                                    i++;
                                    
                                    // 合并多行段落
                                    while (i < lines.length && lines[i].trim().length > 0) {
                                        paragraph += '\n' + lines[i];
                                        i++;
                                    }
                                    
                                    html += '<p>' + _parseInline(paragraph) + '</p>\n';
                                    continue;
                                }
                                
                                // 空行
                                i++;
                            }
                            
                            return html;
                        };
                        
                        return {
                            parse: function(markdown) {
                                return _parseBlock(markdown);
                            },
                            setOptions: function() {
                                // 模拟setOptions方法，实际上不做任何事
                                return this;
                            }
                        };
                    })();
                    console.log("已加载内联marked.js");
                })();
            } else {
                console.log("marked.js已成功加载");
            }
        }, 500);
    </script>
    <!-- CDN版本的 mermaid 和备用加载逻辑 -->
    <script src="https://cdn.jsdelivr.net/npm/mermaid@11.3.0/dist/mermaid.min.js"></script>
    <script>
        if (typeof mermaid === 'undefined') {
            console.log("CDN加载失败：mermaid，尝试加载本地版本");
            document.write('<script src="/api/static/lib/mermaid.min.js"><\/script>');
        }
    </script>
    
    <!-- 确保 mermaid 加载完成后再初始化 -->
    <script>
        function initializeMermaid() {
            if (typeof mermaid === 'undefined') {
                console.log("等待 mermaid 加载...");
                setTimeout(initializeMermaid, 100);
                return;
            }
            console.log("mermaid 已加载，开始初始化");
            try {
                mermaid.initialize({
                    startOnLoad: true,
                    theme: 'default',
                    securityLevel: 'loose',
                    logLevel: 'error'
                });
                console.log("mermaid 初始化成功");
            } catch (error) {
                console.error("mermaid 初始化失败:", error);
            }
        }
        // 页面加载完成后初始化 mermaid
        window.addEventListener('load', initializeMermaid);
    </script>
</head>
<body>
    <div id="userInfo"></div>
    <div id="contentWrapper">
        <button id="downloadPdfBtn" onclick="downloadPdf()" disabled>打印此页面</button>
        <div id="content">
            <div class="blog-header">
                <h1>${blog.title}</h1>
               
            </div>
        </div>


    </div>  
    <div class="score-display">
      
    </div>
    <div class="category-display"></div>
    <div id="graphSection" style="display: none;">
        <h2>知识图谱</h2>
    </div>
    <div id="garps"></div>
    <!-- AI对话窗口 -->
    <div id="aiChatSection" style="display: none;">
        <h2>AI 对话助手</h2>
        <p id="chatPrompt" style="display: none;">🤖 赶紧登录问问AI当前页面内容吧，AI会帮你解答疑惑哦！ 💡 让知识闪闪发光 ✨</p>
        <div id="chatContent" style="display: none;">
            <p id="chatPrompt">🤖 问问AI当前页面内容吧，AI会帮你解答疑惑哦！ 💡 让知识闪闪发光 ✨</p>
            <p id="remainingChats">每日对话次数上限: 50次 <span id="chatCountDisplay" style="display: none;">| 剩余对话次数: <span id="chatCount"></span></span></p>
            <div id="chatHistory"></div>
            <div class="chat-input-container">
                <input type="text" id="userInput" placeholder="输入你的问题...">
                <button id="sendButton" onclick="sendMessage()">发送</button>
            </div>
        </div>
    </div>

    <div id="commentSection">
        <h3>评论</h3>
        <div id="commentList"></div>
        <div class="comment-input-container">
            <textarea id="commentInput" placeholder="输入您的评论..."></textarea>
            <div class="comment-controls">
                <button id="emojiButton" onclick="toggleEmojiPicker()">😊</button>
                <button id="imageUploadButton" onclick="document.getElementById('commentImageUpload').click()">📷</button>
                <input type="file" id="commentImageUpload" accept="image/*" style="display: none;">
                <button id="submitComment" onclick="submitComment()">提交评论</button>
            </div>
            <div id="emojiPicker" class="emoji-picker"></div>
        </div>
    </div>
    <div id="loadingIndicator">正在生成 PDF，请稍候...</div>

    <script>
        // 获取 URL 参数
        const urlParams = new URLSearchParams(window.location.search);
        const filename = urlParams.get('filename');
        const user = urlParams.get('user');
        const key = urlParams.get('key');
        const token = urlParams.get('token');
        // 记录访问量
        function recordView() {
            fetch('/api/record_view', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ filename: filename })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    console.log(`Views: ${data.views}`);
                    // 可以在这里更新页面上的访量显示
                    updateViewCount(data.views);
                }
            })
            .catch(error => console.error('Error:', error));
        }

        // 更新页面上的访问量显示
        function updateViewCount(views) {
            const viewCountElement = document.getElementById('viewCount');
            if (viewCountElement) {
                viewCountElement.textContent = `访问量: ${views}`;
            } else {
                const viewCountDiv = document.createElement('div');
                viewCountDiv.id = 'viewCount';
                viewCountDiv.textContent = `访问量: ${views}`;
                document.body.insertBefore(viewCountDiv, document.body.firstChild);
            }
        }

        // 页面加载时记录访问量
        recordView();


            // 获取当前页面的主域名
            function getMainDomain() {
                return window.location.protocol + '//' + window.location.hostname;
            }
        // 显示用户信息或登录链接
        function displayUserInfo() {
            const userInfoDiv = document.getElementById('userInfo');
            const aiChatSection = document.getElementById('aiChatSection');
            const chatPrompt = document.getElementById('chatPrompt');
            const chatContent = document.getElementById('chatContent');
            // 显示AI聊天部分
            aiChatSection.style.display = 'block';
            if (user && key) {
                const avatar = document.createElement('div');
                avatar.className = 'user-avatar';
                avatar.textContent = user.charAt(0).toUpperCase();               
                const userSpan = document.createElement('span');
                userSpan.textContent = `欢迎，${user}`;               
                userInfoDiv.appendChild(avatar);
                userInfoDiv.appendChild(userSpan);
                // 用户已登录，显示聊天界面
                chatPrompt.style.display = 'none';
                chatContent.style.display = 'block';
            } else {
                const loginLink = document.createElement('a');
                loginLink.id = 'loginLink';
                loginLink.textContent = '登录';
                loginLink.href = getMainDomain();
                userInfoDiv.appendChild(loginLink);
                // 用户未登录，只显示提示信息
                chatPrompt.style.display = 'block';
                chatContent.style.display = 'none';                
            }
        }

        // 页面加载时显示用户信息
        displayUserInfo();

        // 从服务器获取博客内容和分类信息
        fetch(`/api/get_blog_content/${filename}`)
            .then(response => response.json())
            .then(data => {
                console.log('获取的博客内容:', data); // 添加调试信息，查看返回的数据
                if (data.success) {
                    renderContent(data.content);
                    // 显示分类信息
                    const categoryDisplay = document.querySelector('.category-display');
                    if (categoryDisplay) {
                        categoryDisplay.textContent = `分类: ${data.category || '未分类'}`;
                    } else {
                        console.warn('分类显示元素未找到');
                    }
                    document.getElementById('downloadPdfBtn').disabled = false; // 启用按钮
                    
                    // 渲染分数显示部分
                    const scoreDisplay = document.querySelector('.score-display');
                    if (data.category.endsWith('作业')) {
                        console.log('显示电磁学作业分数部分'); // 调试信息
                        scoreDisplay.innerHTML = `
                            <div class="score-container">
                                <span class="score-label">分数:</span>
                                <span class="score-value">${data.score !== undefined && data.score !== null ? data.score : '未打分'}</span>
                                ${user === 'admin' ? `
                                    <input type="number" min="0" max="100" placeholder="打分" id="scoreInput" class="score-input">
                                    <button onclick="submitScore('${data.filename}')" class="submit-button">提交</button>
                                ` : ''}
                            </div>
                        `;
                        // 确保分数已加载
                        fetchScore();
                    }
                } else {
                    console.error('Failed to load blog content:', data.message);
                    document.getElementById('content').innerHTML = '<p>加载博客内容失败</p>';
                }
            })
            .catch(error => {
                console.error('Error:', error); // 输出错误信息
                document.getElementById('content').innerHTML = '<p>加载博客内容时发生错误: ' + error.message + '</p>'; // 显示具体错误信息
            });

        function renderContent(markdown) {
            console.log("开始渲染内容");
            const contentDiv = document.getElementById('content');
            // 处理文本框
            markdown = markdown.replace(/<div class="text-box".*?>([\s\S]*?)<\/div>/g, (match, content) => {
                    // 在文本框前后都添加换行和<br>标签
                    return `<br>\n${match.replace(/>\s*([\s\S]*?)\s*<\/div>$/, '>\n\n$1</div>')}\n<br>`;
                });

            // 处理 TOC
            const tocMatch = markdown.match(/@\[TOC\]\((.*?)\)/);
            let processedMarkdown = markdown;
            if (tocMatch) {
                console.log("处理目录");
                console.log("TOC Match:", tocMatch);
                const tocTitle = tocMatch[1];
                const headers = markdown.match(/^#{1,3}\s*.+$/gm);
                console.log("Headers:", headers);
                if (headers) {
                    let toc = `<h2>${tocTitle}</h2>\n<ul class="toc-list">\n`;
                    let currentLevel = 0;
                    headers.forEach(header => {
                        const level = header.match(/^#+/)[0].length;
                        const title = header.replace(/^#+\s*/, '').trim();
                        const link = title.toLowerCase().replace(/\s+/g, '-').replace(/[^\w\u4e00-\u9fa5-]+/g, '');
                        
                        if (level > currentLevel) {
                            toc += '<ul>'.repeat(level - currentLevel);
                        } else if (level < currentLevel) {
                            toc += '</ul>'.repeat(currentLevel - level);
                        }
                        
                        toc += `<li><a href="#${link}" class="toc-link toc-level-${level}" data-target="${link}">${title}</a></li>\n`;
                        currentLevel = level;
                    });
                    toc += '</ul>'.repeat(currentLevel);
                    toc += '</ul>';
                    
                    // 添加分界线
                    toc += '<hr class="toc-divider">';
                    
                    processedMarkdown = markdown.replace(/@\[TOC\]\(.*?\)(\s*#[^#])/, toc + '$1');
                    console.log("Generated TOC:", toc);
                }
            }


            // 处理附件下载链接
            processedMarkdown = processedMarkdown.replace(/\[([^\]]+)\]\(\/api\/download_attachment\/([^)]+)\)/g, (match, text, url) => {
                // 提取文件名，但不进行额外的编码
                const fileName = url.split('/').pop();
                const fullUrl = `/api/download_attachment/${fileName}?user=${user}&key=${key}&token=${token}`;
                // 在 download 属性中使用 decodeURIComponent 来显示正确的文件名
                return `<a href="${fullUrl}" class="attachment-link" download="${decodeURIComponent(fileName)}">
                    <span class="attachment-icon">📎</span>
                    <span class="attachment-text">${text}</span>
                </a>`;
            });

            // 使用正则表达式保留 <video> 标签
            processedMarkdown = processedMarkdown.replace(/(<video[\s\S]*?<\/video>)/g, (match) => {
                return `\n\n${match}\n\n`;
            });

            // 处理 LaTeX 公式
            processedMarkdown = processedMarkdown.replace(/\\\(([\s\S]*?)\\\)/g, (match, p1) => {
                // 将 * 替换为 \ast
                const processedFormula = p1.replace(/\*/g, '\\ast');
                return `<span class="math-inline">$${processedFormula}$</span>`;
            });

            // 处理 eqnarray 环境
            processedMarkdown = processedMarkdown.replace(/\\begin{eqnarray}([\s\S]*?)\\end{eqnarray}/g, function(match, content) {
                // 分割每一行并处理
                const lines = content.trim().split('\\\\');
                
                const processedLines = lines.map(line => {
                    // 移除行首行尾的空白
                    line = line.trim();
                    // 确保 & = & 格式正确
                    if (line.includes('&')) {
                        // 分割并重新组合，确保格式一致
                        const parts = line.split('&').map(part => part.trim());
                        if (parts.length >= 3) {
                            // 只保留第一个和最后一个部分，中间用 &= 连接
                            return parts[0] + ' &= ' + parts[2];
                        }
                    }
                    return line;
                }).filter(line => line); // 移除空行
                
                // 使用aligned环境，确保换行符被正确处理
                return '$$\\begin{aligned} ' + 
                    processedLines.join(' \\\\\\\\\\  ') + 
                    ' \\end{aligned}$$';
            });

            processedMarkdown = processedMarkdown.replace(/\\\[([\s\S]*?)\\\]/g, (match, p1) => {
                return `<div class="math-block">$$${p1.replace(/\*/g, '\*')}$$</div>`;
            });
            processedMarkdown = processedMarkdown.replace(/\\\(([\s\S]*?)\\\)/g, (match, p1) => {
                return `<span class="math-inline">$${p1.replace(/\*/g, '\\*')}$</span>`;
            });








            // 配置 marked.js
            marked.setOptions({
                highlight: function(code, lang) {
                    if (lang && hljs.getLanguage(lang)) {
                        return hljs.highlight(lang, code).value;
                    } else {
                        return hljs.highlightAuto(code).value;
                    }
                },
                langPrefix: 'hljs language-',
                headerIds: true,
            });

            // 使用 marked.js 解析 Markdown
            console.log("使用 marked.js 解析 Markdown");
            console.log("Processed Markdown:", processedMarkdown);
            const html = marked.parse(processedMarkdown);

            contentDiv.innerHTML = html;

            // 为标题添加 ID
            contentDiv.querySelectorAll('h1, h2, h3').forEach(header => {
                const id = header.textContent.toLowerCase().replace(/\s+/g, '-').replace(/[^\w\u4e00-\u9fa5-]+/g, '');
                header.id = id;
            });

            // 添加目录点击事件监听器
            document.querySelectorAll('.toc-link').forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const targetId = this.getAttribute('data-target');
                    console.log("Clicked link, target ID:", targetId);
                    const targetElement = document.getElementById(targetId);
                    if (targetElement) {
                        console.log("Target element found, scrolling...");
                        targetElement.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    } else {
                        console.log("Target element not found");
                    }
                });
            });

            // 使用 KaTeX 渲染数学公式
            renderMathInElement(contentDiv, {
                delimiters: [
                    {left: "$$", right: "$$", display: true},
                    {left: "$", right: "$", display: false},
                    {left: "\\[", right: "\\]", display: true},
                    {left: "\\(", right: "\\)", display: false}
                ],
                throwOnError: false,
                output: 'htmlAndMathml',
                trust: true,
                strict: false,
                fleqn: true,
                macros: {
                    "\\eqnarray": "\\begin{aligned}",
                    "\\endeqnarray": "\\end{aligned}"
                }
            });




            // 渲染 Mermaid 图表
            console.log("开始渲染 Mermaid 图表");
            try {
                if (typeof mermaid === 'undefined') {
                    console.error("mermaid 未定义，跳过图表渲染");
                } else {
                    // 使用新推荐的 run 方法替代弃用的 init 方法
                    // mermaid.init(undefined, '.mermaid');
                    mermaid.run({
                        querySelector: '.mermaid'
                    }).then(() => {
                        console.log("Mermaid 图表渲染完成");
                    });
                }
            } catch (error) {
                console.error("渲染 Mermaid 图表时出错:", error);
            }

            // 使用 highlight.js 高亮代码块
            console.log("开始高亮代码块");
            document.querySelectorAll('pre code').forEach((block) => {
                hljs.highlightBlock(block);
            });

            console.log("渲染完成");
        }









        function submitComment() {
            const commentText = document.getElementById('commentInput').value;
            if (!commentText.trim()) {
                alert('请输入评论内容');
                return;
            }

            fetch('/api/submit_comment', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    user: user,
                    key: key,
                    filename: filename,
                    comment: commentText,
                    token: token
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    document.getElementById('commentInput').value = '';
                    loadComments();
                } else {
                    alert('提交评论失败: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('提交评论时发生错误');
            });
        }

        // 渲染评论内容的函数，使用与博客内容相同的渲染方式
        function renderCommentContent(commentText) {
            // 使用正则表达式保留 <img> 标签
            let processedText = commentText.replace(/(<img[\s\S]*?>)/g, (match) => {
                return `\n\n${match}\n\n`;
            });

            // 处理 Mermaid 图表 - 在 marked.js 处理之前先转换
            // 匹配 ```mermaid 代码块
            processedText = processedText.replace(/```mermaid\s*\n([\s\S]*?)\n```/g, (match, content) => {
                // 生成唯一ID
                const mermaidId = 'mermaid-comment-' + Math.random().toString(36).substr(2, 9);
                return `<div class="mermaid" id="${mermaidId}">\n${content.trim()}\n</div>`;
            });

            // 处理 LaTeX 公式
            processedText = processedText.replace(/\\\(([\s\S]*?)\\\)/g, (match, p1) => {
                // 将 * 替换为 \ast
                const processedFormula = p1.replace(/\*/g, '\\ast');
                return `<span class="math-inline">$${processedFormula}$</span>`;
            });

            // 处理 eqnarray 环境
            processedText = processedText.replace(/\\begin{eqnarray}([\s\S]*?)\\end{eqnarray}/g, function(match, content) {
                // 分割每一行并处理
                const lines = content.trim().split('\\\\');

                const processedLines = lines.map(line => {
                    // 移除行首行尾的空白
                    line = line.trim();
                    // 确保 & = & 格式正确
                    if (line.includes('&')) {
                        // 分割并重新组合，确保格式一致
                        const parts = line.split('&').map(part => part.trim());
                        if (parts.length >= 3) {
                            // 只保留第一个和最后一个部分，中间用 &= 连接
                            return parts[0] + ' &= ' + parts[2];
                        }
                    }
                    return line;
                }).filter(line => line); // 移除空行

                // 使用aligned环境，确保换行符被正确处理
                return '$$\\begin{aligned} ' +
                    processedLines.join(' \\\\\\\\\\  ') +
                    ' \\end{aligned}$$';
            });

            processedText = processedText.replace(/\\\[([\s\S]*?)\\\]/g, (match, p1) => {
                return `<div class="math-block">$$${p1.replace(/\*/g, '\*')}$$</div>`;
            });
            processedText = processedText.replace(/\\\(([\s\S]*?)\\\)/g, (match, p1) => {
                return `<span class="math-inline">$${p1.replace(/\*/g, '\\*')}$</span>`;
            });

            // 配置 marked.js
            marked.setOptions({
                highlight: function(code, lang) {
                    if (lang && hljs.getLanguage(lang)) {
                        return hljs.highlight(lang, code).value;
                    } else {
                        return hljs.highlightAuto(code).value;
                    }
                },
                langPrefix: 'hljs language-',
                headerIds: false, // 评论中不需要标题ID
            });

            // 使用 marked.js 解析 Markdown
            const html = marked.parse(processedText);

            return html;
        }

        function loadComments() {
            fetch(`/api/get_comments/${filename}`, {
        method: 'GET',
        headers: {
            'Cache-Control': 'no-cache, no-store, must-revalidate',
                    'Pragma': 'no-cache',
                    'If-Modified-Since': '0'
                },
                credentials: 'same-origin'  // 包含cookies
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const commentList = document.getElementById('commentList');
                    commentList.innerHTML = '';
                    data.comments.forEach((comment) => {
                        const commentElement = document.createElement('div');
                        commentElement.className = 'comment';

                        // 渲染评论内容
                        const renderedContent = renderCommentContent(comment.comment);

                        commentElement.innerHTML = `
                            <div class="comment-header">
                                <span class="comment-author">${comment.user}</span>
                                <span class="comment-date">${new Date(comment.timestamp).toLocaleString()}</span>
                            </div>
                            <div class="comment-content">${renderedContent}</div>
                        `;

                        // 只有评论作者和 admin 可以看到删除按钮
                        if (user === comment.user || user === 'admin') {
                            const deleteButton = document.createElement('button');
                            deleteButton.textContent = '删除';
                            deleteButton.className = 'delete-comment';
                            deleteButton.onclick = () => deleteComment(comment.id);
                            commentElement.appendChild(deleteButton);
                        }

                        commentList.appendChild(commentElement);

                        // 为评论内容渲染数学公式
                        const commentContentDiv = commentElement.querySelector('.comment-content');
                        renderMathInElement(commentContentDiv, {
                            delimiters: [
                                {left: "$$", right: "$$", display: true},
                                {left: "$", right: "$", display: false},
                                {left: "\\[", right: "\\]", display: true},
                                {left: "\\(", right: "\\)", display: false}
                            ],
                            throwOnError: false,
                            output: 'htmlAndMathml',
                            trust: true,
                            strict: false,
                            fleqn: true,
                            macros: {
                                "\\eqnarray": "\\begin{aligned}",
                                "\\endeqnarray": "\\end{aligned}"
                            }
                        });

                        // 高亮评论中的代码块
                        commentContentDiv.querySelectorAll('pre code').forEach((block) => {
                            hljs.highlightBlock(block);
                        });

                        // 渲染评论中的 Mermaid 图表
                        setTimeout(() => {
                            try {
                                if (typeof mermaid !== 'undefined') {
                                    const mermaidElements = commentContentDiv.querySelectorAll('.mermaid');
                                    if (mermaidElements.length > 0) {
                                        console.log(`发现 ${mermaidElements.length} 个 Mermaid 图表在评论中`);

                                        // 为每个 Mermaid 元素单独渲染
                                        mermaidElements.forEach((element, index) => {
                                            try {
                                                // 确保元素有唯一ID
                                                if (!element.id) {
                                                    element.id = 'mermaid-comment-' + Math.random().toString(36).substr(2, 9);
                                                }

                                                console.log(`渲染评论中的 Mermaid 图表: ${element.id}`);

                                                // 使用 mermaid.run 渲染特定元素
                                                mermaid.run({
                                                    nodes: [element]
                                                }).then(() => {
                                                    console.log(`评论中的 Mermaid 图表 ${element.id} 渲染完成`);
                                                }).catch(error => {
                                                    console.error(`渲染评论中的 Mermaid 图表 ${element.id} 时出错:`, error);
                                                });
                                            } catch (elementError) {
                                                console.error(`处理 Mermaid 元素时出错:`, elementError);
                                            }
                                        });
                                    }
                                } else {
                                    console.warn("Mermaid 未定义，跳过评论中的图表渲染");
                                }
                            } catch (error) {
                                console.error("渲染评论中的 Mermaid 图表时出错:", error);
                            }
                        }, 100); // 延迟100ms确保DOM已经更新
                    });
                } else {
                    console.error('Failed to load comments:', data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
            });
        }

        function deleteComment(commentId) {
            if (!confirm('确定要删除这条评论吗？')) {
                return;
            }

            console.log(`Attempting to delete comment. User: ${user}, Blog: ${filename}, Comment ID: ${commentId}`);

            fetch('/api/delete_comment', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    user: user,
                    key: key,
                    blog_filename: filename,
                    comment_id: commentId,
                    token: token    
                })
            })
            .then(response => {
                console.log('Response status:', response.status);
                return response.json();
            })
            .then(data => {
                console.log('Server response:', data);
                if (data.success) {
                    loadComments(); // 重新加载评论
                } else {
                    alert('删除评论失败: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('删除评论时发生错误');
            });
        }

        // 页面加载时获取评论
        loadComments();

        // 评论区图片上传事件监听器
        document.getElementById('commentImageUpload').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                if (file.size > 2 * 1024 * 1024) { // 2MB = 2 * 1024 * 1024 bytes
                    alert('图片大小不能超过2MB');
                    this.value = ''; // 清空文件输入，允许用户重新选择
                    return;
                }

                const formData = new FormData();
                formData.append('image', file);
                formData.append('user', user); // 添加用户信息

                const commentTextarea = document.getElementById('commentInput');
                const cursorPosition = commentTextarea.selectionStart;

                // 显示上传进度（可选）
                const progressIndicator = document.createElement('span');
                progressIndicator.textContent = '图片上传中...';
                progressIndicator.style.color = 'gray';
                progressIndicator.style.fontSize = '0.8em';
                commentTextarea.parentNode.insertBefore(progressIndicator, commentTextarea.nextSibling);

                fetch('/api/uploadimage', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const imageUrl = data.imageUrl;
                        const fileName = imageUrl.split('/').pop(); // 获取文件名
                        const altText = fileName.split('.')[0]; // 使用文件名（不包括扩展名）作为 alt 文本
                        const imageHtml = `<img src="${imageUrl}" width="400" alt="${altText}" style="display: block; text-align: left; margin-left: 0; margin-right: auto;">`;

                        // 在光标位置插入图片 HTML
                        commentTextarea.value = commentTextarea.value.substring(0, cursorPosition) +
                                                imageHtml +
                                                commentTextarea.value.substring(cursorPosition);

                        // 将光标移动到插入的 HTML 之后
                        const newCursorPosition = cursorPosition + imageHtml.length;
                        commentTextarea.setSelectionRange(newCursorPosition, newCursorPosition);
                        commentTextarea.focus();
                    } else {
                        alert('图片上传失败：' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('图片上传出错，请重试');
                })
                .finally(() => {
                    // 移除进度指示器
                    if (progressIndicator) {
                        progressIndicator.remove();
                    }
                });
            }
        });

        // 修改 downloadPdf 函数
        function downloadPdf() {
            // 存储当前的页面标题
            const originalTitle = document.title;
            
            // 将页面标题更改为想要的 PDF 文件名
            document.title = `${filename}.pdf`;
            
            // 调用打印功能
            window.print();
            
            // 恢复原始页面标题
            setTimeout(() => {
                document.title = originalTitle;
            }, 1000);
        }


        
         var chart = echarts.init(document.getElementById('garps'), 'white', {renderer: 'canvas'});

        // 辅助函数：格式化长文本
        function formatLongText(text, maxLineLength = 10) {
            var lines = [];
            for (var i = 0; i < text.length; i += maxLineLength) {
                lines.push(text.substr(i, maxLineLength));
            }
            return lines.join('\n');
        }

        // 扩展的颜色组合数组
        const colorCombinations = [
            { start: '#FF6B6B', end: '#4ECDC4' },
            { start: '#45B649', end: '#DCE35B' },
            { start: '#614385', end: '#516395' },
            { start: '#FF512F', end: '#F09819' },
            { start: '#1A2980', end: '#26D0CE' },
            { start: '#FF5F6D', end: '#FFC371' },
            { start: '#11998E', end: '#38EF7D' },
            { start: '#8E2DE2', end: '#4A00E0' },
            { start: '#FC466B', end: '#3F5EFB' },
            { start: '#00B4DB', end: '#0083B0' },
            { start: '#FFA17F', end: '#00223E' },
            { start: '#74EBD5', end: '#9FACE6' },
            { start: '#6190E8', end: '#A7BFE8' },
            { start: '#D4145A', end: '#FBB03B' },
            { start: '#009245', end: '#FCEE21' },
            { start: '#662D8C', end: '#ED1E79' },
            { start: '#EE9CA7', end: '#FFDDE1' },
            { start: '#614385', end: '#516395' },
            { start: '#02AABD', end: '#00CDAC' },
            { start: '#FF512F', end: '#DD2476' },
            { start: '#FF61D2', end: '#FE9090' },
            { start: '#BFF098', end: '#6FD6FF' },
            { start: '#4E65FF', end: '#92EFFD' },
            { start: '#A9C9FF', end: '#FFBBEC' },
            { start: '#3A6073', end: '#16222A' }
        ];

        // 修改 getRandomColor 函数为 getGradientColor 函数
        function getGradientColor(index) {
            return colorCombinations[index % colorCombinations.length];
        }

        // 修改 getComplementaryColor 函数
        function getComplementaryColor(color) {
            let hex = color.replace('#', '');
            let r = parseInt(hex.substr(0, 2), 16);
            let g = parseInt(hex.substr(2, 2), 16);
            let b = parseInt(hex.substr(4, 2), 16);
            r = 255 - r;
            g = 255 - g;
            b = 255 - b;
            return `rgb(${r}, ${g}, ${b})`;
        }

        var option = {
            backgroundColor: '#FFFAFA',
            series: [{
                type: 'graph',
                layout: 'force',
                force: {
                    repulsion: 300,
                    edgeLength: 140,
                    gravity: 0.1,
                    layoutAnimation: true,
                },
                symbolSize: 60,
                roam: true,
                draggable: true,
                focusNodeAdjacency: true,
                label: {
                    show: true,
                    position: 'inside',
                    fontSize: 17
                },
                edgeLabel: {
                    normal: {
                        show: true,
                        position: 'middle',
                    }
                },
                data: [],
                links: [],
                edgeSymbol: ['none', 'arrow'],
                edgeSymbolSize: [0, 10],
                lineStyle: {
                    normal: {
                        curveness: 0
                    }
                },
            }]
        };

        chart.setOption(option);

        function loadGraph() {
            const defaultFilename = filename.split('.')[0];
            fetch('/api/graph/' + defaultFilename + '.json')
                .then(response => {
                    if (!response.ok) {
                        if (response.status === 404) {
                            throw new Error('Graph file not found');
                        }
                        throw new Error('HTTP error! status: ' + response.status);
                    }
                    return response.text();
                })
                .then(text => {
                    console.log("Received response:", text.substring(0, 200)); // 打印前200个字符
                    try {
                        return JSON.parse(text); // 尝试解析JSON
                    } catch (e) {
                        console.error("JSON解析错误:", e);
                        throw new Error("无法解析服务器响应为JSON");
                    }
                })
                .then(graphData => {
                    // 检查 nodes 是否为空
                    if (!graphData.nodes || graphData.nodes.length === 0) {
                        throw new Error('No nodes in graph data');
                    }

                    // 显示图谱部分
                    document.getElementById('graphSection').style.display = 'block';
                    document.getElementById('garps').style.display = 'block';

                    // 处理节点数据
                    graphData.nodes.forEach((node, index) => {
                        const gradientColor = getGradientColor(index);
                        const textColor = getComplementaryColor(gradientColor.end);
                        
                        // 创建更复杂的渐变效果
                        node.itemStyle = {
                            color: {
                                type: 'radial',
                                x: 0.5,
                                y: 0.5,
                                r: 0.5,
                                colorStops: [
                                    { offset: 0, color: echarts.color.lift(gradientColor.start, 0.1) }, // 略微亮化的中心
                                    { offset: 0.7, color: gradientColor.end }, // 主要颜色
                                    { offset: 1, color: echarts.color.lift(gradientColor.end, -0.3) } // 暗化的边缘
                                ]
                            },
                            borderColor: echarts.color.lift(gradientColor.end, 0.2),
                            borderWidth: 3,
                            shadowBlur: 15,
                            shadowColor: 'rgba(0, 0, 0, 0.2)',
                            shadowOffsetX: 0,
                            shadowOffsetY: 5
                        };
                        
                        // 添加高光效果
                        node.emphasis = {
                            itemStyle: {
                                borderColor: '#fff',
                                borderWidth: 2,
                                shadowBlur: 40,
                                shadowColor: gradientColor.end
                            }
                        };

                        node.label = {
                            color: textColor,
                            fontSize: 15,
                            fontWeight: 'bold',
                            position: 'inside'
                        };
                        node.symbolSize = node.symbolSize || 60;
                        node.symbol = 'circle';
                    });

                    // 处理连线数据
                    graphData.links.forEach((link, index) => {
                        const gradientColor = getGradientColor(index);
                        const textColor = gradientColor.end;
                        const textBackgroundColor = getComplementaryColor(gradientColor.end);
                        link.lineStyle = {
                            color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                                { offset: 0, color: gradientColor.start },
                                { offset: 1, color: gradientColor.end }
                            ]),
                            width: 2
                        };
                        link.label = {
                            show: false,
                            position: 'insideMiddle',  // 改为 'end'，使标签靠近目标节点
                            formatter: function(params) {
                                return formatLongText(params.data.value);
                            },
                            fontSize: 15,
                            fontWeight: 'bold',
                            color: "#fff",
                            backgroundColor: "#000",
                            padding: [4, 4],
                            borderRadius: 4,
                            align: 'center',
                            verticalAlign: 'middle',
                            overflow: 'breakAll',
                        };
                    });

                    option.series[0].data = graphData.nodes;
                    option.series[0].links = graphData.links;
                    chart.setOption(option);
                })
                .catch(error => {
                    console.error('Error:', error);
                    // 如果文件不存在或者 nodes 为空，隐藏图谱部分
                    document.getElementById('graphSection').style.display = 'none';
                    document.getElementById('garps').style.display = 'none';
                });
        }

        // 页面加载完成后自动加载图谱
        window.addEventListener('load', loadGraph);

        window.addEventListener('resize', function() {
            if (document.getElementById('graphSection').style.display !== 'none') {
                chart.resize();
            }
        });

        function toggleEmojiPicker() {
            const emojiPicker = document.getElementById('emojiPicker');
            emojiPicker.style.display = emojiPicker.style.display === 'none' ? 'block' : 'none';
            
            if (emojiPicker.style.display === 'block' && emojiPicker.children.length === 0) {
                loadEmojis();
            }
        }

        function loadEmojis() {
            const emojiPicker = document.getElementById('emojiPicker');

            // 创建表情包分类
            const emojiCategories = {
                '😊 表情': [
                    '😀', '😃', '😄', '😁', '😆', '😅', '😂', '🤣', '😊', '😇', '🙂', '🙃', '😉', '😌', '😍', '🥰', '😘', '😗', '😙', '😚', '😋', '😛', '😝', '😜', '🤪', '🤨', '🧐', '🤓', '😎', '🥳', '😏', '😒', '😞', '😔', '😟', '😕', '🙁', '☹️', '😣', '😖', '😫', '😩', '🥺', '😢', '😭', '😤', '😠', '😡', '🤬', '🤯', '😳', '🥵', '🥶', '😱', '😨', '😰', '😥', '😓', '🤗', '🤔', '🤭', '🤫', '🤥', '😶', '😐', '😑', '😬', '🙄', '😯', '😦', '😧', '😮', '😲', '🥱', '😴', '🤤', '😪', '😵', '🤐', '🥴', '🤢', '🤮', '🤧', '😷', '🤒', '🤕'
                ],
                '👋 手势': [
                    '👋', '🤚', '🖐️', '✋', '🖖', '👌', '🤌', '🤏', '✌️', '🤞', '🤟', '🤘', '🤙', '👈', '👉', '👆', '🖕', '👇', '☝️', '👍', '👎', '👊', '✊', '🤛', '🤜', '👏', '🙌', '👐', '🤲', '🤝', '🙏'
                ],
                '❤️ 爱心': [
                    '❤️', '🧡', '💛', '💚', '💙', '💜', '🖤', '🤍', '🤎', '💔', '❣️', '💕', '💞', '💓', '💗', '💖', '💘', '💝', '💟', '♥️', '💌', '💋', '💍', '💎'
                ],
                '🎉 庆祝': [
                    '🎉', '🎊', '🎈', '🎁', '🎀', '🎂', '🍰', '🧁', '🎆', '🎇', '✨', '🎃', '🎄', '🎋', '🎍', '🎑', '🎗️', '🎟️', '🎫', '🏆', '🏅', '🥇', '🥈', '🥉', '⭐', '🌟', '💫', '⚡'
                ],
                '🐱 动物': [
                    '🐶', '🐱', '🐭', '🐹', '🐰', '🦊', '🐻', '🐼', '🐨', '🐯', '🦁', '🐮', '🐷', '🐽', '🐸', '🐵', '🙈', '🙉', '🙊', '🐒', '🐔', '🐧', '🐦', '🐤', '🐣', '🐥', '🦆', '🦅', '🦉', '🦇', '🐺', '🐗', '🐴', '🦄', '🐝', '🐛', '🦋', '🐌', '🐞', '🐜', '🦟', '🦗', '🕷️', '🦂', '🐢', '🐍', '🦎', '🦖', '🦕', '🐙', '🦑', '🦐', '🦞', '🦀', '🐡', '🐠', '🐟', '🐬', '🐳', '🐋', '🦈', '🐊', '🐅', '🐆', '🦓', '🦍', '🦧', '🐘', '🦛', '🦏', '🐪', '🐫', '🦒', '🦘', '🐃', '🐂', '🐄', '🐎', '🐖', '🐏', '🐑', '🦙', '🐐', '🦌', '🐕', '🐩', '🦮', '🐕‍🦺', '🐈', '🐓', '🦃', '🦚', '🦜', '🦢', '🦩', '🕊️', '🐇', '🦝', '🦨', '🦡', '🦦', '🦥', '🐁', '🐀', '🐿️', '🦔'
                ],
                '🍎 食物': [
                    '🍎', '🍐', '🍊', '🍋', '🍌', '🍉', '🍇', '🍓', '🫐', '🍈', '🍒', '🍑', '🥭', '🍍', '🥥', '🥝', '🍅', '🍆', '🥑', '🥦', '🥬', '🥒', '🌶️', '🫑', '🌽', '🥕', '🫒', '🧄', '🧅', '🥔', '🍠', '🥐', '🥯', '🍞', '🥖', '🥨', '🧀', '🥚', '🍳', '🧈', '🥞', '🧇', '🥓', '🥩', '🍗', '🍖', '🦴', '🌭', '🍔', '🍟', '🍕', '🫓', '🥪', '🥙', '🧆', '🌮', '🌯', '🫔', '🥗', '🥘', '🫕', '🍝', '🍜', '🍲', '🍛', '🍣', '🍱', '🥟', '🦪', '🍤', '🍙', '🍚', '🍘', '🍥', '🥠', '🥮', '🍢', '🍡', '🍧', '🍨', '🍦', '🥧', '🧁', '🍰', '🎂', '🍮', '🍭', '🍬', '🍫', '🍿', '🍩', '🍪', '🌰', '🥜', '🍯'
                ],
                '⚽ 运动': [
                    '⚽', '🏀', '🏈', '⚾', '🥎', '🎾', '🏐', '🏉', '🥏', '🎱', '🪀', '🏓', '🏸', '🏒', '🏑', '🥍', '🏏', '🪃', '🥅', '⛳', '🪁', '🏹', '🎣', '🤿', '🥊', '🥋', '🎽', '🛹', '🛷', '⛸️', '🥌', '🎿', '⛷️', '🏂', '🪂', '🏋️‍♀️', '🏋️', '🏋️‍♂️', '🤼‍♀️', '🤼', '🤼‍♂️', '🤸‍♀️', '🤸', '🤸‍♂️', '⛹️‍♀️', '⛹️', '⛹️‍♂️', '🤺', '🤾‍♀️', '🤾', '🤾‍♂️', '🏌️‍♀️', '🏌️', '🏌️‍♂️', '🏇', '🧘‍♀️', '🧘', '🧘‍♂️', '🏄‍♀️', '🏄', '🏄‍♂️', '🏊‍♀️', '🏊', '🏊‍♂️', '🤽‍♀️', '🤽', '🤽‍♂️', '🚣‍♀️', '🚣', '🚣‍♂️', '🧗‍♀️', '🧗', '🧗‍♂️', '🚵‍♀️', '🚵', '🚵‍♂️', '🚴‍♀️', '🚴', '🚴‍♂️'
                ],
                '🎵 音乐': [
                    '🎵', '🎶', '🎼', '🎹', '🥁', '🎷', '🎺', '🎸', '🪕', '🎻', '🎤', '🎧', '📻', '🎚️', '🎛️', '🎙️', '📢', '📣', '📯', '🔔', '🔕'
                ],
                '🌈 自然': [
                    '🌍', '🌎', '🌏', '🌐', '🗺️', '🗾', '🧭', '🏔️', '⛰️', '🌋', '🗻', '🏕️', '🏖️', '🏜️', '🏝️', '🏞️', '🏟️', '🏛️', '🏗️', '🧱', '🪨', '🪵', '🛖', '🏘️', '🏚️', '🏠', '🏡', '🏢', '🏣', '🏤', '🏥', '🏦', '🏨', '🏩', '🏪', '🏫', '🏬', '🏭', '🏯', '🏰', '🗼', '🗽', '⛪', '🕌', '🛕', '🕍', '⛩️', '🕋', '⛲', '⛺', '🌁', '🌃', '🏙️', '🌄', '🌅', '🌆', '🌇', '🌉', '♨️', '🎠', '🎡', '🎢', '💈', '🎪', '🚂', '🚃', '🚄', '🚅', '🚆', '🚇', '🚈', '🚉', '🚊', '🚝', '🚞', '🚋', '🚌', '🚍', '🚎', '🚐', '🚑', '🚒', '🚓', '🚔', '🚕', '🚖', '🚗', '🚘', '🚙', '🛻', '🚚', '🚛', '🚜', '🏎️', '🏍️', '🛵', '🦽', '🦼', '🛴', '🚲', '🛺', '🚁', '🚟', '🚠', '🚡', '🛰️', '🚀', '🛸', '🛶', '⛵', '🚤', '🛥️', '🛳️', '⛴️', '🚢', '⚓', '⛽', '🚧', '🚨', '🚥', '🚦', '🛑', '🚏'
                ]
            };

            // 清空现有内容
            emojiPicker.innerHTML = '';

            // 创建分类标签页
            const tabContainer = document.createElement('div');
            tabContainer.className = 'emoji-tabs';
            tabContainer.style.cssText = `
                display: flex;
                flex-wrap: wrap;
                border-bottom: 1px solid #ddd;
                margin-bottom: 10px;
                gap: 5px;
            `;

            const contentContainer = document.createElement('div');
            contentContainer.className = 'emoji-content';
            contentContainer.style.cssText = `
                max-height: 200px;
                overflow-y: auto;
                display: grid;
                grid-template-columns: repeat(auto-fill, minmax(30px, 1fr));
                gap: 5px;
                padding: 5px;
            `;

            emojiPicker.appendChild(tabContainer);
            emojiPicker.appendChild(contentContainer);

            let firstCategory = true;

            Object.entries(emojiCategories).forEach(([categoryName, emojis]) => {
                // 创建标签按钮
                const tab = document.createElement('button');
                tab.textContent = categoryName.split(' ')[0]; // 只显示表情符号
                tab.title = categoryName; // 完整名称作为提示
                tab.style.cssText = `
                    background: ${firstCategory ? 'var(--primary-color)' : 'var(--bg-tertiary)'};
                    color: ${firstCategory ? 'white' : 'var(--text-primary)'};
                    border: 1px solid var(--border-color);
                    padding: 5px 10px;
                    border-radius: 15px;
                    cursor: pointer;
                    font-size: 14px;
                    transition: all 0.3s ease;
                `;

                tab.addEventListener('click', () => {
                    // 更新标签样式
                    tabContainer.querySelectorAll('button').forEach(btn => {
                        btn.style.background = 'var(--bg-tertiary)';
                        btn.style.color = 'var(--text-primary)';
                    });
                    tab.style.background = 'var(--primary-color)';
                    tab.style.color = 'white';

                    // 显示对应分类的表情
                    showEmojiCategory(emojis, contentContainer);
                });

                tab.addEventListener('mouseenter', () => {
                    if (tab.style.background !== 'var(--primary-color)') {
                        tab.style.background = 'var(--border-color)';
                    }
                });

                tab.addEventListener('mouseleave', () => {
                    if (tab.style.background !== 'var(--primary-color)') {
                        tab.style.background = 'var(--bg-tertiary)';
                    }
                });

                tabContainer.appendChild(tab);

                // 默认显示第一个分类
                if (firstCategory) {
                    showEmojiCategory(emojis, contentContainer);
                    firstCategory = false;
                }
            });
        }

        function showEmojiCategory(emojis, container) {
            container.innerHTML = '';

            emojis.forEach(emoji => {
                if (emoji.trim()) { // 跳过空字符串
                    const span = document.createElement('span');
                    span.textContent = emoji;
                    span.style.cssText = `
                        cursor: pointer;
                        font-size: 20px;
                        padding: 5px;
                        border-radius: 5px;
                        transition: all 0.2s ease;
                        text-align: center;
                        user-select: none;
                    `;

                    span.addEventListener('mouseenter', () => {
                        span.style.background = 'var(--bg-tertiary)';
                        span.style.transform = 'scale(1.2)';
                    });

                    span.addEventListener('mouseleave', () => {
                        span.style.background = 'transparent';
                        span.style.transform = 'scale(1)';
                    });

                    span.onclick = () => insertEmoji(emoji);
                    container.appendChild(span);
                }
            });
        }

        function insertEmoji(emoji) {
            const commentInput = document.getElementById('commentInput');
            const cursorPos = commentInput.selectionStart;
            const textBefore = commentInput.value.substring(0, cursorPos);
            const textAfter = commentInput.value.substring(cursorPos);
            commentInput.value = textBefore + emoji + textAfter;
            commentInput.focus();
            commentInput.selectionStart = commentInput.selectionEnd = cursorPos + emoji.length;
        }

        // 点击页面其他地方时关闭表情选择器
        document.addEventListener('click', function(event) {
            const emojiPicker = document.getElementById('emojiPicker');
            const emojiButton = document.getElementById('emojiButton');
            if (!emojiPicker.contains(event.target) && event.target !== emojiButton) {
                emojiPicker.style.display = 'none';
            }
        });


        let conversationId ="";
        let doc_id = null;
        let dataset_id = "ac4cfa1affa111ef9d352a5c03e306d6";
        // 添加消息历史数组
        let messageHistory = [];
        const MAX_WORDS = 20000;  // 中英文字符总数限制

        // 计算文本中的汉字和英文单词数
        function countWords(text) {
            // 匹配汉字
            const chineseChars = text.match(/[\u4e00-\u9fa5]/g) || [];
            // 匹配英文单词（包括数字和连字符）
            const englishWords = text.match(/[a-zA-Z]+(-[a-zA-Z]+)*|\d+/g) || [];
            
            return {
                total: chineseChars.length + englishWords.length,
                chinese: chineseChars.length,
                english: englishWords.length
            };
        }

        // 管理历史记录的函数
        function manageMessageHistory() {
            let totalWords = 0;
            let managedHistory = [];
            
            // 从最新的消息开始遍历
            for (let i = messageHistory.length - 1; i >= 0; i--) {
                const message = messageHistory[i];
                const messageText = `${message.role}: ${message.content}\n`;
                const wordCount = countWords(messageText);
                
                if (totalWords + wordCount.total <= MAX_WORDS) {
                    managedHistory.unshift(message);
                    totalWords += wordCount.total;
                    console.log(`Added message - Chinese: ${wordCount.chinese}, English: ${wordCount.english}, Total: ${totalWords}/${MAX_WORDS}`);
                } else {
                    // 如果是第一条消息且历史为空，确保至少保留一条
                    if (managedHistory.length === 0) {
                        managedHistory.unshift(message);
                        const firstMsgCount = countWords(messageText);
                        console.log(`Keeping first message - Total words: ${firstMsgCount.total}`);
                    }
                    break;
                }
            }
            
            return managedHistory;
        }

        async function sendMessage() {
            const userInput = document.getElementById('userInput').value;
            const sendButton = document.getElementById('sendButton');           
            if (!userInput) return;

            // 添加用户消息到历史
            messageHistory.push({
                role: 'user',
                content: userInput,
                timestamp: new Date().toISOString()
            });

            // 管理历史记录大小
            messageHistory = manageMessageHistory();

            // 显示用户消息
            appendMessage('You', userInput);

            // 清空输入框
            document.getElementById('userInput').value = '';

            // 禁用发送按钮并显示等待状态
            sendButton.disabled = true;
            sendButton.textContent = '等待中...';

            try {
                // 格式化历史记录
                const formattedHistory = messageHistory
                    .map(msg => `${msg.role}: ${msg.content}`)
                    .join('\n');

                // 发送消息到服务器
                const response = await fetch('/api/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        user: user,
                        key: key,
                        message: userInput,
                        conversationId: conversationId,
                        filename: filename,
                        doc_id: doc_id,
                        dataset_id: dataset_id,
                        token: token,
                        history: formattedHistory 
                    })
                });

                const data = await response.json();

                if (data.success) {
                    // 添加AI回复到历史
                    messageHistory.push({
                        role: 'assistant',
                        content: data.response,
                        timestamp: new Date().toISOString()
                    });

                    // 再次管理历史记录大小
                    messageHistory = manageMessageHistory();

                    // 显示AI回复
                    appendMessage('AI', data.response);
                    // 更新剩余对话次数
                    updateRemainingChats(data.remainingChats);
                    // 保存conversationId
                    conversationId = data.conversationId;

                    // 输出当前历史记录状态
                    const currentStatus = countWords(formattedHistory);
                    console.log(`Current history status - Chinese: ${currentStatus.chinese}, English: ${currentStatus.english}, Total: ${currentStatus.total}/${MAX_WORDS}`);
                } else {
                    appendMessage('System', 'Error: ' + data.message);
                }
            } catch (error) {
                console.error('Error:', error);
                appendMessage('System', 'An error occurred while sending the message.');
            } finally {
                // 恢复发送按钮状态
                sendButton.disabled = false;
                sendButton.textContent = '发送';
            }
        }

        function updateRemainingChats(remainingChats) {
            const chatCountDisplay = document.getElementById('chatCountDisplay');
            const chatCount = document.getElementById('chatCount'); 
            chatCountDisplay.style.display = 'inline';
            chatCount.textContent = remainingChats;
        }

        function appendMessage(sender, message) {
            const chatHistory = document.getElementById('chatHistory');
            const messageElement = document.createElement('div');
            messageElement.className = sender.toLowerCase() + '-message';

            // 使用 renderContent 函数处理消息内容
            const renderedContent = renderChatContent(message);

            messageElement.innerHTML = `<strong>${sender}:</strong> ${renderedContent}`;
            chatHistory.appendChild(messageElement);
            chatHistory.scrollTop = chatHistory.scrollHeight;

            // 初始化新添加的元素
            initializeChatElements(messageElement);
        }

        function renderChatContent(markdown) {
            console.log("开始渲染聊天内容");
            
            let processedMarkdown = markdown;

            // 预处理 LaTeX 公式
            processedMarkdown = processedMarkdown.replace(/\\\((.*?)\\\)/g, (match, p1) => {
                return `$${processLatex(p1)}$`;
            });
            processedMarkdown = processedMarkdown.replace(/\\\[(.*?)\\\]/g, (match, p1) => {
                return `$$${processLatex(p1)}$$`;
            });
            processedMarkdown = processedMarkdown.replace(/\$\$(.*?)\$\$/g, (match, p1) => {
                return `$$${processLatex(p1)}$$`;
            });

            // 处理 eqnarray 环境
            processedMarkdown = processedMarkdown.replace(/\\begin{eqnarray}([\s\S]*?)\\end{eqnarray}/g, function(match, content) {
                // 分割每一行并处理
                const lines = content.trim().split('\\\\');
                
                const processedLines = lines.map(line => {
                    // 移除行首行尾的空白
                    line = line.trim();
                    // 确保 & = & 格式正确
                    if (line.includes('&')) {
                        // 分割并重新组合，确保格式一致
                        const parts = line.split('&').map(part => part.trim());
                        if (parts.length >= 3) {
                            // 只保留第一个和最后一个部分，中间用 &= 连接
                            return parts[0] + ' &= ' + parts[2];
                        }
                    }
                    return line;
                }).filter(line => line); // 移除空行
                
                // 使用aligned环境，确保换行符被正确处理
                return '$$\\begin{aligned} ' + 
                    processedLines.join(' \\\\\\\\\\  ') + 
                    ' \\end{aligned}$$';
            });

            // 配置 marked.js
            marked.setOptions({
                highlight: function(code, lang) {
                    if (lang && hljs.getLanguage(lang)) {
                        return hljs.highlight(lang, code).value;
                    } else {
                        return hljs.highlightAuto(code).value;
                    }
                },
                langPrefix: 'hljs language-',
                headerIds: true,
            });

            // 使用 marked.js 解析 Markdown
            console.log("使用 marked.js 解析 Markdown");
            const html = marked.parse(processedMarkdown);

            // 创建一个临时的 div 元素来容纳渲染后的内容
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = html;

            // 初始化元素（包括数学公式渲染、代码高亮和Mermaid图表）
            initializeChatElements(tempDiv);

            console.log("渲染完成");

            return tempDiv.innerHTML;
        }

        function initializeChatElements(container) {
            // 渲染数学公式
            renderMathInElement(container, {
                delimiters: [
                    {left: "$$", right: "$$", display: true},
                    {left: "$", right: "$", display: false},
                    {left: "\\[", right: "\\]", display: true},
                    {left: "\\(", right: "\\)", display: false}
                ],
                throwOnError: false,
                output: 'htmlAndMathml',
                trust: true,
                strict: false,
                fleqn: true,
                macros: {
                    "\\eqnarray": "\\begin{aligned}",
                    "\\endeqnarray": "\\end{aligned}"
                }
            });

            // 应用代码高亮
            container.querySelectorAll('pre code').forEach((block) => {
                hljs.highlightBlock(block);
            });
        }

        function processLatex(latex) {
            // 转义反斜杠
            latex = latex.replace(/\\/g, '\\\\');
            // 在星号后添加零宽空格，防止被解释为 Markdown 语法
            latex = latex.replace(/\*/g, '*\u200B');
            return latex;
        }


    function submitScore() {
        const scoreInput = document.getElementById('scoreInput');
        const score = scoreInput.value;

        if (score < 0 || score > 100) {
            alert('请提供有效的分数（0-100）。');
            return;
        }

        // 发送分数到后端
        fetch(`/api/submit_score?filename=${filename}&score=${score}&user=${user}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('分数提交成功！');
                // 更新页面显示的分数
                const scoreElement = document.querySelector('.score-value');
                if (scoreElement) {
                    scoreElement.textContent = score;
                    // 根据分数设置颜色
                    if (score >= 90) {
                        scoreElement.style.color = '#4CAF50'; // 绿色
                    } else if (score >= 60) {
                        scoreElement.style.color = '#FFA500'; // 橙色
                    } else {
                        scoreElement.style.color = '#808080'; // 灰色
                    }
                    console.log('分数已更新到页面'); // 调试信息
                } else {
                    console.error('未找到.score-value元素'); // 调试信息
                }
            } else {
                alert('提交分数失败，请重试。');
            }
        })
        .catch(error => {
            console.error('Error submitting score:', error);
            alert('提交分数时发生错误');
        });
    }

        // 在页面加载时获取分数
        let blog = { score: null }; // 确保 blog 变量被定义

        function fetchScore() {
            const urlParams = new URLSearchParams(window.location.search);
            const filename = urlParams.get('filename'); // 从 URL 参数获取文件名
            
            console.log('正在获取分数，文件名:', filename); // 调试信息
            
            if (!filename) {
                console.error('URL中没有找到filename参数');
                return;
            }

            const scoreUrl = `/api/get_score/${filename}`;
            console.log('请求URL:', scoreUrl); // 调试信息

            fetch(scoreUrl)
                .then(response => {
                    console.log('API响应状态:', response.status); // 调试信息
                    return response.json();
                })
                .then(data => {
                    console.log('获取的分数数据:', data); // 调试信息
                    if (data.success && data.score !== null) {
                        blog.score = data.score; // 将获取的分数赋值给 blog.score
                        const scoreElement = document.querySelector('.score-value');
                        if (scoreElement) {
                            scoreElement.textContent = blog.score;
                            // 根据分数设置颜色
                            if (blog.score >= 90) {
                                scoreElement.style.color = '#4CAF50'; // 绿色
                            } else if (blog.score >= 60) {
                                scoreElement.style.color = '#FFA500'; // 橙色
                            } else {
                                scoreElement.style.color = '#808080'; // 灰色
                            }
                            console.log('分数已更新到页面'); // 调试信息
                        } else {
                            console.error('未找到.score-value元素'); // 调试信息
                        }
                    } else {
                        const scoreElement = document.querySelector('.score-value');
                        if (scoreElement) {
                            scoreElement.textContent = '未评分';
                            scoreElement.style.color = '#808080'; // 灰色
                            console.log('显示未评分状态'); // 调试信息
                        }
                    }
                })
                .catch(error => {
                    console.error('获取分数时出错:', error);
                });
        }

        // 确保在DOM加载完成后执行
        document.addEventListener('DOMContentLoaded', fetchScore);
        
        // 为了防止可能的时序问题，也在window.onload中调用
        window.onload = function() {
            console.log('页面完全加载完成，确保获取分数'); // 调试信息
            fetchScore();
        };

    </script>
</body>
</html>