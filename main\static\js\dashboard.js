// 仪表板专用JavaScript文件

class Dashboard {
    constructor() {
        this.currentUser = null;
        this.apiKeys = {};
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.loadUserConfig();
        this.loadWeather();
    }

    setupEventListeners() {
        // 文件上传
        const fileUpload = document.getElementById('file-upload');
        if (fileUpload) {
            fileUpload.addEventListener('change', this.handleFileUpload.bind(this));
        }

        // API密钥保存
        const saveApiKeyBtn = document.getElementById('save-api-key');
        if (saveApiKeyBtn) {
            saveApiKeyBtn.addEventListener('click', this.handleSaveApiKey.bind(this));
        }

        // API密钥类型切换
        const apiKeyType = document.getElementById('api-key-type');
        if (apiKeyType) {
            apiKeyType.addEventListener('change', this.handleApiKeyTypeChange.bind(this));
        }

        // 功能按钮
        this.setupFeatureButtons();
    }

    setupFeatureButtons() {
        // 智能对话按钮
        const chatBtns = document.querySelectorAll('[onclick="openChat()"]');
        chatBtns.forEach(btn => {
            btn.removeAttribute('onclick');
            btn.addEventListener('click', this.openChat.bind(this));
        });

        // 视频学习按钮
        const videoBtns = document.querySelectorAll('[onclick="openVideoLearning()"]');
        videoBtns.forEach(btn => {
            btn.removeAttribute('onclick');
            btn.addEventListener('click', this.openVideoLearning.bind(this));
        });

        // 知识库按钮
        const knowledgeBtns = document.querySelectorAll('[onclick="openKnowledgeBase()"]');
        knowledgeBtns.forEach(btn => {
            btn.removeAttribute('onclick');
            btn.addEventListener('click', this.openKnowledgeBase.bind(this));
        });
    }

    async handleFileUpload(e) {
        const file = e.target.files[0];
        if (!file) return;

        // 检查文件大小 (50MB限制)
        const maxSize = 50 * 1024 * 1024;
        if (file.size > maxSize) {
            this.showMessage('文件大小不能超过50MB', 'error');
            e.target.value = '';
            return;
        }

        // 检查文件类型
        const allowedTypes = ['.docx', '.xlsx', '.pptx', '.jpg', '.jpeg', '.png', '.pdf', '.txt', '.md', '.json', '.eml'];
        const fileExt = '.' + file.name.split('.').pop().toLowerCase();
        
        if (!allowedTypes.includes(fileExt)) {
            this.showMessage('不支持的文件类型', 'error');
            e.target.value = '';
            return;
        }

        await this.uploadFile(file);
        e.target.value = ''; // 清空文件选择
    }

    async uploadFile(file) {
        // 添加文件类型检查
        const allowedExtensions = ['.docx', '.xlsx', '.pptx', '.jpg', '.jpeg', '.png', '.pdf', '.txt', '.md', '.json', '.eml'];
        const fileExtension = '.' + file.name.split('.').pop().toLowerCase();

        console.log('File name:', file.name);
        console.log('File extension:', fileExtension);
        console.log('Allowed extensions:', allowedExtensions);

        if (!allowedExtensions.includes(fileExtension)) {
            this.showMessage(`不支持的文件类型: ${fileExtension}。支持的类型: ${allowedExtensions.join(', ')}`, 'error');
            return;
        }

        const formData = new FormData();
        formData.append('file', file);

        try {
            this.showMessage('正在上传文件...', 'info');

            console.log('Uploading file:', file.name);
            console.log('File size:', file.size);
            console.log('File type:', file.type);

            const response = await fetch('/api/upload-file', {
                method: 'POST',
                body: formData,
                credentials: 'same-origin'
            });

            console.log('Response status:', response.status);
            console.log('Response URL:', response.url);

            // 先获取响应文本，然后尝试解析JSON
            const responseText = await response.text();
            console.log('Response text:', responseText);

            let result;
            try {
                result = JSON.parse(responseText);
            } catch (jsonError) {
                console.error('JSON parse error:', jsonError);
                console.error('Raw response:', responseText);

                // 如果JSON解析失败但状态码是200，可能是代理问题
                if (response.status === 200) {
                    this.showMessage('上传可能成功，但响应格式异常', 'warning');
                    return;
                }
                throw new Error(`Invalid JSON response: ${jsonError.message}`);
            }

            // 检查业务逻辑成功状态，而不仅仅是HTTP状态码
            if (result.success || (response.status === 200 && result.message && result.message.includes('上传成功'))) {
                this.showMessage(result.message || `文件 '${file.name}' 上传成功！`, 'success');
                if (result.remaining_uploads !== undefined) {
                    this.updateRemainingUploads(result.remaining_uploads);
                }

                // 清空文件输入框
                const fileInput = document.getElementById('file-upload');
                if (fileInput) {
                    fileInput.value = '';
                }

                console.log('Upload completed successfully');
            } else {
                this.showMessage(result.message || '文件上传失败', 'error');
            }
        } catch (error) {
            console.error('文件上传错误:', error);

            // 检查是否是网络错误但实际上传可能成功的情况
            if (error.message.includes('Failed to fetch') || error.message.includes('NetworkError')) {
                this.showMessage('网络请求异常，但文件可能已上传成功', 'warning');
            } else {
                this.showMessage(`文件上传失败: ${error.message}`, 'error');
            }
        }
    }

    updateRemainingUploads(remaining) {
        const uploadInfo = document.querySelector('.upload-info');
        if (uploadInfo) {
            // 如果是管理员的大数字，显示为"无限制"
            const displayText = remaining >= 999999 ? '无限制' : remaining;
            uploadInfo.textContent = `剩余上传次数: ${displayText}`;
        }
    }

    async handleSaveApiKey() {
        const keyType = document.getElementById('api-key-type').value;
        const keyValue = document.getElementById('api-key-input').value.trim();
        const saveButton = document.getElementById('save-api-key');

        if (!keyValue) {
            this.showMessage('请输入API密钥', 'error');
            return;
        }

        // 禁用保存按钮，防止重复提交
        saveButton.disabled = true;
        saveButton.textContent = '保存中...';

        const maxRetries = 3;
        let retryCount = 0;

        const delay = ms => new Promise(resolve => setTimeout(resolve, ms));

        while (retryCount < maxRetries) {
            try {
                console.log(`尝试保存API密钥 (第${retryCount + 1}次):`, keyType);
                
                const response = await fetch('/api/save-api-key', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                        'Cache-Control': 'no-cache'  // 防止缓存
                    },
                    body: JSON.stringify({
                        key_type: keyType,
                        key_value: keyValue
                    })
                });

                console.log('服务器响应状态:', response.status);

                // 如果响应状态不是200，等待后重试
                if (!response.ok) {
                    if (retryCount < maxRetries - 1) {
                        retryCount++;
                        await delay(1000 * retryCount); // 递增延迟
                        continue;
                    }
                    throw new Error(`服务器响应错误 (${response.status})`);
                }

                const responseText = await response.text();
                console.log('服务器原始响应:', responseText);

                // 检查响应是否为空
                if (!responseText.trim()) {
                    if (retryCount < maxRetries - 1) {
                        retryCount++;
                        await delay(1000 * retryCount);
                        continue;
                    }
                    throw new Error('服务器返回空响应');
                }

                let result;
                try {
                    result = JSON.parse(responseText);
                } catch (e) {
                    if (retryCount < maxRetries - 1) {
                        retryCount++;
                        await delay(1000 * retryCount);
                        continue;
                    }
                    throw new Error('服务器响应格式错误');
                }

                // 检查响应中是否包含错误信息
                if (!result.success) {
                    throw new Error(result.message || '保存失败');
                }

                // 保存成功
                this.showMessage(`${this.getKeyTypeName(keyType)} API密钥保存成功`, 'success');
                this.apiKeys[keyType] = keyValue;
                break; // 成功后退出重试循环

            } catch (error) {
                if (retryCount === maxRetries - 1) {
                    console.error('保存API密钥失败:', error);
                    // 移除可能的重复错误前缀
                    let errorMessage = error.message;
                    if (errorMessage.startsWith('保存失败: 保存失败:')) {
                        errorMessage = errorMessage.replace('保存失败: ', '');
                    }
                    this.showMessage(errorMessage || '保存失败，请重试', 'error');
                    break;
                }
                retryCount++;
                await delay(1000 * retryCount);
            }
        }

        // 恢复保存按钮状态
        saveButton.disabled = false;
        saveButton.textContent = '保存';
    }

    getKeyTypeName(keyType) {
        const typeNames = {
            'siliconflow': 'Siliconflow',
            'deepseek': 'Deepseek',
            'qwen': '通义千问'
        };
        return typeNames[keyType] || keyType;
    }

    handleApiKeyTypeChange() {
        const keyType = document.getElementById('api-key-type').value;
        const keyInput = document.getElementById('api-key-input');
        
        // 清空输入框
        keyInput.value = '';
        keyInput.placeholder = `输入您的 ${keyType.toUpperCase()} API 密钥`;
    }

    async loadUserConfig() {
        try {
            const response = await fetch('/api/user-config');
            const result = await response.json();

            if (result.success) {
                this.apiKeys = result.data.api_keys || {};
                // 可以在这里设置其他用户配置
            }
        } catch (error) {
            console.error('加载用户配置错误:', error);
        }
    }

    async loadWeather() {
        try {
            const response = await fetch('/api/weather');
            const result = await response.json();
            
            const weatherInfo = document.getElementById('weather-info');
            if (weatherInfo) {
                if (result.success) {
                    weatherInfo.innerHTML = result.weather_html;
                } else {
                    weatherInfo.textContent = '天气信息加载失败';
                }
            }
        } catch (error) {
            console.error('加载天气信息错误:', error);
            const weatherInfo = document.getElementById('weather-info');
            if (weatherInfo) {
                weatherInfo.textContent = '天气信息加载失败';
            }
        }
    }

    // 功能按钮处理
    openChat() {
        // 检查是否有API密钥
        if (!this.hasValidApiKey()) {
            this.showMessage('请先配置API密钥', 'error');
            return;
        }

        // 打开聊天页面
        const origin = window.location.origin;
        const chatUrl = `${origin}/api/mermindfig/chatwithfiles.html`;
        window.open(chatUrl, '_blank');
    }

    openVideoLearning() {
        // 打开视频学习页面
        const origin = window.location.origin;
        const videoUrl = `${origin}/api/collections_page`;
        window.open(videoUrl, '_blank');
    }

    async openKnowledgeBase() {
        try {
            const response = await fetch('/api/knowledge-base');
            const result = await response.json();

            if (result.success) {
                window.open(result.url, '_blank');
            } else {
                this.showMessage(result.message, 'error');
            }
        } catch (error) {
            console.error('打开知识库错误:', error);
            this.showMessage('打开知识库失败', 'error');
        }
    }

    hasValidApiKey() {
        return Object.values(this.apiKeys).some(key => key && key.trim().length > 0);
    }

    showMessage(message, type = 'info', duration = null) {
        const container = document.getElementById('message-container') || this.createMessageContainer();
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${type}`;

        // 添加图标
        let icon = '';
        switch(type) {
            case 'success':
                icon = '✅ ';
                break;
            case 'error':
                icon = '❌ ';
                break;
            case 'warning':
                icon = '⚠️ ';
                break;
            default:
                icon = 'ℹ️ ';
        }

        messageDiv.textContent = icon + message;

        container.appendChild(messageDiv);

        // 显示动画
        setTimeout(() => {
            messageDiv.classList.add('show');
        }, 100);

        // 根据消息类型设置不同的显示时间
        const displayTime = duration || (type === 'warning' ? 5000 : 3000);

        // 自动隐藏
        setTimeout(() => {
            messageDiv.classList.remove('show');
            setTimeout(() => {
                if (container.contains(messageDiv)) {
                    container.removeChild(messageDiv);
                }
            }, 300);
        }, displayTime);
    }

    createMessageContainer() {
        const container = document.createElement('div');
        container.id = 'message-container';
        container.className = 'message-container';
        document.body.appendChild(container);
        return container;
    }
}

// 全局函数（保持向后兼容）
function openChat() {
    if (window.dashboard) {
        window.dashboard.openChat();
    }
}

function openVideoLearning() {
    if (window.dashboard) {
        window.dashboard.openVideoLearning();
    }
}

function openKnowledgeBase() {
    if (window.dashboard) {
        window.dashboard.openKnowledgeBase();
    }
}

// 将仪表板实例暴露到全局并初始化
window.dashboard = null;
document.addEventListener('DOMContentLoaded', function() {
    window.dashboard = new Dashboard();
    console.log('仪表板初始化完成');
});
