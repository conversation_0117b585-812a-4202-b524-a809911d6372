
import os
import json
import datetime
import traceback
from logger_config import video_server_logger as logger, CustomTransLogger



# 添加记录用户数据的函数
def record_class_user_data(user, user_class, message, filename):
    try:
        # 确保class_user_data文件夹存在
        class_data_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'class_user_data')
        os.makedirs(class_data_dir, exist_ok=True)
        
        # 构建JSON文件路径
        json_file_path = os.path.join(class_data_dir, f"{user_class}.json")
        
        # 获取当前时间
        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        # 读取现有数据（如果存在）或创建新文件
        existing_data = []
        if os.path.exists(json_file_path):
            try:
                with open(json_file_path, 'r', encoding='utf-8') as f:
                    existing_data = json.load(f)
                    if not isinstance(existing_data, list):
                        existing_data = []
            except json.JSONDecodeError:
                existing_data = []
        else:
            with open(json_file_path, 'w', encoding='utf-8') as f:
                json.dump([], f, ensure_ascii=False, indent=2)

        # 查找该用户的记录
        user_found = False
        for item in existing_data:
            if item.get('user') == user:
                user_found = True
                # 用户存在，获取或创建chat_views列表
                chat_views = item.get('chat_views', [])
                
                # 添加新的聊天记录
                chat_views.append({
                    "message": message,
                    "filename": filename,
                    "timestamp": current_time
                })
                
                # 更新chat_views列表
                item['chat_views'] = chat_views
                break
        
        # 如果没有找到该用户的记录，创建新记录
        if not user_found:
            new_user_data = {
                "user": user,
                "class": user_class,
                "chat_views": [{
                    "message": message,
                    "filename": filename,
                    "timestamp": current_time
                }]
            }
            existing_data.append(new_user_data)
        
        # 保存数据到JSON文件
        with open(json_file_path, 'w', encoding='utf-8') as f:
            json.dump(existing_data, f, ensure_ascii=False, indent=2)
            
        logger.info(f"Recorded user data for class '{user_class}': {user}")
    except Exception as e:
        logger.error(f"Error recording class user data: {str(e)}")
        logger.error(traceback.format_exc())



def record_video_viewing(user, user_class, video_filename):
    """
    记录用户视频观看数据
    
    Args:
        user (str): 用户名
        user_class (str): 用户的class值
        video_filename (str): 视频文件名
    """
    try:
        # 确保class_user_data文件夹存在
        class_data_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'class_user_data')
        os.makedirs(class_data_dir, exist_ok=True)
        
        # 构建JSON文件路径
        json_file_path = os.path.join(class_data_dir, f"{user_class}.json")
        
        # 获取当前时间
        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        # 读取现有数据（如果存在）
        existing_data = []
        if os.path.exists(json_file_path):
            try:
                with open(json_file_path, 'r', encoding='utf-8') as f:
                    existing_data = json.load(f)
                    if not isinstance(existing_data, list):
                        existing_data = []
            except json.JSONDecodeError:
                # 如果文件存在但不是有效的JSON，重置为空列表
                existing_data = []
        else:
            # 如果文件不存在，创建一个空的JSON文件
            with open(json_file_path, 'w', encoding='utf-8') as f:
                json.dump([], f, ensure_ascii=False, indent=2)


        # 查找该用户的记录
        user_found = False
        for item in existing_data:
            if item.get('user') == user:
                user_found = True
                # 用户存在，检查视频记录
                video_records = item.get('video_views', [])
                
                # 查找是否已有该视频的记录
                video_found = False
                for video_record in video_records:
                    if video_record.get('video_filename') == video_filename:
                        # 更新时间
                        video_record['timestamp'] = current_time
                        video_found = True
                        break
                
                # 如果没有该视频的记录，添加新记录
                if not video_found:
                    video_records.append({
                        "video_filename": video_filename,
                        "timestamp": current_time
                    })
                
                # 更新视频记录列表
                item['video_views'] = video_records
                break
        
        # 如果没有找到该用户的记录，创建新记录
        if not user_found:
            new_user_data = {
                "user": user,
                "class": user_class,
                "video_views": [{
                    "video_filename": video_filename,
                    "timestamp": current_time
                }]
            }
            existing_data.append(new_user_data)
        
        # 保存数据到JSON文件
        with open(json_file_path, 'w', encoding='utf-8') as f:
            json.dump(existing_data, f, ensure_ascii=False, indent=2)
            
        logger.info(f"Recorded video viewing for user '{user}' in class '{user_class}': {video_filename}")
    except Exception as e:
        logger.error(f"Error recording video viewing data: {str(e)}")
        logger.error(traceback.format_exc())