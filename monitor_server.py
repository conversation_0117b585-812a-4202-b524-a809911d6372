import psutil
import time
import subprocess
import os
import sys
import logging
from logging.handlers import RotatingFileHandler
from datetime import datetime
import requests
from requests.exceptions import RequestException
import signal
import traceback
import threading
from logger_config import monitor_logger as logger
import sys
import io
import os

# 设置标准输出和标准错误的编码为UTF-8
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')
sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8')

# 设置环境变量
os.environ['PYTHONIOENCODING'] = 'utf-8'
# 创建日志目录
log_dir = 'logs'
if not os.path.exists(log_dir):
    os.makedirs(log_dir)

# 配置日志处理器
class LimitedRotatingFileHandler(RotatingFileHandler):
    """限制日志行数的处理器"""
    def __init__(self, filename, max_lines=1000, **kwargs):
        super().__init__(filename, **kwargs)
        self.max_lines = max_lines
        
    def emit(self, record):
        """重写emit方法，在每次写入时检查行数"""
        super().emit(record)
        try:
            # 检查文件行数
            with open(self.baseFilename, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            # 如果超过最大行数，只保留最新的max_lines行
            if len(lines) > self.max_lines:
                with open(self.baseFilename, 'w', encoding='utf-8') as f:
                    f.writelines(lines[-self.max_lines:])
        except Exception as e:
            print(f"Error limiting log lines: {e}")
    
    def doRollover(self):
        """在轮转时也执行行数限制"""
        super().doRollover()
        self.emit(None)  # 触发行数检查

# 配置日志
log_file = os.path.join(log_dir, f'monitor_{datetime.now().strftime("%Y%m%d")}.log')
file_handler = LimitedRotatingFileHandler(
    filename=log_file,
    max_lines=5000,
    maxBytes=1024*1024,  # 1MB
    backupCount=3,
    encoding='utf-8'
)
file_handler.setFormatter(logging.Formatter(
    '%(asctime)s - %(levelname)s - %(message)s'
))

console_handler = logging.StreamHandler(sys.stdout)

def cleanup_old_logs():
    """清理旧的日志文件，只保留最近3天的
    支持清理以下格式的日志：
    - monitor_YYYYMMDD.log
    - api_YYYYMMDD.log
    - video_server_YYYYMMDD.log
    """
    try:
        current_date = datetime.now()
        for filename in os.listdir(log_dir):
            if not filename.endswith('.log'):
                continue
                
            file_path = os.path.join(log_dir, filename)
            try:
                # 根据不同的日志文件名格式提取日期
                if filename.startswith('monitor_'):
                    date_str = filename[8:16]
                elif filename.startswith('api_'):
                    date_str = filename[4:12]
                elif filename.startswith('video_server_'):
                    date_str = filename[12:20]
                else:
                    continue
                
                file_date = datetime.strptime(date_str, '%Y%m%d')
                if (current_date - file_date).days > 3:
                    os.remove(file_path)
                    logger.info(f"Removed old log file: {filename}")
            except ValueError:
                logger.warning(f"Could not parse date from filename: {filename}")
                continue
    except Exception as e:
        logger.error(f"Error cleaning up old logs: {e}")



def get_server_process():
    """获取服务器进程"""
    try:
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                cmdline = ' '.join(proc.info['cmdline'] or [])
                if 'python' in proc.info['name'].lower() and 'video_server.py' in cmdline:
                    logger.info(f"Found server process: PID={proc.pid}")
                    return proc
            except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess) as e:
                continue
        return None
    except Exception as e:
        logger.error(f"Error in get_server_process: {str(e)}")
        return None

def log_api_request(response, start_time):
    """记录API请求日志"""
    duration = round((time.time() - start_time) * 1000, 2)
    # 根据状态码选择日志级别
    if response.status_code < 400:
        logger.info(
            f"API Request - {response.request.method} {response.request.url} - "
            f"Status: {response.status_code} - "
            f"Duration: {duration}ms"
        )
    else:
        logger.error(
            f"API Request - {response.request.method} {response.request.url} - "
            f"Status: {response.status_code} - "
            f"Duration: {duration}ms - "
            f"Response: {response.text[:200]}"  # 错误时才记录响应内容
        )

def check_server_health():
    """检查服务器健康状态"""
    try:
        start_time = time.time()
        response = requests.get('http://localhost:5015/health', timeout=5)
        # 修改日志记录方式
        duration = round((time.time() - start_time) * 1000, 2)
        logger.info(
            f"Health check - Status: {response.status_code} - "
            f"Duration: {duration}ms"
        )
        return response.status_code == 200
    except Exception as e:
        logger.error(f"Health check request failed: {str(e)}")
        return False

def log_pipe_reader(pipe, log_level):
    """读取管道并写入日志"""
    try:
        while True:
            try:
                line = pipe.readline()
                if not line:  # 如果没有更多数据，退出循环
                    break

                # 如果line是bytes类型，尝试解码
                if isinstance(line, bytes):
                    try:
                        line = line.decode('utf-8')
                    except UnicodeDecodeError:
                        try:
                            line = line.decode('gbk')
                        except UnicodeDecodeError:
                            try:
                                line = line.decode('latin-1')
                            except UnicodeDecodeError:
                                # 如果所有编码都失败，使用错误处理
                                line = line.decode('utf-8', errors='replace')

                line = line.strip()
                if line:
                    # 避免重复记录已经格式化的日志信息
                    if line.startswith(('INFO:', 'WARNING:', 'ERROR:', 'DEBUG:', 'CRITICAL:')):
                        continue

                    # 检查是否包含 HTTP 状态码
                    if ' 304 ' in line:  # 匹配 304 状态码
                        logger.info(f"VideoServer: {line}")
                    elif ' 4' in line or ' 5' in line:  # 匹配 4xx 或 5xx 状态码
                        logger.error(f"VideoServer: {line}")
                    else:
                        # 使用传入的默认日志级别
                        if log_level == logging.ERROR:
                            logger.error(f"VideoServer: {line}")
                        else:
                            logger.info(f"VideoServer: {line}")
            except UnicodeDecodeError as e:
                logger.warning(f"Encoding error in log_pipe_reader: {str(e)}")
                continue
            except Exception as e:
                logger.error(f"Error reading line in log_pipe_reader: {str(e)}")
                continue
    except Exception as e:
        logger.error(f"Error in log_pipe_reader: {str(e)}")
    finally:
        try:
            pipe.close()
        except Exception:
            pass  # 忽略关闭管道时的错误

    
def restart_server():
    """重启服务器"""
    logger.info("Attempting to restart server...")

    try:
        # 检查并清理端口，增加重试机制
        max_port_clear_attempts = 3
        for attempt in range(max_port_clear_attempts):
            if is_port_in_use(5015):
                logger.warning(f'Port 5015 is in use, attempting to kill existing process (attempt {attempt + 1}/{max_port_clear_attempts})')
                if kill_process_on_port(5015):
                    logger.info("Successfully killed processes on port 5015")
                    time.sleep(2)
                    # 再次检查端口是否真的释放了
                    if not is_port_in_use(5015):
                        break
                    else:
                        logger.warning("Port 5015 still in use after killing processes")
                else:
                    logger.warning(f"Failed to kill processes on port 5015 (attempt {attempt + 1})")

                if attempt < max_port_clear_attempts - 1:
                    time.sleep(3)  # 等待更长时间再重试
            else:
                logger.info("Port 5015 is free")
                break
        else:
            # 如果所有尝试都失败了
            if is_port_in_use(5015):
                logger.error('Failed to free port 5015 after all attempts, trying to start anyway')
                # 不直接返回，尝试启动看看是否能成功

        # 设置环境变量
        os.environ['FLASK_ENV'] = 'production'

        # 检查video_server路径
        video_server_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'video_server.py')
        if not os.path.exists(video_server_path):
            logger.error(f"Video server script not found at: {video_server_path}")
            return False

        logger.info(f"Starting video server from: {video_server_path}")

        # 启动进程并捕获输出
        process = subprocess.Popen(
            [sys.executable, video_server_path],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            universal_newlines=True,
            encoding='utf-8',
            errors='replace',  # 处理编码错误
            bufsize=1  # 行缓冲
        )

        # 创建独立线程来读取输出
        stdout_thread = threading.Thread(
            target=log_pipe_reader,
            args=(process.stdout, logging.INFO),
            daemon=True
        )
        stderr_thread = threading.Thread(
            target=log_pipe_reader,
            args=(process.stderr, logging.ERROR),
            daemon=True
        )

        stdout_thread.start()
        stderr_thread.start()

        # 等待服务器启动，增加进度检查
        logger.info("Waiting for server to start...")
        for i in range(10):  # 最多等待10秒
            time.sleep(1)
            if process.poll() is not None:
                logger.error(f"Server process exited early with code: {process.poll()}")
                return False

            # 每2秒检查一次健康状态
            if i >= 2 and i % 2 == 0:
                if check_server_health():
                    logger.info(f"Server started successfully after {i+1} seconds")
                    return True

        # 最终健康检查
        if check_server_health():
            logger.info("Server restarted successfully")
            return True
        else:
            logger.error("Server failed to start properly - health check failed")
            try:
                process.terminate()
                process.wait(timeout=5)
            except:
                process.kill()
            return False

    except Exception as e:
        logger.error(f"Error in restart_server: {str(e)}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        return False

def is_port_in_use_netstat(port):
    """使用netstat命令检查端口是否被占用（备用方案）"""
    try:
        import subprocess
        result = subprocess.run(['netstat', '-an'], capture_output=True, text=True, encoding='utf-8', errors='replace', timeout=10)
        if result.returncode == 0:
            lines = result.stdout.split('\n')
            for line in lines:
                if f':{port} ' in line and ('LISTENING' in line or 'ESTABLISHED' in line):
                    logger.debug(f"Port {port} found in netstat output: {line.strip()}")
                    return True
        return False
    except Exception as e:
        logger.warning(f"Error using netstat to check port {port}: {e}")
        return False

def is_port_in_use(port):
    """检查端口是否被占用"""
    try:
        connections = psutil.net_connections()
        for conn in connections:
            if conn.laddr.port == port and conn.status in ['LISTEN', 'ESTABLISHED']:
                # 检查进程是否有效
                if conn.pid is not None and conn.pid > 0:
                    try:
                        process = psutil.Process(conn.pid)
                        if process.is_running():
                            logger.debug(f"Port {port} is in use by process {conn.pid} ({process.name()})")
                            return True
                    except (psutil.NoSuchProcess, psutil.AccessDenied):
                        # 进程不存在或无权限访问，可能是僵尸连接
                        continue
                else:
                    # PID为None或0，可能是系统连接
                    logger.debug(f"Port {port} has connection with invalid PID: {conn.pid}")
                    return True

        # 如果psutil没有找到，使用netstat作为备用检查
        netstat_result = is_port_in_use_netstat(port)
        if netstat_result:
            logger.info(f"Port {port} detected as in use by netstat (psutil missed it)")
            return True

        return False
    except Exception as e:
        logger.error(f"Error checking port {port} with psutil: {e}")
        # 发生错误时，尝试使用netstat
        logger.info(f"Falling back to netstat for port {port} check")
        return is_port_in_use_netstat(port)

def kill_process_on_port(port):
    """终止占用指定端口的进程"""
    killed_any = False
    try:
        connections = psutil.net_connections()
        for conn in connections:
            if conn.laddr.port == port and conn.pid is not None and conn.pid > 0:
                try:
                    process = psutil.Process(conn.pid)
                    # 检查进程是否存在且可访问
                    if not process.is_running():
                        continue

                    # 检查是否有权限访问进程
                    try:
                        process.name()  # 测试访问权限
                    except psutil.AccessDenied:
                        logger.warning(f"Access denied to process {conn.pid} on port {port}, skipping")
                        continue

                    logger.info(f"Killing process {process.pid} ({process.name()}) on port {port}")

                    # 首先尝试优雅终止
                    process.terminate()
                    try:
                        process.wait(timeout=3)
                        killed_any = True
                        logger.info(f"Process {conn.pid} terminated gracefully")
                    except psutil.TimeoutExpired:
                        # 如果优雅终止失败，强制杀死
                        logger.warning(f"Process {conn.pid} did not terminate gracefully, forcing kill")
                        process.kill()
                        process.wait(timeout=2)
                        killed_any = True
                        logger.info(f"Process {conn.pid} killed forcefully")

                except (psutil.NoSuchProcess, psutil.ZombieProcess):
                    # 进程已经不存在，这是正常的
                    logger.info(f"Process {conn.pid} no longer exists")
                    killed_any = True
                except psutil.AccessDenied as e:
                    logger.warning(f"Access denied when trying to kill process {conn.pid}: {e}")
                except Exception as e:
                    logger.error(f"Error killing process {conn.pid}: {e}")

    except Exception as e:
        logger.error(f"Error getting connections for port {port}: {e}")

    # 再次检查端口是否已释放
    if killed_any:
        time.sleep(1)  # 给系统一点时间释放端口
        if not is_port_in_use(port):
            logger.info(f"Port {port} successfully freed")
            return True
        else:
            logger.warning(f"Port {port} still in use after killing processes")

    return killed_any

def main():
    logger.info("Starting server monitor")

    # 清理旧日志
    cleanup_old_logs()

    # 首次启动时确保端口未被占用
    logger.info("Initial port cleanup...")
    kill_process_on_port(5015)
    time.sleep(2)

    # 启动服务器
    logger.info("Initial server startup...")
    if not restart_server():
        logger.error("Failed to start server initially, will keep trying...")

    check_interval = 30  # 检查间隔（秒）
    max_retries = 3     # 最大重试次数
    retry_delay = 10    # 重试间隔（秒）
    failure_count = 0   # 失败计数器
    log_cleanup_counter = 0  # 日志清理计数器
    restart_failure_count = 0  # 重启失败计数器
    max_restart_failures = 5  # 最大重启失败次数

    while True:
        try:
            proc = get_server_process()
            server_running = proc is not None and proc.is_running()
            health_check_passed = check_server_health()

            if not server_running or not health_check_passed:
                failure_count += 1
                logger.warning(f"Server check failed ({failure_count}/{max_retries})")

                if failure_count >= max_retries:
                    logger.error(f"Server failed {max_retries} consecutive checks, attempting restart")

                    # 尝试重启服务器
                    restart_success = restart_server()
                    if restart_success:
                        logger.info("Server restart successful")
                        failure_count = 0  # 重置失败计数
                        restart_failure_count = 0  # 重置重启失败计数
                    else:
                        restart_failure_count += 1
                        logger.error(f"Server restart failed ({restart_failure_count}/{max_restart_failures})")

                        if restart_failure_count >= max_restart_failures:
                            logger.critical(f"Server restart failed {max_restart_failures} times, waiting longer before next attempt")
                            time.sleep(60)  # 等待1分钟再重试
                            restart_failure_count = 0  # 重置计数器

                        failure_count = 0  # 重置检查失败计数，避免立即再次重启
                else:
                    logger.info(f"Waiting {retry_delay} seconds before next check")
                    time.sleep(retry_delay)
                    continue  # 跳过正常的检查间隔，直接进行下一次检查
            else:
                if failure_count > 0:
                    logger.info("Server recovered without restart")
                failure_count = 0  # 重置失败计数
                restart_failure_count = 0  # 重置重启失败计数
                logger.info("Server is running normally")

            # 每24小时清理一次日志（基于检查间隔）
            log_cleanup_counter += 1
            if log_cleanup_counter >= (24 * 60 * 60) // check_interval:
                cleanup_old_logs()
                log_cleanup_counter = 0

            time.sleep(check_interval)

        except KeyboardInterrupt:
            logger.info("Monitor stopped by user")
            break
        except Exception as e:
            logger.error(f"Error in main loop: {str(e)}")
            time.sleep(check_interval)

if __name__ == '__main__':
    try:
        main()
    except KeyboardInterrupt:
        logger.info("Monitor stopped by user")
    except Exception as e:
        logger.error(f"Fatal error: {str(e)}")
        sys.exit(1) 