<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <title>二维运动电荷的电场线模拟</title>
  <style>
    body { background: #222; color: #fff; font-family: sans-serif; }
    #controls { margin: 10px 0; }
    canvas { background: #111; display: block; margin: 0 auto; border: 1px solid #444; }
    label, select { font-size: 1.1em; }
  </style>
</head>
<body>
  <h2 style="text-align:center">二维运动电荷的电场线模拟</h2>
  <div style="display:flex;flex-direction:row;justify-content:center;align-items:flex-start;gap:32px;width:fit-content;max-width:100vw;margin:0 auto 18px auto;">
    <div style="display:flex;flex-direction:column;align-items:flex-start;">
      <canvas id="fieldCanvas" width="900" height="600" style="margin-left:0;display:block;"></canvas>
      <canvas id="waveCanvas" width="900" height="120" style="display:block;margin-top:12px;"></canvas>
    </div>
    <div id="controls" style="display:flex;flex-direction:column;align-items:flex-start;gap:26px;background:#f7f7f7;border-radius:14px;padding:28px 22px 24px 22px;box-shadow:0 2px 12px rgba(0,0,0,0.06);min-width:260px;max-width:340px;font-family:'Segoe UI',Arial,sans-serif;font-size:1.13em;letter-spacing:0.01em;line-height:1.7;">
      <div style="display:flex;align-items:center;gap:10px;width:100%;">
        <label for="mode" style="font-weight:600;color:#222;">运动模式</label>
        <select id="mode" style="padding:6px 16px;border-radius:8px;border:1px solid #bbb;background:#fff;font-size:1em;color:#222;">
          <option value="linear">直线运动</option>
          <option value="oscillate">简谐运动</option>
        </select>
      </div>
      <div id="linearParams" style="display:inline-flex;flex-direction:column;align-items:flex-start;gap:12px;width:100%;">
        <div style="display:flex;align-items:center;gap:6px;width:100%;">
          <label style="font-weight:600;color:#222;">初始速度</label>
          <input type="number" id="vInput" min="-5" max="5" step="0.01" value="0.1" style="width:60px;padding:4px 6px;border-radius:6px;border:1px solid #bbb;background:#fff;color:#222;">
          <input type="range" id="vSlider" min="-5" max="5" step="0.01" value="0.1" style="width:110px;">
        </div>
        <div style="display:flex;align-items:center;gap:6px;width:100%;">
          <label style="font-weight:600;color:#222;">加速度</label>
          <input type="number" id="aInput" min="-0.1" max="0.1" step="0.001" value="0" style="width:60px;padding:4px 6px;border-radius:6px;border:1px solid #bbb;background:#fff;color:#222;">
          <input type="range" id="aSlider" min="-0.1" max="0.1" step="0.001" value="0" style="width:110px;">
        </div>
      </div>
      <div id="oscParams" style="display:none;flex-direction:column;align-items:flex-start;gap:12px;width:100%;">
        <div style="display:flex;align-items:center;gap:6px;width:100%;">
          <label style="font-weight:600;color:#222;">频率</label>
          <input type="number" id="omegaInput" min="0.001" max="0.1" step="0.001" value="0.001" style="width:60px;padding:4px 6px;border-radius:6px;border:1px solid #bbb;background:#fff;color:#222;">
          <input type="range" id="omegaSlider" min="0.001" max="0.1" step="0.001" value="0.001" style="width:110px;">
        </div>
        <div style="display:flex;align-items:center;gap:6px;width:100%;">
          <label style="font-weight:600;color:#222;">振幅</label>
          <input type="number" id="ampInput" min="10" max="400" step="1" value="320" style="width:60px;padding:4px 6px;border-radius:6px;border:1px solid #bbb;background:#fff;color:#222;">
          <input type="range" id="ampSlider" min="10" max="400" step="1" value="320" style="width:110px;">
        </div>
      </div>
      <label for="displayMode" style="font-weight:600;color:#222;margin-top:8px;">显示模式</label>
      <select id="displayMode" style="padding:6px 16px;border-radius:8px;border:1px solid #bbb;background:#fff;font-size:1em;color:#222;margin-top:2px;">
        <option value="lines">电场线 Field Lines</option>
        <option value="wavefront">波前模式 Wavefront</option>
      </select>
      <div style="display:flex;align-items:center;gap:10px;width:100%;margin-top:8px;">
        <label for="cSimSlider" style="font-weight:600;color:#222;">波前速度</label>
        <input type="number" id="cSimInput" min="30" max="400" step="1" value="120" style="width:60px;padding:4px 6px;border-radius:6px;border:1px solid #bbb;background:#fff;color:#222;">
        <input type="range" id="cSimSlider" min="30" max="400" step="1" value="120" style="width:110px;">
      </div>
    </div>
  </div>
  <script>
    // 物理常数
    const k = 8.9875517873681764e9; // 静电力常数
    const q = 1e-6; // 电荷量 (库仑)
    const c = 3e8; // 光速 (m/s)，但动画中用缩放
    
    // 画布和参数
    const canvas = document.getElementById('fieldCanvas');
    const ctx = canvas.getContext('2d');
    const W = canvas.width, H = canvas.height;
    const centerY = H/2;
    
    // 波形图画布
    const waveCanvas = document.getElementById('waveCanvas');
    const wctx = waveCanvas.getContext('2d');
    const WW = waveCanvas.width, WH = waveCanvas.height;
    // 选中点，初始为画布右侧中部
    let probe = {x: W-80, y: centerY};
    // 电场强度历史
    let E_history = [];
    const MAX_HISTORY = WW;
    
    // 电荷运动参数
    let mode = 'linear';
    let displayMode = 'lines';
    let t = 0;
    let charge = {
      x: 0,
      y: centerY,
      v: 2, // px/frame
      a: 0.08, // px/frame^2 (加速度减小)
      omega: 0.08, // rad/frame (振荡更快)
      amplitude: 320 // px (振荡幅度更大)
    };
    // 电荷历史记录（用于波前模式下的延迟采样）
    let chargeHistory = [];
    
    // 波前模式相关参数
    let wavefronts = [];
    let lastWavefrontTime = 0;
    const wavefrontInterval = 100; // ms, 0.1s
    const wavefrontTolerance = 0.08; // 8% 容差，波前更宽
    const minWavefronts = 5;
    let c_sim = 120; // 传播速度（像素/帧），可调
    
    document.getElementById('mode').addEventListener('change', e => {
      mode = e.target.value;
      // 控制参数UI显示
      document.getElementById('linearParams').style.display = (mode === 'linear') ? '' : 'none';
      document.getElementById('oscParams').style.display = (mode === 'oscillate') ? '' : 'none';
      resetCharge();
    });
    
    document.getElementById('displayMode').addEventListener('change', e => {
      displayMode = e.target.value;
      if (displayMode === 'wavefront') {
        // 切换到波前模式时立即采样minWavefronts个波前
        wavefronts = [];
        let now = performance.now();
        for (let i = minWavefronts - 1; i >= 0; i--) {
          let t0 = now - i * wavefrontInterval;
          let ax = charge.x;
          let ay = charge.y - 22;
          let E = getEFieldAt(ax, ay);
          // 计算加速度方向
          let acc_x = (mode === 'linear') ? charge.a : -charge.amplitude * charge.omega * charge.omega * Math.sin(charge.omega * t);
          let acc_y = 0;
          // 半径矢量
          let rx = ax - charge.x;
          let ry = ay - charge.y;
          let r = Math.sqrt(rx*rx + ry*ry);
          let a = Math.sqrt(acc_x*acc_x + acc_y*acc_y);
          // 2D叉积
          let cross = acc_x * ry - acc_y * rx;
          let sin_theta = (r > 1e-6 && a > 1e-6) ? Math.abs(cross) / (a * r) : 0;
          let Ea = Math.sqrt(E.ex_rad*E.ex_rad + E.ey_rad*E.ey_rad) * sin_theta;
          wavefronts.push({Ea, t0: t0, x0: charge.x, y0: charge.y});
        }
        lastWavefrontTime = now;
      }
    });
    
    function resetCharge() {
      t = 0;
      charge.x = 0;
      charge.y = centerY;
      if (mode === 'linear') {
        charge.v = parseFloat(document.getElementById('vSlider').value);
        charge.a = parseFloat(document.getElementById('aSlider').value);
      } else if (mode === 'oscillate') {
        charge.omega = parseFloat(document.getElementById('omegaSlider').value);
        charge.amplitude = parseFloat(document.getElementById('ampSlider').value);
      }
      // 清空波前
      wavefronts = [];
    }
    
    // 计算电荷位置和速度
    function updateCharge() {
      t += 1;
      if (mode === 'linear') {
        charge.x += charge.v;
        charge.v += charge.a;
        // 超出右端重置
        if (charge.x > W + 20 || charge.x < -20) {
          resetCharge();
        }
      } else if (mode === 'oscillate') {
        charge.x = W/2 + charge.amplitude * Math.sin(charge.omega * t);
      }
    }
    
    // Calculate the electric field at (x, y) using Liénard–Wiechert formula (近似)
    // 使用Liénard–Wiechert公式近似计算运动电荷的电场，包括辐射场分量
    function getEFieldAt(x, y) {
      // 电荷当前位置
      let rx = x - charge.x;
      let ry = y - charge.y;
      let r2 = rx*rx + ry*ry;
      let r = Math.sqrt(r2);
      if (r < 15) return {ex: 0, ey: 0, ex_rad: 0, ey_rad: 0}; // Avoid singularity 避免奇点

      // 速度和加速度（单位：像素/帧）
      let vx = 0, vy = 0, ax = 0, ay = 0;
      switch(mode) {
        case 'linear':
          vx = charge.v; vy = 0;
          ax = charge.a; ay = 0;
          break;
        case 'oscillate':
          vx = charge.amplitude * charge.omega * Math.cos(charge.omega * t);
          vy = 0;
          ax = -charge.amplitude * charge.omega * charge.omega * Math.sin(charge.omega * t);
          ay = 0;
          break;
      }
      // Convert to "simulation units" (not real SI, for visualization)
      // 换算为"模拟单位"，非真实SI，仅用于可视化
      let beta_x = vx / c_sim, beta_y = vy / c_sim;
      let beta2 = beta_x*beta_x + beta_y*beta_y;
      let n_x = rx/r, n_y = ry/r; // 观察方向 unit vector
      let n_dot_beta = n_x*beta_x + n_y*beta_y;
      // 静电场+运动修正（近区）
      let E_static = k*q/r2 / Math.pow(1 - beta2 - n_dot_beta*n_dot_beta, 1.5);
      let ex_static = E_static * n_x;
      let ey_static = E_static * n_y;
      // 辐射场分量（远区，横向于加速度方向）
      // E_rad ∝ [n × ((n - β) × β_dot)] / (1 - n·β)^3 / r
      let beta_dot_x = ax / c_sim, beta_dot_y = ay / c_sim;
      // (n - beta)
      let nmb_x = n_x - beta_x;
      let nmb_y = n_y - beta_y;
      // (n - beta) × beta_dot (only z component, 2D)
      let cross1 = nmb_x * beta_dot_y - nmb_y * beta_dot_x;
      // n × [result] (again, only z, so back to x/y)
      // In 2D, the cross product with n gives:
      let ex_rad =  cross1 * (-n_y); // x component
      let ey_rad =  cross1 * ( n_x); // y component
      // Normalize and scale
      let denom = Math.pow(1 - n_dot_beta, 3) * r;
      let radBoost = 12; // 放大系数，增强加速度对电场线的影响
      let E_rad = k*q/(c_sim*c_sim) * 12000 * radBoost;
      ex_rad *= E_rad / denom;
      ey_rad *= E_rad / denom;
      // Total field
      return {
        ex: ex_static + ex_rad,
        ey: ey_static + ey_rad,
        ex_rad,
        ey_rad
      };
    }
    
    // 绘制电场线
    function drawFieldLines() {
      const nLines = 16;
      for (let i = 0; i < nLines; i++) {
        let angle = (2*Math.PI/nLines)*i;
        let x = charge.x + 18*Math.cos(angle);
        let y = charge.y + 18*Math.sin(angle);
        drawFieldLine(x, y, angle);
      }
    }
    // 沿电场方向积分画线
    function drawFieldLine(x, y, angle) {
      ctx.beginPath();
      ctx.moveTo(x, y);
      let steps = 180;
      for (let i = 0; i < steps; i++) {
        let {ex, ey} = getEFieldAt(x, y);
        let E = Math.sqrt(ex*ex + ey*ey);
        if (E < 1e-2) break;
        ex /= E; ey /= E;
        x += ex*7; y += ey*7;
        ctx.lineTo(x, y);
        if (x < 0 || x > W || y < 0 || y > H) break;
      }
      ctx.strokeStyle = 'rgba(255,255,0,0.7)';
      ctx.lineWidth = 1.2;
      ctx.stroke();
    }
    
    // 绘制电荷
    function drawCharge() {
      ctx.beginPath();
      ctx.arc(charge.x, charge.y, 12, 0, 2*Math.PI);
      ctx.fillStyle = '#ff3333';
      ctx.shadowColor = '#fff';
      ctx.shadowBlur = 16;
      ctx.fill();
      ctx.shadowBlur = 0;
      ctx.strokeStyle = '#fff';
      ctx.lineWidth = 2;
      ctx.stroke();
    }
    
    // 点击主画布选点
    canvas.addEventListener('click', function(e) {
      const rect = canvas.getBoundingClientRect();
      probe.x = e.clientX - rect.left;
      probe.y = e.clientY - rect.top;
      E_history = [];
    });
    
    // 主动画循环
    function animate() {
      ctx.clearRect(0, 0, W, H);
      updateCharge();
      // 记录当前电荷状态
      chargeHistory.push({
        t: t,
        x: charge.x,
        y: charge.y,
        v: mode === 'linear' ? charge.v : charge.amplitude * charge.omega * Math.cos(charge.omega * t),
        a: mode === 'linear' ? charge.a : -charge.amplitude * charge.omega * charge.omega * Math.sin(charge.omega * t),
        omega: charge.omega,
        amplitude: charge.amplitude,
        mode: mode
      });
      if (chargeHistory.length > 2000) chargeHistory.shift();
      if (displayMode === 'lines') {
        drawFieldLines();
        drawCharge();
      } else if (displayMode === 'wavefront') {
        drawWavefronts();
        drawCharge();
      }
      // 标记选中点
      ctx.save();
      ctx.beginPath();
      ctx.arc(probe.x, probe.y, 6, 0, 2*Math.PI);
      ctx.fillStyle = '#00eaff';
      ctx.shadowColor = '#fff';
      ctx.shadowBlur = 8;
      ctx.fill();
      ctx.shadowBlur = 0;
      ctx.strokeStyle = '#fff';
      ctx.lineWidth = 1.2;
      ctx.stroke();
      ctx.restore();
      // 计算并记录该点电场强度
      let Eabs;
      if (displayMode === 'wavefront') {
        // 波前模式下，直接用当前电荷状态计算电场强度（不考虑延迟）
        let E = getEFieldAt(probe.x, probe.y);
        Eabs = Math.sqrt(E.ex*E.ex + E.ey*E.ey);
      } else {
        let E = getEFieldAt(probe.x, probe.y);
        Eabs = Math.sqrt(E.ex*E.ex + E.ey*E.ey);
      }
      E_history.push(Eabs);
      if (E_history.length > MAX_HISTORY) E_history.shift();
      drawWaveform();
      requestAnimationFrame(animate);
    }
    animate();
    
    // 绘制波形图
    function drawWaveform() {
      wctx.clearRect(0, 0, WW, WH);
      // 坐标轴
      wctx.strokeStyle = '#888';
      wctx.lineWidth = 1;
      wctx.beginPath();
      wctx.moveTo(0, WH/2);
      wctx.lineTo(WW, WH/2);
      wctx.stroke();
      // 曲线
      if (E_history.length < 2) return;
      // 自动缩放Y轴
      let maxE = Math.max(...E_history);
      let minE = Math.min(...E_history);
      let range = maxE - minE;
      let y0 = WH-10;
      let y1 = 10;
      wctx.beginPath();
      for (let i = 0; i < E_history.length; i++) {
        let y = WH - 10 - ((E_history[i] - minE) / (range === 0 ? 1 : range)) * (WH-20);
        if (i === 0) wctx.moveTo(i, y);
        else wctx.lineTo(i, y);
      }
      wctx.strokeStyle = '#00eaff';
      wctx.lineWidth = 2;
      wctx.stroke();
      // 标注
      wctx.fillStyle = '#fff';
      wctx.font = '14px sans-serif';
      wctx.fillText('E-field at selected point 电场强度', 10, 18);
      wctx.fillText('Max: ' + maxE.toExponential(2), 10, WH-8);
      wctx.fillText('Min: ' + minE.toExponential(2), 120, WH-8);
    }
    
    // 波前模式主绘制函数
    function drawWavefronts() {
      // 背景填充为蓝色
      ctx.fillStyle = 'rgb(10,40,120)';
      ctx.fillRect(0, 0, W, H);
      // 当前时间
      let now = performance.now();
      // 每0.1s采样一次电荷附近点a的电场强度
      if (wavefronts.length < minWavefronts || now - lastWavefrontTime > wavefrontInterval) {
        // 取电荷当前位置正上方22像素处为a点，记录波前产生时电荷位置
        let ax = charge.x;
        let ay = charge.y - 22;
        let E = getEFieldAt(ax, ay);
        // 计算加速度方向
        let acc_x = (mode === 'linear') ? charge.a : -charge.amplitude * charge.omega * charge.omega * Math.sin(charge.omega * t);
        let acc_y = 0;
        // 半径矢量
        let rx = ax - charge.x;
        let ry = ay - charge.y;
        let r = Math.sqrt(rx*rx + ry*ry);
        let a = Math.sqrt(acc_x*acc_x + acc_y*acc_y);
        // 2D叉积
        let cross = acc_x * ry - acc_y * rx;
        let sin_theta = (r > 1e-6 && a > 1e-6) ? Math.abs(cross) / (a * r) : 0;
        let Ea = Math.sqrt(E.ex_rad*E.ex_rad + E.ey_rad*E.ey_rad) * sin_theta;
        if (Ea > 0) { // 只采样有效波前
          wavefronts.push({Ea, t0: now, x0: charge.x, y0: charge.y});
        }
        lastWavefrontTime = now;
      }
      // 只保留画布内的波前
      wavefronts = wavefronts.filter(wf => (now - wf.t0) * c_sim / 1000 < Math.max(W, H)*1.5);
      // 计算最大Ea用于归一化颜色
      let maxEa = Math.max(...wavefronts.map(wf => wf.Ea), 1e-8); // 防止除0
      // 对每个波前，遍历画布，找出|E|≈Ea的点
      // 现在用分段绘制
      for (const wf of wavefronts) {
        let age = (now - wf.t0) / 1000; // 秒
        let radius = age * c_sim; // 传播距离
        if (!isFinite(wf.Ea) || wf.Ea <= 0) continue;
        if (radius < 0 || radius > Math.max(W, H)*1.2) continue;
        // 角度分段绘制
        let N = 120;
        // 计算加速度方向
        let acc_x = (mode === 'linear') ? charge.a : -charge.amplitude * charge.omega * charge.omega * Math.sin(charge.omega * t);
        let acc_y = 0;
        let a = Math.sqrt(acc_x*acc_x + acc_y*acc_y);
        let maxR = Math.max(W, H) * 1.2;
        let distNorm = Math.min(1, radius / maxR); // 0~1，传播距离归一化
        let alpha = 0.8 * (1 - distNorm); // 传播远了更透明
        for (let i = 0; i < N; i++) {
          let theta1 = (i / N) * 2 * Math.PI;
          let theta2 = ((i+1) / N) * 2 * Math.PI;
          // 半径方向
          let dx = Math.cos(theta1), dy = Math.sin(theta1);
          // sinθ = |a × r| / (|a||r|) = |acc_x * dy - acc_y * dx| / |a|
          let sin_theta = (a > 1e-6) ? Math.abs(acc_x * dy - acc_y * dx) / a : 0;
          // 颜色归一化，Ea已包含sinθ，这里可直接用sinθ或sinθ*wf.Ea/maxEa
          let norm = sin_theta * (wf.Ea / maxEa); // 0~1
          let color = getHeatColor(norm * (1 - distNorm));
          ctx.save();
          ctx.beginPath();
          ctx.arc(wf.x0, wf.y0, radius, theta1, theta2);
          ctx.lineWidth = 12;
          ctx.strokeStyle = color.replace('rgb', 'rgba').replace(')', `,${alpha})`);
          ctx.shadowColor = color;
          ctx.shadowBlur = 16;
          ctx.stroke();
          ctx.restore();
        }
      }
    }
    
    // 颜色映射函数：0~1 -> 蓝-绿-黄-红
    function getHeatColor(t) {
      // t: 0~1
      let r=0,g=0,b=0;
      if (t < 0.25) { // blue->cyan->green
        r = 0;
        g = t*4*255;
        b = 255;
      } else if (t < 0.5) { // green->yellow
        r = (t-0.25)*4*255;
        g = 255;
        b = 255 - (t-0.25)*4*255;
      } else if (t < 0.75) { // yellow->orange
        r = 255;
        g = 255 - (t-0.5)*4*128;
        b = 0;
      } else { // orange->red
        r = 255;
        g = 127 - (t-0.75)*4*127;
        b = 0;
      }
      return `rgb(${Math.round(r)},${Math.round(g)},${Math.round(b)})`;
    }
    
    // 滑块与输入框联动
    // 直线运动
    const vSlider = document.getElementById('vSlider');
    const vInput = document.getElementById('vInput');
    vSlider.addEventListener('input', e => {
      vInput.value = e.target.value;
      if (mode === 'linear') resetCharge();
    });
    vInput.addEventListener('input', e => {
      vSlider.value = e.target.value;
      if (mode === 'linear') resetCharge();
    });
    const aSlider = document.getElementById('aSlider');
    const aInput = document.getElementById('aInput');
    aSlider.addEventListener('input', e => {
      aInput.value = e.target.value;
      if (mode === 'linear') resetCharge();
    });
    aInput.addEventListener('input', e => {
      aSlider.value = e.target.value;
      if (mode === 'linear') resetCharge();
    });
    // 简谐运动
    const omegaSlider = document.getElementById('omegaSlider');
    const omegaInput = document.getElementById('omegaInput');
    omegaSlider.addEventListener('input', e => {
      omegaInput.value = e.target.value;
      if (mode === 'oscillate') resetCharge();
    });
    omegaInput.addEventListener('input', e => {
      omegaSlider.value = e.target.value;
      if (mode === 'oscillate') resetCharge();
    });
    const ampSlider = document.getElementById('ampSlider');
    const ampInput = document.getElementById('ampInput');
    ampSlider.addEventListener('input', e => {
      ampInput.value = e.target.value;
      if (mode === 'oscillate') resetCharge();
    });
    ampInput.addEventListener('input', e => {
      ampSlider.value = e.target.value;
      if (mode === 'oscillate') resetCharge();
    });
    // 波前速度滑块与输入框联动
    const cSimSlider = document.getElementById('cSimSlider');
    const cSimInput = document.getElementById('cSimInput');
    cSimSlider.addEventListener('input', e => {
      cSimInput.value = e.target.value;
      c_sim = parseFloat(e.target.value);
    });
    cSimInput.addEventListener('input', e => {
      cSimSlider.value = e.target.value;
      c_sim = parseFloat(e.target.value);
    });
    // 用指定电荷状态计算电场
    function getEFieldAtWithState(x, y, state) {
      let rx = x - state.x;
      let ry = y - state.y;
      let r2 = rx*rx + ry*ry;
      let r = Math.sqrt(r2);
      if (r < 15) return {ex: 0, ey: 0, ex_rad: 0, ey_rad: 0};
      let vx = 0, vy = 0, ax = 0, ay = 0;
      switch(state.mode) {
        case 'linear':
          vx = state.v; vy = 0;
          ax = state.a; ay = 0;
          break;
        case 'oscillate':
          vx = state.amplitude * state.omega * Math.cos(state.omega * state.t);
          vy = 0;
          ax = -state.amplitude * state.omega * state.omega * Math.sin(state.omega * state.t);
          ay = 0;
          break;
      }
      let beta_x = vx / c_sim, beta_y = vy / c_sim;
      let beta2 = beta_x*beta_x + beta_y*beta_y;
      let n_x = rx/r, n_y = ry/r;
      let n_dot_beta = n_x*beta_x + n_y*beta_y;
      let E_static = k*q/r2 / Math.pow(1 - beta2 - n_dot_beta*n_dot_beta, 1.5);
      let ex_static = E_static * n_x;
      let ey_static = E_static * n_y;
      let beta_dot_x = ax / c_sim, beta_dot_y = ay / c_sim;
      let nmb_x = n_x - beta_x;
      let nmb_y = n_y - beta_y;
      let cross1 = nmb_x * beta_dot_y - nmb_y * beta_dot_x;
      let ex_rad =  cross1 * (-n_y);
      let ey_rad =  cross1 * ( n_x);
      let denom = Math.pow(1 - n_dot_beta, 3) * r;
      let radBoost = 12;
      let E_rad = k*q/(c_sim*c_sim) * 12000 * radBoost;
      ex_rad *= E_rad / denom;
      ey_rad *= E_rad / denom;
      return {
        ex: ex_static + ex_rad,
        ey: ey_static + ey_rad,
        ex_rad,
        ey_rad
      };
    }
  </script>
</body>
</html> 