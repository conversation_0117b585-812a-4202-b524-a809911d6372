# AI 题目生成器

## 概述

基于 `video_player.html` 中自动问题生成的整套方法，创建了一个独立的HTML应用 `gen_questions.html`，用于生成选择题和计算题。

## 主要功能

### 1. 简化的配置界面
- **课程文件夹配置**：只需要指定课程文件夹名称
- **文件选择**：从用户的RAG文件列表中选择多个文档
- **题目数量配置**：可以分别设置选择题和计算题的数量
- **文件命名**：支持时间戳自动命名和自定义命名

### 2. 用户认证
- 从URL参数中获取用户信息（user, key, token）
- 自动验证用户身份，确保安全访问
- 支持现有的用户认证体系

### 3. 文件管理
- **文件列表获取**：自动获取用户的RAG文件列表
- **多选支持**：支持选择多个文档进行题目生成
- **文件操作**：提供刷新、全选、清空选择等功能

### 4. 题目生成
- **选择题生成**：基于选定文档生成多选题
- **计算题生成**：基于选定文档生成计算题
- **多文档支持**：可以基于多个文档的内容生成题目

### 5. 结果保存
- **自动保存**：题目生成完成后自动保存为JSON格式
- **智能保存位置**：
  - 如果原有课程目录存在，保存到 `coursevideo/课程名/questions/` 目录
  - 如果原有课程目录不存在，保存到根目录的 `gen_questions_data/` 文件夹
- **文件结构**：包含题目内容、时间戳、统计信息等

## 技术实现

### 前端 (gen_questions.html)
- **响应式设计**：适配不同屏幕尺寸
- **现代UI**：使用渐变背景、卡片式布局、动画效果
- **交互体验**：实时状态反馈、进度显示、错误处理
- **文件选择**：可视化的文件选择界面，支持多选

### 后端 API
- **`/api/gen_questions`**：题目生成接口，支持多文档ID，自动保存生成的题目
- **`/api/save_questions`**：独立的题目保存接口（可选使用）
- **`/api/ragfiles`**：获取用户文件列表接口

### 核心修改

#### 1. question_generator.py
```python
# 支持多文档ID处理
if isinstance(document_ids, str):
    document_ids = [document_ids]

# 合并多个文档的内容
all_content = []
for doc_id in document_ids:
    doc_content = get_document_chunks(...)
    if doc_content and doc_content.get('code') == 0:
        doc_text = doc_content['data']['content']
        if doc_text:
            all_content.append(doc_text)

# 合并所有文档内容
if all_content:
    content = "\n\n=== 文档分隔 ===\n\n".join(all_content)
```

#### 2. video_server.py
```python
# 处理多选文档ID
selected_doc_ids = data.get('selected_doc_ids', '')
if selected_doc_ids:
    if isinstance(selected_doc_ids, str):
        doc_ids_list = [id.strip() for id in selected_doc_ids.split(',') if id.strip()]
    else:
        doc_ids_list = selected_doc_ids
elif doc_id:
    doc_ids_list = [doc_id]
else:
    doc_ids_list = []

# 使用多文档ID生成题目
questions_data = question_generator.generate_questions(
    dataset_ids=dataset_id if isinstance(dataset_id, list) else [dataset_id],
    document_ids=doc_ids_list,
    num_questions=num_questions
)
```

## 使用方法

### 1. 访问页面
```
http://localhost:5015/api/mermindfig/gen_question/gen_questions.html?user=用户名&key=密钥&token=令牌
```

### 2. 配置参数
- 填写课程文件夹名称
- 从文件列表中选择需要的文档
- 设置选择题和计算题的数量
- 选择文件命名方式

### 3. 生成题目
- 点击"开始生成题目"按钮
- 系统会依次生成选择题和计算题
- 生成完成后自动保存到指定目录

### 4. 查看结果
- 页面会显示生成的题目内容
- 文件保存在 `gen_questions_data` 目录中
- JSON文件包含完整的题目信息和元数据

## 配置参数

### 默认配置
```javascript
const defaultConfig = {
    "folder": "en-MIT Physics II Electricity and Magnetism",
    "dataset_ids": "a3870650ffa111ef92aa2a5c03e306d6"
};
```

### URL参数
- `user`: 用户名
- `key`: 用户密钥（哈希值）
- `token`: 用户令牌

## 文件结构

```
项目根目录/
├── gen_questions_data/         # 生成的题目保存目录
│   ├── questions_20250623_212855.json
│   └── test_multi_doc_1750685335.json
└── mermindfig/gen_question/
    ├── gen_questions.html      # 主页面
    └── README.md               # 说明文档
```

## 生成的JSON格式

```json
{
  "filename": "questions_20250623_212855",
  "timestamp": "2025-06-23 21:28:55",
  "choice_questions": [
    {
      "id": "1",
      "question": "题目内容",
      "options": ["A. 选项1", "B. 选项2", "C. 选项3", "D. 选项4"],
      "correct_answer": "A",
      "explanation": "解析内容"
    }
  ],
  "calculation_questions": [
    {
      "id": "1",
      "question": "计算题内容",
      "solution_steps": [
        {
          "step_number": "1",
          "description": "步骤描述",
          "calculation": "计算过程"
        }
      ],
      "final_answer": "最终答案",
      "key_points": ["知识点1", "知识点2"]
    }
  ],
  "total_choice_count": 5,
  "total_calculation_count": 2,
  "generated_at": "2025-06-23T21:28:57.433490"
}
```

## 注意事项

1. **用户认证**：必须通过正确的URL参数提供用户认证信息
2. **文档选择**：至少需要选择一个文档才能生成题目
3. **生成时间**：题目生成可能需要较长时间，请耐心等待
4. **文件保存**：生成的文件会自动保存，无需手动操作
5. **多文档支持**：支持基于多个文档生成题目，内容会自动合并

## 测试

可以使用 `test_new_gen_questions.py` 脚本进行API测试：

```bash
python test_new_gen_questions.py
```

测试包括：
- RAG文件列表获取
- 多文档题目生成
- 题目保存功能
