<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>电场演示合集</title>
    <script src="https://cdn.plot.ly/plotly-2.9.0.min.js"></script>
    <style>
        :root {
            --primary-color: #4a90e2;
            --secondary-color: #f39c12;
            --bg-color: #f5f7fa;
            --dark-bg: #2c3e50;
            --light-text: #ecf0f1;
            --dark-text: #34495e;
            --accent-color: #e74c3c;
        }

        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            min-height: 100vh;
            color: var(--dark-text);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 50px;
        }

        .header h1 {
            font-size: 3em;
            margin: 0;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2em;
            margin: 20px 0;
            opacity: 0.9;
        }

        .demos-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-top: 40px;
        }

        .demo-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            cursor: pointer;
        }

        .demo-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.3);
        }

        .demo-card h3 {
            color: #333;
            font-size: 1.5em;
            margin: 0 0 15px 0;
            display: flex;
            align-items: center;
        }

        .demo-card .icon {
            font-size: 1.8em;
            margin-right: 10px;
        }

        .demo-card p {
            color: #666;
            line-height: 1.6;
            margin-bottom: 20px;
        }

        .demo-card .features {
            list-style: none;
            padding: 0;
            margin: 20px 0;
        }

        .demo-card .features li {
            color: #555;
            margin: 8px 0;
            padding-left: 20px;
            position: relative;
        }

        .demo-card .features li:before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #4ECDC4;
            font-weight: bold;
        }

        .demo-card .launch-btn {
            background: linear-gradient(45deg, #4ECDC4, #44A08D);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            font-size: 1em;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
        }

        .demo-card .launch-btn:hover {
            background: linear-gradient(45deg, #45B7B8, #3D8B7A);
            transform: scale(1.02);
        }

        .demo-card .launch-btn:active {
            transform: scale(0.98);
        }

        .theory-section {
            background: rgba(255,255,255,0.1);
            border-radius: 15px;
            padding: 30px;
            margin: 40px 0;
            color: white;
        }

        .theory-section h2 {
            margin-top: 0;
            color: #FFD700;
        }

        .theory-section .formula {
            background: rgba(0,0,0,0.2);
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            margin: 15px 0;
            text-align: center;
            font-size: 1.1em;
        }

        .footer {
            text-align: center;
            color: rgba(255,255,255,0.8);
            margin-top: 60px;
            padding: 20px;
            border-top: 1px solid rgba(255,255,255,0.2);
        }

        /* 演示页面样式 */
        .demo-page {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: var(--bg-color);
            z-index: 1000;
            overflow-y: auto;
        }

        .demo-page.active {
            display: block;
        }

        .demo-header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 20px;
            text-align: center;
            position: relative;
        }

        .demo-header h1 {
            margin: 0;
            font-size: 2em;
        }

        .back-btn {
            position: absolute;
            left: 20px;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(255,255,255,0.2);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1em;
            transition: background 0.3s ease;
        }

        .back-btn:hover {
            background: rgba(255,255,255,0.3);
        }

        .demo-content {
            padding: 20px;
        }

        /* 静态电场演示样式 */
        .static-demo-container {
            display: flex;
            flex-direction: row;
            width: 100%;
            max-width: 1400px;
            margin: 20px auto;
            flex-grow: 1;
            box-sizing: border-box;
        }

        #plot {
            flex: 1 1 0;
            min-width: 0;
            height: 800px;
            max-width: 1000px;
            border: 1px solid rgba(74, 144, 226, 0.3);
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            margin-right: 20px;
            overflow: auto;
            background-color: white;
            transition: box-shadow 0.3s ease;
        }

        #plot:hover {
            box-shadow: 0 6px 25px rgba(74, 144, 226, 0.15);
        }

        /* 演示页面内的控件样式 */
        .demo-page input, .demo-page select, .demo-page button {
            margin: 10px;
            padding: 12px;
            border: none;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .demo-page input, .demo-page select {
            background-color: white;
            border: 1px solid rgba(74, 144, 226, 0.2);
        }

        .demo-page input:focus, .demo-page select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.2);
        }

        .demo-page button {
            background: linear-gradient(135deg, var(--primary-color), #2980b9);
            color: white;
            cursor: pointer;
            font-weight: bold;
            letter-spacing: 0.5px;
            position: relative;
            overflow: hidden;
        }

        .demo-page button::after {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0));
            opacity: 0;
            transition: opacity 0.3s;
        }

        .demo-page button:hover::after {
            opacity: 1;
        }

        .demo-page button:active {
            transform: translateY(2px);
        }

        .demo-page button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }

        .demo-page .input-group {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            margin: 15px 0;
            position: relative;
            width: 100%;
        }

        .demo-page label {
            margin-bottom: 8px;
            font-weight: 600;
            color: var(--dark-text);
            display: flex;
            align-items: center;
        }

        .demo-page .charge-icon {
            display: inline-block;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            margin-right: 8px;
            font-size: 12px;
            text-align: center;
            line-height: 16px;
            color: white;
        }

        .demo-page .positive {
            background-color: var(--accent-color);
        }

        .demo-page .negative {
            background-color: var(--primary-color);
        }

        .demo-page input, .demo-page select {
            width: calc(100% - 24px); /* 减去左右padding和margin */
            max-width: 100%;
            box-sizing: border-box;
        }

        .demo-page .controls {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            width: 30%;
            min-width: 280px;
            height: auto;
            background-color: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        }

        .demo-page .controls-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: var(--dark-text);
            border-bottom: 2px solid var(--primary-color);
            padding-bottom: 8px;
            width: 100%;
        }

        .demo-page .info-text {
            font-size: 13px;
            color: #7f8c8d;
            margin-top: 5px;
            font-style: italic;
        }

        .demo-page .slider-container {
            display: flex;
            align-items: center;
            width: 100%;
        }

        .demo-page .slider {
            flex-grow: 1;
            margin-right: 10px;
            height: 8px;
            border-radius: 4px;
            background: linear-gradient(to right, var(--primary-color), var(--accent-color));
            outline: none;
            appearance: none;
            -webkit-appearance: none;
        }

        .demo-page .slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: white;
            border: 2px solid var(--primary-color);
            cursor: pointer;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
            transition: all 0.2s ease;
        }

        .demo-page .slider::-webkit-slider-thumb:hover {
            transform: scale(1.1);
            box-shadow: 0 3px 8px rgba(0,0,0,0.3);
        }

        .demo-page .slider::-moz-range-thumb {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: white;
            border: 2px solid var(--primary-color);
            cursor: pointer;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
            transition: all 0.2s ease;
        }

        .demo-page .slider::-moz-range-thumb:hover {
            transform: scale(1.1);
            box-shadow: 0 3px 8px rgba(0,0,0,0.3);
        }

        .demo-page #charge1Value, .demo-page #charge2Value {
            min-width: 40px;
            text-align: center;
            font-weight: bold;
            color: var(--dark-text);
        }

        .demo-page #diskControls {
            display: none;
            width: 100%;
        }

        .demo-page #view3dButton {
            background: linear-gradient(135deg, #2ecc71, #27ae60);
            margin-top: 15px;
            display: none;
        }

        .demo-page #view3dButton:hover::after {
            opacity: 1;
        }

        #modal3d {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.8);
            z-index: 2000;
            justify-content: center;
            align-items: center;
            flex-direction: column;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        #modal3d.visible {
            opacity: 1;
        }

        #modal3dContent {
            width: 90%;
            height: 90%;
            background-color: white;
            border-radius: 10px;
            overflow: hidden;
            position: relative;
            transform: scale(0.95);
            transition: transform 0.3s ease;
        }

        #modal3d.visible #modal3dContent {
            transform: scale(1);
        }

        #iframe3d {
            width: 100%;
            height: 100%;
            border: none;
        }

        #closeModal {
            position: absolute;
            top: 10px;
            right: 10px;
            background-color: rgba(0, 0, 0, 0.7);
            color: white;
            border: none;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            font-size: 16px;
            cursor: pointer;
            z-index: 2001;
            transition: background-color 0.2s ease;
        }

        #closeModal:hover {
            background-color: rgba(0, 0, 0, 0.9);
        }
        
        /* 波动演示专用样式，作用域限定#waveContent */
        #waveContent {
            font-family: "微软雅黑", Arial, sans-serif;
            margin: 0;
            padding: 20px;
            width: 100%;
            min-height: calc(100vh - 120px);
            box-sizing: border-box;
            background: #f7fafd;
            overflow-y: auto;
        }
        #waveContent h2 {
            margin: 0;
            padding: 32px 0 18px 0;
            text-align: center;
            font-size: 2.1em;
            color: #1976d2;
        }
        #waveContent .wave-controls-bar {
            background: #fff;
            border-radius: 12px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.07);
            padding: 18px 24px 10px 24px;
            margin: 0 auto 18px auto;
            max-width: 1200px;
        }
        #waveContent #wave-controls {
            display: flex;
            flex-direction: column;
            gap: 16px;
            width: 100%;
            min-width: 520px;
            max-width: 900px;
            margin-left: 0;
            margin-right: auto;
        }
        #waveContent .main-flex {
            display: flex;
            flex-direction: row;
            align-items: flex-start;
            justify-content: center;
            gap: 56px;
            max-width: 1400px;
            margin: 0 auto 40px auto;
            width: 100%;
        }
        #waveContent .main-left {
            flex: 1 1 700px;
            min-width: 0;
            max-width: 900px;
        }
        #waveContent .main-right {
            width: 320px;
            min-width: 260px;
            max-width: 360px;
            display: flex;
            flex-direction: column;
            align-items: stretch;
            gap: 22px;
            background: #fff;
            border-radius: 12px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.07);
            padding: 24px 18px 18px 18px;
        }
        #waveContent .main-right .btn {
            width: 100%;
            margin: 0 0 8px 0;
        }
        #waveContent .main-right .speed-control {
            display: flex;
            align-items: center;
            gap: 6px;
            width: 100%;
        }
        @media (max-width: 1100px) {
            #waveContent .main-flex {
                flex-direction: column;
                align-items: stretch;
            }
            #waveContent .main-right {
                width: 100%;
                max-width: 100%;
                margin-top: 18px;
                flex-direction: row;
                gap: 18px;
                justify-content: flex-start;
                align-items: flex-start;
                padding: 18px 8px 8px 8px;
            }
            #waveContent .main-right .btn, #waveContent .main-right .speed-control {
                width: auto;
                flex: 1 1 0;
            }
        }
        #waveContent .wave-input {
            margin-bottom: 0;
            margin-right: 0;
            display: flex;
            align-items: center;
            gap: 18px;
            width: 100%;
            flex-wrap: wrap;
        }
        #waveContent .wave-input label {
            margin-right: 0;
        }
        #waveContent .wave-input input[type="number"] {
            width: 100px;
            min-width: 60px;
            max-width: 140px;
            flex: 1 1 100px;
            padding: 6px 8px;
            font-size: 1em;
            border: 1px solid #b0c4de;
            border-radius: 5px;
            box-sizing: border-box;
        }
        #waveContent .btn {
            margin: 0 8px;
            padding: 8px 18px;
            font-size: 1em;
            border-radius: 6px;
            background: linear-gradient(90deg, #4a90e2, #1976d2);
            color: #fff;
            border: none;
            cursor: pointer;
            font-weight: bold;
            transition: background 0.2s;
        }
        #waveContent .btn:hover {
            background: linear-gradient(90deg, #1976d2, #4a90e2);
        }
        #waveContent #speed-slider {
            width: 140px;
            vertical-align: middle;
        }
        #waveContent #plots {
            margin: 0 auto 40px auto;
            min-height: 600px;
            max-width: 1200px;
        }
        #waveContent .plot-div {
            min-height: 350px;
            margin-bottom: 30px;
            background: #fff;
            border-radius: 10px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.06);
            padding: 10px 0 0 0;
        }
        #waveContent #velocity-info {
            margin: 18px auto 0 auto;
            max-width: 1200px;
        }
    </style>
</head>
<body>
    <!-- 主页面 - 卡片布局 -->
    <div id="mainPage">
        <div class="container">
            <div class="header">
                <h1>🌊 电磁学演示合集</h1>
                <p>交互式电磁场与电磁波可视化分析工具</p>
                <p>适用于物理教学和科研演示</p>
            </div>

            <div class="theory-section">
                <h2>📚 理论基础</h2>
                <p>电磁学是研究电场、磁场及其相互作用的物理学分支，遵循麦克斯韦方程组。核心概念包括：</p>
                <div class="formula">
                    E = kQ/r² (库仑定律)　　∇²E - μ₀ε₀ ∂²E/∂t² = 0 (波动方程)
                </div>
                <p>电场线从正电荷出发终止于负电荷，等势线与电场线垂直</p>
                <p>变化的电磁场产生电磁波，在真空中传播速度 c = 1/√(μ₀ε₀) ≈ 3×10⁸ m/s</p>
            </div>

            <div class="demos-grid">
                <div class="demo-card" onclick="openDemo('static')">
                    <h3><span class="icon">⚡</span>静态/多电荷电场</h3>
                    <p>演示点电荷和均匀带电圆盘产生的静电场分布，支持电场线和等势线可视化。</p>
                    <ul class="features">
                        <li>点电荷电场线和等势线</li>
                        <li>可调节电荷量和位置</li>
                        <li>均匀带电圆盘3D电场线</li>
                        <li>实时参数调节</li>
                        <li>多种显示模式切换</li>
                    </ul>
                    <button class="launch-btn">🚀 启动演示</button>
                </div>

                <div class="demo-card" onclick="openDemo('moving')">
                    <h3><span class="icon">🌊</span>运动电荷电场</h3>
                    <p>展示运动电荷产生的变化电场和电磁波传播现象，可观察电磁感应效应。</p>
                    <ul class="features">
                        <li>运动电荷电场变化</li>
                        <li>电磁波传播演示</li>
                        <li>可拖动电荷轨迹</li>
                        <li>实时电场分布</li>
                        <li>电磁感应现象</li>
                    </ul>
                    <button class="launch-btn">🚀 启动演示</button>
                </div>

                <div class="demo-card" onclick="openDemo('wave')">
                    <h3><span class="icon">〰️</span>波的叠加演示</h3>
                    <p>演示多个波源产生的波的叠加现象，可观察干涉、拍频等波动现象。</p>
                    <ul class="features">
                        <li>多波源叠加效应</li>
                        <li>相速度和群速度计算</li>
                        <li>可调节波的参数</li>
                        <li>实时波形显示</li>
                        <li>干涉和拍频现象</li>
                    </ul>
                    <button class="launch-btn">🚀 启动演示</button>
                </div>

                <div class="demo-card" onclick="openExternalDemo('elcetric_wave.html')">
                    <h3><span class="icon">🌊</span>基础电磁波传播</h3>
                    <p>演示单个波源产生的电磁波在二维空间中的传播过程，支持波源拖动和参数调节。</p>
                    <ul class="features">
                        <li>可拖动波源位置</li>
                        <li>实时调节振幅、频率、波速</li>
                        <li>距离衰减效果</li>
                        <li>蓝-红热力图显示</li>
                        <li>移动波源后原波继续传播</li>
                    </ul>
                    <button class="launch-btn">🚀 启动演示</button>
                </div>

                <div class="demo-card" onclick="openExternalDemo('electromagnetic_interference.html')">
                    <h3><span class="icon">🔄</span>多波源干涉</h3>
                    <p>展示多个波源产生的电磁波相互干涉的现象，可观察到明显的干涉条纹。</p>
                    <ul class="features">
                        <li>支持多达8个波源</li>
                        <li>每个波源可独立拖动</li>
                        <li>实时显示干涉图样</li>
                        <li>不同波源用不同颜色标识</li>
                        <li>动态添加/删除波源</li>
                    </ul>
                    <button class="launch-btn">🚀 启动演示</button>
                </div>

                <div class="demo-card" onclick="openExternalDemo('single_slit_diffraction.html')">
                    <h3><span class="icon">🚪</span>单缝衍射</h3>
                    <p>基于惠更斯原理演示电磁波通过单缝时的衍射现象，符合夫琅禾费衍射理论。</p>
                    <ul class="features">
                        <li>可调节缝宽和波长</li>
                        <li>实时计算衍射角度</li>
                        <li>显示λ/a比值和衍射条件</li>
                        <li>可拖动波源改变入射角</li>
                        <li>基于惠更斯原理的精确计算</li>
                    </ul>
                    <button class="launch-btn">🚀 启动演示</button>
                </div>

                <div class="demo-card" onclick="openExternalDemo('double_slit_diffraction.html')">
                    <h3><span class="icon">🔀</span>双缝衍射</h3>
                    <p>演示电磁波通过双缝时的衍射和干涉现象，展示经典的杨氏双缝实验。</p>
                    <ul class="features">
                        <li>可调节双缝间距和缝宽</li>
                        <li>实时显示干涉条纹</li>
                        <li>计算条纹间距和衍射角</li>
                        <li>可拖动波源和双缝位置</li>
                        <li>同时展示衍射和干涉效应</li>
                    </ul>
                    <button class="launch-btn">🚀 启动演示</button>
                </div>
            </div>

            <div class="theory-section">
                <h2>🔬 物理现象说明</h2>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); gap: 20px;">
                    <div>
                        <h3 style="color: #4ECDC4;">静电场</h3>
                        <p>静止电荷产生的电场，电场线从正电荷出发，终止于负电荷，等势线与电场线垂直。</p>
                    </div>
                    <div>
                        <h3 style="color: #4ECDC4;">电磁感应</h3>
                        <p>运动电荷产生变化的电磁场，根据麦克斯韦方程组，变化的电场产生磁场。</p>
                    </div>
                    <div>
                        <h3 style="color: #4ECDC4;">电磁波传播</h3>
                        <p>变化的电磁场在空间中传播形成电磁波，具有波长、频率、振幅等波动特性。</p>
                    </div>
                    <div>
                        <h3 style="color: #4ECDC4;">干涉现象</h3>
                        <p>两个或多个相干波源产生的波在空间中相遇时，会发生相长干涉和相消干涉，形成稳定的干涉图样。</p>
                    </div>
                    <div>
                        <h3 style="color: #4ECDC4;">衍射现象</h3>
                        <p>当波遇到障碍物或通过狭缝时，会发生绕射现象。当缝宽与波长相当时，衍射效应最为明显。</p>
                    </div>
                    <div>
                        <h3 style="color: #4ECDC4;">波的叠加</h3>
                        <p>多个波在同一点的振幅按叠加原理相加，可产生干涉、拍频等复杂现象。</p>
                    </div>
                </div>
            </div>

            <div class="footer">
                <p>💡 这些演示基于经典电磁学理论，涵盖静电场、电磁感应、电磁波传播、干涉与衍射等核心概念</p>
                <p>使用JavaScript、Canvas和WebGL技术实现，适用于大学物理、电磁学课程教学和科研演示</p>
            </div>
        </div>
    </div>

    <!-- 静态电场演示页面 -->
    <div id="staticDemo" class="demo-page">
        <div class="demo-header">
            <button class="back-btn" onclick="backToMain()">← 返回</button>
            <h1>静态/多电荷电场演示</h1>
        </div>
        <div class="demo-content">
            <div class="static-demo-container">
                <div id="plot"></div>
                <div class="controls">
                    <div class="controls-title">参数设置</div>
                    <div class="input-group">
                        <label for="charge1"><span class="charge-icon positive">+</span>电荷1 (q1):</label>
                        <div class="slider-container">
                            <input type="range" id="charge1" min="-5" max="5" value="1.0" step="0.1" class="slider">
                            <span id="charge1Value">1.0</span>
                        </div>
                        <div class="info-text">正值表示正电荷，负值表示负电荷</div>
                    </div>
                    <div class="input-group">
                        <label for="charge2"><span class="charge-icon negative">-</span>电荷2 (q2):</label>
                        <div class="slider-container">
                            <input type="range" id="charge2" min="-5" max="5" value="-1.0" step="0.1" class="slider">
                            <span id="charge2Value">-1.0</span>
                        </div>
                        <div class="info-text">正值表示正电荷，负值表示负电荷</div>
                    </div>
                    <div class="input-group">
                        <label for="displayType">显示类型:</label>
                        <select id="displayType">
                            <option value="both">同时显示</option>
                            <option value="contour">仅等势线</option>
                            <option value="field">仅电场线</option>
                        </select>
                    </div>
                    <div class="input-group">
                        <label for="fieldType">电场类型:</label>
                        <select id="fieldType">
                            <option value="pointCharges">点电荷</option>
                            <option value="chargedDisk">均匀带电圆盘</option>
                        </select>
                    </div>
                    <div id="diskControls" style="display: none; width: 100%;">
                        <div class="input-group">
                            <label for="diskRadius"><span class="charge-icon positive">R</span>圆盘半径:</label>
                            <div class="slider-container">
                                <input type="range" id="diskRadius" min="0.5" max="5" value="2.0" step="0.1" class="slider">
                                <span id="diskRadiusValue">2.0</span>
                            </div>
                        </div>
                        <div class="input-group">
                            <label for="diskCharge"><span class="charge-icon positive">Q</span>圆盘电荷量:</label>
                            <div class="slider-container">
                                <input type="range" id="diskCharge" min="-5" max="5" value="1.0" step="0.1" class="slider">
                                <span id="diskChargeValue">1.0</span>
                            </div>
                            <div class="info-text">正值表示正电荷，负值表示负电荷</div>
                        </div>
                    </div>
                    <button onclick="generatePlot()">生成电场图</button>
                    <button id="view3dButton" onclick="open3dView()" style="display: none; background: linear-gradient(135deg, #2ecc71, #27ae60); margin-top: 15px;">查看3D电场线</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 运动电荷电场演示页面 -->
    <div id="movingDemo" class="demo-page">
        <div class="demo-header">
            <button class="back-btn" onclick="backToMain()">← 返回</button>
            <h1>运动电荷电场演示</h1>
        </div>
        <div class="demo-content">
            <iframe id="movingFieldIframe" style="width:100%; height:calc(100vh - 120px); border:none; border-radius:12px; box-shadow:0 2px 12px rgba(0,0,0,0.08);"></iframe>
        </div>
    </div>

    <!-- 波的叠加演示页面 -->
    <div id="waveDemo" class="demo-page">
        <div class="demo-header">
            <button class="back-btn" onclick="backToMain()">← 返回</button>
            <h1>波的叠加演示</h1>
        </div>
        <div class="demo-content" id="waveContent">
            <!-- 波动演示内容将在这里动态插入 -->
        </div>
    </div>

    <!-- 3D电场线模态窗口 -->
    <div id="modal3d">
        <div id="modal3dContent">
            <button id="closeModal" onclick="close3dView()">×</button>
            <iframe id="iframe3d" src="about:blank"></iframe>
        </div>
    </div>

    <script>
    const urlParams = new URLSearchParams(window.location.search);
    const user = urlParams.get('user');
    const key = urlParams.get('key');
    const token = urlParams.get('token');  // 获取 token 参数

    // 页面切换逻辑
    function openDemo(demoType) {
        const mainPage = document.getElementById('mainPage');
        const staticDemo = document.getElementById('staticDemo');
        const movingDemo = document.getElementById('movingDemo');
        const waveDemo = document.getElementById('waveDemo');

        // 隐藏主页面
        mainPage.style.display = 'none';

        // 显示对应的演示页面
        if (demoType === 'static') {
            staticDemo.classList.add('active');
            // 初始化静态电场演示
            setTimeout(() => {
                initStaticDemoControls();
                generatePlot();
            }, 100);
        } else if (demoType === 'moving') {
            movingDemo.classList.add('active');
            // 加载运动电荷演示
            let paramStr = `user=${encodeURIComponent(user||'')}&key=${encodeURIComponent(key||'')}&token=${encodeURIComponent(token||'')}`;
            document.getElementById('movingFieldIframe').src = `electric_field.html?${paramStr}`;
        } else if (demoType === 'wave') {
            waveDemo.classList.add('active');
            // 初始化波动演示
            initWaveDemo();
        }
    }

    function backToMain() {
        const mainPage = document.getElementById('mainPage');
        const staticDemo = document.getElementById('staticDemo');
        const movingDemo = document.getElementById('movingDemo');
        const waveDemo = document.getElementById('waveDemo');

        // 隐藏所有演示页面
        staticDemo.classList.remove('active');
        movingDemo.classList.remove('active');
        waveDemo.classList.remove('active');

        // 显示主页面
        mainPage.style.display = 'block';

        // 清理资源
        destroyWaveDemo();
        document.getElementById('movingFieldIframe').src = 'about:blank';
    }

    // 打开外部演示页面
    function openExternalDemo(filename) {
        // 添加用户认证参数
        let paramStr = '';
        if (user || key || token) {
            const params = new URLSearchParams();
            if (user) params.append('user', user);
            if (key) params.append('key', key);
            if (token) params.append('token', token);
            paramStr = '?' + params.toString();
        }
        window.open(filename + paramStr, '_blank');
    }

    // 添加卡片交互效果
    document.addEventListener('DOMContentLoaded', function() {
        const cards = document.querySelectorAll('.demo-card');

        cards.forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.background = 'rgba(255, 255, 255, 1)';
            });

            card.addEventListener('mouseleave', function() {
                this.style.background = 'rgba(255, 255, 255, 0.95)';
            });
        });
    });

function generatePlot() {
    // 获取电场类型
    const fieldType = document.getElementById('fieldType').value;
    
    if (fieldType === 'pointCharges') {
        generatePointChargesPlot();
    } else if (fieldType === 'chargedDisk') {
        // 不再调用generateChargedDiskPlot，而是直接显示3D视图
        open3dView();
    }
}

function generatePointChargesPlot() {
    // 获取输入的电荷量
    const q1 = parseFloat(document.getElementById('charge1').value);
    const q2 = parseFloat(document.getElementById('charge2').value);
    
    // 更新显示的电荷值
    document.getElementById('charge1Value').textContent = q1.toFixed(1);
    document.getElementById('charge2Value').textContent = q2.toFixed(1);
    
    const k = 1.0;
    const x1 = -2.0, y1 = 0.0;
    const x2 = 2.0, y2 = 0.0;

    // 更新电荷图标
    updateChargeIcons(q1, q2);

    // 生成等势线用的高精度网格（200x200）
    const xContour = Array.from({length: 200}, (_, i) => -8 + i * 0.08); // x轴范围
    const yContour = Array.from({length: 200}, (_, i) => -8 + i * 0.08); // y轴范围
    const V = [];
    
    // 计算等势线数据
    for (let j = 0; j < xContour.length; j++) {
        V[j] = [];
        for (let i = 0; i < yContour.length; i++) {
            const r1 = Math.sqrt((xContour[i] - x1) ** 2 + (yContour[j] - y1) ** 2); // 电荷1位置
            const r2 = Math.sqrt((xContour[i] - x2) ** 2 + (yContour[j] - y2) ** 2); // 电荷2位置
            const safeR1 = r1 < 1e-10 ? 1e-10 : r1;
            const safeR2 = r2 < 1e-10 ? 1e-10 : r2;
            V[j][i] = k * q1 / safeR1 + k * q2 / safeR2; // 计算电势
        }
    }

    // 生成电场线用的50x50网格（而不是200x200）
    const gridSize = 50;
    const xField = [], yField = [], Ex = [], Ey = [];

    // 计算电场分量
    for (let i = 0; i < gridSize; i++) {
        for (let j = 0; j < gridSize; j++) {
            const x = -8 + i * (16 / (gridSize - 1));
            const y = -8 + j * (16 / (gridSize - 1));

            // 计算到两个电荷的距离
            const r1 = Math.sqrt((x - x1) ** 2 + (y - y1) ** 2);
            const r2 = Math.sqrt((x - x2) ** 2 + (y - y2) ** 2);

            // 计算电场分量
            const safeR1 = r1 < 1e-10 ? 1e-10 : r1;
            const safeR2 = r2 < 1e-10 ? 1e-10 : r2;

            const ex = k * q1 * (x - x1) / (safeR1 ** 3) + k * q2 * (x - x2) / (safeR2 ** 3);
            const ey = k * q1 * (y - y1) / (safeR1 ** 3) + k * q2 * (y - y2) / (safeR2 ** 3);

            // 计算电场强度
            const arrowLength = Math.sqrt(ex ** 2 + ey ** 2);
            
            // 只取电场箭头长度大于0.1小于1.5的点
            if (arrowLength > 0.005 && arrowLength < 4) {
                xField.push(x);
                yField.push(y);
                Ex.push(ex);
                Ey.push(ey);
            }
        }
    }

    // 设置等势线参数
    const v_min = -2, v_max = 2, step = 0.05;
    const displayType = document.getElementById('displayType').value;
    const data = [];

    // 绘制等势线
    if (displayType === 'both' || displayType === 'contour') {
        data.push({
            z: V,
            x: xContour,
            y: yContour,
            type: 'contour',
            colorscale: 'Jet',
            contours: {
                start: v_min, 
                end: v_max, 
                size: step,
                labelfont: {
                    family: 'Roboto, Arial, sans-serif',
                    size: 12,
                    color: 'rgba(0,0,0,0.7)'
                }
            },
            line: {width: 1},
            colorbar: {
                title: '电势值',
                titlefont: {
                    family: 'Roboto, Arial, sans-serif',
                    size: 14
                },
                tickfont: {
                    family: 'Roboto, Arial, sans-serif',
                    size: 12
                }
            }
        });
    }

    // 绘制电场矢量箭头（使用展平后的一维数组）
    if (displayType === 'both' || displayType === 'field') {
        const maxArrowLength = 0.6; // 设置箭头的最大长度
        const arrowData = xField.map((x, index) => {
            const y = yField[index];
            const ex = Ex[index];
            const ey = Ey[index];
            const arrowWidth = 0.05; // 箭头宽度

            // 计算箭头的原始长度
            const originalLength = Math.sqrt(ex ** 2 + ey ** 2);
            const scale = originalLength > 0 ? Math.min(maxArrowLength / originalLength, 1) : 0;
            
            // 计算电场方向角度
            const angle = Math.atan2(ey, ex);
            const minLength = 0.1;
            
            // 计算最小长度在x和y方向的投影
            const minDx = minLength * Math.cos(angle);
            const minDy = minLength * Math.sin(angle);

            // 计算箭头的终点，加上最小长度的投影
            const endX = x + ex * scale * maxArrowLength + minDx;
            const endY = y + ey * scale * maxArrowLength + minDy;

            // 计算箭头的两侧点
            const arrowHeadX1 = endX - arrowWidth * Math.cos(angle - Math.PI / 6);
            const arrowHeadY1 = endY - arrowWidth * Math.sin(angle - Math.PI / 6);
            const arrowHeadX2 = endX - arrowWidth * Math.cos(angle + Math.PI / 6);
            const arrowHeadY2 = endY - arrowWidth * Math.sin(angle + Math.PI / 6);

            return {
                x: [x, endX, arrowHeadX1, endX, arrowHeadX2],
                y: [y, endY, arrowHeadY1, endY, arrowHeadY2],
                mode: 'lines+text', // 使用 'lines+text' 模式
                line: { width: 2, color: '#2c3e50' }, // 更改线条颜色
                text: '', // 可以添加文本标签
                textposition: 'top center'
            };
        });

        data.push(...arrowData); // 将箭头数据添加到数据数组中
    }

    // 添加电荷位置标记
    data.push({
        x: [x1, x2],
        y: [y1, y2],
        mode: 'markers+text',
        marker: {
            size: [Math.abs(q1) * 10, Math.abs(q2) * 10],
            color: [q1 > 0 ? '#e74c3c' : '#4a90e2', q2 > 0 ? '#e74c3c' : '#4a90e2'],
            symbol: 'circle',
            line: {
                color: 'white',
                width: 2
            }
        },
        text: [q1 > 0 ? '+' : '-', q2 > 0 ? '+' : '-'],
        textfont: {
            color: 'white',
            size: 14,
            family: 'Arial, sans-serif'
        },
        textposition: 'middle center',
        showlegend: false
    });

    // 绘制布局
    const layout = {
        title: {
            text: '点电荷电场和等势线可视化',
            font: {
                family: 'Roboto, Arial, sans-serif',
                size: 24,
                color: '#2c3e50'
            },
            y: 0.95
        },
        xaxis: {
            range: [-8, 8], 
            title: {
                text: 'x',
                font: {
                    family: 'Roboto, Arial, sans-serif',
                    size: 16,
                    color: '#2c3e50'
                }
            },
            gridcolor: '#ecf0f1',
            zerolinecolor: '#bdc3c7'
        },
        yaxis: {
            range: [-8, 8], 
            title: {
                text: 'y',
                font: {
                    family: 'Roboto, Arial, sans-serif',
                    size: 16,
                    color: '#2c3e50'
                }
            },
            gridcolor: '#ecf0f1',
            zerolinecolor: '#bdc3c7'
        },
        showlegend: false,
        width: 800,
        height: 800,
        paper_bgcolor: 'white',
        plot_bgcolor: 'white',
        margin: {
            l: 65,
            r: 50,
            b: 65,
            t: 90,
        }
    };

    Plotly.newPlot('plot', data, layout);
}

// 更新电荷图标
function updateChargeIcons(q1, q2) {
    const charge1Icon = document.querySelector('label[for="charge1"] .charge-icon');
    const charge2Icon = document.querySelector('label[for="charge2"] .charge-icon');
    
    if (q1 > 0) {
        charge1Icon.textContent = '+';
        charge1Icon.className = 'charge-icon positive';
    } else {
        charge1Icon.textContent = '-';
        charge1Icon.className = 'charge-icon negative';
    }
    
    if (q2 > 0) {
        charge2Icon.textContent = '+';
        charge2Icon.className = 'charge-icon positive';
    } else {
        charge2Icon.textContent = '-';
        charge2Icon.className = 'charge-icon negative';
    }
}

// 切换电场类型时显示/隐藏相应控件
function toggleFieldTypeControls() {
    const fieldType = document.getElementById('fieldType').value;
    const pointChargesControls = document.querySelectorAll('.input-group:nth-child(1), .input-group:nth-child(2)');
    const diskControls = document.getElementById('diskControls');
    const plotDiv = document.getElementById('plot');
    const modal3d = document.getElementById('modal3d');
    const view3dButton = document.getElementById('view3dButton');
    
    if (fieldType === 'pointCharges') {
        pointChargesControls.forEach(control => control.style.display = 'flex');
        diskControls.style.display = 'none';
        plotDiv.style.display = 'block';
        view3dButton.style.display = 'none';
        // 重新生成图形
        generatePlot();
    } else if (fieldType === 'chargedDisk') {
        pointChargesControls.forEach(control => control.style.display = 'none');
        diskControls.style.display = 'block';
        plotDiv.style.display = 'none';
        view3dButton.style.display = 'none';
        // 只有在3D视图未显示的情况下才打开它
        if (modal3d.style.display !== 'flex') {
            open3dView();
        }
    }
}

// 打开3D视图
function open3dView() {
    const modal = document.getElementById('modal3d');
    const iframe = document.getElementById('iframe3d');
    const view3dButton = document.getElementById('view3dButton');
    
    // 获取圆盘参数
    const diskRadius = parseFloat(document.getElementById('diskRadius').value);
    const diskCharge = parseFloat(document.getElementById('diskCharge').value);
    


    // 设置iframe的src，传递参数，并添加用户认证参数
    iframe.src = `electric3d.html?radius=${diskRadius}&charge=${diskCharge}&user=${user}&key=${key}&token=${token}`;  
    
    // 显示模态窗口
    modal.style.display = 'flex';
    
    // 隐藏"查看3D电场线"按钮
    view3dButton.style.display = 'none';
    
    // 添加动画效果
    setTimeout(() => {
        modal.classList.add('visible');
    }, 10);
    
    // 禁止背景滚动
    document.body.style.overflow = 'hidden';
}

// 关闭3D视图
function close3dView() {
    const modal = document.getElementById('modal3d');
    const iframe = document.getElementById('iframe3d');
    const view3dButton = document.getElementById('view3dButton');
    
    // 添加动画效果
    modal.classList.remove('visible');
    
    // 等待动画完成后隐藏模态窗口
    setTimeout(() => {
        modal.style.display = 'none';
        
        // 清空iframe内容，减少内存占用
        iframe.src = 'about:blank';
        
        // 确保"查看3D电场线"按钮可见
        const fieldType = document.getElementById('fieldType').value;
        if (fieldType === 'chargedDisk') {
            view3dButton.style.display = 'none';
        }
    }, 300);
    
    // 允许背景滚动
    document.body.style.overflow = 'auto';
}

// 初始化静态演示控件
function initStaticDemoControls() {
    // 初始化滑块事件监听器 - 点电荷
    document.getElementById('charge1').addEventListener('input', function() {
        // 只更新显示的值，不渲染图形
        document.getElementById('charge1Value').textContent = parseFloat(this.value).toFixed(1);
    });

    document.getElementById('charge2').addEventListener('input', function() {
        // 只更新显示的值，不渲染图形
        document.getElementById('charge2Value').textContent = parseFloat(this.value).toFixed(1);
    });

    // 添加change事件监听器，在滑块拖动结束后才渲染
    document.getElementById('charge1').addEventListener('change', function() {
        generatePlot();
    });

    document.getElementById('charge2').addEventListener('change', function() {
        generatePlot();
    });

    // 初始化滑块事件监听器 - 带电圆盘
    document.getElementById('diskRadius').addEventListener('input', function() {
        // 只更新显示的值，不渲染图形
        document.getElementById('diskRadiusValue').textContent = parseFloat(this.value).toFixed(1);
    });

    document.getElementById('diskCharge').addEventListener('input', function() {
        // 只更新显示的值，不渲染图形
        document.getElementById('diskChargeValue').textContent = parseFloat(this.value).toFixed(1);
    });

    // 添加change事件监听器，在滑块拖动结束后才渲染
    document.getElementById('diskRadius').addEventListener('change', function() {
        const fieldType = document.getElementById('fieldType').value;
        const modal3d = document.getElementById('modal3d');
        if (fieldType === 'chargedDisk' && modal3d.style.display === 'flex') {
            // 只有在当前是圆盘模式且3D视图已打开的情况下，才重新加载3D视图
            open3dView();
        }
    });

    document.getElementById('diskCharge').addEventListener('change', function() {
        const fieldType = document.getElementById('fieldType').value;
        const modal3d = document.getElementById('modal3d');
        if (fieldType === 'chargedDisk' && modal3d.style.display === 'flex') {
            // 只有在当前是圆盘模式且3D视图已打开的情况下，才重新加载3D视图
            open3dView();
        }
    });

    // 添加电场类型切换事件监听器
    document.getElementById('fieldType').addEventListener('change', toggleFieldTypeControls);

    // 添加显示类型切换事件监听器
    document.getElementById('displayType').addEventListener('change', function() {
        generatePlot();
    });
}

// 波动演示初始化标志
let waveDemoInitialized = false;

// 波动演示相关逻辑
function initWaveDemo() {
    if (waveDemoInitialized) return;
    waveDemoInitialized = true;

    const waveContent = document.getElementById('waveContent');
    // 插入HTML结构
    waveContent.innerHTML = `
        <h2>多波叠加演示</h2>
        <div class="wave-controls-bar">
            <div id="wave-controls"></div>
        </div>
        <div class="main-flex">
            <div class="main-left">
                <div id="velocity-info"></div>
                <div id="plots"></div>
            </div>
            <div class="main-right">
                <button class="btn" id="addWaveBtn">添加波</button>
                <button class="btn" id="removeWaveBtn">删除波</button>
                <div class="speed-control">
                    <label for="speed-slider"><b>动画速度：</b></label>
                    <input type="range" id="speed-slider" min="0.001" max="2" step="0.01" value="1">
                    <span id="speed-value">1.00</span> ×
                </div>
            </div>
        </div>
    `;
    // 波动演示JS逻辑
    window._waveDemo = (function(){
        let waves = [
            { freq: 20, speed: 10, amplitude: 1, phaseA: 0 },
            { freq: 22, speed: 10, amplitude: 1, phaseA: 0 }
        ];
        let t = 0;
        let timer = null;
        let animSpeedFactor = 1.0;
        let yRange = null; // 缓存y轴范围

        function renderWaveInputs() {
            const container = document.getElementById('wave-controls');
            container.innerHTML = '';
            waves.forEach((wave, i) => {
                const div = document.createElement('div');
                div.className = 'wave-input';
                div.innerHTML = `
                    <label>波${i+1} 频率f: <input type="number" value="${wave.freq}" min="0" step="0.1" onchange="_waveDemo.updateWave(${i}, 'freq', this.value)"></label>
                    <label>速度v: <input type="number" value="${wave.speed}" min="0" step="0.1" onchange="_waveDemo.updateWave(${i}, 'speed', this.value)"></label>
                    <label>振幅A: <input type="number" value="${wave.amplitude}" min="0" step="0.01" onchange="_waveDemo.updateWave(${i}, 'amplitude', this.value)"></label>
                    <label>
                        相位系数a (rad/s):
                        <input type="number" id="phaseA${i}" value="${wave.phaseA || 0}" step="0.1" style="width:80px;" onchange="_waveDemo.updateWave(${i}, 'phaseA', this.value)">
                        <span style="font-size:12px;">× t</span>
                    </label>
                `;
                container.appendChild(div);
            });
        }

        function updateWave(i, key, value) {
            if (key === 'amplitude' || key === 'freq' || key === 'speed') {
                waves[i][key] = parseFloat(value);
            } else if (key === 'phaseA') {
                waves[i].phaseA = parseFloat(value);
            }
            recalcYRange();
            drawPlots();
        }

        function addWave() {
            waves.push({ freq: 20, speed: 10, amplitude: 1, phaseA: 0 });
            recalcYRange();
            renderWaveInputs();
            drawPlots();
        }

        function removeWave() {
            if (waves.length > 1) {
                waves.pop();
                recalcYRange();
                renderWaveInputs();
                drawPlots();
            }
        }

        function recalcYRange() {
            // 重新计算合成波的y轴范围
            const N = 1000;
            const L = 6 * Math.PI;
            const x = Array.from({length: N}, (_, i) => i * L / (N-1));
            let y_sum = Array(N).fill(0);
            // 计算缩放因子
            const maxSpeed = Math.max(...waves.map(w => w.speed));
            const maxAnimSpeed = 10;
            const speedScale = maxAnimSpeed / maxSpeed;
            for (let w of waves) {
                const animSpeed = w.speed * speedScale;
                const k = 2 * Math.PI * w.freq / w.speed;
                const omega = 2 * Math.PI * w.freq - (w.phaseA || 0);
                let y = x.map(xi => w.amplitude * Math.sin(k * xi - omega * t));
                for (let j = 0; j < N; j++) y_sum[j] += y[j];
            }
            const ySumMax = Math.max(...y_sum);
            const ySumMin = Math.min(...y_sum);
            const yRangePad = 0.5;
            yRange = [ySumMin - yRangePad, ySumMax + yRangePad];
        }

        function drawPlots() {
            const N = 1000;
            const L = 6 * Math.PI;
            const x = Array.from({length: N}, (_, i) => i * L / (N-1));
            const plotsDiv = document.getElementById('plots');
            plotsDiv.innerHTML = '';

            // 计算最大速度
            const maxSpeed = Math.max(...waves.map(w => w.speed));
            const maxAnimSpeed = 10; // 最大动画速度
            // 计算缩放因子
            const speedScale = maxAnimSpeed / maxSpeed;

            let ys = [];
            let y_sum = Array(N).fill(0);

            // 计算每个波
            for (let w of waves) {
                // 缩放后的速度
                const animSpeed = w.speed * speedScale;
                const k = 2 * Math.PI * w.freq / w.speed;
                const omega = 2 * Math.PI * w.freq - (w.phaseA || 0);
                let y = x.map(xi => w.amplitude * Math.sin(k * xi - omega * t));
                ys.push(y);
                for (let j = 0; j < N; j++) y_sum[j] += y[j];
            }

            // 计算相速度和群速度
            let v_phase = '—', v_group = '—';
            if (waves.length === 2) {
                const [w1, w2] = waves;
                const k1 = 2 * Math.PI * w1.freq / w1.speed;
                const k2 = 2 * Math.PI * w2.freq / w2.speed;
                const omega1 = 2 * Math.PI * w1.freq - (w1.phaseA || 0);
                const omega2 = 2 * Math.PI * w2.freq - (w2.phaseA || 0);
                v_phase = ((omega1 + omega2) / (k1 + k2)).toFixed(2);
                v_group = ((omega2 - omega1) / (k2 - k1)).toFixed(2);
            }
            document.getElementById('velocity-info').innerHTML =
                `<div style='background:#f5f5f5;padding:10px 20px;border-radius:8px;display:inline-block;margin-bottom:10px;'>
                    <b>相速度 v<sub>phase</sub>：</b> <span style='color:#1976d2;font-weight:bold;'>${v_phase}</span> &nbsp;&nbsp; 
                    <b>群速度 v<sub>group</sub>：</b> <span style='color:#388e3c;font-weight:bold;'>${v_group}</span><br>
                    <span style="color:gray;">（仅在两个波时显示，单位与输入速度一致）</span>
                </div>`;

            // 先画合成波
            const divSum = document.createElement('div');
            divSum.id = 'plot-sum';
            divSum.className = 'plot-div';
            plotsDiv.appendChild(divSum);
            // 固定y轴范围：合成波最大振幅和多一点
            const maxAmp = waves.reduce((sum, w) => sum + Math.abs(w.amplitude), 0);
            Plotly.newPlot(divSum.id, [{
                x: x, y: y_sum,
                name: '合成波',
                line: { width: 3, color: 'black' }
            }], {
                title: '合成波形',
                xaxis: { title: 'x' },
                yaxis: { title: 'y', range: [-maxAmp-0.5, maxAmp+0.5] },
                margin: { t: 40 }
            }, {displayModeBar: false});

            // 再画每个波
            ys.forEach((y, i) => {
                const div = document.createElement('div');
                div.id = `plot-wave-${i}`;
                div.className = 'plot-div';
                plotsDiv.appendChild(div);
                Plotly.newPlot(div.id, [{
                    x: x, y: y,
                    name: `波${i+1}`,
                    line: { width: 2 }
                }], {
                    title: `波${i+1} 波形`,
                    xaxis: { title: 'x' },
                    yaxis: { title: 'y' },
                    margin: { t: 40 }
                }, {displayModeBar: false});
            });
        }

        function startAnimation() {
            if (timer) clearInterval(timer);
            timer = setInterval(() => {
                t += 0.03 * animSpeedFactor;
                drawPlots();
            }, 30);
        }

        // 初始化
        document.getElementById('speed-slider').addEventListener('input', function() {
            animSpeedFactor = parseFloat(this.value);
            document.getElementById('speed-value').textContent = animSpeedFactor.toFixed(2);
        });
        document.getElementById('addWaveBtn').onclick = addWave;
        document.getElementById('removeWaveBtn').onclick = removeWave;
        renderWaveInputs();
        recalcYRange();
        drawPlots();
        startAnimation();

        return {
            updateWave,
            getWaves: () => waves,
            destroy: function() {
                if (timer) clearInterval(timer);
                timer = null;
            }
        };
    })();
}

function destroyWaveDemo() {
    if (!waveDemoInitialized) return;
    waveDemoInitialized = false;
    if (window._waveDemo && typeof window._waveDemo.destroy === 'function') {
        window._waveDemo.destroy();
    }
    const waveContent = document.getElementById('waveContent');
    if (waveContent) {
        waveContent.innerHTML = '';
    }
    window._waveDemo = null;
}
    </script>
</body>
</html>