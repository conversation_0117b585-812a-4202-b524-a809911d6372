from typing import List, Dict, Any, Optional, Set, Tuple
import asyncio
import logging
import time
import re
from datetime import datetime, timed<PERSON><PERSON>
from  duckduckgosearch import Web_Agent
import json
import os
from getAnswer import get_summary_from_dataset, create_session,send_chat_message,get_document_chunks
import uuid
from logger_config import log_with_request_id # 从新模块导入
from logger_config import deepsearch_logger as deeplogger
import traceback






dataset_id="a3870650ffa111ef92aa2a5c03e306d6" #test知识库










def split_query_into_parts(query: str, num_parts: int = 5) -> List[str]:
    """
    将查询文本平均分成指定数量的部分
    
    Args:
        query: 要分割的查询文本
        num_parts: 要分割成的部分数量
        
    Returns:
        分割后的文本列表
    """
    # 移除多余空白并分割成句子
    sentences = [s.strip() for s in re.split('[。.；;？?！!]', query) if s.strip()]
    
    if not sentences:
        return [query]  # 如果没有可分割的句子，返回原始查询
        
    # 如果句子数量少于指定部分数，减少部分数
    num_parts = min(num_parts, len(sentences))
    
    # 计算每部分应该包含的句子数
    sentences_per_part = len(sentences) // num_parts
    remainder = len(sentences) % num_parts
    
    parts = []
    current_index = 0
    
    # 分配句子到各个部分
    for i in range(num_parts):
        # 计算当前部分应该包含的句子数
        current_count = sentences_per_part + (1 if i < remainder else 0)
        # 提取句子并组合
        part_sentences = sentences[current_index:current_index + current_count]
        parts.append('。'.join(part_sentences) + '。')
        current_index += current_count
    
    return parts

def count_text_length(text: str) -> int:
    """
    计算文本的有效长度，区分中英文
    
    Args:
        text: 输入文本
        
    Returns:
        int: 文本的有效长度(中文字符数 + 英文单词数)
    """
    # 分离中文和非中文部分
    chinese_chars = 0
    english_text = []
    current_word = []
    
    for char in text:
        if '\u4e00' <= char <= '\u9fff':  # 中文字符范围
            # 如果之前有英文单词，保存它
            if current_word:
                english_text.append(''.join(current_word))
                current_word = []
            chinese_chars += 1
        elif char.isalpha():  # 英文字母
            current_word.append(char)
        else:  # 空格、标点等
            if current_word:  # 结束当前英文单词
                english_text.append(''.join(current_word))
                current_word = []
    
    # 处理最后一个英文单词
    if current_word:
        english_text.append(''.join(current_word))
    
    # 计算英文单词数
    english_words = len([word for word in english_text if word])
    
    return chinese_chars + english_words


def should_use_full_chunks(query: str) -> bool:
    """
    判断是否需要使用完整的文档chunks而不是摘要
    
    Args:
        query: 用户查询
        
    Returns:
        bool: 是否需要使用完整chunks
    """
    # 需要全文的关键词
    full_text_keywords = {
        # 文档结构相关
        '目录', '章节', '结构', 'structure', 'contents', 'chapters',
        
        # 完整性相关
        '完整', '全文', '全部内容', 'full text', 'complete', 'entire',
        
        # 分析类型
        '分析', '总结', '概述', 'analyze', 'summarize', 'overview',
        
        # 比较相关
        '对比', '比较', '区别', 'compare', 'difference', 'versus',
        
        # 统计相关
        '统计', '计数', '多少', 'count', 'statistics', 'number of',
        
        # 提取相关
        '提取', '列举', '列出', 'extract', 'list', 'enumerate',
        
        # 特定部分
        '章', '节', '段落', 'chapter', 'section', 'paragraph',
        
        # 格式相关
        '格式', '样式', 'format', 'style', 'layout',
    }
    
    # 查询词处理
    query_lower = query.lower()
    
    # 1. 关键词匹配
    if any(keyword in query_lower for keyword in full_text_keywords):
        return True
        
    # 2. 查询长度判断（较长的查询可能需要更完整的上下文）
    query_length = count_text_length(query)
    if query_length> 50:  # 可调整阈值
        return True
        
    # 3. 问题类型判断
    question_words = {'如何', '为什么', '怎样', 'how', 'why', 'what'}
    if any(word in query_lower for word in question_words):
        return True
    
    # 4. 特殊标点符号判断（可能表示复杂查询）
    special_chars = {'?', '？', ';', '；', '、', ',', '，'}
    if any(char in query for char in special_chars):
        return True
        
    return False

# 调用 /api/completion 获取答案
def get_completion(session_id="", chat_id="", api_key_rag="", api_key="", api_key_type="", query="", method=0, selected_doc_ids="", web_agent=None):
    deeplogger.info(f"Debug - get_completion called with method {method}")
    local_data = None
    webfullanswer = None
    request_id = str(uuid.uuid4())[:8]
    if method != 1:
        try:
            if not selected_doc_ids:
                deeplogger.info("Debug - Using chat message")
                response = ""
            
            elif len(selected_doc_ids)==1:
                deeplogger.info("Debug - Using selected document chunks")
                # 根据query判断是否需要使用完整chunks
                if should_use_full_chunks(query):  #使用第一个文件全文
                    deeplogger.info("Debug - Using full document chunks")
                    response = get_document_chunks(
                        api_key=api_key_rag,
                        base_url="http://localhost:8080",
                        dataset_id=dataset_id,
                        document_id=selected_doc_ids[0],
                        keywords=None,
                        chunk_id=None,
                        offset=0,
                        limit=1024
                    )
                    if response and response.get('code') == 0:
                        content = response['data']['content']
                        deeplogger.info(f"\nSuccessfully retrieved full document content, length: {count_text_length(content)}")
                        deeplogger.info(f"Successfully retrieved full document content, length: {count_text_length(content)}")
                        local_data = content
                    else:
                        deeplogger.info("\nNo valid content found")
                        deeplogger.info(f"Response: {response}")
                        local_data = "未找到有效内容"
                else:   #使用第一个文件检索
                    deeplogger.info("Debug - Using single document summary")
                    deeplogger.info(f"使用单文件检索{query}")
                    local_data, references_docs = get_summary_from_dataset(
                        api_key_rag=api_key_rag,
                        api_key=api_key,
                        api_key_type=api_key_type,
                        question=query,
                        dataset_ids=[dataset_id],
                        document_ids=selected_doc_ids
                    )

            else:  #使用多文件检索
                    deeplogger.info(f"query字数：{query},{count_text_length(query)}")
                    deeplogger.info("Debug - Long query detected, using multi-keyword search")                  
                    # 对每个关键词进行检索
                    deeplogger.info(f"多文件检索selected_doc_ids: {selected_doc_ids}")
                    chunk, references_docs = get_summary_from_dataset(
                            api_key_rag=api_key_rag,
                            api_key=api_key,
                            api_key_type=api_key_type,
                            question=query,
                            dataset_ids=[dataset_id],
                            document_ids=selected_doc_ids
                        )
                   
                    if chunk:
                        # 使用换行符和分隔线合并chunks
                        local_data = chunk
                        source_docs = references_docs
                        deeplogger.info(f"成功检索内容，检索字数: {count_text_length(local_data)}")
                    else:
                        local_data = ""
                        source_docs = []
                        deeplogger.error(f"警告: 未检索到任何内容{chunk}")

        except Exception as e:
            deeplogger.info(f"Debug - Error in knowledge base request: {str(e)}")
            local_data = None

    if method == 1 or method == 2:
        deeplogger.info(f"请稍等正在生成在线query回答……: {query}")
        try:
            webfullanswer = web_agent.chain_of_thought_search(query)
            deeplogger.info(f"webfullanswer字数为: {count_text_length(webfullanswer.get('final_summary'))}")
            deeplogger.info(f"webfullanswer为: {webfullanswer.get('final_summary')[:300]}")
        except Exception as e:
            deeplogger.info(f"在线搜索过程中出现问题: {str(e)}")
            webfullanswer = None

    
    try:
        if method == 0 or method == 4:  # 仅本地知识库
            result = {
                'answer': local_data if local_data else "未找到本地知识库相关答案",
                'type': 'local',
                'source_docs': source_docs
            }
            
        elif method == 1:  # 仅网络搜索
            if webfullanswer and isinstance(webfullanswer, dict):
                result = {
                    'answer': webfullanswer.get('final_summary', "未找到相关网络搜索结果"),
                    'type': 'web',
                    'sources': webfullanswer.get('sources', [])
                }
            else:
                result = {
                    'answer': str(webfullanswer) if webfullanswer else "未找到相关网络搜索结果",
                    'type': 'web',
                    'sources': []
                }
                
        elif method == 2:  # 混合模式
            parts = []
            sources = []
            
            if local_data:
                parts.append(str(local_data))
            else:
                deeplogger.info("警告: local_data 为空，未能添加到结果中。")
            
            if webfullanswer and isinstance(webfullanswer, dict):
                web_text = webfullanswer.get('final_summary', '')
                if web_text:
                    parts.append(web_text)
                sources.extend(webfullanswer.get('sources', []))
            deeplogger.info(f"混合模式parts字数为: {count_text_length("\n\n".join(parts))}")
            deeplogger.info(f"混合模式索引结果: {"\n\n".join(parts)[:1000]}")
            result = {
                'answer': "\n\n".join(parts) if parts else "未找到任何相关答案",
                'type': 'mixed',
                'sources': sources,
                'source_docs': source_docs
            }
                
    except Exception as e:
        deeplogger.info(f"警告: 结果合并过程中出现错误: {str(e)}")
        result = {
            'answer': "处理搜索结果时发生错误",
            'type': 'error',
            'sources': []
        }
    log_with_request_id(f"检索结束....................", request_id=request_id)
    return result



# Maximum words allowed in context (25k words for safety margin)
MAX_CONTEXT_WORDS = 25000


def count_words(text: str) -> int:
    """Count words in a text string"""
    return len(text.split())

def trim_context_to_word_limit(context_list: List[str], max_words: int = MAX_CONTEXT_WORDS) -> List[str]:
    """Trim context list to stay within word limit while preserving most recent/relevant items"""
    total_words = 0
    trimmed_context = []
    
    # Process in reverse to keep most recent items
    for item in reversed(context_list):
        words = count_words(item)
        if total_words + words <= max_words:
            trimmed_context.insert(0, item)  # Insert at start to maintain original order
            total_words += words
        else:
            break
            
    return trimmed_context

class ResearchProgress:
    def __init__(self, total_depth: int, total_breadth: int):
        self.current_depth = 1  # Start from 1 and increment up to total_depth
        self.total_depth = total_depth
        self.current_breadth = 0  # Start from 0 and count up to total_breadth as queries complete
        self.total_breadth = total_breadth
        self.current_query: Optional[str] = None
        self.total_queries = 0
        self.completed_queries = 0

class DeepResearchSkill:
    def __init__(self, 
                 query: str, 
                 api_key: str, 
                 api_key_type: str,         
                 api_key_rag: str, 
                 breadth: int, 
                 depth: int, 
                 concurrency_limit: int,
                 method: int,
                 summary_length: int,
                 language: str,
                 doc_ids: Optional[List[str]] = None,
                 init_start_time: float = None):
        self.query = query
        self.api_key_rag = api_key_rag
        self.api_key = api_key
        self.api_key_type = api_key_type
        self.web_agent = Web_Agent(api_key=api_key, api_key_type=api_key_type)
        self.breadth = breadth
        self.depth = depth
        self.concurrency_limit = concurrency_limit
        self.doc_ids = doc_ids if doc_ids is not None else []
        self.research_sources = []  # Track all research sources
        self.research_source_docs = []  # Track all research source docs
        self.context = []  # Track all context
        self.method = method
        self.summary_length = summary_length
        self.language = language
        self.init_start_time = init_start_time
    async def generate_search_queries(self, query: str, num_queries: int = 3) -> List[Dict[str, str]]:
        """Generate SERP queries for research"""
        
        # 根据language选择提示词语言
        if self.language == 'Chinese':
            prompt = f"""
            你是一位专业的研究人员，负责生成搜索查询。
            
            请根据以下提示，生成{num_queries}个独特的搜索查询来深入研究该主题。
            对于每个查询，请提供研究目标。
            格式要求：每组查询使用'查询: <查询内容>' 后跟 '目标: <研究目标>'：
            
            {query}
            """
        else:
            prompt = f"""
            You are an expert researcher generating search queries.
            
            Given the following prompt, generate {num_queries} unique search queries to research the topic thoroughly. 
            For each query, provide a research goal. Format each pair as:
            Query: <query>
            Goal: <goal>
            
            {query}
            """
         
        response, _ = self.web_agent._generate_summary_r1(prompt)
        
        # Debugging output
        deeplogger.info(f"Generate search queries response: {response[:800]}")
        
        queries = []
        current_query = {}
        
        # 使用正则表达式匹配查询和目标
        pattern = r'(?:Query:|查询:)\s*"?(.*?)"?\s*(?=Goal:|目标:|Query:|查询:|$)'
        goal_pattern = r'(?:Goal:|目标:)\s*(.*?)(?=Query:|查询:|$)'
        
        query_matches = re.finditer(pattern, response, re.DOTALL)
        goal_matches = re.finditer(goal_pattern, response, re.DOTALL)
        
        for query_match, goal_match in zip(query_matches, goal_matches):
            query_text = query_match.group(1).strip()
            goal_text = goal_match.group(1).strip()
            if query_text and goal_text:  # 确保两者都不为空
                queries.append({
                    'query': query_text,
                    'researchGoal': goal_text
                })
        
        deeplogger.info(f"Parsed {len(queries)} queries: {queries}")
        
        return queries[:num_queries]

    async def generate_research_plan(self, query: str, num_questions: int = 3) -> List[str]:
        """Generate follow-up questions to clarify research direction"""
        # Get initial search results to inform query generation
        search_results, source_docs = await self.get_search_results(query)
        deeplogger.info(f"Initial web knowledge obtained: {count_text_length(search_results)}")

        # Get current time for context
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        # 根据language选择提示词语言
        if self.language == 'Chinese':
            prompt = f"""
            你是一位专业的研究人员。你的任务是分析原始查询和搜索结果，
            然后生成有针对性的问题，探索该主题的不同方面和时间段。
            
            原始查询: {query}
            
            当前时间: {current_time}
            
            搜索结果:
            {search_results}
            
            基于这些结果、原始查询和当前时间，生成{num_questions}个独特的问题。
            每个问题都应该探索该主题的不同方面或时间段，考虑到截至{current_time}的最新发展。
            
            格式要求：每个问题单独一行，以'问题: '开头
            """
        else:
            prompt = f"""
            You are an expert researcher. Your task is to analyze the original query and search results, 
            then generate targeted questions that explore different aspects and time periods of the topic.
            
            Original query: {query}
            
            Current time: {current_time}
            
            Search results:
            {search_results}
            
            Based on these results, the original query, and the current time, generate {num_questions} unique questions. 
            Each question should explore a different aspect or time period of the topic, considering recent developments up to {current_time}.
            
            Format each question on a new line starting with 'Question: '
            """
            
        deeplogger.info(f"generate_research_plan函数prompt：{prompt[:800]}")
        response, _ = self.web_agent._generate_summary(prompt)
        deeplogger.info(f"对应的response：{response[:600]}")
        
        # 根据language选择解析关键词
        question_prefix = '问题:' if self.language == 'chinese' else 'Question:'
        
        questions = [q.replace(question_prefix, '').strip()
                     for q in response.split('\n')
                     if q.strip().startswith(question_prefix)]
                     
        deeplogger.info(f"generate_research_plan函数返回：{questions[:num_questions]}")
        return questions[:num_questions]

    async def get_search_results(self, query: str) -> Tuple[str, List[str]]:
        """Get search results using get_completion"""
        # 安全处理查询参数
        safe_query = sanitize_filename(query)
        deeplogger.info(f"get_search_results: Original Query: {query[:100]}...")
        deeplogger.info(f"get_search_results: Sanitized Query: {safe_query[:100]}...")
        
        # 安全处理文档ID
        doc_ids = []
        try:
            if self.doc_ids:
                if isinstance(self.doc_ids, str):
                    doc_ids = [d.strip() for d in self.doc_ids.split(',') if d.strip()]
                elif isinstance(self.doc_ids, (list, tuple)):
                    doc_ids = [str(d).strip() for d in self.doc_ids if str(d).strip()]
                
                # 确保每个ID也是安全的文件名
                doc_ids = [sanitize_filename(doc_id) for doc_id in doc_ids]
                
            deeplogger.info(f"get_search_results: Processed Doc IDs: {doc_ids}")
        except Exception as e:
            error_trace = traceback.format_exc()
            deeplogger.error(f"Error processing doc_ids: {str(e)}\n{error_trace}")
            doc_ids = []
        
        try:
            # 创建一个带有超时的临时文件名（如果需要）
            temp_file_base = f"temp_search_{int(time.time())}"
            
            # 尝试获取搜索结果
            deeplogger.info(f"Calling get_completion for query: {safe_query[:100]}...")
            response_data = get_completion(
                api_key_rag=self.api_key_rag,
                api_key=self.api_key,
                api_key_type=self.api_key_type,
                query=query,  # 使用原始查询进行搜索
                method=self.method,
                selected_doc_ids=doc_ids,
                web_agent=self.web_agent
            )
            
            if not response_data:
                deeplogger.warning("get_completion returned None or empty response")
                return "", []
                
            source_docs = []
            if isinstance(response_data, dict):
                source_docs = response_data.get('source_docs', [])
                deeplogger.info(f"Retrieved source_docs: {len(source_docs)} documents")
                
                if 'answer' in response_data:
                    answer = response_data['answer']
                    deeplogger.info(f"get_search_results知识库函数搜索{safe_query}，返回：{count_text_length(answer)}字，内容为：{answer[:100] if answer else 'No content'}")
                    return answer, source_docs
            
            # 如果不是预期的格式，转换为字符串
            result_str = str(response_data)
            return result_str, source_docs
            
        except Exception as e:
            error_trace = traceback.format_exc()
            deeplogger.error(f"Error in get_search_results: {str(e)}\n{error_trace}")
            # 返回一个错误消息和空列表，而不是引发异常
            return f"Error retrieving search results: {str(e)}", []

    async def process_research_results(self, query: str, context: str, num_learnings: int = 3) -> Dict[str, Any]:
        """Process research results to extract learnings and follow-up questions"""
        deeplogger.info(f"Processing research results for query: {query}")
        
        # 根据language选择提示词语言
        if self.language == 'Chinese':
            prompt = f"""
            你是一位专业的研究人员，正在分析搜索结果。
            
            请根据以下关于'{query}'的研究结果，提取关键发现并提出后续问题。
            对于每个发现，如果有来源URL请包含在内。
            格式要求：
            每个发现格式为'发现 [来源网址]: <内容>'
            每个问题格式为'问题: <问题内容>'
            
            {context}
            """
        else:
            prompt = f"""
            You are an expert researcher analyzing search results.
            
            Given the following research results for the query '{query}', extract key learnings and suggest follow-up questions. 
            For each learning, include a citation to the source URL if available. 
            Format each learning as 'Learning [source_url]: <insight>' and each question as 'Question: <question>':
            
            {context}
            """

        response, _ = self.web_agent._generate_summary(prompt)

        lines = response.split('\n')
        learnings = []
        questions = []
        citations = {}

        # 根据language选择解析关键词
        learning_prefix = '发现' if self.language == 'chinese' else 'Learning'
        question_prefix = '问题:' if self.language == 'chinese' else 'Question:'

        for line in lines:
            line = line.strip()
            if line.startswith(learning_prefix):
                url_match = re.search(r'\[(.*?)\]:', line)
                if url_match:
                    url = url_match.group(1)
                    learning = line.split(':', 1)[1].strip()
                    learnings.append(learning)
                    citations[learning] = url
                else:
                    # Try to find URL in the line itself
                    url_match = re.search(
                        r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+', line)
                    if url_match:
                        url = url_match.group(0)
                        learning = line.replace(url, '').replace(f'{learning_prefix}:', '').strip()
                        learnings.append(learning)
                        citations[learning] = url
                        deeplogger.info(f"找到参考文献：{learning}，{url}")
                    else:
                        learnings.append(line.replace(f'{learning_prefix}:', '').strip())
                        deeplogger.info(f"找到参考文献：{line.replace(f'{learning_prefix}:', '').strip()}")
            elif line.startswith(question_prefix):
                questions.append(line.replace(question_prefix, '').strip())
                deeplogger.info(f"找到问题：{line.replace(question_prefix, '').strip()}")

            deeplogger.info(f"process_research_results函数返回找到学习：{learnings},找到问题：{questions},找到参考文献：{citations}")

        return {
            'learnings': learnings[:num_learnings],
            'followUpQuestions': questions[:num_learnings],
            'citations': citations
        }

    async def deep_research(
            self,
            query: str,
            breadth: int,
            depth: int,
            learnings: List[str] = None,
            citations: Dict[str, str] = None,
            visited_urls: Set[str] = None,
            on_progress=None
    ) -> Dict[str, Any]:
        """Conduct deep iterative research"""
        if learnings is None:
            learnings = []
        if citations is None:
            citations = {}
        if visited_urls is None:
            visited_urls = set()

        progress = ResearchProgress(depth, breadth)
        deeplogger.info(f"开始深度研究，当前深度：{depth}，当前广度：{breadth}")
        if on_progress:
            on_progress(progress)

        # Generate search queries
        serp_queries = await self.generate_search_queries(query, num_queries=breadth)
        
        # 如果查询生成失败，使用原始查询
        if not serp_queries:
            deeplogger.warning(f"Query generation failed, using original query: {query}")
            serp_queries = [{
                'query': query,
                'researchGoal': 'Explore the original query'
            }]
        
        progress.total_queries = len(serp_queries)
        deeplogger.info(f"generate_search_queries函数返回数：{len(serp_queries)}")
        all_learnings = learnings.copy()
        all_citations = citations.copy()
        all_visited_urls = visited_urls.copy()
        all_context = []
        all_sources = []
        all_source_docs = []
        # Process queries with concurrency limit
        semaphore = asyncio.Semaphore(self.concurrency_limit)

        async def process_query(serp_query: Dict[str, str]) -> Optional[Dict[str, Any]]:
            async with semaphore:
                try:
                    # 获取并清理查询字符串，以防止文件操作错误
                    original_query = serp_query['query']
                    safe_query = sanitize_filename(original_query)
                    
                    progress.current_query = original_query
                    if on_progress:
                        on_progress(progress)
                    
                    deeplogger.info(f"process_query函数开始，当前查询：{safe_query}")
                    
                    try:
                        # 获取搜索结果
                        context, source_docs = await self.get_search_results(original_query)
                        deeplogger.info(f"搜索结果获取成功，内容长度：{len(context) if context else 0}")
                    except Exception as e:
                        # 如果获取搜索结果失败，记录错误并返回部分结果而不是 None
                        error_msg = f"Error in get_search_results: {str(e)}"
                        error_trace = traceback.format_exc()
                        deeplogger.error(f"{error_msg}\n{error_trace}")
                        return {
                            'learnings': [],
                            'visited_urls': [],
                            'followUpQuestions': ["What are recent developments in this area?"],
                            'researchGoal': serp_query.get('researchGoal', "Research the topic"),
                            'citations': {},
                            'context': f"搜索结果获取失败，错误: {str(e)}",
                            'sources': [],
                            'source_docs': []
                        }
                    
                    try:
                        # 仅记录source_docs的长度和部分内容，避免打印整个内容
                        deeplogger.debug(f"Source docs count: {len(source_docs) if isinstance(source_docs, list) else 'not a list'}")
                        if source_docs and isinstance(source_docs, list) and len(source_docs) > 0:
                            deeplogger.debug(f"First few source docs: {source_docs[:2]}")
                    except Exception as e:
                        deeplogger.error(f"Error logging source_docs info: {str(e)}")
                    
                    deeplogger.info(f"process_query函数context：{context[:1000] if context else 'No context'}")
                    
                    # 从上下文中提取URL
                    try:
                        urls = re.findall(r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+', context) if context else []
                        visited = set(urls)
                    except Exception as url_err:
                        deeplogger.error(f"Error extracting URLs: {str(url_err)}")
                        visited = set()
                    
                    # 处理研究结果以提取学习内容和引用
                    try:
                        results = await self.process_research_results(
                            query=original_query,
                            context=context if context else ""
                        )
                    except Exception as res_err:
                        deeplogger.error(f"Error processing research results: {str(res_err)}")
                        # 如果处理结果失败，返回部分结果
                        results = {
                            'learnings': [],
                            'followUpQuestions': ["What are recent developments in this area?"],
                            'citations': {}
                        }
                    
                    # 更新进度
                    progress.completed_queries += 1
                    progress.current_breadth += 1
                    if on_progress:
                        on_progress(progress)

                    # 提取源
                    sources = []
                    try:
                        if context and "参考文献" in context:
                            source_section = context.split("参考文献")[1] if len(context.split("参考文献")) > 1 else ""
                            source_lines = source_section.split("\n")
                            for line in source_lines:
                                if "http" in line:
                                    title = line.split(":", 1)[0].strip() if ":" in line else ""
                                    url_match = re.search(r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+', line)
                                    url = url_match.group(0) if url_match else ""
                                    if url:
                                        deeplogger.info(f"找到参考文献：{title}，{url}")
                                        sources.append({"title": title, "url": url, "step": len(sources) + 1})
                    except Exception as src_err:
                        deeplogger.error(f"Error extracting sources: {str(src_err)}")

                    return {
                        'learnings': results.get('learnings', []),
                        'visited_urls': list(visited),
                        'followUpQuestions': results.get('followUpQuestions', []),
                        'researchGoal': serp_query.get('researchGoal', "Research the topic"),
                        'citations': results.get('citations', {}),
                        'context': context if context else "",
                        'sources': sources,
                        'source_docs': source_docs if source_docs else []
                    }

                except Exception as e:
                    error_trace = traceback.format_exc()
                    deeplogger.error(f"Error processing query '{serp_query.get('query', 'unknown')}': {str(e)}\n{error_trace}")
                    
                    # 返回基本结果而不是 None，以便研究可以继续进行
                    return {
                        'learnings': [],
                        'visited_urls': [],
                        'followUpQuestions': ["What are recent developments in this area?"],
                        'researchGoal': serp_query.get('researchGoal', "Research the topic"),
                        'citations': {},
                        'context': "",
                        'sources': [],
                        'source_docs': []
                    }

        # Process queries concurrently with limit
        tasks = [process_query(query) for query in serp_queries]
        results = await asyncio.gather(*tasks)
        results = [r for r in results if r is not None]

        # Update breadth progress based on successful queries
        progress.current_breadth = len(results)
        progress.completed_queries += len(results)  # 更新已完成的查询数量
        if on_progress:
            on_progress(progress)

        # Collect all results
        for result in results:
            all_learnings.extend(result['learnings'])
            all_visited_urls.update(result['visited_urls'])
            all_citations.update(result['citations'])
            if result['context']:
                all_context.append(result['context'])
            if result['sources']:
                all_sources.extend(result['sources'])
            if result['source_docs']:
                all_source_docs.extend(result['source_docs'])



            # 检查用时是否超过20分钟
            elapsed_time = time.time() - self.init_start_time
            # if elapsed_time > 1800:  # 20分钟 = 1200秒
            deeplogger.warning(f"递归用时{elapsed_time}秒")
            #     break  # 退出递归

            # Continue deeper if needed
            if depth > 1 and elapsed_time < 1800:
                new_breadth = max(2, breadth // 2)
                new_depth = depth - 1
                progress.current_depth += 1

                # Create next query from research goal and follow-up questions
                next_query = f"""
                Previous research goal: {result['researchGoal']}
                Follow-up questions: {' '.join(result['followUpQuestions'])}
                """
                deeplogger.info(f"用时{elapsed_time}秒，开始递归研究，当前问题：{next_query}，当前深度：{new_depth}，当前广度：{new_breadth}")
                # Recursive research
                deeper_results = await self.deep_research(
                    query=next_query,
                    breadth=new_breadth,
                    depth=new_depth,
                    learnings=all_learnings,
                    citations=all_citations,
                    visited_urls=all_visited_urls,
                    on_progress=on_progress
                )
                deeplogger.warning(f"递归结束，当前深度：{new_depth}，当前广度：{new_breadth}")
                all_learnings = deeper_results['learnings']
                all_visited_urls.update(deeper_results['visited_urls'])
                all_citations.update(deeper_results['citations'])
                if deeper_results.get('context'):
                    all_context.extend(deeper_results['context'])
                if deeper_results.get('sources'):
                    all_sources.extend(deeper_results['sources'])
                if deeper_results.get('source_docs'):
                    all_source_docs.extend(deeper_results['source_docs'])
        # Update class tracking
        self.context.extend(all_context)
        self.research_sources.extend(all_sources)
        self.research_source_docs.extend(all_source_docs)
        # Trim context to stay within word limits
        trimmed_context = trim_context_to_word_limit(all_context)
        deeplogger.info(f"Trimmed context from depth {depth},{len(all_context)} items to {len(trimmed_context)} items to stay within word limit")

        return {
            'learnings': list(set(all_learnings)),
            'visited_urls': list(all_visited_urls),
            'citations': all_citations,
            'context': trimmed_context,
            'sources': all_sources,
            'source_docs': all_source_docs
        }

    async def run(self, on_progress=None) -> str:
        """Run the deep research process and generate final report"""
        start_time = time.time()

        follow_up_questions = await self.generate_research_plan(self.query)
        answers = ["Automatically proceeding with research"] * len(follow_up_questions)

        qa_pairs = [f"Q: {q}\nA: {a}" for q, a in zip(follow_up_questions, answers)]
        numquery=count_text_length(self.query)
        if numquery>30:
            # 根据language选择提示词语言
            if self.language == 'Chinese':
                prompt = f"""分析以下问题{self.query}，请将该问题简化为不超过30个字，同时保持原意基本不变。"""
            else:
                prompt = f"""Analyze the following question '{self.query}' and simplify it to no more than 30 words while maintaining the original meaning."""
                
            simplequery, totaltokens = self.web_agent._generate_summary_r1(prompt)
            deeplogger.info(f"create_search_plan函数简化为：{simplequery[:150]}")
            combined_query = f"""
            Initial Query: {simplequery}\nFollow-up Questions and Answers:\n
                """ + "\n".join(qa_pairs)
        else:
            combined_query = f"""
            Initial Query: {self.query}\nFollow-up Questions and Answers:\n
            """ + "\n".join(qa_pairs)

        deeplogger.warning(f"run函数开始，初始查询：follow-up Questions and Answers:\n{combined_query}")
        results = await self.deep_research(
            query=combined_query,
            breadth=self.breadth,
            depth=self.depth,
            on_progress=on_progress
        )
        try:
            if isinstance(results['source_docs'], list):
                docs_sample = str(results['source_docs'][:3]) + "..." if len(results['source_docs']) > 3 else str(results['source_docs'])
            else:
                docs_sample = str(results['source_docs'])[:500] + "..." if len(str(results['source_docs'])) > 500 else str(results['source_docs'])
            
            deeplogger.info(f"{'='*50} source_docs (sample): {docs_sample}")
        except Exception as e:
            deeplogger.info(f"打印调试信息时出错: {str(e)}")
        deeplogger.info(f"\n\n{150*'='}\n\n results: {results}")
        deeplogger.info(f"\n\n{150*'='}\n\n deep_research函数返回结果：{results}")
        # Prepare context with citations
        context_with_citations = []
        for learning in results['learnings']:
            citation = results['citations'].get(learning, '')
            if citation:
                context_with_citations.append(f"{learning} [Source: {citation}]")
            else:
                context_with_citations.append(learning)

        # Add all research context
        if results.get('context'):
            context_with_citations.extend(results['context'])

        # Trim final context to word limit
        final_context = trim_context_to_word_limit(context_with_citations,90000)
        
        # Generate final report with language-specific prompt
        if self.language == 'Chinese':
            final_report_prompt = f"""
            你是一位专业的研究人员，负责创建一份全面的研究报告。

            请根据以下研究发现，撰写一份关于主题：{self.query} 的结构完整的报告。

            研究发现：
            {"\n\n".join(final_context)}

            你的报告应遵循以下指导原则：
            1. 包含清晰且引人入胜的引言
            2. 将发现内容按逻辑分成不同章节，并配有适当的标题
            3. 提供总结关键见解的结论部分
            4. 保持学术性的语言风格，并包含适当的引用
            5. 整合所有相关的研究发现
            6. 确保报告长度约为 {self.summary_length} 字

            请用中文撰写报告。
            """
        else:
            final_report_prompt = f"""
            You are an expert researcher tasked with creating a comprehensive research report.

            Based on the following research findings, please develop a well-structured report on the topic: {self.query}

            Research findings:
            {"\n\n".join(final_context)}

            Your report should adhere to the following guidelines:
            1. Include a clear and engaging introduction
            2. Organize findings into logical sections with appropriate headings
            3. Provide a conclusion that summarizes the key insights
            4. Maintain an academic tone and include proper citations
            5. Incorporate all relevant information from the research findings
            6. Ensure the report is approximately {self.summary_length} words long

            Please write the report in English.
            """
            
        deeplogger.info(f"final_report_prompt字数：{count_text_length(final_report_prompt)}")
        final_report, tokens_used = self.web_agent._generate_summary(final_report_prompt)
        deeplogger.info(f"final_report字数：{count_text_length(final_report)}")
        
        # End time and logging
        end_time = time.time()
        execution_time = timedelta(seconds=end_time - start_time)
        deeplogger.info(f"Total research execution time: {execution_time}")
        
        # Add sources to the report with language-specific text
        sources_text = ""
        if results['sources']:
            sources_text = "\n\n参考文献：\n" if self.language == 'Chinese' else "\n\nReferences:\n"
            for i, source in enumerate(results['sources']):
                # Get the source title
                source_title = source.get('title', 'Source')
                source_url = source.get('url', '')
                ref_num=0
                # Check if the source title contains multiple numbered references
                if re.search(r'\[\d+\].*\[\d+\]', source_title):
                    # Split by numbered reference pattern
                    ref_matches = re.findall(r'\[(\d+)\](.*?)(?=\[\d+\]|$)', source_title)
                    for ref_num, ref_content in ref_matches:
                        ref_content = ref_content.strip()
                        if ref_content:  # Only add if there's actual content
                            sources_text += f"\n\n[{ref_num}] {ref_content}"
                else:
                    # Regular single reference format
                    sources_text += f"\n\n- [{i+1+ref_num}] {source_title}: {source_url}\n"
                
        if results['source_docs']:
            source_docs = results['source_docs']
            formatted_refs = []
            # Create a set to track unique document names
            unique_doc_names = set()
            
            for i, doc in enumerate(source_docs, 1):
                # 清理文件名
                doc_name = doc.replace('.json', '').replace('.pdf', '')
                # 如果文件名包含下划线或者破折号，用空格替换
                doc_name = doc_name.replace('admin_', '').replace('-', '')
                # Only add the document if it's not already in our set
                if doc_name not in unique_doc_names:
                    unique_doc_names.add(doc_name)
                    formatted_refs.append(f"\n\n[{i}] {doc_name}")
            
            sources_text += f"\n\n本地参考文献：\n" + "\n".join(formatted_refs)
        


        
        final_report += sources_text
        # Add token usage information in the appropriate language
        token_usage_text = f"\n\n本次消耗tokens:{tokens_used}" if self.language == 'Chinese' else f"\n\nTokens used:{tokens_used}"
        final_report += token_usage_text
        
        return final_report

# 主函数，用于调用深度研究功能
async def run_deep_research(
    query: str,
    api_key_rag: str,
    api_key: str,
    api_key_type: str,
    breadth: int,
    depth: int,
    method: int,
    summary_length: int,
    language: str,
    doc_ids: Optional[List[str]] = None,
    init_start_time: float = None
) -> str:
    """
    运行深度研究功能并返回研究报告
    
    参数:
    - query: 研究查询
    - session_id: 会话ID
    - chat_id: 聊天ID
    - api_key_rag: RAG API密钥
    - api_key_deepseek: Deepseek API密钥
    - web_agent: Web代理对象，用于生成摘要
    - breadth: 研究广度（每层查询数量）
    - depth: 研究深度（递归层数）
    
    返回:
    - 研究报告文本
    """
    # 创建进度回调函数
    def progress_callback(progress: ResearchProgress):
        deeplogger.info(f"Research progress: Depth {progress.current_depth}/{progress.total_depth}, "
                   f"Breadth {progress.current_breadth}/{progress.total_breadth}, "
                   f"Queries {progress.completed_queries}/{progress.total_queries}")
        if progress.current_query:
            deeplogger.info(f"Current query: {progress.current_query}")
    
    # 创建深度研究对象
    researcher = DeepResearchSkill(
        query=query,
        api_key_rag=api_key_rag,
        api_key=api_key,
        api_key_type=api_key_type,
        breadth=breadth,
        depth=depth,
        method=method,
        summary_length=summary_length,
        language=language,
        doc_ids=doc_ids,
        concurrency_limit=2,
        init_start_time=init_start_time
    )
    
    # 运行研究并返回报告
    report = await researcher.run(on_progress=progress_callback)
    return report

# 同步包装函数，方便在非异步环境中调用
def research_topic(
    query: str,
    api_key_rag: str,
    api_key: str,
    api_key_type: str,
    breadth: int,
    depth: int,
    method: int,
    summary_length: int,
    language: str,
    doc_ids: Optional[List[str]] = None,
    init_start_time: float = None
) -> str:
    """
    同步版本的深度研究函数，用于在非异步环境中调用
    
    参数与run_deep_research相同
    """
    
    # 创建一个新的事件循环
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    
    try:
        # 在事件循环中运行异步函数
        report = loop.run_until_complete(
            run_deep_research(
                query=query,
                api_key_rag=api_key_rag,
                api_key=api_key,
                api_key_type=api_key_type,
                breadth=breadth,
                depth=depth,
                method=method,
                summary_length=summary_length,
                language=language,
                doc_ids=doc_ids,
                init_start_time=init_start_time
            )
        )
        return report
    finally:
        # 关闭事件循环
        loop.close()

# 示例使用方法

def load_user_config(username):
    with open('user_configs.json', 'r', encoding='utf-8') as f:
        configs = json.load(f)
    return configs.get(username, {})

# 新添加的文件名清理函数
def sanitize_filename(text):
    """
    清理字符串，移除或替换文件名中的非法字符
    
    Args:
        text (str): 需要清理的文本
        
    Returns:
        str: 清理后的文本，适合用作文件名
    """
    if not text:
        return "unnamed"
        
    # 替换Windows和大多数文件系统不允许的字符
    invalid_chars = r'<>:"/\|?*()[]'
    for char in invalid_chars:
        text = text.replace(char, '_')
        
    # 替换其他可能导致问题的字符
    text = text.replace('\n', ' ').replace('\r', ' ').replace('\t', ' ')
    
    # 确保文件名不会太长
    if len(text) > 100:
        text = text[:97] + "..."
        
    return text

if __name__ == "__main__":
    username = "admin"
    user_config = load_user_config(username)  
    api_key_deepseek = user_config.get('api_key_deepseek', '')
    selected_doc_ids = user_config.get('selected_doc_ids', '')
    api_key_type = user_config.get('api_key_type', 'deepseek')
    api_key_qwen = user_config.get('api_key_qwen', '')
    api_key_siliconflow = user_config.get('api_key_siliconflow', '')
    if api_key_type == "deepseek":
        api_key = api_key_deepseek
    elif api_key_type == "qwen":
        api_key = api_key_qwen
    elif api_key_type == "siliconflow":
        api_key = api_key_siliconflow
    method = user_config.get('method', 2)
    # method = 1
    summary_length = user_config.get('summary_length', 2000)
    language = user_config.get('language', 'Chinese')
    deeplogger.info(f"selected_doc_ids: {selected_doc_ids}")
    api_key_rag = user_config.get('api_key_rag', '')
    query = "Help me complete the section on 'NLO Technological Innovations Driven by 2D Material Integration.' As a comparison, first discuss the limitations of traditional bulk NLO materials: large size, phase-matching constraints, poor integration compatibility, etc."  # your test query
    history = ""  # 添加空的历史记录
    init_start_time = time.time()
    web_agent = Web_Agent(api_key=api_key, api_key_type=api_key_type)
    result=research_topic(
    query,
    api_key_rag,
    api_key,
    api_key_type,
    breadth=4,
    depth=2,
    method=method,
    summary_length=summary_length,
    language=language,
    doc_ids=selected_doc_ids,
    init_start_time=init_start_time
    )
    deeplogger.info(result)
    # doc_ids = selected_doc_ids.split(',') if isinstance(selected_doc_ids, str) else selected_doc_ids 
    # response_data = get_completion(
    #             api_key_rag=api_key_rag,
    #             api_key=api_key,
    #             query=query,
    #             method=method,
    #             selected_doc_ids=doc_ids,
    #             web_agent=web_agent
    #         )
    # deeplogger.info(response_data)   
    # deeplogger.info(selected_doc_ids)     
    # if isinstance(response_data, dict) and 'answer' in response_data:
    #     deeplogger.info(f"get_search_results联网知识库函数搜索{query}，返回：{count_text_length(response_data['answer'])}字，内容为：{response_data['answer'][:100]}")
        