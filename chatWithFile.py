import requests
import json
import os
import re
import baidusearch 
import duckduckgosearch
from getAnswer import get_summary_from_dataset, create_session,send_chat_message,get_document_chunks
import logging
import base64
import uuid
from sentence_transformers import SentenceTransformer
from typing import List, Tuple
import torch
import warnings
import PyPDF2
import time
from logger_config import log_with_request_id  # 从新模块导入
import asyncio
from deep_researcher import research_topic, sanitize_filename
from typing import Dict, Any, Set, Optional
from tool.arxiv import ArxivCrawler
from tool.proquest import ProQuestCrawler
import traceback
from logger_config import deepsearch_logger as deeplogger



base_url = "http://localhost:8080/v1/api"
api_key_model_2 = "ragflow-Y1NTkyNDBlZmZhNTExZWY5ZmNhZWUzZD"  # 模型DM
api_key_model_1 = "ragflow-Y1NTkyNDBlZmZhNTExZWY5ZmNhZWUzZD"  # 2DNM
api_key_chat="ragflow-Y1NTkyNDBlZmZhNTExZWY5ZmNhZWUzZD" #chat生成模型
# api_key_rag=api_key_model_2   #上传知识库
api_key_deepseek = "sk-izwfqiufrwdqfkvqzklcyaashpmraqaaosuchkjnettqpabb"  #deepseek
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
ALLOWED_EXTENSIONS = ['.docx', '.xlsx', '.pptx', '.jpg', '.jpeg', '.png', '.pdf', '.txt', '.md', '.json', '.eml']
# 定义用户文件记录的 JSON 文件路径
USER_FILES_JSON = os.path.join(SCRIPT_DIR, "user", "user_files.json")
RAGDOC_DIR = os.path.join(SCRIPT_DIR, "ragdoc")
DOC_IDS_JSON = os.path.join(SCRIPT_DIR, "ragdoc", "doc_ids.json")
VIDEO_BASE_PATH = os.path.join(SCRIPT_DIR, "video")
PDF_BASE_PATH=os.path.join(SCRIPT_DIR, "doc")
RESET_PERFORMED = False#初始化每次次数限制参数
USER_RAG_COUNTS_FILE = os.path.join(SCRIPT_DIR, 'user_rag_counts.json')
max_rag_uploads = 2  # 确保这个值与 manage_user_rag_count 函数中的 max_uploads 一致
dataset_id="a3870650ffa111ef92aa2a5c03e306d6" #test知识库
request_id = str(uuid.uuid4())[:8]





def should_use_full_chunks(query: str) -> bool:
    """
    判断是否需要使用完整的文档chunks而不是摘要
    
    Args:
        query: 用户查询
        
    Returns:
        bool: 是否需要使用完整chunks
    """
    # 需要全文的关键词
    full_text_keywords = {
        # 文档结构相关
        '目录', '章节', '结构', 'structure', 'contents', 'chapters',
        
        # 完整性相关
        '完整', '全文', '全部内容', 'full text', 'complete', 'entire',
        
        # 分析类型
        '分析', '总结', '概述', 'analyze', 'summarize', 'overview',
        
        # 比较相关
        '对比', '比较', '区别', 'compare', 'difference', 'versus',
        
        # 统计相关
        '统计', '计数', '多少', 'count', 'statistics', 'number of',
        
        # 提取相关
        '提取', '列举', '列出', 'extract', 'list', 'enumerate',
        
        # 特定部分
        '章', '节', '段落', 'chapter', 'section', 'paragraph',
        
        # 格式相关
        '格式', '样式', 'format', 'style', 'layout',
    }
    
    # 查询词处理
    query_lower = query.lower()
    
    # 1. 关键词匹配
    if any(keyword in query_lower for keyword in full_text_keywords):
        return True
        
    # 2. 查询长度判断（较长的查询可能需要更完整的上下文）
    query_length = count_text_length(query)
    if query_length> 50:  # 可调整阈值
        return True
        
    # 3. 问题类型判断
    question_words = {'如何', '为什么', '怎样', 'how', 'why', 'what'}
    if any(word in query_lower for word in question_words):
        return True
    
    # 4. 特殊标点符号判断（可能表示复杂查询）
    special_chars = {'?', '？', ';', '；', '、', ',', '，'}
    if any(char in query for char in special_chars):
        return True
        
    return False

def split_query_into_parts(query: str, num_parts: int = 5) -> List[str]:
    """
    将查询文本平均分成指定数量的部分
    
    Args:
        query: 要分割的查询文本
        num_parts: 要分割成的部分数量
        
    Returns:
        分割后的文本列表
    """
    # 移除多余空白并分割成句子
    sentences = [s.strip() for s in re.split('[。.；;？?！!]', query) if s.strip()]
    
    if not sentences:
        return [query]  # 如果没有可分割的句子，返回原始查询
        
    # 如果句子数量少于指定部分数，减少部分数
    num_parts = min(num_parts, len(sentences))
    
    # 计算每部分应该包含的句子数
    sentences_per_part = len(sentences) // num_parts
    remainder = len(sentences) % num_parts
    
    parts = []
    current_index = 0
    
    # 分配句子到各个部分
    for i in range(num_parts):
        # 计算当前部分应该包含的句子数
        current_count = sentences_per_part + (1 if i < remainder else 0)
        # 提取句子并组合
        part_sentences = sentences[current_index:current_index + current_count]
        parts.append('。'.join(part_sentences) + '。')
        current_index += current_count
    
    return parts

# 调用 /api/completion 获取答案
def get_completion(session_id="", chat_id="", api_key_rag="", api_key="", api_key_type="", query="", method=0, selected_doc_ids="", web_agent=None):
    deeplogger.info(f"Debug - get_completion called with method {method}")
    local_data = None
    webfullanswer = None
    request_id = str(uuid.uuid4())[:8]
    if method != 1:
        try:
            if not selected_doc_ids:
                deeplogger.info("Debug - Using chat message")
                response = ""
            
            elif len(selected_doc_ids)==1:
                deeplogger.info("Debug - Using selected document chunks")
                # 根据query判断是否需要使用完整chunks
                if should_use_full_chunks(query):  #使用第一个文件全文
                    log_with_request_id(f"使用第一个文件全文{query}", request_id=request_id)
                    deeplogger.info("Debug - Using full document chunks")
                    response = get_document_chunks(
                        api_key_rag=api_key_rag,
                        base_url="http://localhost:8080",
                        dataset_id=dataset_id,
                        document_id=selected_doc_ids[0],
                        keywords=None,
                        chunk_id=None,
                        offset=0,
                        limit=1024
                    )
                    if response and response.get('code') == 0:
                        content = response['data']['content']
                        deeplogger.info(f"\nSuccessfully retrieved full document content, length: {count_text_length(content)}")
                        deeplogger.info(f"Successfully retrieved full document content, length: {count_text_length(content)}")
                        local_data = content
                        source_docs = []
                    else:
                        deeplogger.info("\nNo valid content found")
                        deeplogger.info(f"Response: {response}")
                        local_data = "未找到有效内容"
                        source_docs = []
                else:   #使用第一个文件检索
                    deeplogger.info("Debug - Using single document summary")
                    deeplogger.info(f"使用单文件检索{query}")
                    local_data, references_docs = get_summary_from_dataset(
                        api_key_rag=api_key_rag,
                        api_key=api_key,
                        api_key_type=api_key_type,
                        question=query,
                        dataset_ids=[dataset_id],
                        document_ids=selected_doc_ids
                    )
                    source_docs = references_docs
            else:  #使用多文件检索
                    deeplogger.info(f"query字数：{query},{count_text_length(query)}")
                    deeplogger.info("Debug - Long query detected, using multi-keyword search")                  
                    # 对每个关键词进行检索
                    deeplogger.info(f"多文件检索selected_doc_ids: {selected_doc_ids}")
                    chunk, references_docs = get_summary_from_dataset(
                            api_key_rag=api_key_rag,
                            api_key=api_key,
                            api_key_type=api_key_type,
                            question=query,
                            dataset_ids=[dataset_id],
                            document_ids=selected_doc_ids
                        )
                   
                    if chunk:
                        # 使用换行符和分隔线合并chunks
                        local_data = chunk
                        source_docs = references_docs
                        deeplogger.info(f"成功检索内容，检索字数: {count_text_length(local_data)}")
                    else:
                        local_data = ""
                        source_docs = []
                        deeplogger.error(f"警告: 未检索到任何内容{chunk}")

        except Exception as e:
            deeplogger.info(f"Debug - Error in knowledge base request: {str(e)}")
            local_data = None
            source_docs = []
    if method == 1 or method == 2:
        deeplogger.info(f"请稍等正在生成在线query回答……: {query}")
        try:
            webfullanswer = web_agent.chain_of_thought_search(query)
            deeplogger.info(f"webfullanswer字数为: {count_text_length(webfullanswer.get('final_summary'))}")
            deeplogger.info(f"webfullanswer为: {webfullanswer.get('final_summary')[:300]}")
        except Exception as e:
            deeplogger.info(f"在线搜索过程中出现问题: {str(e)}")
            webfullanswer = None

    
    try:
        if method == 0 or method == 4:  # 仅本地知识库
            result = {
                'answer': local_data if local_data else "未找到本地知识库相关答案",
                'type': 'local',
                'source_docs': source_docs
            }
            
        elif method == 1:  # 仅网络搜索
            if webfullanswer and isinstance(webfullanswer, dict):
                result = {
                    'answer': webfullanswer.get('final_summary', "未找到相关网络搜索结果"),
                    'type': 'web',
                    'sources': webfullanswer.get('sources', [])
                }
            else:
                result = {
                    'answer': str(webfullanswer) if webfullanswer else "未找到相关网络搜索结果",
                    'type': 'web',
                    'sources': []
                }
                
        elif method == 2:  # 混合模式
            parts = []
            sources = []
            
            if local_data:
                parts.append(str(local_data))
            else:
                deeplogger.info("警告: local_data 为空，未能添加到结果中。")
            
            if webfullanswer and isinstance(webfullanswer, dict):
                web_text = webfullanswer.get('final_summary', '')
                if web_text:
                    parts.append(web_text)
                sources.extend(webfullanswer.get('sources', []))
            parts_text = '\n\n'.join(parts)
            deeplogger.info(f"混合模式parts字数为: {count_text_length(parts_text)}")
            deeplogger.info(f"混合模式索引结果: {parts_text[:1000]}")
            result = {
                'answer': "\n\n".join(parts) if parts else "未找到任何相关答案",
                'type': 'mixed',
                'sources': sources,
                'source_docs': source_docs
            }
                
    except Exception as e:
        deeplogger.info(f"警告: 结果合并过程中出现错误: {str(e)}")
        result = {
            'answer': "处理搜索结果时发生错误",
            'type': 'error',
            'sources': []
        }
    log_with_request_id(f"检索结束....................", request_id=request_id)
    return result


def count_words(text):
    # Split English words by whitespace and count them
    english_words = len([word for word in text.split() if any(c.isalpha() for c in word)])
    # Count Chinese characters
    chinese_chars = sum(1 for char in text if '\u4e00' <= char <= '\u9fff')
    return english_words + chinese_chars

def truncate_text(content: str, max_words: int = 70000) -> str:
    """
    Truncate text to a maximum number of words/characters, handling both English and Chinese text.
    
    Args:
        content (str): The text content to truncate
        max_words (int): Maximum number of words/characters allowed (default: 20000)
    
    Returns:
        str: Truncated text
    """
    if not content:
        return ""
        
    current_count = 0
    result_chars = []
    
    for char in content:
        # 所有字符都保留并计数
        if '\u4e00' <= char <= '\u9fff':  # 中文字符
            current_count += 1
        elif char.isspace():  # 空格
            # 检查是否是英文单词的分隔符
            if result_chars and result_chars[-1].isascii():
                current_count += 1
        else:  # 所有其他字符（包括英文、特殊字符等）
            if not result_chars or not result_chars[-1].isascii():
                current_count += 1
                
        if current_count > max_words:
            break
            
        result_chars.append(char)
    truncatetext=''.join(result_chars)
    log_with_request_id(f"合并后检索内容长度: {current_count}, summary_prompt:{truncatetext[:2000]}", level=logging.WARNING, request_id=request_id)
    return truncatetext

def should_include_diagram_guidelines(query: str) -> bool:
    """
    检查查询中是否包含需要生成图表的关键词
    
    Args:
        query (str): 用户查询
        
    Returns:
        bool: 是否需要包含图表生成指南
    """
    diagram_keywords = {
        # 基础图表关键词
        '流程图', '流程', 'flow', 'flowchart',
        '时序图', '序列图', 'sequence', 'sequence diagram',
        '类图', 'class diagram',
        '状态图', 'state diagram',
        '关系图', 'relationship', 'er diagram',
        '思维导图', 'mindmap', 'mind map',
        '时间线', 'timeline', '甘特图', 'gantt',
        '框图', '模块图', 'block diagram',
        '图表', '图解', 'diagram',
        '饼图', 'pie',
        '用户旅程', 'journey',
        '可视化', 'visualize', 'visualization',
        # 知识图谱相关关键词
        '知识图谱', '知识图', 'knowledge graph',
        '概念图', 'concept map',
        '语义网络', 'semantic network',
        '本体', 'ontology',
        '关系网络', 'relation network',
        '实体关系', 'entity relationship',
        '知识网络', 'knowledge network'
    }
    
    query_lower = query.lower()
    return any(keyword in query_lower for keyword in diagram_keywords)
def get_diagram_guidelines(query: str) -> str:
    """
    根据查询类型返回相应的图表生成指南
    """
    # 基础指南
    base_guidelines = """
    Guidelines for your response:
    1. Use a neutral, journalistic tone and report facts as presented in the content.
    2. Include Mermaid diagrams between ```mermaid and ``` tags.
    3. Mermaid diagrams use English punctuation regardless of language.
    """
    
    # 根据关键词匹配具体的图表类型指南
    if any(word in query.lower() for word in ['流程', 'flow', 'process', '步骤', 'step']):
        return base_guidelines + """
    3. Create a flowchart using this format:
        ```mermaid
        flowchart LR
            A[Hard] -->|Text| B(Round)
            B --> C{Decision}
            C -->|One| D[Result 1]
            C -->|Two| E[Result 2]
        ```
        Flowchart guidelines:
        - Direction: TB (top to bottom), BT, RL, LR
        - Node shapes:
          - [] for rectangular nodes
          - () for round nodes
          - {} for diamond nodes (decisions)
          - ([]) for stadium-shaped nodes
          - [[]] for subroutine nodes
          - [()] for cylindrical nodes
          - (()), circle nodes
        - Arrows:
          - --> for arrows
          - --- for lines without arrows
          - -.-> for dotted lines
          - ==> for thick lines
        - Add text on arrows using |text|
        """

        
    elif any(word in query.lower() for word in ['时序', 'sequence', '交互', 'interaction']):
        return base_guidelines + """
    3. Create a sequence diagram using this format:
        ```mermaid
        sequenceDiagram
            Alice->>John: Hello John, how are you?
            loop HealthCheck
                John->>John: Fight against hypochondria
            end
            Note right of John: Rational thoughts!
            John-->>Alice: Great!
            John->>Bob: How about you?
            Bob-->>John: Jolly good!
        ```
        Sequence diagram guidelines:
        - Participants: Use participant or actor
        - Arrows:
          - ->> for solid arrow
          - -->> for dotted arrow
          - -x for lost message
        - Notes:
          - Note right of [Actor]
          - Note left of [Actor]
          - Note over [Actor1,Actor2]
        - Loops and Alt:
          - loop [text]
          - alt [text]
          - opt [text]
        """
    elif any(word in query.lower() for word in ['思维导图', 'mindmap', 'mind map', '知识图谱', '概念图']):
        return base_guidelines + """
    3. Create a mindmap using this format:
        ```mermaid
        mindmap
            root((Central Topic))
                Main Topic 1
                    Subtopic 1.1
                    Subtopic 1.2
                        Detail 1.2.1
                        Detail 1.2.2
                Main Topic 2
                    Subtopic 2.1
                    Subtopic 2.2
                Main Topic 3
                    Subtopic 3.1
                        Detail 3.1.1
                    Subtopic 3.2
        ```
        Mindmap guidelines:
        - Root node: (()) for circle shape, [] for square, or no shape
        - Indentation defines hierarchy levels
        - Node shapes:
          - No shape (default)
          - ((text)) for circle
          - [text] for square
          - (text) for rounded rectangle
        - Content limitations:
          - LaTeX math formulas are not supported in nodes
          - Use quotes for text containing special characters
          - For complex math formulas, use text descriptions in nodes
            and provide the formulas separately
        - Best practices:
          - Keep structure balanced and clear
          - Use concise labels
          - Organize topics logically
          - Maximum recommended depth: 4-5 levels
        """  
    elif any(word in query.lower() for word in ['甘特', 'gantt', '进度', 'schedule']):
        return base_guidelines + """
    3. Create a Gantt chart using this format:
        ```mermaid
        gantt
            title A Gantt Diagram
            dateFormat  YYYY-MM-DD
            section Section
            A task           :a1, 2014-01-01, 30d
            Another task     :after a1  , 20d
            section Another
            Task in sec      :2014-01-12  , 12d
            another task      : 24d
        ```
        Gantt chart guidelines:
        - Define sections using section keyword
        - Task syntax: [Task name] : [Task Id], [Start date], [Duration]
        - Duration formats: d (days), h (hours), m (minutes), s (seconds)
        - Dependencies: after [Task Id]
        - States: done, active, crit
        """        
    elif any(word in query.lower() for word in ['类图', 'class', '结构', 'structure']):
        return base_guidelines + """
    3. Create a class diagram using this format:
        ```mermaid
        classDiagram
            Animal <|-- Duck
            Animal <|-- Fish
            Animal : +int age
            Animal : +String gender
            Animal: +isMammal()
            Animal: +mate()
            class Duck{
                +String beakColor
                +swim()
                +quack()
            }
            class Fish{
                -int sizeInFeet
                -canEat()
            }
        ```
        Class diagram guidelines:
        - Relationships:
          - <|-- for inheritance
          - *-- for composition
          - o-- for aggregation
          - --> for association
        - Access modifiers:
          - + public
          - - private
          - # protected
          - ~ package/internal
        - Methods and attributes in class
        """



    elif any(word in query.lower() for word in ['状态', 'state', '转换', 'transition']):
        return base_guidelines + """
    3. Create a state diagram using this format:
        ```mermaid
        stateDiagram-v2
            [*] --> Still
            Still --> [*]
            Still --> Moving
            Moving --> Still
            Moving --> Crash
            Crash --> [*]
        ```
        State diagram guidelines:
        - [*] represents start/end state
        - --> for transitions
        - Use state keyword for complex states
        - Composite states:
          state fork_state <<fork>>
          state join_state <<join>>
        - Notes using note left/right of
        """
        

    elif any(word in query.lower() for word in ['饼图', 'pie', '占比', 'percentage']):
        return base_guidelines + """
    3. Create a pie chart using this format:
        ```mermaid
        pie
            title What Voldemort doesn't have?
            "Nose" : 45.2
            "Wand" : 34.1
            "Body" : 15.6
            "Soul" : 5.1
        ```
        Pie chart guidelines:
        - Title is optional
        - Each slice: "Label" : value
        - Values determine slice size
        - Keep it simple and readable
        """
    elif any(word in query.lower() for word in ['用户旅程', 'journey', '体验', 'experience']):
        return base_guidelines + """
    3. Create a user journey diagram using this format:
        ```mermaid
        journey
            title My working day
            section Go to work
              Make tea: 5: Me
              Go upstairs: 3: Me
              Do work: 1: Me, Cat
            section Go home
              Go downstairs: 5: Me
              Sit down: 3: Me
        ```
        Journey diagram guidelines:
        - Title using title keyword
        - Sections using section keyword
        - Tasks: [Task name]: [Score]: [Actors]
        - Score range: 1-5
        - Multiple actors separated by comma
        """

    elif any(word in query.lower() for word in ['时间线', 'timeline', '历程', '发展']):
        return base_guidelines + """
    3. Create a timeline diagram using this format:
        ```mermaid
        timeline
            title 发展历程
            section 阶段1
                事件1: 描述
                事件2: 描述
            section 阶段2
                事件3: 描述
                事件4: 描述
        ```
        Timeline guidelines:
        - Organize events chronologically
        - Group events into sections
        - Add clear descriptions
        - Keep time progression clear
        """
    
    # 如果没有匹配到具体类型，返回通用指南
    return base_guidelines + """
    3. Choose the most appropriate diagram type:
        - Flowchart: 流程图，用于展示过程和决策
        - Sequence Diagram: 时序图，展示对象间的交互
        - Class Diagram: 类图，展示面向对象的结构
        - State Diagram: 状态图，展示状态转换
        - Entity Relationship Diagram: 实体关系图，数据库设计
        - User Journey: 用户旅程图，展示用户体验
        - Gantt: 甘特图，项目进度管理
        - Pie Chart: 饼图，展示数据占比
        - Quadrant Chart: 象限图，展示分类矩阵
        - Requirement Diagram: 需求图，展示系统需求
        - Git Graph: Git分支图，版本控制
        - C4 Diagram: C4架构图，系统架构设计
        - Mindmap: 思维导图，概念组织
        - Timeline: 时间线，展示时间进程
        - ZenUML: UML时序图，详细交互设计
        - Sankey: 桑基图，流向和数量关系
        - XY Chart: XY图，数据关系可视化
        - Block Diagram: 区块图，系统结构
        - Packet Diagram: 数据包图，网络通信
        - Kanban: 看板图，任务管理
        
        Each diagram type has its own syntax and best practices.
        Choose based on what you're trying to communicate.
    """

def count_text_length(text: str) -> int:
    """
    计算文本的有效长度，区分中英文
    
    Args:
        text: 输入文本
        
    Returns:
        int: 文本的有效长度(中文字符数 + 英文单词数)
    """
    # 分离中文和非中文部分
    chinese_chars = 0
    english_text = []
    current_word = []
    
    for char in text:
        if '\u4e00' <= char <= '\u9fff':  # 中文字符范围
            # 如果之前有英文单词，保存它
            if current_word:
                english_text.append(''.join(current_word))
                current_word = []
            chinese_chars += 1
        elif char.isalpha():  # 英文字母
            current_word.append(char)
        else:  # 空格、标点等
            if current_word:  # 结束当前英文单词
                english_text.append(''.join(current_word))
                current_word = []
    
    # 处理最后一个英文单词
    if current_word:
        english_text.append(''.join(current_word))
    
    # 计算英文单词数
    english_words = len([word for word in english_text if word])
    
    return chinese_chars + english_words

class HistorySearcher:
    """历史内容语义搜索器"""
    
    def __init__(self):
        # 加载多语言语义模型
        self.model = SentenceTransformer('paraphrase-multilingual-mpnet-base-v2')
        
    def split_into_chunks(self, text: str, chunk_size: int = 500) -> List[str]:
        """
        将文本分割成适当大小的块，考虑中英文混合情况
        
        Args:
            text: 要分割的文本
            chunk_size: 每个块的最大长度(中文字符 + 英文单词)
            
        Returns:
            分割后的文本块列表
        """
        # 首先按段落分割
        paragraphs = [p.strip() for p in text.split('\n\n') if p.strip()]
        
        chunks = []
        current_chunk = []
        current_length = 0
        
        for para in paragraphs:
            para_length = count_text_length(para)
            
            # 如果段落本身超过chunk_size，则按句子分割
            if para_length > chunk_size:
                sentences = [s.strip() for s in para.split('。') if s.strip()]
                for sent in sentences:
                    sent_length = count_text_length(sent)
                    if current_length + sent_length > chunk_size:
                        if current_chunk:
                            chunks.append('。'.join(current_chunk))
                            current_chunk = []
                            current_length = 0
                    current_chunk.append(sent)
                    current_length += sent_length
            else:
                if current_length + para_length > chunk_size:
                    chunks.append('。'.join(current_chunk))
                    current_chunk = []
                    current_length = 0
                current_chunk.append(para)
                current_length += para_length
        
        # 处理最后一个chunk
        if current_chunk:
            chunks.append('。'.join(current_chunk))
            
        return chunks

    def compute_similarity(self, query: str, chunks: List[str]) -> List[Tuple[float, str]]:
        """
        计算查询与各个文本块的相似度
        
        Args:
            query: 查询文本
            chunks: 文本块列表
            
        Returns:
            列表of(相似度, 文本块)元组，按相似度降序排序
        """
        # 确保有文本块要处理
        if not chunks:
            return []
            
        # 编码查询和文本块
        query_embedding = self.model.encode(query, convert_to_tensor=True)
        chunks_embeddings = self.model.encode(chunks, convert_to_tensor=True)
        
        # 计算余弦相似度
        similarities = torch.nn.functional.cosine_similarity(
            query_embedding.unsqueeze(0), 
            chunks_embeddings
        )
        
        # 将相似度和文本块配对并排序
        scored_chunks = list(zip(similarities.tolist(), chunks))
        scored_chunks.sort(reverse=True)
        
        return scored_chunks

def extract_relevant_history(history: str, query: str, top_k: int = 3, min_similarity: float = 0.3) -> str:
    """
    从历史记录中提取与查询最相关的片段
    
    Args:
        history: 历史记录文本
        query: 查询文本
        top_k: 返回的最相关片段数量
        min_similarity: 最小相似度阈值
        
    Returns:
        相关历史片段的组合文本
    """
    if not history or not query:
        return ""
        
    try:
        # 创建搜索器实例
        searcher = HistorySearcher()
        
        # 将历史记录分割成块
        history_chunks = searcher.split_into_chunks(history)
        
        # 计算相似度并获取最相关的块
        scored_chunks = searcher.compute_similarity(query, history_chunks)
        
        # 筛选相似度超过阈值的相关块
        relevant_chunks = [
            chunk for score, chunk in scored_chunks 
            if score >= min_similarity
        ][:top_k]
        
        # 如果没有找到相关内容，返回空字符串
        if not relevant_chunks:
            return ""
            
        # 组合相关块，添加分隔符
        return "\n\n".join(relevant_chunks)
        
    except Exception as e:
        deeplogger.info(f"Warning: Error in extracting relevant history: {str(e)}")
        return ""

def create_lastsummary_prompt(content: str, query: str, language: str, summary_length: str, history: str = "") -> str:
    """
    Create a comprehensive prompt combining context analysis and formatting requirements
    
    Args:
        content (str): Current content
        query (str): User query
        language (str): Output language
        summary_length (str): Expected response length
        history (str, optional): Conversation history
    """
    # 提取相关历史内容
    relevant_history = extract_relevant_history(history, query) if history else ""
    
    # Truncate content
    truncated_content = truncate_text(content) if content else ""
    
    base_prompt = f"""
    Analyze the following information and provide a detailed response in {language}. Format your response using proper Markdown syntax:

    Current Question: 
    {query}

    {"Reference Content:\n" + truncated_content if truncated_content else ""}
    
    {'Relevant Historical Context:\n' + relevant_history if relevant_history else ''}

    Follow this chain of analytical thinking:
    1. Identify key concepts and components in the question
    2. Analyze relationships between these concepts
    3. Determine what background knowledge is necessary
    4. Establish the most logical order for presenting information
    5. Consider which examples or analogies would aid explanation
    6. Evaluate which visual aids (diagrams) or mathematical expressions would enhance understanding

    Response Requirements:
    1. Use proper Markdown formatting:
       - Use # for main headings
       - Use ## for subheadings
       - Use - or * for bullet points
       - Use 1. 2. 3. for numbered lists
       - Use **text** for bold emphasis
       - Use *text* for italic emphasis
       - Use > for blockquotes
       - Use --- for horizontal rules
       - Use [text](link) for links
       - Use proper line breaks between sections
    2. Minimum length: {summary_length} words
    3. Prioritize information from current content
    4. Supplement with historical context where appropriate
    5. Maintain coherence and completeness

    Structure your response as follows:
    1. Start with a main heading (#) for overview
    2. Use subheadings (##) for major sections:
    3. Under each section, organize content appropriately using:
       - Lists where needed
       - Bold text for emphasis
       - Mathematical expressions when required
       - Diagrams if helpful

    Mathematical Expression Guidelines:
    1. Use single $ for inline math, double $$ for display math
    2. Prefix Greek letters and math functions with \\: $\\alpha$, $\\beta$, $\\sin(x)$
    3. Use _ for subscripts, ^ for superscripts: $H_2O$, $e^{{x^2}}$
    4. Format fractions as: $\\frac{{num}}{{den}}$
    5. Format integrals as: $\\int_0^\\infty e^{{-x}} dx$
    6. Format sums and products as: $\\sum_{{i=1}}^n i^2$

    Important Notes:
    - Never use code block formatting (```) for regular text content
    - Only use code blocks for actual code or diagram syntax
    - Ensure proper spacing between sections (use blank lines)
    - Use appropriate heading levels (# ## ###)
    - If the response is in Chinese, use appropriate technical terminology with English terms in parentheses where helpful
    - Format all mathematical expressions within $ or $$ delimiters
    - Use Mermaid diagram syntax only within ```mermaid code blocks
    """
    
    # Add diagram guidelines if needed
    if should_include_diagram_guidelines(query):
        return base_prompt + get_diagram_guidelines(query)    
    return base_prompt
def manage_user_submit_count(username, method=0, increment=False):
    submit_count_file = 'user_submit_counts.json'
    base_max_submits = 5
    extra_submits = 50 if method == 1 else 0
    max_submits = base_max_submits + extra_submits

    # 如果文件不存在，创建一个空的 JSON 文件
    if not os.path.exists(submit_count_file):
        with open(submit_count_file, 'w') as f:
            json.dump({}, f)

    # 读取当前的提交次数
    with open(submit_count_file, 'r', encoding='utf-8') as f:
        submit_counts = json.load(f)

    # 如果用户不在记录中，初始化为0
    if username not in submit_counts or username=="admin":
        submit_counts[username] = {'count': 0, 'method': method}
    elif submit_counts[username]['method'] != method:
        # 如果用户切换了方法，重置计数
        submit_counts[username] = {'count': 0, 'method': method}

    # 如果需要增加计数
    if increment:
        submit_counts[username]['count'] += 1

    # 写回文件
    with open(submit_count_file, 'w', encoding='utf-8') as f:
        json.dump(submit_counts, f)

    # 返回剩余的提交次数
    return max_submits - submit_counts[username]['count']



def is_allowed_file(filename):
    return os.path.splitext(filename)[1].lower() in ALLOWED_EXTENSIONS


def update_user_files(username, filename):
    os.makedirs(os.path.dirname(USER_FILES_JSON), exist_ok=True)
    user_files = {}
    
    if os.path.exists(USER_FILES_JSON):
        try:
            with open(USER_FILES_JSON, 'r', encoding='utf-8') as f:
                content = f.read()
                if content:
                    user_files = json.loads(content)
                else:
                    deeplogger.info(f"警告: {USER_FILES_JSON} 是空文件")
        except json.JSONDecodeError:
            deeplogger.info(f"警告: {USER_FILES_JSON} 不是有效的 JSON 格式，将创建新的文件")
        except Exception as e:
            deeplogger.info(f"读取 {USER_FILES_JSON} 时发生错误: {str(e)}")
    
    if username not in user_files:
        user_files[username] = []

    if filename not in user_files[username]:
        user_files[username].append(filename)

    try:
        with open(USER_FILES_JSON, 'w', encoding='utf-8') as f:
            json.dump(user_files, f, indent=2)
    except Exception as e:
        deeplogger.info(f"写入 {USER_FILES_JSON} 时发生错误: {str(e)}")

def get_user_files_with_doc_ids(username, limit=9):
    user_files = []
    doc_ids = {}
    files_with_doc_ids = {}

    # 读取 user_files.json
    if os.path.exists(USER_FILES_JSON):
        try:
            with open(USER_FILES_JSON, 'r', encoding='utf-8') as f:
                user_data = json.load(f)
                user_files = user_data.get(username, [])
        except json.JSONDecodeError:
            deeplogger.info(f"警告: {USER_FILES_JSON} 不是有效的 JSON 格式")
        except Exception as e:
            deeplogger.info(f"读取 {USER_FILES_JSON} 时发生错误: {str(e)}")

    # 读取 doc_ids.json
    if os.path.exists(DOC_IDS_JSON):
        # 使用检测到的编码读取文件
        with open(DOC_IDS_JSON, 'r', encoding='utf-8') as f:
             doc_ids = json.load(f)
    # 筛选出有 doc_id 的文件，并创建文件名到 doc_id 的映射
    for file in reversed(user_files):  # 从最新的文件开始
        if file in doc_ids:
            files_with_doc_ids[file] = doc_ids[file]
            if len(files_with_doc_ids) == limit:  # 只保留最新的 limit 个文件
                break
    return files_with_doc_ids



#记录用户上传rag次数
def manage_user_rag_count(username, increment=False):
    try:
        # 如果文件不存在，创建一个空的 JSON 文件
        if not os.path.exists(USER_RAG_COUNTS_FILE):
            with open(USER_RAG_COUNTS_FILE, 'w', encoding='utf-8') as f:
                json.dump({}, f, ensure_ascii=False)
            logging.info(f"Created new file: {USER_RAG_COUNTS_FILE}")

        # 读取当前的上传次数
        with open(USER_RAG_COUNTS_FILE, 'r', encoding='utf-8') as f:
            upload_counts = json.load(f)

        # 如果用户不在记录中，初始化为0
        if username not in upload_counts or username=="admin":
            upload_counts[username] = 0
            logging.info(f"Initialized count for user: {username}")

        # 如果需要增加计数
        if increment:
            upload_counts[username] = min(upload_counts[username] + 1, max_rag_uploads)
            logging.info(f"Incremented count for user: {username}. New count: {upload_counts[username]}")

        # 写回文件
        with open(USER_RAG_COUNTS_FILE, 'w', encoding='utf-8') as f:
            json.dump(upload_counts, f, indent=2, ensure_ascii=False)
        logging.info(f"Updated {USER_RAG_COUNTS_FILE}")

        # 返回当前的上传次数
        current_count = upload_counts[username]
        logging.info(f"Current upload count for {username}: {current_count}")
        return current_count

    except Exception as e:
        logging.error(f"Error in manage_user_rag_count: {str(e)}")
        return 0  # 如果出错，返回0

def extract_references(response_data):
    references = {}
    if isinstance(response_data, dict) and "data" in response_data:
        reference_data = response_data["data"].get("reference", {})
        # 如果 reference_data 为空，直接返回空的 references
        if not reference_data:
            return references
        chunks = reference_data.get("chunks", [])
        for chunk in chunks:
            doc_name = chunk.get("doc_name", "")
            positions = chunk.get("positions", [])
            content = chunk.get("content", "")
            similarity = chunk.get("similarity", 0)
            
            # 处理位置信息
            processed_positions = positions if isinstance(positions, list) else [positions]
            
            if doc_name:
                if doc_name not in references:
                    references[doc_name] = {
                        "positions": processed_positions,
                        "content": [content],
                        "similarity": similarity
                    }
                else:
                    references[doc_name]["positions"].extend(processed_positions)
                    references[doc_name]["content"].append(content)
                    references[doc_name]["similarity"] = max(references[doc_name]["similarity"], similarity)
    
    return references

def load_user_config(username):
    with open('user_configs.json', 'r', encoding='utf-8') as f:
        configs = json.load(f)
    return configs.get(username, {})
def create_translation_prompt(query: str) -> str:
    return f"""
    Translate the following query into English:
    "{query}"   
    Ensure that the output contains only the translated result, without any additional text or commentary.
    """

def format_references(references):
    if not references:
        return ""

    formatted_output = []
    ragdoc_BASE_PATH = os.path.join(SCRIPT_DIR, "ragdoc")
    
    for doc_name, ref_data in references.items():
        file_path = os.path.join(ragdoc_BASE_PATH, doc_name)
        file_path = file_path.replace("\\", "/")
        
        if os.path.isfile(file_path):
            with open(file_path, "rb") as f:
                bytes = f.read()
                b64 = base64.b64encode(bytes).decode()
                href = f'<a href="data:application/octet-stream;base64,{b64}" download="{doc_name}">{doc_name}</a>'
        else:
            href = doc_name
        
        # 提取并格式化页码
        positions = ref_data.get('positions', [])
        if positions and isinstance(positions[0], list):  # 如果positions是嵌套列表
            pages = set(pos[0] for pos in positions if pos)  # 提取每个位置列表的第一个元素作为页码
        elif positions and isinstance(positions[0], (int, float)):  # 如果positions是数字列表
            pages = set(positions)
        else:
            pages = set()
        
        page_str = ", ".join(map(str, sorted(pages))) if pages else "N/A"
        
        formatted_output.append(f'<div style="font-size: 14px; margin-bottom: 10px;">')
        formatted_output.append(f'<strong>文档:</strong> {href} <strong>页码:</strong> {page_str}')
        formatted_output.append('</div>')
        formatted_output.append('<hr>')  # 添加分隔线
    
    return "\n".join(formatted_output)
    # 检测输入文本的语言
def is_chinese(text):
    # 简单检查是否包含中文字符
    return any('\u4e00' <= char <= '\u9fff' for char in text)
def analyze_paper_section(text: str) -> str:
    """
    分析论文段落属于哪个部分
    """
    # 定义各节特征关键词
    section_keywords = {
        'abstract': [
            'abstract', 'summary', 'overview', 'highlights', 
            '摘要', '概要', '概述', '总览',
            'this paper', 'this study', 'this research',
            '本文', '本研究', '本实验'
        ],
        
        'introduction': [
            'introduce', 'background', 'overview', 'motivation', 'context',
            'literature review', 'state of the art', 'state-of-the-art',
            'previous work', 'prior work', 'related work',
            'research gap', 'significance', 'importance',
            'objective', 'purpose', 'aim', 'goal',
            '介绍', '引言', '背景', '综述', '动机',
            '文献综述', '研究现状', '相关工作',
            '研究意义', '研究目的', '研究目标',
            '研究空白', '创新点'
        ],
        
        'methodology': [
            'method', 'procedure', 'experiment', 'approach', 'protocol',
            'experimental', 'measurement', 'technique', 'algorithm',
            'setup', 'apparatus', 'equipment', 'instrument',
            'sample', 'specimen', 'preparation', 'processing',
            'calculation', 'analysis method', 'analytical method',
            '方法', '实验', '步骤', '流程', '工艺',
            '测量', '技术', '算法', '装置', '设备',
            '仪器', '样品', '制备', '处理', '计算',
            '分析方法', '实验设计', '实验条件'
        ],
        
        'results': [
            'result', 'data', 'finding', 'observation', 'measurement',
            'analysis', 'characterization', 'performance', 'evaluation',
            'figure', 'table', 'plot', 'graph', 'curve',
            'experimental result', 'numerical result', 'statistical analysis',
            '结果', '数据', '发现', '观察', '分析',
            '表征', '性能', '评估', '图', '表',
            '曲线', '实验结果', '数值结果', '统计分析',
            '测试结果', '表现', '特性'
        ],
        
        'discussion': [
            'discuss', 'implication', 'suggest', 'compare', 'interpret',
            'mechanism', 'explanation', 'hypothesis', 'theory',
            'comparison', 'correlation', 'relationship', 'trend',
            'analysis', 'insight', 'perspective', 'viewpoint',
            'possible reason', 'potential cause', 'contribution',
            '讨论', '比较', '建议', '解释', '机理',
            '假设', '理论', '关系', '趋势', '分析',
            '见解', '观点', '原因', '贡献', '影响',
            '机制', '推测', '解释'
        ],
        
        'conclusion': [
            'conclude', 'summary', 'future work', 'outlook',
            'recommendation', 'perspective', 'implication',
            'limitation', 'challenge', 'future direction',
            'further study', 'further research', 'future research',
            '总结', '结论', '展望', '建议', '局限性',
            '挑战', '未来方向', '进一步研究', '研究展望',
            '启示', '意义', '贡献度'
        ],
        
        'acknowledgment': [
            'acknowledge', 'acknowledgement', 'acknowledgment', 'thank',
            'gratitude', 'appreciation', 'funding', 'support',
            'grant', 'sponsor', 'financial support',
            '致谢', '感谢', '基金', '资助', '支持',
            '项目资助', '基金资助'
        ],
        
        'references': [
            'reference', 'bibliography', 'citation', 'cited',
            'literature', 'publication', 'author',
            '参考文献', '引用', '文献', '作者'
        ],
        
        'supplementary': [
            'supplementary', 'supplemental', 'supporting',
            'appendix', 'additional', 'extra',
            '补充材料', '附录', '补充信息', '支持材料'
        ]
    }
    
    text_lower = text.lower()
    for section, keywords in section_keywords.items():
        if any(keyword in text_lower for keyword in keywords):
            return section
    return 'general'

def get_section_weights(section: str) -> tuple[str, str]:
    """
    根据论文部分返回参考资料和历史对话的权重
    """
    weights = {
        'abstract': ('Medium', 'High'),      # 摘要需要概括全文，更需要上下文连贯
        'introduction': ('High', 'Medium'),  # 引言需要大量参考文献支持
        'methodology': ('High', 'Low'),      # 方法部分主要依赖专业知识
        'results': ('High', 'Low'),          # 结果呈现需要准确的专业表达
        'discussion': ('Medium', 'High'),    # 讨论部分需要更多上下文和连贯性
        'conclusion': ('Medium', 'High'),    # 结论需要与前文保持一致
        'acknowledgment': ('Low', 'High'),   # 致谢部分主要依赖上下文
        'references': ('High', 'Low'),       # 参考文献格式需要严格遵守规范
        'supplementary': ('High', 'Low'),    # 补充材料需要准确的专业表达
        'general': ('Medium', 'Medium')      # 默认平衡处理
    }
    return weights.get(section, ('Medium', 'Medium'))

def get_section_specific_guidance(section: str) -> str:
    """
    根据论文部分返回具体的润色指导
    """
    guidance = {
        'abstract': """
        - Ensure concise yet comprehensive summary
        - Highlight key findings and significance
        - Maintain proper length and focus
        - Include essential research components
        """,        
        'introduction': """
        - Emphasize the research background and significance
        - Strengthen literature review connections
        - Highlight research gaps and objectives
        """,
        'methodology': """
        - Focus on technical accuracy and clarity
        - Ensure reproducibility of methods
        - Use precise technical terminology
        """,
        'results': """
        - Maintain objective presentation of findings
        - Enhance data interpretation clarity
        - Use appropriate statistical terminology
        """,
        'discussion': """
        - Strengthen argument development
        - Improve comparison with existing literature
        - Enhance interpretation of implications
        """,
        'conclusion': """
        - Reinforce main findings and contributions
        - Improve future work suggestions
        - Strengthen research significance
        """,
        'acknowledgment': """
        - Maintain appropriate formal tone
        - Ensure all contributors are properly credited
        - Follow funding agency requirements
        """,
        'references': """
        - Ensure consistent citation format
        - Verify citation accuracy
        - Follow journal guidelines
        """,
        'supplementary': """
        - Maintain technical accuracy
        - Ensure clear organization
        - Provide sufficient methodological details
        """,        
        'general': """
        - Improve overall academic expression
        - Enhance logical flow and coherence
        - Maintain consistent terminology
        """
    }
    return guidance.get(section, guidance['general'])

def get_priority_principle(ref_weight: str, hist_weight: str) -> str:
    """
    根据权重返回优先级原则
    """
    if ref_weight == 'High' and hist_weight == 'Low':
        return "Prioritize technical accuracy and reference knowledge integration while maintaining consistent terminology"
    elif ref_weight == 'Medium' and hist_weight == 'High':
        return "Focus on argument development and contextual consistency while incorporating reference knowledge"
    else:
        return "Balance technical accuracy with contextual coherence"
def create_polish_prompt(query: str, local_content: str, history: str) -> str:
    paper_section = analyze_paper_section(query)
    reference_weight, history_weight = get_section_weights(paper_section)
    
    truncated_local_content = truncate_text(local_content)
    truncated_history = truncate_text(history)
    target_language = "Chinese" if is_chinese(query) else "English"
    
    return f"""
    As an academic writing expert, you are tasked with polishing and enhancing academic text. Before proceeding with the task, follow this chain of thought:

    1. Analysis Phase:
        - Identify the text's academic section type: {paper_section}
        - Assess the writing style and current academic level
        - Note key terminology and technical concepts
        - Evaluate the logical flow and argument structure
        - Consider the target audience and publication context

    2. Resource Evaluation:
        - Reference Knowledge (Priority: {reference_weight}): Analyze how to integrate relevant technical information
        - Conversation History (Priority: {history_weight}): Consider context continuity
        - Section-specific requirements: Review the special focus areas for this section type

    3. Enhancement Strategy:
        - Determine which aspects need the most attention
        - Plan how to maintain balance between technical accuracy and readability
        - Consider how to preserve the author's voice while improving expression
        - Identify opportunities for structural improvements

    Now, proceed with the polishing task using the following information:

    Text to polish:
    {query}

    Reference knowledge:
    {truncated_local_content}

    Related conversation history:
    {truncated_history}

    Section-specific polishing focus:
    {get_section_specific_guidance(paper_section)}

    Polishing principles:
    1. Maintain the original paragraph structure and basic framework
    2. {get_priority_principle(reference_weight, history_weight)}
    3. Ensure logical coherence and scientific accuracy
    4. Optimize language expression to better align with academic standards
    5. The output MUST be in {target_language}, exactly matching the language of the input text

    After completing your analysis, provide only the polished text without explanations or comments.
    """

# def clean_summary_text(text: str) -> str:
#     """
#     清理文本首尾的代码框符号，保留中间内容
    
#     Args:
#         text (str): 原始文本        
#     Returns:
#         str: 清理后的文本
#     """
#     # 去除首尾空白
#     cleaned = text.strip()
    
#     # 处理文本开头的代码块标记
#     if cleaned.startswith('```'):
#         # 找到第一个代码块的结束位置
#         first_block_end = cleaned.find('\n', 3)
#         if first_block_end != -1:
#             # 去除开头的```和语言标识符这一行
#             cleaned = cleaned[first_block_end + 1:].strip()
#         else:
#             # 如果没有换行，直接去除开头的```
#             cleaned = cleaned[3:].strip()
    
#     # 处理文本结尾的代码块标记
#     if cleaned.endswith('```'):
#         # 从后向前找到最后一个换行
#         last_block_start = cleaned.rfind('\n', 0, -3)
#         if last_block_start != -1:
#             # 去除最后一个换行后的内容（包括```）
#             cleaned = cleaned[:last_block_start].strip()
#         else:
#             # 如果没有换行，直接去除结尾的```
#             cleaned = cleaned[:-3].strip()
    
#     return cleaned


def clean_markdown_indentation(text: str) -> str:
    """
    清理Markdown文本中的段首空格，保持代码块内部的缩进
    
    Args:
        text (str): 原始文本
        
    Returns:
        str: 处理后的文本，非代码块区域的段首空格被移除
        
    Example:
        Input:
            "    Some text\n    ```python\n    def func():\n        pass\n    ```\n    # Title"
        Output:
            "Some text\n```python\ndef func():\n    pass\n```\n# Title"
    """
    if not text:
        return text
        
    lines = text.splitlines()
    cleaned_lines = []
    in_code_block = False
    
    # 列表项的正则模式
    list_patterns = [
        r'^\s*[-*+]\s+\S',  # 无序列表: "- item", "* item", "+ item"
        r'^\s*\d+\.\s+\S'   # 有序列表: "1. item", "2. item" 等
    ]
    
    for line in lines:
        # 检查是否遇到代码块标记
        if line.lstrip().startswith('```'):
            in_code_block = not in_code_block
            # 代码块标记本身不需要缩进
            cleaned_lines.append(line.lstrip())
            continue
            
        if in_code_block:
            # 在代码块内部，保持原有格式
            cleaned_lines.append(line)
        else:
            stripped_line = line.lstrip()
            # 特殊处理标题行，确保#前没有空格
            if stripped_line.startswith('#'):
                cleaned_lines.append(stripped_line)
            # 特殊处理列表项（保持缩进层级）
            elif any(re.match(pattern, line) for pattern in list_patterns):
                # 保持列表的缩进层级
                indent_level = len(line) - len(stripped_line)
                # 如果缩进大于2个空格，保留适当的缩进（每级2个空格）
                if indent_level > 4:
                    indent = ' ' * ((indent_level // 2) * 2)
                    cleaned_lines.append(indent + stripped_line)
                else:
                    cleaned_lines.append(stripped_line)
            else:
                # 其他所有内容去除段首空格
                cleaned_lines.append(stripped_line)
    
    return '\n'.join(cleaned_lines)









def process_query(method, deep_search_enabled, net_method, query_source, api_key, 
                  api_key_type, language, summary_length, selected_doc_ids, session_id, 
                  chat_id, api_key_rag, query, history, max_results: int= 5, enable_thinking: bool=False):
    # 生成请求ID
    # 添加初始日志，这可能给API调用提供了必要的延迟
    log_with_request_id("Starting query processing", request_id=request_id)
    total_tokens=0
    if not query:
        log_with_request_id("Query is empty", level=logging.WARNING, request_id=request_id)
        return "警告: 输入内容为空"

    # 设置 WebAgentClass 之前添加日志
    log_with_request_id(f"Setting up WebAgent with method: {net_method}", request_id=request_id)
    
    if net_method == "duckduckgo":
        WebAgentClass = duckduckgosearch.Web_Agent
    else:  
        WebAgentClass = duckduckgosearch.Web_Agent

    # API初始化之前添加日志
    log_with_request_id(f"Initializing WebAgent with query_source: {query_source}", request_id=request_id)
    
    if query:
        if query_source == "用户输入":
            query_length=25
            web_agent1 = WebAgentClass(api_key=api_key, api_key_type=api_key_type,summary_length=summary_length,language=language)            
            log_with_request_id("WebAgent initialized for user input", request_id=request_id)
                    
        elif query_source == "英文翻译":
            query_length=100
            translation_prompt = create_translation_prompt(query)
            web_agent1 = WebAgentClass(api_key=api_key, api_key_type=api_key_type,summary_length=summary_length,language=language)
            query, totaltokens = web_agent1._generate_summary(translation_prompt) 
            total_tokens+=totaltokens  
            log_with_request_id("WebAgent initialized for translation", request_id=request_id)
    else:
        return
    

    deeplogger.info(f"deep_search_enabled: {deep_search_enabled}")
    deeplogger.info(f"method: {method}")
    if deep_search_enabled and method == 0:
        try:
            init_start_time = time.time()
            
            # 安全处理 doc_ids
            if isinstance(selected_doc_ids, str):
                # 移除任何可能的非法字符
                clean_doc_ids = selected_doc_ids.replace('[', '').replace(']', '').replace('"', '').replace("'", "")
                doc_ids = [doc_id.strip() for doc_id in clean_doc_ids.split(',') if doc_id.strip()]
            else:
                doc_ids = selected_doc_ids if selected_doc_ids else []
                
            log_with_request_id(f"深度搜索的处理文档IDs: {doc_ids}", request_id=request_id)
            
            # 特别记录所有参数，帮助调试
            debug_params = {
                "query": query[:100] + "..." if len(query) > 100 else query,
                "api_key_rag": api_key_rag[:5] + "..." if api_key_rag else None,
                "api_key": api_key[:5] + "..." if api_key else None,
                "api_key_type": api_key_type,
                "method": method,
                "doc_ids": doc_ids,
                "language": language,
                "summary_length": summary_length
            }
            log_with_request_id(f"深度搜索传入的参数: {debug_params}", request_id=request_id)
            
            # 调用 research_topic 函数
            result = research_topic(
                query,  
                api_key_rag,
                api_key=api_key,
                api_key_type=api_key_type,
                breadth=4,
                depth=2,
                method=method,
                summary_length=summary_length,
                language=language,
                doc_ids=doc_ids,
                init_start_time=init_start_time
            )
            
            if not result or result.strip() == "":
                log_with_request_id("警告：research_topic 返回了空结果", level=logging.WARNING, request_id=request_id)
                return "深度研究完成，但未能生成有效的研究报告。这可能是由于API错误或连接问题导致的。请稍后再试或联系管理员。"
            
            deeplogger.info(f"lastresponse: {result[:200]}...")
            return result
            
        except Exception as e:
            error_trace = traceback.format_exc()
            log_with_request_id(f"深度搜索过程中出现错误: {str(e)}\n{error_trace}", level=logging.ERROR, request_id=request_id)
            return f"在执行深度研究过程中发生错误: {str(e)}。请联系管理员或稍后再试。"



    #arxiv论文分析
    if method == 5:
        try:
            log_with_request_id(f"{"=="*20}\n开始论文搜索,获取论文数量为{max_results}",level=logging.INFO,request_id=request_id)
            
            # 创建ArxivCrawler实例
            crawler = ArxivCrawler(
                proxy="http://192.168.0.2:1070",
                query=query,
                api_key=api_key,
                api_key_type=api_key_type,
                language=language,
                summary_length=summary_length,
                max_results=max_results,
                top_k=4
            )  
            if deep_search_enabled: 
                # 使用asyncio.run运行异步函数
                arxiv_result, references, totaltokens = asyncio.run(crawler.process_complex_query(query))
            else:
                # 使用asyncio.run运行异步函数
                arxiv_result, references, totaltokens = asyncio.run(crawler.generate_combined_answer())
            total_tokens+=totaltokens
            arxiv_result += f"\n\n{references}\n" 
            arxiv_result += f"\n总共消耗的token: {total_tokens}" 
            return arxiv_result      
        except Exception as e:
            logging.error(f"论文分析失败: {str(e)}", exc_info=True)
            raise



    #arxiv论文分析
    if method == 6:
        try:
            log_with_request_id(f"{"=="*20}\n开始论文搜索,获取论文数量为{max_results}",level=logging.INFO,request_id=request_id)
            
            # 创建ArxivCrawler实例
            crawler = ProQuestCrawler(
                proxy="http://192.168.0.2:1070",
                query=query,
                api_key=api_key,
                api_key_type=api_key_type,
                language=language,
                summary_length=summary_length,
                max_results=max_results,
                top_k=9
            )  
            if deep_search_enabled: 
                # 使用asyncio.run运行异步函数
                proquest_result, references, totaltokens = asyncio.run(crawler.process_complex_query(query))
            else:
                # 使用asyncio.run运行异步函数
                proquest_result, references, totaltokens = asyncio.run(crawler.generate_combined_answer())
            total_tokens+=totaltokens
            # 修复错误: 处理 proquest_result 可能为 None 的情况
            if proquest_result is None:
                proquest_result = "无法获取有效的搜索结果。请尝试使用不同的搜索关键词。"
            
            # 确保 references 也不是 None
            if references is None:
                references = ""
                
            proquest_result += f"\n\n{references}\n" 
            proquest_result += f"\n总共消耗的token: {total_tokens}" 
            return proquest_result      
        except Exception as e:
            logging.error(f"论文分析失败: {str(e)}", exc_info=True)
            raise





    if method != 3:
        try:
            log_with_request_id(f"Processing document final IDs: method {method}", request_id=request_id)
            doc_ids = selected_doc_ids.split(',') if isinstance(selected_doc_ids, str) else selected_doc_ids            
            log_with_request_id(f"Processing document final IDs: {doc_ids}", request_id=request_id)
            deeplogger.info(f"Processing document final IDs: {doc_ids}")            
            response_data = get_completion(session_id, chat_id, api_key_rag, api_key, api_key_type, query, method, doc_ids, web_agent1)
            log_with_request_id(f"得到的检索内容结构体字数: {count_text_length(str(response_data))}", request_id=request_id)
            log_with_request_id(f"得到的检索内容结构体: {str(response_data)}", request_id=request_id)
            source_docs = response_data.get('source_docs', [])
            if source_docs:
                    formatted_refs = []
                    # 使用集合来跟踪已经添加过的文档名
                    unique_doc_names = set()
                    for i, doc in enumerate(source_docs, 1):
                        # 清理文件名
                        doc_name = doc.replace('.json', '').replace('.pdf', '')
                        # 如果文件名包含下划线或者破折号，用空格替换
                        doc_name = doc_name.replace('_', ' ').replace('-', ' ')
                        # 只有当文档名不在已处理过的集合中时，才添加到格式化引用列表
                        if doc_name not in unique_doc_names:
                            unique_doc_names.add(doc_name)
                            formatted_refs.append(f"\n\n[{i}] {doc_name}")

            # log_with_request_id(f"response_data的结构: {response_data}", request_id=request_id)
            # 使用log_with_request_id记录详细参数
            debug_info = f"""
            Debug - All process parameters:
            Method: {method}
            Message: {query}
            Net method: {net_method}
            Query source: {query_source}
            API key deepseek: {api_key_deepseek[:5]}... (length: {len(api_key_deepseek) if api_key_deepseek else 0})
            Language: {language}
            Summary length: {summary_length}
            Selected doc ids: {doc_ids}
            Session ID: {session_id}
            Chat ID: {chat_id}
            API key RAG: {api_key_rag[:5]}... (length: {len(api_key_rag) if api_key_rag else 0})
            Query length: {len(query) if query else 0}
            History length: {len(history) if history else 0}
            """
            

    
            if method == 4:  # 添加对method 4的特殊处理,论文润色
                local_polish = response_data.get('answer', '') if response_data and response_data.get('type') in ['local', 'mixed'] else ''
                log_with_request_id(f"参考索引字数:{count_text_length(local_polish)}，内容为:{local_polish[:100]}", level=logging.WARNING, request_id=request_id)  
                polish_prompt = create_polish_prompt(query, local_polish, history)
                log_with_request_id(f"polish_prompt字数为{count_text_length(polish_prompt)}，内容为:{polish_prompt}", level=logging.WARNING, request_id=request_id)
                polished_result, totaltokens = web_agent1._generate_summary(polish_prompt, enable_thinking)  
                total_tokens+=totaltokens
                log_with_request_id(f"polished_result字数为{count_text_length(polished_result)}，内容为:{polished_result[:100]}", level=logging.WARNING, request_id=request_id)             
                # 清理结果中的代码块标记
                cleaned_result = polished_result
                # 移除 "Polished Text:" 前缀
                if cleaned_result.startswith('Polished Text:'):
                    cleaned_result = cleaned_result.replace('Polished Text:', '', 1)
                if cleaned_result.startswith('Polished text:'):
                    cleaned_result = cleaned_result.replace('Polished text:', '', 1)  
                # 清理代码块和标题前的空格
                cleaned_result = clean_markdown_indentation(cleaned_result)
                # 格式化参考索引
                if source_docs:              
                    cleaned_result = f"{cleaned_result}\n\n参考索引：\n" + "\n".join(formatted_refs)
                
                cleaned_result += f"\n\n本次消耗tokens:{total_tokens}"
                return cleaned_result.strip()
            
            # 根据 response_data 的新结构调整处理方式
            if method == 1:  # 仅网络搜索
                web_content = response_data.get('answer', '')
                log_with_request_id(f"联网生成的答案web_content: {web_content}", request_id=request_id)
                combined_text = f"{web_content}"
                sources = response_data.get('sources', [])
                totaltokens=response_data.get('total_tokens', 0)
            elif method == 2:  # 混合模式
                combined_text = f"{response_data.get('answer', '')}"
                totaltokens=response_data.get('total_tokens', 0)               
                total_tokens+=totaltokens
                sources = response_data.get('sources', [])
            else:  # 仅本地知识库
                log_with_request_id(f"Processing document method1: {method}", request_id=request_id)
                combined_text = f"{response_data.get('answer', '')}"
                references=[]
                sources = []

            if not combined_text.strip():
                return "警告1chatwithfile: 没有找到相关的内容。"
            log_with_request_id(f"Processing document combined_text: {combined_text[:100]}", request_id=request_id)
            # 生成最终摘要
            if method != 1:
                log_with_request_id(f"Processing document laet method: {method}", request_id=request_id)
                summary_prompt = create_lastsummary_prompt(combined_text, query, language, summary_length, history)                
                log_with_request_id(f"最终提示词summary_prompt长度: {count_text_length(summary_prompt)}, summary_prompt:{summary_prompt[:100]}", level=logging.WARNING, request_id=request_id)
                summary_context, totaltokens = web_agent1._generate_summary(summary_prompt, enable_thinking)
                total_tokens+=totaltokens
                log_with_request_id(f"成功生成答案字数: {count_text_length(summary_context)}", request_id=request_id)
                # 清理代码块和标题前的空格
                summary_context = clean_markdown_indentation(summary_context)  
            else:
                # 清理代码块和标题前的空格
                combined_text = clean_markdown_indentation(combined_text)
                summary_context = combined_text


            # 处理参考文献
            combined_info_with_numbers = ""
            if sources:
                for source in sources:
                    combined_info_with_numbers += f"- [{source['step']}] {source['title']}: {source['url']}\n"

            # 拼接最终结果
            final_output = summary_context

            if combined_info_with_numbers:
                final_output += f"\n\n在线参考文献：\n{combined_info_with_numbers}"
            if  source_docs:
                final_output +=f"\n\n本地参考文献：\n\n" + "\n\n".join(formatted_refs)
            final_output += f"\n\n本次消耗tokens:{total_tokens}"
            log_with_request_id(f"Processing document 传到后端结果final_output: {final_output[:500]}", request_id=request_id)
            return final_output

        except Exception as e:
            traceback_str = traceback.format_exc()
            deeplogger.info(f"Debug - Error in get_completion: {str(e)}")
            deeplogger.info(f"Traceback: {traceback_str}")
            return f"警告: 获取答案时发生错误 - {str(e)}"
    else:
        # method == 3 的处理保持不变，聊天模式。
        log_with_request_id(f"Processing document method: {method}", request_id=request_id)
        combined_text = ""
        chat_prompt = create_lastsummary_prompt(combined_text, query, language, summary_length, history)
        log_with_request_id(f"Processing document chat_prompt: {chat_prompt[:500]}", request_id=request_id)
        chat_result, total_tokens = web_agent1._generate_summary(chat_prompt, enable_thinking) 
        # 清理代码块和标题前的空格
        chat_result = clean_markdown_indentation(chat_result)
        chat_result += f"\n\n 本次消耗tokens:{total_tokens}"
        log_with_request_id(f"Processing document chat_result3: {chat_result[:500]}", request_id=request_id)
        return chat_result



###############################################################################################################
#分段书写，未完成
###############################################################################################################



###############################################################################################################

def to_bool(val):
    if isinstance(val, bool):
        return val
    if isinstance(val, str):
        return val.lower() == "true"
    if isinstance(val, int):
        return val != 0
    return False

# 使用示例
if __name__ == "__main__":
    username = "admin"
    user_config = load_user_config(username)  
    # 从用户配置中获取 process_query 所需参数
    # 从用户配置中获取 process_query 所需的参数
    method = user_config.get('method', 3)  # 默认值为3
    net_method = user_config.get('net_method', 'duckduckgo')
    query_source = user_config.get('query_source', '用户输入')
    api_key_deepseek = user_config.get('api_key_deepseek', '')
    api_key_siliconflow = user_config.get('api_key_siliconflow', '')
    api_key_qwen = user_config.get('api_key_qwen', '')
    api_key_type = user_config.get('api_key_type', '')
    api_key = api_key_deepseek if api_key_type == "deepseek" else api_key_siliconflow if api_key_type == "siliconflow" else api_key_qwen if api_key_type == "qwen" else ""
    language = user_config.get('language', 'Chinese')
    summary_length = user_config.get('summary_length', 500)
    selected_doc_ids = user_config.get('selected_doc_ids', '')
    deeplogger.info(f"selected_doc_ids: {selected_doc_ids}")
    session_id = user_config.get('conversation_id', '')
    api_key_rag = user_config.get('api_key_rag', '')
    deep_search_enabled = bool(int(user_config.get('deep_search_enabled', 0)))
    enable_thinking = to_bool(user_config.get('enable_thinking', False))   
   
    deeplogger.info(f"enable_thinking: {enable_thinking}")
    message ="""
      根据论文全文内容，作为一个论文评审专家，请指出论文存在的所有错误和不当之处，包括格式错误和内容的错误，给出错误所在的章节。
      """  # your test query
    history = ""  # 添加空的历史记录
    chat_id_test="a3870650ffa111ef92aa2a5c03e306d6" #test_chat
    remaining_submits = manage_user_submit_count(username, method=method, increment=True)
    deeplogger.info(f"剩余提交次数: {remaining_submits}")               
    chatfile_history = ""   
    response = process_query(method, deep_search_enabled, net_method, query_source, api_key, api_key_type, language, 
                                 summary_length, selected_doc_ids, session_id, chat_id_test, api_key_rag, message, chatfile_history, enable_thinking)
       
    deeplogger.info(f"lastresponse: {response}")
    # query="""西安天气怎么样"""
    # WebAgentClass = duckduckgosearch.Web_Agent
    # web_agent = WebAgentClass(api_key=api_key_deepseek, api_key_type=api_key_type,summary_length=summary_length)   
    # results = web_agent.chain_of_thought_search(query)

    # if results['final_summary']:
    #     deeplogger.info("\n最终结果：")
    #     deeplogger.info(results['final_summary'])
        
    #     if results['sources']:
    #         deeplogger.info("\n信息来源：")
    #         for source in results['sources']:
    #             deeplogger.info(f"- [{source['step']}] {source['title']}: {source['url']}")
    # else:
    #     deeplogger.info("未能获取到搜索结果")