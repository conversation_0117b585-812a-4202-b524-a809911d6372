<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>∑∫ LaTeX 公式编辑器</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 90%;
            margin: 0 auto;
            padding: 20px;
            background-color: #FFFBE6;
        }
        
        h2 {
            color: #5A4A1C;
            text-align: center;
            margin-bottom: 20px;
            font-weight: 500;
        }
        
        .title-icon {
            font-size: 1.3em;
            margin-right: 5px;
            vertical-align: middle;
        }
        
        .latex-container {
            width: 100%;
            height: 90vh;
            border: 1px solid #F3E2A9;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        }
        
        iframe {
            width: 100%;
            height: 100%;
            border: none;
        }
        
        .loading-message {
            text-align: center;
            padding: 20px;
            font-size: 16px;
            color: #8B7355;
        }
        
        .math-icon {
            font-family: "Times New Roman", serif;
            font-weight: bold;
            font-style: italic;
        }
    </style>
</head>
<body>
    <h2>
        🧮 开源 LateX公式编辑器 - [妈叔出品]
    </h2>
    
    <div class="latex-container">
        <div class="loading-message" id="loadingMessage">
            <span style="font-size: 1.5em;">⏳</span> 正在加载 LaTeX 编辑器，请稍候...
        </div>
        <iframe 
            id="latexEditor" 
            src="https://www.latexlive.com/home" 
            title="LaTeX 公式编辑器"
            onload="hideLoading()">
        </iframe>
    </div>

    <script>
        function hideLoading() {
            document.getElementById('loadingMessage').style.display = 'none';
        }
        
        // 如果iframe加载失败，显示错误消息
        window.addEventListener('error', function(e) {
            if (e.target.tagName === 'IFRAME') {
                document.getElementById('loadingMessage').innerHTML = 
                    '<span style="font-size: 1.5em;">⚠️</span> 无法加载 LaTeX 编辑器，请检查网络连接或直接访问 <a href="https://www.latexlive.com/home" target="_blank">LaTeX Live</a>';
                document.getElementById('loadingMessage').style.display = 'block';
            }
        }, true);
        
        // 在页面标题中也添加数学图标
        document.title = "∑∫ LaTeX 公式编辑器";
    </script>
</body>
</html> 