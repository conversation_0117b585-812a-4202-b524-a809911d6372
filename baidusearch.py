import requests
from typing import List, Tuple
from bs4 import BeautifulSoup
import chardet
import os
import re
import time
DEBUG = os.environ.get('DEBUG') == 'True'
class Web_Agent:
    def __init__(self, api_key, num_results=3, max_tokens=4000, model="deepseek-chat", temperature=0.5, comprehension_grade=14, summary_length=600, full_summary_length=600, query_length_threshold=10):
        self.api_key = api_key
        self.num_results = num_results
        self.max_tokens = max_tokens
        self.model = model
        self.temperature = temperature
        self.comprehension_grade = comprehension_grade
        self.summary_length = summary_length
        self.query_length_threshold = query_length_threshold  # New variable
        self.full_summary_length = full_summary_length
    
    
    
    def search_baidu(self, query, num_results=10):
        """Search Baidu for the given query and return the results."""
        ResponseEntry = Tuple[str, str, str,str]
        results =self.WebSearch_Tool(query, num_results)
        result_list: List[ResponseEntry] = []  # type: ignore
        for result in results:
            result_list.append((result["title"], result["url"], result["description"],result["keyword"]))
        return result_list



    def WebSearch_Tool(self, query: str, num_results: int = 3):
        if len(query) > self.query_length_threshold:
            keywords = self._generate_keywords(query)
            print(f"Generated keywords for query '{query}':")
            print(", ".join(keywords))
            print("-" * 50)
            
            all_search_results = []
            for keyword in keywords:
                print(f"Searching for keyword: '{keyword}'")
                results = self._perform_search(keyword, num_results=1)
                all_search_results.extend(results)
                print(f"Found {len(results)} results for '{keyword}'")
                print("-" * 30)
            
            print(f"Total results found: {len(all_search_results)}")
            return all_search_results
        else:
            print(f"Query '{query}' is below threshold. Performing direct search.")
            return self._perform_search(query, num_results)

    def _generate_keywords(self, query: str, max_attempts: int = 3) -> List[str]:
        # Initial prompt
        prompt = f"""Extract the most important keywords from the following query. 
        Provide the keywords separated by commas, without any additional text or explanations. 
        Include all relevant terms, regardless of the number of keywords: '{query}'"""
        
        for attempt in range(max_attempts):
            response = self._generate_summary(prompt)
            keywords = [keyword.strip() for keyword in response.split(',') if keyword.strip()]
            
            if keywords:  # As long as we have at least one keyword
                return keywords
            
            # If no keywords were extracted, update prompt for correction
            prompt = f"""The previous response did not provide any keywords. 
            Please extract all important keywords from the query, separated by commas, 
            without any additional text. Query: '{query}'"""
        
        # If max attempts reached, use a fallback method
        print(f"Failed to generate valid keywords after {max_attempts} attempts. Using fallback method.")
        return self._fallback_keyword_generation(query)

    def _fallback_keyword_generation(self, query: str) -> List[str]:
        # Simple fallback method: split query into words and remove common stop words
        stop_words = set(['a', 'an', 'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by'])
        words = query.lower().split()
        return [word for word in words if word not in stop_words]

    def _perform_search(self, query: str, num_results: int) -> List[dict]:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Referer': 'https://www.baidu.com/',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }
        search_url = f"https://www.baidu.com/s?wd={query}&rn={num_results}"
        self.log_debug(f"Search URL: {search_url}")

        try:
            response = requests.get(search_url, headers=headers, timeout=10)
            response.raise_for_status()
            soup = BeautifulSoup(response.text, 'html.parser')

            search_results = []
            for g in soup.find_all('div', class_='result'):
                anchor = g.find('a')
                title = g.find('h3').text if g.find('h3') else 'No title'
                url = anchor.get('href', 'No URL') if anchor else 'No URL'
                
                description = ''
                description_span = g.find('div', class_='c-abstract')
                if description_span:
                    description = description_span.get_text(strip=True)
                else:
                    description = g.get_text(strip=True)

                search_results.append({
                    'title': title,
                    'description': description,
                    'url': url,
                    'keyword':query
                })
                            
            if DEBUG:
                print(f"Successfully retrieved {len(search_results)} search results for query: {query}")
                print(f"Search results preview: {search_results[:5]}")

            return search_results

        except requests.RequestException as e:
            error_message = f"Error performing search for query '{query}': {str(e)}"
            if DEBUG:
                print(error_message)
            return []

    def log_debug(self, message):
        if DEBUG:
            print(message)



    def summarize_content(self, content: str, url: str, keyword: str) -> dict:
        """Summarize the content using DeepSeek API."""
        urlcontent = self.fetch_url_content(url)


        if urlcontent is None:
            # 处理获取 URL 内容失败的情况
            print(f"Failed to fetch content from URL: {url}")
            urlcontent = ""  # 使用空字符串作为后备
        else:
            urlcontent = urlcontent.replace('\n', '')


        summary_prompt = self._create_summary_prompt(content, urlcontent, keyword)
        # print(summary_prompt)
        summary = self._generate_summary(summary_prompt)
        # print(summary)
        return self._format_summary(summary, url)




    def fetch_url_content(self, url: str) -> str:
        """Fetch content from a URL and return the text."""
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        try:
            response = requests.get(url, headers=headers)
            response.raise_for_status()
            # 使用 chardet 检测编码格式
            detected_encoding = chardet.detect(response.content)['encoding']
            response.encoding = detected_encoding
            soup = BeautifulSoup(response.text, 'html.parser')
         
            # Remove script and style elements
            for script in soup(["script", "style"]):
                script.decompose()

            text = soup.get_text()

            # Break into lines and remove leading and trailing space on each
            lines = (line.strip() for line in text.splitlines())
            # Break multi-headlines into a line each
            chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
            # Drop blank lines
            text = '\n'.join(chunk for chunk in chunks if chunk)

            if DEBUG:
                print(f"Successfully retrieved content from {url}")
                print(f"Content preview: {text[:4000]}...")

            return text

        except requests.RequestException as e:
            error_message = f"Error retrieving content from {url}: {str(e)}"
            if DEBUG:
                print(error_message)
            return None

    def _create_summary_prompt(self, content: str, url: str, keyword: str) -> str:
        grade_descriptions = {
            1: "a 6-year-old in 1st grade", 2: "a 7-year-old in 2nd grade", 3: "an 8-year-old in 3rd grade",
            4: "a 9-year-old in 4th grade", 5: "a 10-year-old in 5th grade", 6: "an 11-year-old in 6th grade",
            7: "a 12-year-old in 7th grade", 8: "a 13-year-old in 8th grade", 9: "a 14-year-old in 9th grade",
            10: "a 15-year-old in 10th grade", 11: "a 16-year-old in 11th grade", 12: "a 17-year-old in 12th grade",
            13: "a college undergraduate", 14: "a master's degree student", 15: "a PhD candidate"
        }
        grade_description = grade_descriptions.get(self.comprehension_grade, "an average adult")

        return f"""

        Summarize the following web content from {url[:5000]}, {content[:3000]} for {grade_description}:
          # Limit content to first 4000 characters

        Your task is to provide a comprehensive and informative summary of the main content of the {keyword}., along with an SEO-optimized headline. Follow these guidelines:

        1. Generate an SEO-optimized headline that:
        - Captures user interest without sensationalism
        - Accurately represents the main topic
        - Uses relevant keywords
        - Is concise (ideally 50-60 characters)
        - Maintains professionalism
        
        2. Format your headline exactly as follows:
        HEADLINE: [Your SEO-optimized headline here]

        3. Write your summary using the inverted pyramid style:
        - Start with a strong lede (opening sentence) that entices readers and summarizes the most crucial information
        - Present the most important information first
        - Follow with supporting details and context
        - End with the least essential information

        4. Adjust the language complexity strictly targeted to the reading level for {grade_description}. This means:
        - Use vocabulary appropriate for this comprehension level
        - Adjust sentence structure complexity accordingly
        - Explain concepts in a way that would be clear to someone at this educational level
        - Do not specifically mention the target's age or grade level in the summary response

        5. Clearly explain the main topic or discovery being discussed
        6. Highlight key points, findings, or arguments presented in the content
        7. Provide relevant context or background information that helps understand the topic
        8. Mention any significant implications, applications, or future directions discussed
        9. If applicable, include important quotes or statistics that support the main points
        10. Do not omit the statistics and summarization of the parameter values that appear in the text.
        Your summary should be approximately {self.summary_length} words long. Use a neutral, journalistic tone, and ensure that you're reporting the facts as presented in the content, not adding personal opinions or speculation.

        Format your response as follows:
        HEADLINE: [Your SEO-optimized headline here]

        [Your comprehensive summary here, following the inverted pyramid style]
        """

    def _preprocess_content(self, content: str) -> str:
        # 移除可能的敏感内容或特殊字符
        content = re.sub(r'[^\w\s.,;!?]', '', content)
        # 限制内容长度
        return content[:1000]  # 例如，限制为1000字符





    def _generate_summary(self, prompt: str) -> str:
        print("Prompt sent to API:")
        print(prompt[:500])  # 打印提示的前500个字符
        print("-" * 50)
        url = "https://api.deepseek.com/beta/completions"
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        data = {
            "model": self.model,
            "prompt": self._preprocess_content(prompt),
            "max_tokens": self.max_tokens,
            "temperature": self.temperature
        }

        
        max_retries = 1
        for attempt in range(max_retries):
            try:
                response = requests.post(url, headers=headers, json=data)
                response.raise_for_status()
                response_json = response.json()
                # print(f"API Response (attempt {attempt + 1}):")
                # print(json.dumps(response_json, indent=2))
                # print("-" * 50)
                
                if 'choices' in response_json and len(response_json['choices']) > 0:
                    summary = response_json['choices'][0]['text'].strip()
                    # print("Generated summary:")
                    # print(summary[:500])  # 打印摘要的前500个字符
                    return summary
                else:
                    print(f"Unexpected API response: {response_json}")
                    return "API response does not contain 'choices' or 'text' key."
            except requests.RequestException as e:
                # print(f"Error in API request (attempt {attempt + 1}): {str(e)}")
                if hasattr(e.response, 'text'):
                    print(f"Response content: {e.response.text}")
                if attempt == max_retries - 1:
                   return f"Error generating summary after {max_retries} attempts: {str(e)}"
                time.sleep(1)  # 在重试之前等待1秒
 


    def _format_summary(self, summary: str, url: str) -> dict:
        # Split the summary into headline and body
        parts = summary.split('\n', 1)
        if len(parts) == 2 and parts[0].startswith('HEADLINE:'):
            headline = parts[0].replace('HEADLINE:', '').strip()
            body = parts[1].strip()
        else:
            # If no headline is found, use the first sentence as the headline
            sentences = summary.split('. ')
            headline = sentences[0].strip()
            body = '. '.join(sentences[1:]).strip()

        # If the headline is empty or still "Summary of Web Content", generate a generic one
        if not headline or headline == "Summary of Web Content":
            headline = f"Summary of {url.split('//')[1].split('/')[0]}"

        return {
            "title": headline,
            "url": url,
            "description": body
        }

    def process_request(self, user_request: str) -> list:
        """Process the user request and return summarized results."""
        search_results = self.search_baidu(user_request, num_results=self.num_results)
        summarized_results = []
        for title, url, description, keyword in search_results:
            summary = self.summarize_content(description, url, keyword)
            print("-*" * 80 + '\n')
            print(summary)
            print("-*" * 80 + '\n')
            summarized_results.append(summary)
        return summarized_results









    def create_summary_prompt_full(self, content: str, query: str) -> str:
        return f"""
        Refer to the following content:
        {content[:8000]}  # Limit content to first 8000 characters
        Provide a detailed answer to the query: {query}
        
        Guidelines for your response:
        1. Your answer should be at least {self.full_summary_length} words long.
        2. If the query includes specific requirements for the response, prioritize meeting those requirements.
        3. Use a neutral, journalistic tone.
        4. Ensure you're reporting facts as presented in the content, without adding personal opinions or speculation.
        5. If the content doesn't provide enough information to reach {self.full_summary_length} words, focus on thoroughly explaining the available information and indicate areas where more details could be beneficial.
        """

    def process_request_full(self, query: str) -> dict:
        results = self.process_request(query)
        # print(results)
        combined_description = "\n\n".join([item['description'] for item in results if item['description']])
        summary_prompt = self.create_summary_prompt_full(combined_description, query)
        print(f"summary_prompt:{summary_prompt}")
        url = "https://api.deepseek.com"
        # summary_prompt=self._format_summary(summary_prompt,url)
        # print(summary_prompt)
        summaryfull = self._generate_summary(summary_prompt)
        summaryfull = self._format_summary(summaryfull, url)['description']
        # 组合 combined_info
        combined_info_list = [f"Title: {result['title'][:60]}, URL: [URL]({result['url']})" for result in results]
        combined_info = "\n".join(combined_info_list)
        # 创建 final_result 字典
        final_result = {
            'summaryfull': summaryfull,
            'combined_info': combined_info
        }  
        print(final_result)   
        print("-" * 180 + '\n')
        print(final_result['summaryfull'])
        print("-" * 180 + '\n')
        print(final_result['combined_info'])
        print("-" * 180 + '\n')
        
        return final_result





# 示例调用
if __name__ == "__main__":
    api_key = "sk-izwfqiufrwdqfkvqzklcyaashpmraqaaosuchkjnettqpabb"
    agent = Web_Agent(api_key)
    query = "什么是空间辐射?"
    agent.process_request_full(query)
    # At the end, you need to translate the response into Chinese.