<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理员仪表盘 - AI助教平台</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(102,126,234,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
        }

        .header-content {
            position: relative;
            z-index: 2;
        }

        .header h1 {
            font-size: 2.5rem;
            color: #667eea;
            margin-bottom: 10px;
            font-weight: 700;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
        }

        .header .subtitle {
            color: #666;
            font-size: 1.1rem;
            font-weight: 400;
        }

        .admin-controls {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .control-section {
            margin-bottom: 25px;
        }

        .control-section:last-child {
            margin-bottom: 0;
        }

        .section-title {
            font-size: 1.3rem;
            color: #667eea;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
            font-weight: 600;
        }

        .vip-settings {
            background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .vip-form {
            display: grid;
            grid-template-columns: 1fr 1fr auto;
            gap: 15px;
            align-items: end;
        }

        .form-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .form-group label {
            font-weight: 600;
            color: #333;
            font-size: 0.9rem;
        }

        .form-control {
            padding: 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: white;
        }

        .form-control:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            text-decoration: none;
            justify-content: center;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .btn-danger {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
            color: white;
        }

        .btn-danger:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(255, 107, 107, 0.3);
        }

        .btn-warning {
            background: linear-gradient(135deg, #ffa726 0%, #fb8c00 100%);
            color: white;
        }

        .btn-warning:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(255, 167, 38, 0.3);
        }

        .delete-controls {
            background: linear-gradient(135deg, #ffebee 0%, #fce4ec 100%);
            border-radius: 12px;
            padding: 20px;
        }

        .alert {
            padding: 15px 20px;
            border-radius: 8px;
            margin-top: 15px;
            display: none;
            align-items: center;
            gap: 10px;
            font-weight: 500;
        }

        .alert-success {
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-error {
            background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .users-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(450px, 1fr));
            gap: 25px;
            margin-top: 30px;
        }

        .user-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .user-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
        }

        .user-header {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #f0f0f0;
        }

        .user-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            position: relative;
        }

        .vip-crown {
            color: #ffd700 !important;
            animation: glow 2s ease-in-out infinite alternate;
        }

        @keyframes glow {
            from { text-shadow: 0 0 5px #ffd700, 0 0 10px #ffd700; }
            to { text-shadow: 0 0 10px #ffd700, 0 0 20px #ffd700; }
        }

        .user-info-header {
            flex: 1;
        }

        .username {
            font-size: 1.3rem;
            font-weight: 700;
            color: #333;
            margin-bottom: 5px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .vip-badge {
            background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
            color: #333;
            padding: 4px 10px;
            border-radius: 15px;
            font-size: 0.7rem;
            font-weight: 600;
            display: inline-flex;
            align-items: center;
            gap: 4px;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .user-status {
            display: flex;
            gap: 8px;
        }

        .status-badge {
            padding: 4px 10px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .status-badge.admin {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
            color: white;
        }

        .status-badge.vip {
            background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
            color: #333;
        }

        .status-badge.regular {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            color: white;
        }

        .user-stats {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin-bottom: 25px;
        }

        .stat-item {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 10px;
            padding: 15px;
            display: flex;
            align-items: center;
            gap: 12px;
            transition: all 0.3s ease;
        }

        .stat-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .stat-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.1rem;
        }

        .stat-info {
            flex: 1;
        }

        .stat-number {
            font-size: 1.4rem;
            font-weight: 700;
            color: #333;
            line-height: 1;
        }

        .stat-label {
            font-size: 0.8rem;
            color: #666;
            margin-top: 2px;
        }

        .user-details {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .detail-section {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 10px;
            padding: 15px;
        }

        .detail-title {
            font-size: 1rem;
            font-weight: 600;
            color: #667eea;
            margin-bottom: 12px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .file-list {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .file-item {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 8px 12px;
            background: white;
            border-radius: 6px;
            transition: all 0.2s ease;
        }

        .file-item:hover {
            background: #f0f0f0;
        }

        .file-item i {
            color: #667eea;
            width: 16px;
        }

        .file-name {
            flex: 1;
            font-size: 0.9rem;
            color: #333;
            word-break: break-all;
        }

        .blog-info {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 2px;
        }

        .blog-date {
            color: #666;
            font-size: 0.75rem;
        }

        .video-item {
            position: relative;
        }

        .btn-delete {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
            color: white;
            border: none;
            border-radius: 4px;
            padding: 4px 8px;
            cursor: pointer;
            font-size: 0.8rem;
            transition: all 0.2s ease;
        }

        .btn-delete:hover {
            transform: scale(1.1);
            box-shadow: 0 2px 8px rgba(255, 107, 107, 0.3);
        }

        .more-files {
            color: #666;
            font-style: italic;
        }

        .empty-state {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #999;
            font-style: italic;
            justify-content: center;
            padding: 20px;
        }

        @media (max-width: 768px) {
            .vip-form {
                grid-template-columns: 1fr;
            }

            .users-grid {
                grid-template-columns: 1fr;
            }

            .header h1 {
                font-size: 2rem;
            }

            .user-stats {
                grid-template-columns: 1fr;
            }

            .stat-item {
                padding: 12px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面头部 -->
        <div class="header">
            <div class="header-content">
                <h1>
                    <i class="fas fa-crown"></i>
                    管理员仪表盘
                </h1>
                <p class="subtitle">AI助教平台 - 系统管理与用户监控</p>
            </div>
        </div>

        <!-- 管理控制面板 -->
        <div class="admin-controls">
            <!-- VIP 设置区域 -->
            <div class="control-section">
                <div class="vip-settings">
                    <h3 class="section-title">
                        <i class="fas fa-gem"></i>
                        VIP 用户设置
                    </h3>
                    <div class="vip-form">
                        <div class="form-group">
                            <label for="vipUserSelect">选择用户</label>
                            <select id="vipUserSelect" class="form-control">
                                <option value="">请选择用户...</option>
                                {% for username in users_data.keys() %}
                                    <option value="{{ username }}">{{ username }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="vipExpiry">VIP到期时间</label>
                            <input type="datetime-local" id="vipExpiry" class="form-control">
                        </div>
                        <button id="setVipBtn" class="btn btn-primary">
                            <i class="fas fa-star"></i>
                            设置 VIP
                        </button>
                    </div>
                </div>
            </div>

            <!-- 系统维护区域 -->
            <div class="control-section">
                <div class="delete-controls">
                    <h3 class="section-title">
                        <i class="fas fa-tools"></i>
                        系统维护
                    </h3>
                    <p style="margin-bottom: 15px; color: #666;">
                        <i class="fas fa-info-circle"></i>
                        删除冗余文件将保留每个用户最新的100个文件，并清理本地和知识库中的多余文件
                    </p>
                    <button id="deleteRedundantBtn" class="btn btn-danger">
                        <i class="fas fa-trash-alt"></i>
                        删除冗余文件
                    </button>
                    <div id="resultMessage" class="alert"></div>
                </div>
            </div>
        </div>

        <!-- 用户信息网格 -->
        <div class="users-grid">
            {% for username, data in users_data.items() %}
                <div class="user-card">
                    <div class="user-header">
                        <div class="user-avatar">
                            {% if data.is_vip %}
                                <i class="fas fa-crown vip-crown"></i>
                            {% else %}
                                <i class="fas fa-user"></i>
                            {% endif %}
                        </div>
                        <div class="user-info-header">
                            <h3 class="username">
                                {{ username }}
                                {% if data.is_vip %}
                                    <span class="vip-badge">
                                        <i class="fas fa-gem"></i>
                                        VIP
                                        {% if data.vip_days_left %}
                                            <small>({{ data.vip_days_left }}天)</small>
                                        {% endif %}
                                    </span>
                                {% endif %}
                            </h3>
                            <div class="user-status">
                                {% if username == 'admin' %}
                                    <span class="status-badge admin">管理员</span>
                                {% elif data.is_vip %}
                                    <span class="status-badge vip">VIP用户</span>
                                {% else %}
                                    <span class="status-badge regular">普通用户</span>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="user-stats">
                        <div class="stat-item">
                            <div class="stat-icon">
                                <i class="fas fa-paper-plane"></i>
                            </div>
                            <div class="stat-info">
                                <div class="stat-number">{{ data.submit_count }}</div>
                                <div class="stat-label">提交次数</div>
                            </div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-icon">
                                <i class="fas fa-video"></i>
                            </div>
                            <div class="stat-info">
                                <div class="stat-number">{{ data.upload_count }}</div>
                                <div class="stat-label">上传视频</div>
                            </div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-icon">
                                <i class="fas fa-file-alt"></i>
                            </div>
                            <div class="stat-info">
                                <div class="stat-number">{{ data.rag_count }}</div>
                                <div class="stat-label">RAG文件</div>
                            </div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-icon">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <div class="stat-info">
                                <div class="stat-number">{{ data.total_activity }}</div>
                                <div class="stat-label">总活动</div>
                            </div>
                        </div>
                    </div>

                    <div class="user-details">
                        <div class="detail-section">
                            <h4 class="detail-title">
                                <i class="fas fa-database"></i>
                                RAG 文件 ({{ data.rag_files|length }})
                            </h4>
                            <div class="file-list">
                                {% if data.rag_files %}
                                    {% for file in data.rag_files[:5] %}
                                        <div class="file-item">
                                            <i class="fas fa-file"></i>
                                            <span class="file-name">{{ file }}</span>
                                        </div>
                                    {% endfor %}
                                    {% if data.rag_files|length > 5 %}
                                        <div class="file-item more-files">
                                            <i class="fas fa-ellipsis-h"></i>
                                            <span>还有 {{ data.rag_files|length - 5 }} 个文件...</span>
                                        </div>
                                    {% endif %}
                                {% else %}
                                    <div class="empty-state">
                                        <i class="fas fa-inbox"></i>
                                        <span>暂无文件</span>
                                    </div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="detail-section">
                            <h4 class="detail-title">
                                <i class="fas fa-blog"></i>
                                博客文章 ({{ data.blogs|length }})
                            </h4>
                            <div class="file-list">
                                {% if data.blogs %}
                                    {% for blog in data.blogs[:3] %}
                                        <div class="file-item">
                                            <i class="fas fa-edit"></i>
                                            <div class="blog-info">
                                                <span class="file-name">{{ blog.filename }}</span>
                                                <small class="blog-date">{{ blog.created_at }}</small>
                                            </div>
                                        </div>
                                    {% endfor %}
                                    {% if data.blogs|length > 3 %}
                                        <div class="file-item more-files">
                                            <i class="fas fa-ellipsis-h"></i>
                                            <span>还有 {{ data.blogs|length - 3 }} 篇文章...</span>
                                        </div>
                                    {% endif %}
                                {% else %}
                                    <div class="empty-state">
                                        <i class="fas fa-inbox"></i>
                                        <span>暂无文章</span>
                                    </div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="detail-section">
                            <h4 class="detail-title">
                                <i class="fas fa-play-circle"></i>
                                视频文件 ({{ data.videos|length }})
                            </h4>
                            <div class="file-list">
                                {% if data.videos %}
                                    {% for video in data.videos %}
                                        <div class="file-item video-item">
                                            <i class="fas fa-video"></i>
                                            <span class="file-name">{{ video }}</span>
                                            <button class="btn-delete delete-video-btn"
                                                    data-username="{{ username }}"
                                                    data-video="{{ video }}"
                                                    title="删除视频">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    {% endfor %}
                                {% else %}
                                    <div class="empty-state">
                                        <i class="fas fa-inbox"></i>
                                        <span>暂无视频</span>
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            {% endfor %}
        </div>
    </div>

    <script>
        // 从 URL 获取参数的函数
        const urlParams = new URLSearchParams(window.location.search);

        document.getElementById('deleteRedundantBtn').addEventListener('click', function() {
            if (confirm('确定要删除冗余文件吗？这将只保留每个用户最新的100个文件并删除本地和知识库文件')) {
                fetch('/api/delete_redundant', {
                    method: 'POST',
                })
                .then(response => response.json())
                .then(data => {
                    const resultMessage = document.getElementById('resultMessage');
                    resultMessage.innerHTML = `<i class="fas fa-check-circle"></i> ${data.message}`;
                    resultMessage.className = 'alert alert-success';
                    resultMessage.style.display = 'flex';
                    console.log('Deleted files:', data.deleted_files);
                    // 可以选择在这里刷新页面或更新文件列表
                    setTimeout(() => {
                        location.reload();
                    }, 3000); // 3秒后刷新页面
                })
                .catch(error => {
                    console.error('Error:', error);
                    const resultMessage = document.getElementById('resultMessage');
                    resultMessage.innerHTML = `<i class="fas fa-exclamation-circle"></i> 删除冗余文件时出错`;
                    resultMessage.className = 'alert alert-error';
                    resultMessage.style.display = 'flex';
                });
            }
        });

        // 添加删除视频的功能
        document.addEventListener('click', function(e) {
            if (e.target && e.target.classList.contains('delete-video-btn')) {
                const username = e.target.getAttribute('data-username');
                const video = e.target.getAttribute('data-video');
                if (confirm(`确定要删除用户 ${username} 的视频 ${video} 吗？`)) {
                    fetch('/api/delete_video', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({ username: username, video: video }),
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // 显示成功消息
                            showNotification('视频删除成功', 'success');
                            // 从页面中移除该视频项
                            e.target.parentElement.remove();
                        } else {
                            showNotification('删除失败: ' + data.error, 'error');
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        showNotification('删除过程中发生错误', 'error');
                    });
                }
            }
        });

        // 添加这个函数来获取 cookie
        function getCookie(name) {
            let cookieValue = null;
            if (document.cookie && document.cookie !== '') {
                const cookies = document.cookie.split(';');
                for (let i = 0; i < cookies.length; i++) {
                    const cookie = cookies[i].trim();
                    if (cookie.substring(0, name.length + 1) === (name + '=')) {
                        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                        break;
                    }
                }
            }
            return cookieValue;
        }

        // 设置默认的 VIP 到期时间（当前时间+30天）
        function setDefaultVipExpiry() {
            const now = new Date();
            now.setDate(now.getDate() + 30);
            const defaultExpiry = now.toISOString().slice(0, 16);
            document.getElementById('vipExpiry').value = defaultExpiry;
        }

        // 页面加载时设置默认时间
        document.addEventListener('DOMContentLoaded', setDefaultVipExpiry);

        // VIP 设置按钮点击事件
        document.getElementById('setVipBtn').addEventListener('click', function() {
            const username = document.getElementById('vipUserSelect').value;
            const vip_expiry = document.getElementById('vipExpiry').value;
            
            if (!username) {
                showNotification('请选择用户', 'error');
                return;
            }

            if (!vip_expiry) {
                showNotification('请设置到期时间', 'error');
                return;
            }

            // 从 URL 参数获取管理员认证信息
            const adminToken = urlParams.get('token');
            const adminUsername = urlParams.get('user');
            const adminKey = urlParams.get('key');

            if (!adminToken || !adminUsername) {
                showNotification('管理员认证信息无效，请重新登录', 'error');
                return;
            }

            if (confirm(`确定要将用户 ${username} 设置为 VIP 吗？到期时间: ${vip_expiry}`)) {
                fetch('/api/set_vip', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: username,
                        vip_expiry: vip_expiry,
                        admin_username: adminUsername,
                        admin_token: adminToken,
                        admin_key: adminKey
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showNotification('VIP 设置成功', 'success');
                        setTimeout(() => {
                            location.reload();
                        }, 1500);
                    } else {
                        showNotification('设置失败: ' + data.message, 'error');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showNotification('设置过程中发生错误', 'error');
                });
            }
        });

        // 通知函数
        function showNotification(message, type = 'info') {
            // 创建通知元素
            const notification = document.createElement('div');
            notification.className = `notification notification-${type}`;

            const icon = type === 'success' ? 'fas fa-check-circle' :
                        type === 'error' ? 'fas fa-exclamation-circle' :
                        'fas fa-info-circle';

            notification.innerHTML = `
                <i class="${icon}"></i>
                <span>${message}</span>
                <button class="notification-close" onclick="this.parentElement.remove()">
                    <i class="fas fa-times"></i>
                </button>
            `;

            // 添加通知样式
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'success' ? 'linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%)' :
                           type === 'error' ? 'linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%)' :
                           'linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%)'};
                color: ${type === 'success' ? '#155724' : type === 'error' ? '#721c24' : '#0c5460'};
                padding: 15px 20px;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                z-index: 10000;
                display: flex;
                align-items: center;
                gap: 10px;
                min-width: 300px;
                max-width: 500px;
                animation: slideIn 0.3s ease-out;
                border: 1px solid ${type === 'success' ? '#c3e6cb' : type === 'error' ? '#f5c6cb' : '#bee5eb'};
            `;

            // 添加动画样式
            const style = document.createElement('style');
            style.textContent = `
                @keyframes slideIn {
                    from {
                        transform: translateX(100%);
                        opacity: 0;
                    }
                    to {
                        transform: translateX(0);
                        opacity: 1;
                    }
                }
                .notification-close {
                    background: none;
                    border: none;
                    color: inherit;
                    cursor: pointer;
                    padding: 0;
                    margin-left: auto;
                    opacity: 0.7;
                    transition: opacity 0.2s;
                }
                .notification-close:hover {
                    opacity: 1;
                }
            `;
            document.head.appendChild(style);

            // 添加到页面
            document.body.appendChild(notification);

            // 自动移除
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.style.animation = 'slideIn 0.3s ease-out reverse';
                    setTimeout(() => {
                        notification.remove();
                    }, 300);
                }
            }, 5000);
        }

        // 页面加载动画
        document.addEventListener('DOMContentLoaded', function() {
            // 为用户卡片添加加载动画
            const userCards = document.querySelectorAll('.user-card');
            userCards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                setTimeout(() => {
                    card.style.transition = 'all 0.5s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });
    </script>
</body>
</html>
