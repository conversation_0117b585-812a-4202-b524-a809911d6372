<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <title>电磁波传播与干涉衍射演示</title>
  <style>
    body {
      margin: 0;
      background: #001133;
      overflow: hidden;
      user-select: none;
      font-family: 'Microsoft YaHei', <PERSON><PERSON>, sans-serif;
    }
    #controls {
      position: absolute;
      top: 15px;
      left: 15px;
      background: rgba(255,255,255,0.95);
      border-radius: 12px;
      padding: 15px 20px;
      z-index: 10;
      box-shadow: 0 4px 12px rgba(0,0,0,0.3);
      min-width: 280px;
    }
    #controls h3 {
      margin: 0 0 12px 0;
      color: #333;
      font-size: 16px;
    }
    #controls .control-group {
      margin-bottom: 12px;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
    #controls label {
      font-weight: 500;
      color: #555;
      min-width: 60px;
    }
    #controls input[type=range] {
      width: 120px;
      margin: 0 8px;
    }
    #controls .value-display {
      min-width: 50px;
      text-align: right;
      font-weight: bold;
      color: #2196F3;
    }
    #info {
      position: absolute;
      bottom: 15px;
      left: 15px;
      background: rgba(255,255,255,0.9);
      border-radius: 8px;
      padding: 10px 15px;
      z-index: 10;
      font-size: 14px;
      color: #666;
      max-width: 300px;
    }
  </style>
</head>
<body>
  <div id="controls">
    <h3>🌊 电磁波参数控制</h3>
    <div class="control-group">
      <label>振幅:</label>
      <input id="amplitude" type="range" min="20" max="150" value="80">
      <span id="ampval" class="value-display">80</span>
    </div>
    <div class="control-group">
      <label>频率:</label>
      <input id="frequency" type="range" min="0.3" max="3" step="0.05" value="1.2">
      <span id="freqval" class="value-display">1.20 Hz</span>
    </div>
    <div class="control-group">
      <label>波速:</label>
      <input id="wavespeed" type="range" min="100" max="500" step="10" value="250">
      <span id="speedval" class="value-display">250 px/s</span>
    </div>
  </div>

  <div id="info">
    💡 <strong>操作提示:</strong><br>
    • 拖动白色圆点移动波源<br>
    • 移动后原波继续传播，新位置产生新波<br>
    • 蓝色=无波动，红色=最大强度
  </div>

  <canvas id="wavecanvas"></canvas>
  <script>
    const canvas = document.getElementById('wavecanvas');
    const ctx = canvas.getContext('2d');
    let width = window.innerWidth;
    let height = window.innerHeight;
    canvas.width = width;
    canvas.height = height;

    // 控件
    const ampSlider = document.getElementById('amplitude');
    const freqSlider = document.getElementById('frequency');
    const speedSlider = document.getElementById('wavespeed');
    const ampVal = document.getElementById('ampval');
    const freqVal = document.getElementById('freqval');
    const speedVal = document.getElementById('speedval');

    // 波参数
    let amplitude = parseFloat(ampSlider.value);
    let frequency = parseFloat(freqSlider.value);
    let waveSpeed = parseFloat(speedSlider.value);

    // 波源
    let source = { x: width/2, y: height/2 };
    let dragging = false;
    let dragOffset = {x:0, y:0};

    // 波源历史（每次移动后记录旧波源）
    let oldWaveSources = [];
    let currentWaveStartTime = performance.now()/1000;

    // 改进的颜色映射：深蓝(背景) → 浅蓝 → 青 → 黄 → 橙 → 红(最大强度)
    function amplitudeToColor(amplitude, maxAmplitude) {
      // 将振幅标准化到 [0, 1] 范围
      let intensity = Math.abs(amplitude) / maxAmplitude;
      intensity = Math.max(0, Math.min(1, intensity));

      // 背景色 (深蓝)
      const bgR = 0, bgG = 17, bgB = 51; // #001133

      let r, g, b;

      if (intensity < 0.2) {
        // 深蓝 → 蓝
        let t = intensity / 0.2;
        r = bgR + t * (0 - bgR);
        g = bgG + t * (100 - bgG);
        b = bgB + t * (255 - bgB);
      } else if (intensity < 0.4) {
        // 蓝 → 青
        let t = (intensity - 0.2) / 0.2;
        r = 0 + t * (0 - 0);
        g = 100 + t * (255 - 100);
        b = 255 + t * (255 - 255);
      } else if (intensity < 0.6) {
        // 青 → 黄绿
        let t = (intensity - 0.4) / 0.2;
        r = 0 + t * (128 - 0);
        g = 255 + t * (255 - 255);
        b = 255 + t * (0 - 255);
      } else if (intensity < 0.8) {
        // 黄绿 → 橙
        let t = (intensity - 0.6) / 0.2;
        r = 128 + t * (255 - 128);
        g = 255 + t * (165 - 255);
        b = 0 + t * (0 - 0);
      } else {
        // 橙 → 红
        let t = (intensity - 0.8) / 0.2;
        r = 255 + t * (255 - 255);
        g = 165 + t * (0 - 165);
        b = 0 + t * (0 - 0);
      }

      return [Math.round(r), Math.round(g), Math.round(b)];
    }

    // 监听控件变化
    ampSlider.oninput = () => {
      amplitude = parseFloat(ampSlider.value);
      ampVal.textContent = amplitude;
    };
    freqSlider.oninput = () => {
      frequency = parseFloat(freqSlider.value);
      freqVal.textContent = frequency.toFixed(2) + ' Hz';
    };
    speedSlider.oninput = () => {
      waveSpeed = parseFloat(speedSlider.value);
      speedVal.textContent = waveSpeed + ' px/s';
    };

    // 拖动波源逻辑
    canvas.addEventListener('mousedown', e => {
      const rect = canvas.getBoundingClientRect();
      const mx = e.clientX - rect.left;
      const my = e.clientY - rect.top;
      // 检查是否点击在波源附近
      if (Math.hypot(mx - source.x, my - source.y) < 25) {
        dragging = true;
        dragOffset.x = mx - source.x;
        dragOffset.y = my - source.y;
        canvas.style.cursor = 'grabbing';
      }
    });

    window.addEventListener('mousemove', e => {
      if (dragging) {
        const rect = canvas.getBoundingClientRect();
        let mx = e.clientX - rect.left;
        let my = e.clientY - rect.top;
        source.x = Math.max(20, Math.min(width-20, mx - dragOffset.x));
        source.y = Math.max(20, Math.min(height-20, my - dragOffset.y));
      } else {
        // 鼠标悬停效果
        const rect = canvas.getBoundingClientRect();
        const mx = e.clientX - rect.left;
        const my = e.clientY - rect.top;
        if (Math.hypot(mx - source.x, my - source.y) < 25) {
          canvas.style.cursor = 'grab';
        } else {
          canvas.style.cursor = 'default';
        }
      }
    });

    window.addEventListener('mouseup', e => {
      if (dragging) {
        dragging = false;
        canvas.style.cursor = 'default';

        // 保存当前波源为历史波源（继续传播但不产生新波）
        let now = performance.now()/1000;
        oldWaveSources.push({
          x: source.x,
          y: source.y,
          amplitude: amplitude,
          frequency: frequency,
          waveSpeed: waveSpeed,
          startTime: currentWaveStartTime,
          endTime: now  // 停止产生新波的时间
        });

        // 重置当前波源的开始时间
        currentWaveStartTime = now;
      }
    });

    // 窗口自适应
    window.addEventListener('resize', () => {
      width = window.innerWidth;
      height = window.innerHeight;
      canvas.width = width;
      canvas.height = height;
    });

    // 计算波动强度的函数
    function calculateWaveAmplitude(x, y, waveSource, currentTime) {
      let dx = x - waveSource.x;
      let dy = y - waveSource.y;
      let distance = Math.hypot(dx, dy);

      // 波传播的时间
      let waveTime = currentTime - waveSource.startTime;

      // 如果是历史波源，检查是否已停止产生新波
      if (waveSource.endTime && currentTime > waveSource.endTime) {
        // 只考虑在停止时间之前产生的波
        let maxWaveDistance = waveSource.waveSpeed * (waveSource.endTime - waveSource.startTime);
        let currentWaveDistance = waveSource.waveSpeed * waveTime;

        // 波前已经超过了停止时产生的最远波
        if (distance > currentWaveDistance || distance < currentWaveDistance - maxWaveDistance) {
          return 0;
        }
      }

      // 波还没到达这个位置
      if (distance > waveSource.waveSpeed * waveTime) {
        return 0;
      }

      // 计算相位
      let phase = 2 * Math.PI * waveSource.frequency * (waveTime - distance / waveSource.waveSpeed);

      // 距离衰减 (1/r 衰减，加上最小距离避免无穷大)
      let attenuation = 1 / (1 + distance / 50);

      // 返回波动振幅
      return waveSource.amplitude * attenuation * Math.sin(phase);
    }

    // 主绘制函数
    function draw() {
      let now = performance.now() / 1000;

      // 清空画布并设置背景色
      ctx.fillStyle = '#001133';
      ctx.fillRect(0, 0, width, height);

      // 创建图像数据用于热力图
      let imageData = ctx.createImageData(width, height);
      let data = imageData.data;

      // 采样步长（提高性能）
      let step = 2;

      for (let y = 0; y < height; y += step) {
        for (let x = 0; x < width; x += step) {
          let totalAmplitude = 0;

          // 计算当前波源的贡献
          let currentWaveSource = {
            x: source.x,
            y: source.y,
            amplitude: amplitude,
            frequency: frequency,
            waveSpeed: waveSpeed,
            startTime: currentWaveStartTime
          };
          totalAmplitude += calculateWaveAmplitude(x, y, currentWaveSource, now);

          // 计算所有历史波源的贡献
          for (let oldSource of oldWaveSources) {
            totalAmplitude += calculateWaveAmplitude(x, y, oldSource, now);
          }

          // 将振幅转换为颜色
          let color = amplitudeToColor(totalAmplitude, amplitude);

          // 填充像素（考虑采样步长）
          for (let dy = 0; dy < step && y + dy < height; dy++) {
            for (let dx = 0; dx < step && x + dx < width; dx++) {
              let idx = ((y + dy) * width + (x + dx)) * 4;
              data[idx] = color[0];     // R
              data[idx + 1] = color[1]; // G
              data[idx + 2] = color[2]; // B
              data[idx + 3] = 255;      // A
            }
          }
        }
      }

      // 将图像数据绘制到画布
      ctx.putImageData(imageData, 0, 0);

      // 绘制波源
      ctx.beginPath();
      ctx.arc(source.x, source.y, 15, 0, 2 * Math.PI);
      ctx.fillStyle = 'white';
      ctx.fill();
      ctx.strokeStyle = dragging ? '#FF6B6B' : '#4ECDC4';
      ctx.lineWidth = 3;
      ctx.stroke();

      // 在波源中心绘制小点
      ctx.beginPath();
      ctx.arc(source.x, source.y, 4, 0, 2 * Math.PI);
      ctx.fillStyle = dragging ? '#FF6B6B' : '#4ECDC4';
      ctx.fill();

      // 清理过期的历史波源
      let maxDistance = Math.max(width, height) * 1.5;
      oldWaveSources = oldWaveSources.filter(oldSource => {
        let timeSinceEnd = oldSource.endTime ? now - oldSource.endTime : 0;
        let maxWaveDistance = oldSource.waveSpeed * timeSinceEnd;
        return maxWaveDistance < maxDistance;
      });

      requestAnimationFrame(draw);
    }

    // 启动动画
    draw();
  </script>
</body>
</html> 