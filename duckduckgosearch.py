import io
import json
import os
import random
import socks
import uuid
import requests
import urllib3
from urllib3.util.retry import Retry
from requests.adapters import HTTPAdapter
import undetected_chromedriver as uc
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import Web<PERSON><PERSON><PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
import tempfile
import os


from duckduckgo_search import DDGS
from typing import List, Tuple, Optional
from bs4 import BeautifulSoup
import chardet
import re
import PyPDF2
from io import BytesIO
import pdfplumber
import warnings
from io import BytesIO
from logger_config import deepsearch_logger as deeplogger
import time
from logger_config import log_with_request_id 
from model.deepseek import generate_summary_r11,generate_summary,generate_summary_with_system_prompt,generate_summary_graph
from model.qwen import qwen_generate_summary,qwen_generate_summary_with_system_prompt,qwen_generate_summary_r11,qwen_generate_summary_graph
import traceback
from dataclasses import dataclass
import logging
from enum import Enum
from contextlib import contextmanager
from abc import ABC, abstractmethod
from PyPDF2 import PdfReader
import hashlib
import urllib.parse

request_id = str(uuid.uuid4())[:8]

# 禁用不安全请求警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)


class SearchErrorType(Enum):
    NETWORK = "network_error"
    API = "api_error"
    PARSING = "parsing_error"
    TIMEOUT = "timeout_error"
    RATE_LIMIT = "rate_limit_error"
    CONTENT_FILTER = "content_filter_error"
    UNKNOWN = "unknown_error"

class SearchError(Exception):
    """自定义搜索异常类"""
    def __init__(self, message: str, error_type: SearchErrorType = SearchErrorType.UNKNOWN):
        self.message = message
        self.error_type = error_type
        super().__init__(self.message)

@contextmanager
def handle_request_errors():
    """处理请求错误的上下文管理器"""
    try:
        yield
    except requests.exceptions.ConnectionError as e:
        raise SearchError(f"网络连接错误: {str(e)}", SearchErrorType.NETWORK)
    except requests.exceptions.Timeout as e:
        raise SearchError(f"请求超时: {str(e)}", SearchErrorType.TIMEOUT)
    except requests.exceptions.TooManyRedirects as e:
        raise SearchError(f"重定向次数过多: {str(e)}", SearchErrorType.NETWORK)
    except requests.exceptions.RequestException as e:
        if hasattr(e.response, 'status_code'):
            if e.response.status_code == 429:
                raise SearchError("达到速率限制", SearchErrorType.RATE_LIMIT)
            elif e.response.status_code == 403:
                raise SearchError("内容被过滤或访问被拒绝", SearchErrorType.CONTENT_FILTER)
        raise SearchError(f"请求异常: {str(e)}", SearchErrorType.UNKNOWN)
    except Exception as e:
        raise SearchError(f"未知错误: {str(e)}", SearchErrorType.UNKNOWN)

def merge_results(main_results, additional_results):
    """合并结果并去重"""
    seen_urls = set()
    merged_results = []
    for result in main_results + additional_results:
        url = result.get('url')
        if url and url not in seen_urls:
            seen_urls.add(url)
            merged_results.append(result)
    return merged_results

def clear_memory(results):
    """清理不必要的大型数据"""
    for result in results:
        if 'body' in result:
            del result['body']
    return results





class BaseSearchGenerator(ABC):
    """Base class for search result generation"""
    
    def __init__(self, api_key: str, model: str, temperature: float = 0.5):
        self.api_key = api_key
        self.model = model
        self.temperature = temperature
        
    @abstractmethod
    def generate(self, prompt: str, system_prompt: Optional[str] = None) -> Tuple[str, int]:
        """Generate search results
        
        Args:
            prompt: The prompt to generate from
            system_prompt: Optional system prompt
            
        Returns:
            Tuple of (generated_text, total_tokens)
        """
        pass

class StandardGenerator(BaseSearchGenerator):
    """Standard search result generator"""
    
    def generate(self, prompt: str, system_prompt: Optional[str] = None) -> Tuple[str, int]:
        url = "https://api.siliconflow.cn/v1/chat/completions"
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        messages = []
        if system_prompt:
            messages.append({"role": "system", "content": system_prompt})
        messages.append({"role": "user", "content": prompt})
        
        data = {
            "model": self.model,
            "messages": messages,
            "stream": True,
            "max_tokens": 4096,
            "temperature": self.temperature,
        }
        
        with handle_request_errors():
            response = requests.post(url, headers=headers, json=data, stream=True)
            if response.status_code != 200:
                raise SearchError(
                    f"API request failed with status {response.status_code}",
                    SearchErrorType.API
                )
            
            return self._process_stream_response(response)
            
    def _process_stream_response(self, response) -> Tuple[str, int]:
        all_content = []
        total_tokens = 0
        
        for chunk in response.iter_content(chunk_size=8192):
            if not chunk:
                continue
                
            decoded_chunk = chunk.decode('utf-8')
            if '"code":60000' in decoded_chunk:
                deeplogger.warning("Received error code 60000, skipping chunk")
                continue
                
            if decoded_chunk.startswith('data: '):
                decoded_chunk = decoded_chunk[6:]
                
            if decoded_chunk.strip() == "[DONE]":
                break
                
            try:
                json_data = json.loads(decoded_chunk)
                content = json_data['choices'][0]['delta'].get('content', '')
                if content:
                    all_content.append(content)
                if 'usage' in json_data and 'total_tokens' in json_data['usage']:
                    total_tokens = json_data['usage']['total_tokens']
            except json.JSONDecodeError as e:
                deeplogger.error(f"JSON parsing error: {str(e)}")
                match = re.search(r'"content":"(.*?)"', decoded_chunk)
                if match:
                    all_content.append(match.group(1))
        
        combined_content = ''.join(all_content)
        deeplogger.info(f"Generated content (first 100 chars): {combined_content[:100]}")
        deeplogger.info(f"Total tokens used: {total_tokens}")
        
        return combined_content, total_tokens


class Web_Agent:
    def __init__(self, api_key: str = "", api_key_type: str = "silliconflow", language: str="Chinese",
                 max_tokens: int = 4000, model: str = "Qwen/Qwen2.5-72B-Instruct-128K", temperature: float = 0.5,
                summary_length: int = 600, proxy_host: str = "***********", proxy_port: int = 1070, socks_proxy_port:int=1080,
                 proxy_protocol: str = "socks5", timeout: int = 20):
        """Initialize Web Agent with direct parameters
        
        Args:
            api_key: API key for the service
            api_key_type: Type of API key (silliconflow or deepseek)
            num_results: Number of search results to return
            max_tokens: Maximum tokens for generation
            model: Model name to use
            temperature: Temperature for generation
            comprehension_grade: Grade level for comprehension
            summary_length: Length of summary
            full_summary_length: Length of full summary
            query_length_threshold: Threshold for query length
            proxy_host: Proxy host
            proxy_port: Proxy port
            proxy_protocol: Proxy protocol
            timeout: Timeout in seconds
            rate_limit_delay: Delay between rate limited requests
        """
        # Store all parameters as instance variables
        self.api_key = api_key
        self.api_key_type = api_key_type
        self.max_tokens = max_tokens
        self.model = model
        self.temperature = temperature
        self.summary_length = summary_length
        self.proxy_host = proxy_host
        self.proxy_port = proxy_port
        self.socks_proxy_port = socks_proxy_port
        self.proxy_protocol = proxy_protocol
        self.timeout = timeout
        self.language=language

        # Initialize other instance variables
        self.search_cache = {}
        self.sensitive_words = self._load_sensitive_words()
        # Configure proxy
        self.proxy = f"{proxy_protocol}://{proxy_host}:{proxy_port}"
        self.proxy_dict = {
            'http': self.proxy,
            'https': self.proxy
        }
        
        # Initialize DDGS session
        self.ddg_session = DDGS(
            proxy=self.proxy,
            timeout=self.timeout
        )
        
        # Initialize generators
        self.standard_generator = StandardGenerator(
            self.api_key,
            self.model,
            self.temperature
        )
        self.browser = None
        self.browser_tmp_dir = None

        self.proxy_list = [
            {"host": "***********", "port": 1080, "protocol": "socks5"},
            {"host": "***********", "port": 1070, "protocol": "http"},  # 常见的 Clash 代理端口
        ]
        self.current_proxy_index = 0
        self.proxy_fail_count = {}

    def _get_next_proxy(self) -> dict:
        """获取下一个可用的代理"""
        start_index = self.current_proxy_index
        while True:
            proxy = self.proxy_list[self.current_proxy_index]
            self.current_proxy_index = (self.current_proxy_index + 1) % len(self.proxy_list)
            
            # 如果代理失败次数少于3次，或者已经尝试了所有代理，就使用这个代理
            if self.proxy_fail_count.get(f"{proxy['host']}:{proxy['port']}", 0) < 3 or self.current_proxy_index == start_index:
                return proxy
            
            # 如果所有代理都失败超过3次，重置失败计数
            if self.current_proxy_index == start_index:
                self.proxy_fail_count.clear()

    def _get_browser(self):
        """获取或创建浏览器实例"""
        if self.browser is None:
            retry_count = 0
            max_retries = 3
            
            while retry_count < max_retries:
                try:
                    options = uc.ChromeOptions()
                    
                    # 基础配置
                    options.add_argument('--no-sandbox')
                    options.add_argument('--disable-dev-shm-usage')
                    options.add_argument('--disable-gpu')
                    
                    # 高级反检测配置
                    options.add_argument('--disable-blink-features=AutomationControlled')
                    options.add_argument('--disable-infobars')
                    options.add_argument('--disable-browser-side-navigation')
                    options.add_argument('--disable-features=IsolateOrigins,site-per-process')
                    
                    # 模拟真实浏览器环境
                    options.add_argument('--window-size=1920,1080')
                    options.add_argument('--start-maximized')
                    
                    # 随机化配置
                    screen_sizes = ['1920,1080', '1366,768', '1440,900', '1536,864']
                    options.add_argument(f'--window-size={random.choice(screen_sizes)}')
                    
                    # 高级用户代理配置
                    user_agents = [
                        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                        'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:123.0) Gecko/20100101 Firefox/123.0',
                        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********'
                    ]
                    options.add_argument(f'user-agent={random.choice(user_agents)}')
                    
                    # 语言和时区配置
                    languages = ['zh-CN,zh;q=0.9,en;q=0.8', 'en-US,en;q=0.9,zh;q=0.8', 'en-GB,en;q=0.9']
                    options.add_argument(f'--lang={random.choice(languages)}')
                    
                    # 代理配置
                    if self.proxy_host:
                        if self.proxy_protocol.lower() == 'http':
                            proxy_url = f"http://{self.proxy_host}:{self.http_proxy_port}"
                            options.add_argument(f'--proxy-server={proxy_url}')
                            deeplogger.info(f"使用HTTP代理: {proxy_url}")
                        elif self.proxy_protocol.lower() == 'socks5':
                            proxy_url = f"socks5://{self.proxy_host}:{self.socks_proxy_port}"
                            options.add_argument(f'--proxy-server={proxy_url}')
                            deeplogger.info(f"使用SOCKS5代理: {proxy_url}")
                    
                    # 创建临时目录
                    if hasattr(self, 'browser_tmp_dir') and self.browser_tmp_dir:
                        try:
                            import shutil
                            shutil.rmtree(self.browser_tmp_dir)
                        except:
                            pass
                    
                    self.browser_tmp_dir = tempfile.mkdtemp()
                    options.add_argument(f'--user-data-dir={self.browser_tmp_dir}')
                    
                    # 创建浏览器实例
                    try:
                        if self.proxy_host and self.proxy_protocol.lower() == 'http':
                            capabilities = webdriver.DesiredCapabilities.CHROME.copy()
                            capabilities['proxy'] = {
                                'httpProxy': f"{self.proxy_host}:{self.http_proxy_port}",
                                'sslProxy': f"{self.proxy_host}:{self.http_proxy_port}",
                                'proxyType': 'MANUAL',
                            }
                            capabilities['acceptInsecureCerts'] = True
                            
                            # 高级浏览器指纹配置
                            prefs = {
                                'webrtc.ip_handling_policy': 'disable_non_proxied_udp',
                                'webrtc.multiple_routes_enabled': False,
                                'webrtc.nonproxied_udp_enabled': False,
                                'profile.default_content_setting_values.plugins': 1,
                                'profile.content_settings.plugin_whitelist.adobe-flash-player': 1,
                                'profile.content_settings.exceptions.plugins.*,*.per_resource.adobe-flash-player': 1,
                                'profile.password_manager_enabled': False,
                                'credentials_enable_service': False
                            }
                            options.add_experimental_option('prefs', prefs)
                            
                            self.browser = uc.Chrome(options=options, desired_capabilities=capabilities)
                        else:
                            self.browser = uc.Chrome(options=options)
                        
                        # 高级反检测JavaScript
                        stealth_js = '''
                            // 修改 webdriver 标记
                            Object.defineProperty(navigator, 'webdriver', {
                                get: () => undefined
                            });
                            
                            // 添加语言和平台信息
                            Object.defineProperty(navigator, 'languages', {
                                get: () => ['zh-CN', 'zh', 'en-US', 'en']
                            });
                            
                            // 模拟真实的 plugins
                            Object.defineProperty(navigator, 'plugins', {
                                get: () => {
                                    return {
                                        length: 5,
                                        item: () => ({
                                            type: 'application/x-google-chrome-pdf',
                                            suffixes: 'pdf',
                                            description: 'Portable Document Format'
                                        })
                                    };
                                }
                            });
                            
                            // 添加 Chrome 运行时
                            window.chrome = {
                                app: {
                                    isInstalled: false,
                                },
                                webstore: {
                                    onInstallStageChanged: {},
                                    onDownloadProgress: {},
                                },
                                runtime: {
                                    PlatformOs: {
                                        MAC: 'mac',
                                        WIN: 'win',
                                        ANDROID: 'android',
                                        CROS: 'cros',
                                        LINUX: 'linux',
                                        OPENBSD: 'openbsd',
                                    },
                                    PlatformArch: {
                                        ARM: 'arm',
                                        X86_32: 'x86-32',
                                        X86_64: 'x86-64',
                                    },
                                    PlatformNaclArch: {
                                        ARM: 'arm',
                                        X86_32: 'x86-32',
                                        X86_64: 'x86-64',
                                    },
                                    RequestUpdateCheckStatus: {
                                        THROTTLED: 'throttled',
                                        NO_UPDATE: 'no_update',
                                        UPDATE_AVAILABLE: 'update_available',
                                    }
                                }
                            };
                            
                            // 添加 Permissions API
                            const originalQuery = window.navigator.permissions.query;
                            window.navigator.permissions.query = (parameters) => (
                                parameters.name === 'notifications' ?
                                    Promise.resolve({ state: Notification.permission }) :
                                    originalQuery(parameters)
                            );
                            
                            // 模拟 WebGL
                            const getParameter = WebGLRenderingContext.prototype.getParameter;
                            WebGLRenderingContext.prototype.getParameter = function(parameter) {
                                if (parameter === 37445) {
                                    return 'Intel Inc.';
                                }
                                if (parameter === 37446) {
                                    return 'Intel(R) Iris(TM) Graphics 6100';
                                }
                                return getParameter.apply(this, [parameter]);
                            };
                        '''
                        
                        self.browser.execute_cdp_cmd('Page.addScriptToEvaluateOnNewDocument', {
                            'source': stealth_js
                        })
                        
                        # 设置超时
                        self.browser.set_page_load_timeout(30)
                        self.browser.set_script_timeout(20)
                        
                        return self.browser
                        
                    except Exception as browser_error:
                        if "This version of ChromeDriver only supports Chrome version" in str(browser_error):
                            deeplogger.error("Chrome版本不匹配，请更新Chrome浏览器或安装正确版本的ChromeDriver")
                            raise Exception("Chrome版本不匹配，请更新Chrome浏览器到最新版本") from browser_error
                        raise
                    
                except Exception as e:
                    retry_count += 1
                    deeplogger.error(f"浏览器初始化失败 (尝试 {retry_count}/{max_retries}): {str(e)}")
                    
                    if self.browser:
                        try:
                            self.browser.quit()
                        except:
                            pass
                        self.browser = None
                    
                    if self.browser_tmp_dir and os.path.exists(self.browser_tmp_dir):
                        try:
                            import shutil
                            shutil.rmtree(self.browser_tmp_dir)
                        except:
                            pass
                    
                    if retry_count < max_retries:
                        time.sleep(random.uniform(2, 5))  # 随机等待时间
                    else:
                        if "切换到" in str(e):
                            deeplogger.warning("所有代理都测试失败，继续使用当前代理")
                            return self._create_browser_without_test()
                        raise Exception(f"浏览器初始化失败，已重试{max_retries}次: {str(e)}")
        
        return self.browser

    def _create_browser_without_test(self):
        """创建浏览器实例但不进行代理测试"""
        options = uc.ChromeOptions()
        options.add_argument('--headless')
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--ignore-certificate-errors')
        options.add_argument('--ignore-ssl-errors')
        
        if self.proxy_host:
            if self.proxy_protocol.lower() == 'http':
                proxy_url = f"http://{self.proxy_host}:{self.http_proxy_port}"
            else:
                proxy_url = f"socks5://{self.proxy_host}:{self.socks_proxy_port}"
            options.add_argument(f'--proxy-server={proxy_url}')
        
        self.browser = uc.Chrome(options=options)
        self.browser.set_page_load_timeout(60)
        return self.browser

    def _fetch_with_browser(self, url: str) -> Optional[str]:
        """使用浏览器获取内容"""
        try:
            browser = self._get_browser()
            
            # 设置页面加载策略
            browser.execute_cdp_cmd('Network.setBypassServiceWorker', {'bypass': True})
            browser.execute_cdp_cmd('Network.setCacheDisabled', {'cacheDisabled': True})
            
            # 尝试访问页面
            try:
                browser.get(url)
                
                # 处理Cloudflare验证
                cloudflare_selectors = [
                    "//iframe[contains(@title, 'challenge')]",  # CF iframe
                    "//form[@id='challenge-form']",  # CF 表单
                    "//input[@type='checkbox']",     # CF checkbox
                    "//div[contains(@class, 'cf-browser-verification')]",  # CF 验证div
                    "//div[contains(@class, 'challenge-running')]"  # CF 运行中状态
                ]
                
                # 等待Cloudflare元素出现
                wait = WebDriverWait(browser, 10)
                for selector in cloudflare_selectors:
                    try:
                        element = wait.until(EC.presence_of_element_located((By.XPATH, selector)))
                        if element.is_displayed():
                            deeplogger.info("检测到Cloudflare验证，开始处理...")
                            
                            # 切换到iframe（如果存在）
                            iframes = browser.find_elements(By.TAG_NAME, "iframe")
                            for iframe in iframes:
                                try:
                                    browser.switch_to.frame(iframe)
                                    # 查找并点击验证按钮或复选框
                                    for button in browser.find_elements(By.TAG_NAME, "button"):
                                        if "verify" in button.get_attribute("innerHTML").lower():
                                            button.click()
                                            time.sleep(2)
                                    checkboxes = browser.find_elements(By.CSS_SELECTOR, "input[type='checkbox']")
                                    for checkbox in checkboxes:
                                        if checkbox.is_displayed():
                                            checkbox.click()
                                            time.sleep(2)
                                    browser.switch_to.default_content()
                                except:
                                    browser.switch_to.default_content()
                                    continue
                            
                            # 等待验证完成
                            time.sleep(5)  # 给予足够时间完成验证
                            
                            # 检查是否仍在验证页面
                            if any(browser.find_elements(By.XPATH, selector) for selector in cloudflare_selectors):
                                deeplogger.warning("Cloudflare验证仍在进行中...")
                                # 增加额外等待时间
                                time.sleep(10)
                                
                            break
                    except:
                        continue
                
                # 等待页面加载完成
                wait = WebDriverWait(browser, 30)
                try:
                    wait.until(lambda d: d.execute_script('return document.readyState') == 'complete')
                    # 额外等待以确保动态内容加载
                    time.sleep(3)
                except:
                    deeplogger.warning("页面加载等待超时，继续处理已加载内容")
                
                # 执行JavaScript来获取页面内容
                page_content = browser.execute_script("""
                    function getVisibleText() {
                        // 移除不需要的元素
                        const elementsToRemove = document.querySelectorAll('script, style, noscript, iframe, img');
                        elementsToRemove.forEach(el => el.remove());
                        
                        // 获取所有可见文本
                        const walker = document.createTreeWalker(
                            document.body,
                            NodeFilter.SHOW_TEXT,
                            {
                                acceptNode: function(node) {
                                    // 检查节点是否可见
                                    const style = window.getComputedStyle(node.parentElement);
                                    if (style.display === 'none' || style.visibility === 'hidden' || style.opacity === '0') {
                                        return NodeFilter.FILTER_REJECT;
                                    }
                                    // 过滤空白文本
                                    if (node.textContent.trim().length === 0) {
                                        return NodeFilter.FILTER_REJECT;
                                    }
                                    return NodeFilter.FILTER_ACCEPT;
                                }
                            }
                        );
                        
                        let text = '';
                        let node;
                        while (node = walker.nextNode()) {
                            text += node.textContent.trim() + '\\n';
                        }
                        
                        return text.trim();
                    }
                    return getVisibleText();
                """)
                
                if not page_content:
                    deeplogger.warning("JavaScript提取内容失败，尝试使用传统方法")
                    # 使用BeautifulSoup作为备选方案
                    soup = BeautifulSoup(browser.page_source, 'html.parser')
                    # 移除不需要的元素
                    for element in soup.find_all(['script', 'style', 'meta', 'link', 'noscript']):
                        element.decompose()
                    page_content = soup.get_text(separator='\\n', strip=True)
                
                if not page_content:
                    deeplogger.warning("无法提取页面内容")
                    return None
                
                # 清理内容
                page_content = re.sub(r'\\n\\s*\\n', '\\n', page_content)  # 移除多余的空行
                page_content = re.sub(r'\\s+', ' ', page_content)  # 规范化空白字符
                
                return page_content.strip()
                
            except Exception as e:
                deeplogger.error(f"获取页面内容时发生错误: {str(e)}")
                return None
            
        except Exception as e:
            deeplogger.error(f"浏览器获取内容失败: {str(e)}")
            return None
        finally:
            try:
                if hasattr(self, 'browser') and self.browser:
                    self.browser.quit()
                    self.browser = None
            except:
                pass

    def _recursive_search_and_analyze(self, query: str, objective: str, depth: int = 0, max_depth: int = 2, 
                                    total_length: int = 0, max_length: int = 1000, 
                                    total_tokens: int = 0) -> Tuple[list, list, int, int]:
        """
        递归执行搜索和分析过程
        
        Args:
            query: 搜索查询
            objective: 搜索目标
            depth: 当前递归深度
            max_depth: 最大递归深度
            total_length: 当前累计长度
            max_length: 最大允许长度
            total_tokens: 当前累计tokens
            
        Returns:
            Tuple[list, list, int, int]: (结果列表, 来源列表, 累计长度, 累计tokens)
        """
        # 基本情况: 达到最大深度或超过最大长度限制
        if depth >= max_depth or total_length >= max_length:
            return [], [], total_length, total_tokens

        # 主要搜索（带重试）
        step_results = None
        for retry in range(3):
            step_results = self.search_duckduckgo(
                query=query,
                num_results=3,
                max_retries=2,
                initial_delay=2
            )
            if step_results:
                break
            time.sleep(2)
            
        if not step_results:
            return [], [], total_length, total_tokens
            
        # 分析结果
        analysis, analysis_tokens = self._analyze_step_results(step_results, objective)
        total_tokens += analysis_tokens
        
        # 合并和清理结果
        all_results = clear_memory(step_results)
        
        if self.language == 'Chinese':
           is_chinese = True
        else:
           is_chinese = False   
        step_text = f"{'步骤' if is_chinese else 'Step'} {depth + 1}:\n{analysis.get('summary', '')}\n"
        step_length = self._count_summary_length(step_text)
        
        # 检查是否超过长度限制
        if total_length + step_length > max_length:
            return [], [], total_length, total_tokens
            
        # 记录结果
        results = [{
            'step': str(depth + 1),
            'findings': analysis.get('summary', '无结果'),
            'sources': [result['url'] for result in all_results if result['url']],
            'reliability': analysis.get('reliability_score', '3'),
            'missing_info': analysis.get('missing_aspects', []) if analysis.get('needs_more_info') else []
        }]
        
        # 记录来源
        sources = []
        for idx, result in enumerate(all_results):
            if result['url']:
                sources.append({
                    'step': str(depth + 1),
                    'title': result['title'],
                    'url': result['url']
                })
        
        total_length += step_length
        
        # 检查是否需要补充搜索
        query_info = self._analyze_query(query)
        skip_additional_search = query_info['type'] in ['weather', 'news', 'definition', 'howto', 
                                                      'comparison', 'sports', 'event', 'flight', 
                                                      'traffic', 'stock']
        

        deeplogger.info(f"当前深度: {depth},需要补充搜索: {skip_additional_search},分析: {analysis}, 文本长度长度: {total_length}")
        if not skip_additional_search and analysis.get('needs_more_info') and \
           len(analysis.get('missing_aspects', [])) > 0:
            
            # 递归处理每个缺失的信息点
            for missing_aspect in analysis.get('missing_aspects', []):
                deeplogger.info(f"\n需要补充信息，执行额外搜索: {missing_aspect}")
                sub_results, sub_sources, new_length, sub_tokens = self._recursive_search_and_analyze(
                    query=missing_aspect,
                    objective=f"补充信息: {missing_aspect}",
                    depth=depth + 1,
                    max_depth=max_depth,
                    total_length=total_length,
                    max_length=max_length,
                    total_tokens=total_tokens
                )
                
                if sub_results:
                    # 合并分析结果
                    results.extend(sub_results)
                    sources.extend(sub_sources)
                    total_length = new_length
                    total_tokens += sub_tokens  # 修改这里，累加子搜索的tokens
                    
                    # 如果达到长度限制，提前结束
                    if total_length >= max_length:
                        break
                        
        return results, sources, total_length, total_tokens

    def chain_of_thought_search(self, query: str) -> dict:
        """改进的链式搜索，增加错误处理、结果验证和内存管理"""
        try:
            # 创建搜索计划并验证
            search_plan_result, totaltokens = self._create_search_plan(query)
            # 确保total_tokens是整数
            if isinstance(totaltokens, str):
                try:
                    total_tokens = int(totaltokens.strip())
                except (ValueError, TypeError):
                    total_tokens = 0
            else:
                # 添加这个else分支来处理非字符串情况
                total_tokens = totaltokens if isinstance(totaltokens, int) else 0
            
            try:
                # 记录原始结果用于调试
                
                # 如果search_plan_result是空或None，创建一个基本的搜索计划
                if not search_plan_result:
                    search_plan = {
                        "final_objective": query,
                        "steps": [{
                            "step_name": "基础搜索",
                            "query": query,
                            "objective": "获取基本信息"
                        }]
                    }
                # 如果是字符串，尝试解析JSON
                elif isinstance(search_plan_result, str):
                    # 清理可能的格式问题
                    cleaned_result = search_plan_result.strip()                  
                    try:
                        # 首先尝试查找 plan_json 字段
                        plan_json_pattern = r'plan_json:\s*({[^}]*(?:{[^}]*})*[^}]*})'
                        plan_json_match = re.search(plan_json_pattern, cleaned_result, re.DOTALL)
                        
                        if plan_json_match:
                            cleaned_result = plan_json_match.group(1)
                            deeplogger.info(f"从plan_json提取的内容: {cleaned_result}")
                        else:
                            # 如果没有找到plan_json，尝试查找任何完整的JSON对象
                            json_pattern = r'({[^}]*(?:{[^}]*})*[^}]*})'
                            json_matches = list(re.finditer(json_pattern, cleaned_result))
                            if json_matches:
                                # 使用最后一个匹配的完整JSON
                                cleaned_result = json_matches[-1].group(1)
                                deeplogger.info(f"从完整JSON提取的内容: {cleaned_result}")
                            else:
                                raise ValueError("未找到有效的JSON内容")
                        
                        # 尝试解析JSON
                        search_plan = json.loads(cleaned_result)
                        
                        # 验证搜索计划的结构
                        if not isinstance(search_plan, dict):
                            raise ValueError("搜索计划必须是一个字典")
                        
                        if 'steps' not in search_plan or not isinstance(search_plan['steps'], list):
                            raise ValueError("搜索计划必须包含steps数组")
                            
                        deeplogger.info(f"成功解析的搜索计划: {json.dumps(search_plan, ensure_ascii=False)}")
                        
                    except (json.JSONDecodeError, ValueError) as e:
                        deeplogger.error(f"JSON解析或验证失败: {str(e)}")
                        # 使用基本搜索计划作为后备方案
                        search_plan = {
                            "final_objective": query,
                            "steps": [{
                                "step_name": "基础搜索",
                                "query": query,
                                "objective": "获取基本信息"
                            }]
                        }
                        deeplogger.info("使用基本搜索计划作为后备方案")
                # 如果已经是字典，直接使用
                else:
                    search_plan = search_plan_result
                
                # 如果搜索计划无效，使用基本计划
                if not search_plan or not search_plan.get('steps'):
                    search_plan = {
                        "final_objective": query,
                        "steps": [{
                            "step_name": "基础搜索",
                            "query": query,
                            "objective": "获取基本信息"
                        }]
                    }
                
                deeplogger.info(f"最终使用的搜索计划: {json.dumps(search_plan, ensure_ascii=False)}")
                
                final_results = []
                all_sources = []
                total_length = 0
                
                deeplogger.info(f"总的搜索计划xxxxxxxxxxxxxxxxxxxxxx: {search_plan}")
                for step in search_plan['steps']:
                    deeplogger.info(f"\n执行搜索步骤: {step['step_name']}")
                    deeplogger.info(f"搜索查询: {step['query']}")
                    
                    # 使用递归搜索和分析
                    step_results, step_sources, new_length, new_tokens = self._recursive_search_and_analyze(
                        query=step['query'],
                        objective=step['objective'],
                        total_length=total_length,
                        total_tokens=total_tokens
                    )
                    total_tokens = new_tokens
                    if step_results:
                        final_results.extend(step_results)
                        all_sources.extend(step_sources)
                        total_length = new_length
                        
                        
                        # 如果达到长度限制，提前结束
                        if total_length >= 30000:
                            break
                    
                    time.sleep(2)  # 避免速率限制
                
                # 检查是否有有效结果
                if not final_results:
                    return {
                        'final_summary': "所有搜索步骤均未返回有效结果",
                        'search_steps': "[]",
                        'sources': [],
                        'total_tokens':total_tokens,
                        'search_process': {
                            'initial_query': query,
                            'error': "无有效搜索结果"
                        }
                    }
                
                # 生成最终总结
                final_results_str = json.dumps(final_results, ensure_ascii=False)
                deeplogger.info(f"chain_of_thought_search搜索结果utf8编码后：{final_results_str[:1000]}")
                final_summary, totaltokens = self._generate_final_summary(final_results, query)
                # 确保totaltokens是整数
                if isinstance(totaltokens, str):
                    try:
                        totaltokens = int(totaltokens.strip())
                    except (ValueError, TypeError):
                        totaltokens = 0
                total_tokens += totaltokens
                deeplogger.info(f"最后的结果为：{final_summary}")
                deeplogger.info(f'最后模型生成结果｛final_summary｝')
                final_summary = final_summary.encode('utf-8', 'ignore').decode('utf-8')
                
                return {
                    'final_summary': final_summary or "无法获取搜索结果",
                    'search_steps': final_results_str,
                    'sources': all_sources,
                    'total_tokens':total_tokens,
                    'search_process': {
                        'initial_query': query,
                        'steps_completed': len(final_results),
                        'additional_searches': [
                            step['missing_info'] 
                            for step in final_results 
                            if step.get('missing_info')
                        ]
                    }
                }
                
            except Exception as e:
                deeplogger.info(f"搜索过程发生错误: {str(e)}")
                traceback.deeplogger.info_exc()
                return {
                    'final_summary': f"搜索过程中发生错误: {str(e)}",
                    'search_steps': "[]",
                    'sources': [],
                    'search_process': {
                        'initial_query': query,
                        'error': str(e)
                    }
                }

        except Exception as e:
            deeplogger.info(f"搜索过程中发生错误: {str(e)}")
            traceback.deeplogger.info_exc()
            return {
                'final_summary': f"搜索过程中发生错误: {str(e)}",
                'search_steps': "[]",
                'sources': [],
                'search_process': {
                    'initial_query': query,
                    'error': str(e)
                }
            }

    def _analyze_query(self, query: str) -> dict:
        """
        分析查询语言和类型，返回相应的分析结果
        
        Args:
            query: 用户查询字符串
            
        Returns:
            dict: 包含查询语言和类型的字典
        """
        # 检测语言
        is_chinese = bool(re.search(r'[\u4e00-\u9fff]', query))
        
        # 定义问题类型的关键词模式
        patterns = {
            'weather': {
                'cn': r'天气|气温|下雨|温度|湿度|阴晴',
                'en': r'weather|temperature|rain|humidity|forecast'
            },
            'news': {
                'cn': r'新闻|事件|发生|报道|最新|近期',
                'en': r'news|event|happen|report|latest|recent'
            },
            'definition': {
                'cn': r'是什么|定义|解释|意思|介绍|说明',
                'en': r'what is|define|explain|mean|describe'
            },
            'howto': {
                'cn': r'如何|怎么|怎样|教程|步骤|方法',
                'en': r'how to|steps|guide|method|way to'
            },
            'comparison': {
                'cn': r'对比|比较|区别|差异|优劣|哪个好',
                'en': r'compare|difference|versus|vs|better|which'
            },
            'sports': {
                'cn': r'比赛|赛事|结果|比分|赛况|排名',
                'en': r'sports|game|event|result|score|ranking'
            },
            'event': {
                'cn': r'活动|演出|展览|会议|庆典|节日',
                'en': r'event|show|exhibition|meeting|festival|holiday'
            },
            'flight': {
                'cn': r'航班|机票|航班号|航班状态|航班延误|航班取消',
                'en': r'flight|airline|flight number|flight status|flight delay|flight cancellation'
            },
            'traffic': {
                'cn': r'路况|拥堵|事故|施工|管制|拥堵',
                'en': r'traffic|congestion|accident|construction|control|congestion'
            },
            'stock': {
                'cn': r'股票|股市|股票价格|股票市场|股票走势|股票分析',
                'en': r'stock|stock market|stock price|stock movement|stock analysis'
            }
        }
        
        # 检测查询类型
        query_type = 'general'  # 默认类型
        lang_key = 'cn' if is_chinese else 'en'
        
        # 计算查询长度
        if is_chinese:
            # 对于中文，计算中文字符的数量
            chinese_chars = len(re.findall(r'[\u4e00-\u9fff]', query))
            query_length = chinese_chars
        else:
            # 对于英文，计算单词数量
            words = query.split()
            query_length = len(words)
        
        # 只有当查询长度在合适范围内时才进行模式匹配
        # 中文20字以内，英文10个单词以内
        length_threshold = 20 if is_chinese else 20
        if query_length <= length_threshold:
            for qtype, pattern in patterns.items():
                if re.search(pattern[lang_key], query.lower()):
                    query_type = qtype
                    break
        
        return {
            'language': 'chinese' if is_chinese else 'english',
            'type': query_type
        }

    def _get_search_prompt(self, query_info: dict) -> str:
        """
        根据查询分析结果生成相应的系统提示词
        
        Args:
            query_info: 查询分析结果字典
            
        Returns:
            str: 系统提示词
        """
        # 获取当前时间
        current_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
        
        # 基础提示词模板
        base_prompt = {
            'chinese': """你是一个专业的搜索规划助手。请严格按照以下JSON格式输出搜索计划，不要包含任何其他文本：

                {
                    "final_objective": "搜索目标的简要描述",
                    "steps": [
                        {
                            "step_name": "步骤名称",
                            "query": "搜索关键词",
                            "objective": "步骤目标"
                        }
                    ]
                }

                规则：
                1. 直接输出JSON，不要包含"plan_json:"或其他前缀
                2. 不要包含任何额外的解释或注释
                3. 最多5个搜索步骤
                4. 每个步骤必须重点明确
                5. 使用清晰简洁的语言
                6. 确保步骤之间逻辑连贯
                7. 使用双引号而不是单引号
                8. 所有字段都必须存在且不为空""",

            'english': """You are a professional search planning assistant. Output the search plan strictly in the following JSON format without any additional text:

                {
                    "final_objective": "Brief description of search goal",
                    "steps": [
                        {
                            "step_name": "Step name",
                            "query": "Search query",
                            "objective": "Step objective"
                        }
                    ]
                }

                    Rules:
                    1. Output JSON directly without "plan_json:" or any other prefix
                    2. Do not include any additional explanations or comments
                    3. Maximum 5 search steps
                    4. Each step must be focused and specific
                    5. Use clear, concise language
                    6. Ensure steps build upon each other logically
                    7. Use double quotes, not single quotes
                    8. All fields must exist and not be empty"""
                         }
        
        # 特定类型的额外提示
        type_prompts = {
            'weather': {
                'chinese': f"\n\n针对天气查询的特殊说明（当前时间：{current_time}）：\n- 优先搜索实时天气信息\n- 关注未来24小时天气预报\n- 考虑包含温度（使用摄氏度℃）、降水、空气质量等相关信息\n- 可能需要考虑地理位置相关的特定信息\n- 由于天气信息的即时性，限制为最多3个搜索步骤",
                'english': f"\n\nSpecial instructions for weather queries (Current time: {current_time}):\n- Prioritize real-time weather information\n- Focus on 24-hour forecast\n- Consider including temperature (in Celsius ℃), precipitation, air quality\n- May need to consider location-specific information\n- Due to the immediacy of weather information, limit to maximum 3 search steps"
            },
            'news': {
                'chinese': f"\n\n针对新闻查询的特殊说明（当前时间：{current_time}）：\n- 优先搜索最新信息（过去24小时内）\n- 考虑多个新闻来源的报道\n- 注意信息的时效性和可靠性\n- 可能需要包含事件背景信息\n- 为保证新闻时效性，限制为最多3个搜索步骤",
                'english': f"\n\nSpecial instructions for news queries (Current time: {current_time}):\n- Prioritize latest information (within past 24 hours)\n- Consider multiple news sources\n- Pay attention to timeliness and reliability\n- May need to include background information\n- To ensure news timeliness, limit to maximum 3 search steps"
            },
            'stock': {
                'chinese': f"\n\n针对股票市场查询的特殊说明（当前时间：{current_time}）：\n- 优先获取最新的股票价格和市场数据\n- 关注今日交易信息和市场动态\n- 考虑相关的市场新闻和分析\n- 可能需要包含历史数据对比\n- 由于市场信息的即时性，限制为最多3个搜索步骤",
                'english': f"\n\nSpecial instructions for stock market queries (Current time: {current_time}):\n- Prioritize latest stock prices and market data\n- Focus on today's trading information and market movements\n- Consider related market news and analysis\n- May need to include historical data comparison\n- Due to market information immediacy, limit to maximum 3 search steps"
            },
            'traffic': {
                'chinese': f"\n\n针对交通状况查询的特殊说明（当前时间：{current_time}）：\n- 优先获取实时路况信息\n- 关注交通事故和道路施工信息\n- 考虑公共交通运营状况\n- 可能需要包含备选路线建议\n- 由于交通信息的即时性，限制为最多3个搜索步骤",
                'english': f"\n\nSpecial instructions for traffic condition queries (Current time: {current_time}):\n- Prioritize real-time traffic conditions\n- Focus on accidents and road construction information\n- Consider public transportation status\n- May need to include alternative route suggestions\n- Due to traffic information immediacy, limit to maximum 3 search steps"
            },
            'sports': {
                'chinese': f"\n\n针对体育赛事查询的特殊说明（当前时间：{current_time}）：\n- 优先获取最新的比赛结果和赛况\n- 关注实时比分和赛事直播信息\n- 考虑赛前预测和赛后分析\n- 可能需要包含球队/运动员状态信息\n- 由于赛事信息的即时性，限制为最多3个搜索步骤",
                'english': f"\n\nSpecial instructions for sports event queries (Current time: {current_time}):\n- Prioritize latest match results and updates\n- Focus on live scores and streaming information\n- Consider pre-match predictions and post-match analysis\n- May need to include team/athlete status information\n- Due to event information immediacy, limit to maximum 3 search steps"
            },
            'flight': {
                'chinese': f"\n\n针对航班查询的特殊说明（当前时间：{current_time}）：\n- 优先获取实时航班状态\n- 关注延误和登机口信息\n- 考虑天气对航班的影响\n- 可能需要包含备选航班信息\n- 由于航班信息的即时性，限制为最多3个搜索步骤",
                'english': f"\n\nSpecial instructions for flight queries (Current time: {current_time}):\n- Prioritize real-time flight status\n- Focus on delays and gate information\n- Consider weather impact on flights\n- May need to include alternative flight options\n- Due to flight information immediacy, limit to maximum 3 search steps"
            },
            'event': {
                'chinese': f"\n\n针对活动查询的特殊说明（当前时间：{current_time}）：\n- 优先获取最新的活动状态和变更信息\n- 关注活动时间、地点和票务信息\n- 考虑交通和天气对活动的影响\n- 可能需要包含相关注意事项\n- 由于活动信息的时效性，限制为最多3个搜索步骤",
                'english': f"\n\nSpecial instructions for event queries (Current time: {current_time}):\n- Prioritize latest event status and changes\n- Focus on event time, location and ticket information\n- Consider transportation and weather impact\n- May need to include related notices\n- Due to event information timeliness, limit to maximum 3 search steps"
            },
            'definition': {
                'chinese': "\n\n针对定义解释类查询的特殊说明：\n- 优先搜索权威来源\n- 考虑包含基本定义和深入解释\n- 可能需要包含相关概念\n- 注意专业术语的准确性\n- 为确保定义准确性，限制为最多3个搜索步骤",
                'english': "\n\nSpecial instructions for definition queries:\n- Prioritize authoritative sources\n- Consider including basic definition and detailed explanation\n- May need to include related concepts\n- Pay attention to terminology accuracy\n- To ensure definition accuracy, limit to maximum 3 search steps"
            },
            'howto': {
                'chinese': "\n\n针对操作指导类查询的特殊说明：\n- 优先搜索步骤化的说明\n- 考虑包含具体示例\n- 注意搜索最佳实践\n- 可能需要包含常见问题解决方案\n- 为保证指导清晰度，限制为最多3个搜索步骤",
                'english': "\n\nSpecial instructions for how-to queries:\n- Prioritize step-by-step instructions\n- Consider including specific examples\n- Look for best practices\n- May need to include common problem solutions\n- To ensure instruction clarity, limit to maximum 3 search steps"
            },
            'comparison': {
                'chinese': "\n\n针对比较类查询的特殊说明：\n- 优先搜索客观对比信息\n- 考虑多个比较维度\n- 注意信息的全面性\n- 可能需要包含优劣分析\n- 为确保比较重点突出，限制为最多3个搜索步骤",
                'english': "\n\nSpecial instructions for comparison queries:\n- Prioritize objective comparison information\n- Consider multiple comparison dimensions\n- Pay attention to comprehensiveness\n- May need to include pros and cons analysis\n- To ensure comparison focus, limit to maximum 3 search steps"
            }
        }
        
        # 获取基础提示词
        prompt = base_prompt[query_info['language']]
        
        # 如果有特定类型的额外提示，添加它
        if query_info['type'] in type_prompts:
            prompt = prompt.replace('最多5步', '最多3步').replace('最多5个搜索步骤', '最多3个搜索步骤').replace('不超过5个步骤', '不超过3个步骤')
            prompt = prompt.replace('maximum 5 steps', 'maximum 3 steps').replace('Maximum 5 search steps', 'Maximum 3 search steps').replace('no more than 5 steps', 'no more than 3 steps')
            prompt += type_prompts[query_info['type']][query_info['language']]
            
        return prompt

    def _create_search_plan(self, query: str) -> dict:
        """创建搜索计划，根据查询类型和语言生成相应的提示词"""
        # 定义特定类型的查询
        type_prompts = {
            'sports': True,
            'event': True,
            'flight': True,
            'traffic': True,
            'stock': True,
            'weather': True,
            'news': True,
            'definition': True,
            'howto': True,
            'comparison': True
        }
        
        # 分析查询
        query_info = self._analyze_query(query)
        deeplogger.info(f"问题类型为: {query_info['type']}")
        # 获取相应的系统提示词
        sys_prompt = self._get_search_prompt(query_info)
        
        # 创建用户提示词
        user_prompt = f"Create a search plan for the query: {query}" if query_info['language'] == 'english' else f"为以下查询创建搜索计划：{query}"
        
        # 生成搜索计划
        plan_json, totaltokens = self._generate_summary_with_system_prompt(sys_prompt, user_prompt)

        deeplogger.info(f"生成的搜索计划原始内容: {plan_json}")
        
        # 去除多余的空格和换行符
        plan_json = plan_json.strip()
        try:
            # 尝试直接解析JSON
            try:
                plan_dict = json.loads(plan_json)
            except json.JSONDecodeError:
                # 如果直接解析失败，尝试提取JSON部分
                json_pattern = r'({[^}]*(?:{[^}]*})*[^}]*})'
                json_matches = list(re.finditer(json_pattern, plan_json))
                if json_matches:
                    # 使用最后一个匹配的完整JSON
                    json_str = json_matches[-1].group(1)
                    plan_dict = json.loads(json_str)
                else:
                    raise ValueError("未找到有效的JSON内容")
            
            # 根据查询类型确定最大步骤数
            max_steps = 3 if query_info['type'] in type_prompts else 5
            
            # 确保步骤不超过限制
            if len(plan_dict.get('steps', [])) > max_steps:
                plan_dict['steps'] = plan_dict['steps'][:max_steps]
            
            deeplogger.info(f"成功解析的搜索计划: {json.dumps(plan_dict, ensure_ascii=False)}")
            return plan_dict, totaltokens
        
        except (json.JSONDecodeError, ValueError) as e:
            # 如果解析失败，记录错误并返回简单的单步计划
            deeplogger.error(f"create_search_plan函数解析失败: {str(e)}")
            prompt = f"""Analyze the following question {query} and simplify it to no more than 30 words while keeping the original meaning largely unchanged."""
            simplequery, totaltokens = self._generate_summary_r1(prompt)
            deeplogger.info(f"create_search_plan函数简化为：{simplequery[:150]}")
            return {
                "final_objective": query,
                "steps": [{"step_name": "基础搜索", "query": simplequery, "objective": "获取基本信息"}]
            }, totaltokens

    def _count_summary_length(self, text: str) -> int:
        """
        计算文本中的中文字数和英文单词数之和
        
        Args:
            text: 要计算的文本
            
        Returns:
            int: 中文字数和英文单词数之和
        """
        # 计算中文字符数
        chinese_chars = len(re.findall(r'[\u4e00-\u9fff]', text))
        
        # 计算英文单词数（将文本按非字母数字字符分割）
        english_words = len(re.findall(r'\b[a-zA-Z]+\b', text))
        
        return chinese_chars + english_words

    def _analyze_step_results(self, results: list, objective: str) -> dict:
        """
        分析搜索结果，根据查询语言提供相应的分析
        
        Args:
            results: 搜索结果列表
            objective: 搜索目标
            
        Returns:
            Tuple[dict, int]: 包含分析结果的字典和token消耗数
        """
        # 检测目标语言
        if self.language == "Chinese":
            is_chinese = True
        else:
            is_chinese = False
        
        if not results:
            return {
                "summary": "未找到相关信息" if is_chinese else "No relevant information found",
                "needs_more_info": True,
                "additional_query": f"使用不同关键词搜索: {objective}" if is_chinese else f"Try different keywords: {objective}"
            }, 0
        
        processed_results = []
        total_tokens = 0
        
        # 提取关键词用于相关性排序
        objective_keywords = self._extract_keywords(objective)
        
        for result in results:
            try:
                detailed_content = self.summarize_content(
                    content=result.get('body', ''),
                    url=result.get('url', ''),
                    keyword=result.get('keyword', ''),
                    title=result.get('title', '')
                )
                if detailed_content and detailed_content.get('description'):
                    # 计算相关性分数
                    relevance_score = self._calculate_relevance(
                        objective_keywords,
                        detailed_content.get('title', ''),
                        detailed_content.get('description', '')
                    )
                    total_tokens += detailed_content.get('totaltokens', 0)
                    detailed_content['relevance_score'] = relevance_score
                    processed_results.append(detailed_content)
            except Exception as e:
                deeplogger.info(f"处理URL内容时出错: {str(e)}" if is_chinese else f"Error processing URL content: {str(e)}")
                continue
        
        if not processed_results:
            return {
                "summary": "无法处理搜索结果" if is_chinese else "Unable to process search results",
                "needs_more_info": True,
                "additional_query": f"尝试其他来源: {objective}" if is_chinese else f"Try other sources: {objective}"
            }, 0
        
        # 按相关性分数排序结果
        processed_results.sort(key=lambda x: x.get('relevance_score', 0), reverse=True)
        
        # 合并所有处理后的内容，使用更结构化的格式
        combined_sections = []
        seen_content = set()  # 用于去重
        
        for idx, result in enumerate(processed_results, 1):
            title = result.get('title', '').strip()
            description = result.get('description', '').strip()
            url = result.get('url', '').strip()
            
            # 跳过空内容
            if not description:
                continue
                
            # 内容去重
            content_hash = hashlib.md5(description.encode()).hexdigest()
            if content_hash in seen_content:
                continue
            seen_content.add(content_hash)
            
            # 格式化内容段落
            section = []
            if is_chinese:
                section.append(f"来源 {idx}:")
                if title:
                    section.append(f"标题: {title}")
                section.append(f"内容: {description}")
                section.append(f"链接: {url}")
                section.append(f"相关度: {result.get('relevance_score', 0):.2f}")
            else:
                section.append(f"Source {idx}:")
                if title:
                    section.append(f"Title: {title}")
                section.append(f"Content: {description}")
                section.append(f"URL: {url}")
                section.append(f"Relevance: {result.get('relevance_score', 0):.2f}")
            
            section.append("-" * 40)  # 分隔线
            combined_sections.append("\n".join(section))
        
        combined_content = "\n".join(combined_sections)
        
        if not combined_content:
            return {
                "summary": "未找到相关信息" if is_chinese else "No relevant information found",
                "needs_more_info": False,
                "missing_aspects": [],
                "reliability_score": "1"
            }, 0
        
        # 添加分析目标说明
        analysis_header = f"分析目标: {objective}\n{'='*50}\n\n" if is_chinese else f"Analysis Objective: {objective}\n{'='*50}\n\n"
        combined_content = analysis_header + combined_content
        
        # 根据语言选择系统提示词
        sys_prompt = """你是专业的信息分析专家。请仔细分析搜索结果中的所有信息，生成准确的JSON格式输出。

                    输出格式要求：
                    {
                        "summary": "string类型，提取的所有相关信息，不允许嵌套JSON",
                        "needs_more_info": boolean类型(true/false),
                        "missing_aspects": string数组["缺失信息1", "缺失信息2"],
                        "reliability_score": string类型("1"到"5"的字符串)
                    }

                    注意事项：
                    1. summary字段必须是字符串，不能包含嵌套的JSON对象
                    2. 所有信息必须准确、完整
                    3. reliability_score必须是字符串类型的1-5分值
                    4. 仅输出JSON，不要包含其他内容""" if is_chinese else """You are a professional information analyst. Please analyze all information in the search results and generate accurate JSON output.

                    Output Format Requirements:
                    {
                        "summary": "string type, all extracted relevant information, nested JSON not allowed",
                        "needs_more_info": boolean type(true/false),
                        "missing_aspects": string array["missing info 1", "missing info 2"],
                        "reliability_score": string type(string from "1" to "5")
                    }

                    Notes:
                    1. summary field must be a string, cannot contain nested JSON objects
                    2. All information must be accurate and complete
                    3. reliability_score must be a string value from 1-5
                    4. Output JSON only, do not include other content"""

        # 根据语言选择用户提示词
        analysis_prompt = f"""请分析以下内容，重点提取与分析目标"{objective}"相关的所有重要信息。

                        分析目标：
                        {objective}

                        内容：
                        {combined_content}

                        要求：
                        1. 重点提取与分析目标"{objective}"直接相关的信息，特别是数字、统计数据、关键指标等
                        2. 确保提取的信息准确性，保留原始数据和重要数值
                        3. 考虑各信息来源的相关度和可信度
                        4. 标注信息的时效性
                        5. 确保输出格式符合要求""" if is_chinese else f"""Please analyze the following content and extract all important information specifically related to the analysis objective "{objective}".

                        Analysis Objective:
                        {objective}

                        Content:
                        {combined_content}

                        Requirements:
                        1. Focus on extracting information directly related to the analysis objective "{objective}", especially numbers, statistics, and key metrics
                        2. Ensure accuracy of extracted information, preserve original data and important values
                        3. Consider the relevance and credibility of each source
                        4. Note information timeliness
                        5. Ensure output format compliance"""

        analysis_result, total_token = self._generate_summary_with_system_prompt(sys_prompt, analysis_prompt)
        total_tokens += total_token
        try:
            # 尝试解析JSON
            analysis_dict = json.loads(analysis_result)
            analysis_content = str(analysis_dict.get("summary", ""))
            if analysis_content == "":
                combined_results = combined_content              
            else:
                combined_results = f" 总结：{analysis_content}\n{'='*50}\n内容:{combined_content}\n{'='*50}"
            # 验证和规范化JSON结构
            # summary = str(analysis_dict.get("summary", ""))
            validated_dict = {
                "summary": f" {combined_results}...",
                "needs_more_info": bool(analysis_dict.get("needs_more_info", False)),
                "missing_aspects": [str(aspect) for aspect in analysis_dict.get("missing_aspects", [])],
                "reliability_score": str(min(max(int(float(analysis_dict.get("reliability_score", "3"))), 1), 5))
            }
            deeplogger.info(f"分析结果为：{validated_dict}")
            return validated_dict, total_tokens
            
        except (json.JSONDecodeError, ValueError, TypeError) as e:
            # 如果JSON解析失败，返回基本格式
            deeplogger.error(f"分析结果解析失败: {str(e)}返回combined_content")
            return {
                "summary": f" {combined_content}..." if is_chinese else 
                          f"{combined_content}...",
                "needs_more_info": False,
                "missing_aspects": [],
                "reliability_score": "3"
            }, total_tokens

    def _generate_final_summary(self, results: list, original_query: str) -> str:
        """
        根据搜索结果生成最终的综合报告，会根据查询语言生成相应的提示词
        
        Args:
            results: 搜索结果列表
            original_query: 原始查询
            
        Returns:
            str: 生成的综合报告
        """
        log_with_request_id(f"开始生成最终答案{'='*50}", level=logging.INFO, request_id=request_id)
        # 检测查询语言
        totaltokens = 0
        is_chinese = bool(re.search(r'[\u4e00-\u9fff]', original_query))
        
        # 获取当前时间
        current_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
        
        # 合并所有发现，限制总长度不超过30000
        all_findings = ""
        new_length = 0
        max_length = 30000
        step_text = ""
        step_count = 0
        
        for r in results:
            # 构建当前步骤的文本
            step_text = f"{'步骤' if is_chinese else 'Step'} {r['step']}:\n{r['findings']}\n"
            step_count = self._count_summary_length(step_text)
            # 计算添加这个步骤后的总长度
            new_length += step_count
            # 如果添加这个步骤会超过限制，就停止添加
            if new_length > max_length:
                break           
            # 如果不超过限制，就添加这个步骤
            all_findings += step_text
            step_text = ""
            step_count = 0
        
        deeplogger.info(f"最后的all_findings提示词变量为 /n {'='*50} \n {all_findings}")
        log_with_request_id(f"检索的总内容为/n{'='*50}\n{all_findings}", level=logging.INFO, request_id=request_id)
        
        # 系统提示词
        if is_chinese:
            system_prompt = """你是一位专业的信息分析专家，擅长处理各类复杂问题并生成专业分析报告。
                            你的分析需要保持专业、客观、严谨的写作风格。"""
        else:
            system_prompt = """You are a professional information analyst, skilled in handling complex problems and generating professional analysis reports.
                            Your analysis should maintain a professional, objective, and rigorous writing style."""
        
        # 用户提示词
        if is_chinese:
            user_prompt = f"""问题分析：
                        请首先分析"{original_query}"的类型，可能是：
                        1. 研究性问题：需要深入的背景调研，学术分析和数据支持
                        2. 分析性问题：需要专业的见解和建议
                        3. 技术性问题：需要具体的解决方案和技术细节
                        4. 对比性问题：需要多维度的比较数据
                        5. 事实性问题：需要准确的数字和事实依据
                        6. 其他类型：根据具体情况调整

                        参考资料：
                        {all_findings}

                        基本要求：
                        - 字数限制：{self.summary_length}字符以内
                        - 输出语言：{self.language}
                        - 如果输出公式，请采用latex格式，行内公式用$公式$，行间公式用$$行间公式$$

                        请根据问题类型选择最合适的格式，生成专业的分析报告。只输出你对于{original_query}的回答，不要输出其他内容"""
        else:
            user_prompt = f"""Question Analysis:
                        First analyze the type of "{original_query}", which could be:
                        1. Research Question: Requiring in-depth background research, academic analysis and data support
                        2. Analytical Question: Needing professional insights and recommendations
                        3. Technical Question: Requiring specific solutions and technical details
                        4. Comparative Question: Needing multi-dimensional comparison data
                        5. Factual Question: Requiring accurate numbers and factual evidence
                        6. Other Types: Adjust based on specific needs

                        Reference Material:
                        {all_findings}

                        Basic Requirements:
                        - Length Limit: {self.summary_length} characters
                        - Output Language: {self.language}
                        - If you output formulas, please use LaTeX format: inline formulas as $formula$, and display formulas as $$formula$$

                        Please select the most appropriate format based on the question type and generate a professional analysis report. Only output your answer for {original_query}, do not output other content."""

        result, totaltokens = self._generate_summary_with_system_prompt(system_prompt, user_prompt)
        return result, totaltokens








    def search_duckduckgo(self, query, region='wt-wt', safesearch='off', num_results=3, max_retries=5, initial_delay=1):
        """
        改进的搜索函数，通过内容相关性判断结果
        
        Args:
            query (str): 搜索查询
            region (str): 搜索地区，默认'wt-wt'
            safesearch (str): 安全搜索设置，默认'off'
            num_results (int): 返回结果数量，默认3
            max_retries (int): 最大重试次数，默认5
            initial_delay (int): 初始延迟时间（秒），默认1
            
        Returns:
            list: 相关搜索结果列表
        """
        # 检测查询语言和类型
        query_info = self._analyze_query(query)
        
        # 如果查询类型在预定义模式中，将结果数量设置为1

        patterns = {
                'sports': True,
                'event': True,
                'flight': True,
                'traffic': True,
                'stock': True,
                'weather': True,
                'news': True,
                'definition': True,
                'howto': True,
                'comparison': True
                            }

        if query_info['type'] in patterns:
            num_results = 1
            
        # 检测查询语言
        is_chinese = query_info['language'] == 'chinese'
        log_prefix = "搜索过程" if is_chinese else "Search process"
        
        # 检查缓存
        cache_key = f"{query}_{region}_{num_results}"
        if cache_key in self.search_cache:
            cache_entry = self.search_cache[cache_key]
            # 检查缓存是否在24小时内
            if time.time() - cache_entry['timestamp'] < 24 * 3600:
                deeplogger.info(f"{log_prefix}: " + ("使用缓存结果" if is_chinese else "Using cached results"))
                return cache_entry['results']
            else:
                # 清除过期缓存
                del self.search_cache[cache_key]

        results = []
        retry_count = 0
        
        # 根据查询语言优化搜索策略
        search_strategies = []
        if is_chinese:
            search_strategies = [
                # 对中文查询，优先使用直接网页搜索
                lambda ddg: self._direct_web_search(query, num_results),
                # 然后是文本搜索
                lambda ddg: ddg.text(
                    keywords=query,
                    region=region or 'cn-zh',  # 中文查询默认使用中文区域
                    safesearch=safesearch,
                    max_results=num_results
                ),
                # 最后是新闻搜索
                lambda ddg: ddg.news(
                    keywords=query,
                    region=region or 'cn-zh',
                    safesearch=safesearch,
                    max_results=num_results
                )
            ]
        else:
            search_strategies = [
                # 对英文查询，优先使用文本搜索
                lambda ddg: ddg.text(
                    keywords=query,
                    region=region,
                    safesearch=safesearch,
                    max_results=num_results
                ),
                # 然后是直接网页搜索
                lambda ddg: self._direct_web_search(query, num_results),
                # 最后是新闻搜索
                lambda ddg: ddg.news(
                    keywords=query,
                    region=region,
                    safesearch=safesearch,
                    max_results=num_results
                )
            ]

        while retry_count < max_retries and not results:
            try:
                # 计算延迟时间并等待
                wait_time = initial_delay * (1 + random.random())
                deeplogger.info(f"{log_prefix}: " + (f"等待 {wait_time:.2f} 秒..." if is_chinese else f"Waiting {wait_time:.2f} seconds..."))
                time.sleep(wait_time)
                
                strategy_index = retry_count % len(search_strategies)
                deeplogger.info(f"{log_prefix}: " + (f"尝试搜索策略 {strategy_index + 1}/{len(search_strategies)}" if is_chinese else f"Trying search strategy {strategy_index + 1}/{len(search_strategies)}"))
                
                # 执行搜索策略
                try:
                    if strategy_index == 0 and is_chinese:
                        search_results = search_strategies[0](None)
                    else:
                        ddg = DDGS(
                            proxy=self.proxy,
                            timeout=15
                        )
                        search_results = list(search_strategies[strategy_index](ddg))
                    
                    result_count = len(search_results) if search_results else 0
                    deeplogger.info(f"{log_prefix}: " + (f"获取到 {result_count} 条原始结果" if is_chinese else f"Retrieved {result_count} raw results"))
                    
                    if search_results:
                        # 输出搜索结果摘要
                        for idx, result in enumerate(search_results):
                            deeplogger.info(f"\n{log_prefix}: " + (f"结果 {idx + 1}:" if is_chinese else f"Result {idx + 1}:"))
                            deeplogger.info(f"{'标题' if is_chinese else 'Title'}: {result.get('title', '')[:50]}...")
                            deeplogger.info(f"URL: {result.get('href', result.get('url', ''))}")
                            deeplogger.info(f"{'内容' if is_chinese else 'Content'}: {result.get('body', result.get('description', ''))[:100]}...")
                        
                        # 提取关键词并检查相关性
                        relevant_results = []
                        query_keywords = self._extract_keywords(query)
                        deeplogger.info(f"{log_prefix}: " + (f"提取的关键词: {query_keywords}" if is_chinese else f"Extracted keywords: {query_keywords}"))
                        
                        # 设置相关性阈值（中文查询使用较低的阈值，因为分词可能不够准确）
                        relevance_threshold = 0.15 if is_chinese else 0.2
                        
                        for result in search_results:
                            title = result.get('title', '')
                            body = result.get('body', result.get('description', ''))
                            url = result.get('href', result.get('url', ''))
                            
                            # 计算相关性得分
                            relevance_score = self._calculate_relevance(
                                query_keywords,
                                title,
                                body
                            )
                            deeplogger.info(f"{log_prefix}: " + (f"标题: {title[:50]}... 相关性得分: {relevance_score}" if is_chinese else f"Title: {title[:50]}... Relevance score: {relevance_score}"))
                            
                            if relevance_score > relevance_threshold:
                                relevant_results.append({
                                    'title': title,
                                    'url': url,
                                    'body': body,
                                    'keyword': query,
                                    'relevance': relevance_score
                                })
                        
                        # 处理相关结果
                        if relevant_results:
                            # 按相关性得分排序
                            relevant_results.sort(key=lambda x: x['relevance'], reverse=True)
                            deeplogger.info(f"{log_prefix}: " + (f"成功获取到 {len(relevant_results)} 条相关搜索结果" if is_chinese else f"Successfully retrieved {len(relevant_results)} relevant results"))
                            
                            # 更新缓存
                            self.search_cache[cache_key] = {
                                'results': relevant_results,
                                'timestamp': time.time()
                            }
                            
                            return relevant_results
                        else:
                            deeplogger.info(f"{log_prefix}: " + ("搜索结果与查询内容不相关，尝试下一个策略" if is_chinese else "Search results not relevant, trying next strategy"))
                            
                except Exception as search_error:
                    deeplogger.info(f"{log_prefix}: " + (f"搜索策略 {strategy_index + 1} 执行失败: {str(search_error)}" if is_chinese else f"Search strategy {strategy_index + 1} failed: {str(search_error)}"))
                    if retry_count == max_retries - 1:
                        raise search_error
                        
            except Exception as e:
                deeplogger.info(f"{log_prefix}: " + (f"搜索尝试 {retry_count + 1}/{max_retries} 失败: {str(e)}" if is_chinese else f"Search attempt {retry_count + 1}/{max_retries} failed: {str(e)}"))
                if retry_count == max_retries - 1:
                    raise e
                
            retry_count += 1
        
        deeplogger.info(f"{log_prefix}: " + ("所有搜索尝试都失败，返回空结果" if is_chinese else "All search attempts failed, returning empty results"))
        return []
    
    def _extract_keywords(self, text):
        """
        根据查询语言提取关键词
        
        Args:
            text (str): 查询文本
            
        Returns:
            list: 提取的关键词列表
        """
        # 检测语言
        is_chinese = bool(re.search(r'[\u4e00-\u9fff]', text))
        
        # 中文停用词
        chinese_stop_words = {
            '的', '了', '是', '在', '我', '有', '和', '就', '不', '人', '都', 
            '一', '一个', '上', '也', '很', '到', '说', '要', '去', '你', '会', 
            '着', '没有', '看', '好', '自己', '这', '什么', '怎么', '如何',
            '吗', '呢', '啊', '哪', '那', '但', '但是', '而', '而且', '或', '或者',
            '所以', '因为', '因此', '可以', '可能', '应该', '今天', '明天', '昨天'
        }
        
        # 英文停用词
        english_stop_words = {
            'a', 'an', 'and', 'are', 'as', 'at', 'be', 'by', 'for', 'from',
            'has', 'he', 'in', 'is', 'it', 'its', 'of', 'on', 'that', 'the',
            'to', 'was', 'were', 'will', 'with', 'the', 'this', 'but', 'they',
            'have', 'had', 'what', 'when', 'where', 'who', 'which', 'why', 'how',
            'all', 'any', 'both', 'each', 'few', 'more', 'most', 'other', 'some',
            'such', 'no', 'nor', 'not', 'only', 'own', 'same', 'so', 'than',
            'too', 'very', 'can', 'cannot', "can't", 'could', 'may', 'might',
            'must', 'need', 'shall', 'should', 'would', 'today', 'tomorrow', 'yesterday'
        }

        if is_chinese:
            # 中文处理
            # 1. 移除标点符号和特殊字符，保留中文、数字
            text = re.sub(r'[^\u4e00-\u9fff\d]', ' ', text)
            
            # 2. 基本的中文分词：按2-3个字符组合
            words = []
            text_chars = list(text)
            
            # 提取数字
            numbers = re.findall(r'\d+', text)
            words.extend(numbers)
            
            # 处理中文字符
            i = 0
            while i < len(text_chars):
                # 尝试3字组合
                if i + 2 < len(text_chars):
                    trigram = ''.join(text_chars[i:i+3])
                    if all('\u4e00' <= char <= '\u9fff' for char in trigram):
                        words.append(trigram)
                
                # 尝试2字组合
                if i + 1 < len(text_chars):
                    bigram = ''.join(text_chars[i:i+2])
                    if all('\u4e00' <= char <= '\u9fff' for char in bigram):
                        words.append(bigram)
                
                # 单字
                if '\u4e00' <= text_chars[i] <= '\u9fff':
                    words.append(text_chars[i])
                
                i += 1
            
            # 移除停用词
            words = [w for w in words if w not in chinese_stop_words and len(w.strip()) > 0]
            
        else:
            # 英文处理
            # 1. 转换为小写
            text = text.lower()
            
            # 2. 移除标点符号，保留字母、数字
            text = re.sub(r'[^a-z0-9\s]', ' ', text)
            
            # 3. 分词
            words = text.split()
            
            # 4. 提取数字
            numbers = [word for word in words if word.isdigit()]
            
            # 5. 处理非数字词
            words = [
                word for word in words 
                if word not in english_stop_words 
                and len(word.strip()) > 1  # 忽略单字母
            ]
            
            # 6. 添加提取的数字
            words.extend(numbers)
        
        # 去重并保持原有顺序
        seen = set()
        return [x for x in words if not (x in seen or seen.add(x))]

    def _calculate_relevance(self, query_keywords, title, body):
        """改进的相关性计算"""
        if not query_keywords:
            return 0
        
        # 将标题和正文合并，转为小写
        content = f"{title} {body}".lower()
        
        # 计算关键词匹配
        matches = 0
        total_weight = len(query_keywords)
        
        for keyword in query_keywords:
            keyword = keyword.lower()
            # 标题匹配权重更高
            if keyword in title.lower():
                matches += 1.5
            # 内容匹配
            if keyword in content:
                matches += 1.0
                
        # 计算相关性得分 (0-1之间)
        relevance = matches / (total_weight * 1.5)  # 考虑到权重调整
        
        return min(1.0, relevance)  # 确保得分不超过1

    def _direct_web_search(self, query, num_results=3):
        """直接从网页获取搜索结果，使用 HTTP 代理替代 SOCKS"""
        try:
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.5',
                'Connection': 'keep-alive'
            }
            
            # 尝试不同的搜索 URL
            search_urls = [
                f"https://html.duckduckgo.com/html/?q={query}",
                f"https://duckduckgo.com/html/?q={query}",
                f"https://lite.duckduckgo.com/lite/?q={query}"
            ]
            
            for url in search_urls:
                try:
                    # 首先尝试不使用代理
                    response = requests.get(
                        url,
                        headers=headers,
                        timeout=15
                    )
                    
                    if response.status_code != 200:
                        # 如果直接访问失败，尝试使用 HTTP 代理
                        http_proxy = {
                            'http': 'http://***********:1070',
                            'https': 'http://***********:1070'
                        }
                        response = requests.get(
                            url,
                            headers=headers,
                            proxies=http_proxy,
                            timeout=15
                        )
                    
                    if response.status_code == 200:
                        soup = BeautifulSoup(response.text, 'html.parser')
                        results = []
                        
                        # 处理不同的页面结构
                        result_divs = (
                            soup.find_all('div', class_='result') or 
                            soup.find_all('div', class_='links_main') or
                            soup.find_all('div', class_='result-link')
                        )
                        
                        for result in result_divs:
                            title_elem = (
                                result.find('a', class_='result__a') or
                                result.find('a', class_='link') or
                                result.find('a')
                            )
                            snippet_elem = (
                                result.find('a', class_='result__snippet') or
                                result.find('div', class_='result__snippet') or
                                result.find('div', class_='link-text')
                            )
                            
                            if title_elem:
                                title = title_elem.get_text().strip()
                                url = title_elem.get('href', '')
                                body = snippet_elem.get_text().strip() if snippet_elem else ''
                                
                                if title and url:
                                    results.append({
                                        'title': title,
                                        'url': url,
                                        'body': body,
                                    })
                        
                        if results:
                            return results[:num_results]
                            
                except Exception as e:
                    deeplogger.info(f"URL {url} 搜索失败: {str(e)}")
                    continue
            
            deeplogger.info("所有直接搜索尝试都失败")
            return []
            
        except Exception as e:
            deeplogger.info(f"直接网页搜索失败: {str(e)}")
            return []
        
        
    def summarize_content(self, content: str, url: str, keyword: str, title: str) -> dict:
        """改进的内容总结函数"""
        try:
            # 获取URL内容
            urlcontent = self.fetch_url_content(url)
            
            # 如果无法获取URL内容，使用原始content
            if urlcontent is None:
                deeplogger.info(f"无法获取URL内容: {url},使用原始内容:{content}")
                return {
                    "title": title,
                    "url": url,
                    "description": content
                }
            
            # 预处理内容
            cleaned_urlcontent = self._preprocess_content(urlcontent)
            cleaned_content = self._preprocess_content(content)
            deeplogger.info(f"搜索到的原始内容: {cleaned_content}")
            deeplogger.info(f"总结的网页内容: {cleaned_urlcontent}")
            # 如果两个内容都为空，返回原始内容
            if not cleaned_urlcontent and not cleaned_content:
                deeplogger.info("清理后的内容为空")
                return {
                    "title": title,
                    "url": url,
                    "description": content or "无有效内容"
                }
            
            # 使用非空的内容
            final_content = cleaned_urlcontent or cleaned_content
            
            deeplogger.info(f'处理后的内容: {final_content[:200]}...') 
            deeplogger.info("-" * 80)
            
            # 生成摘要
            summary_prompt = self._create_summary_prompt(
                cleaned_content, 
                cleaned_urlcontent, 
                keyword
            )
            
            summary,totaltokens = self._generate_summary(summary_prompt)
            summary += cleaned_content
            deeplogger.info(f"大模型提取的网页内容: {summary}")
            # 如果摘要生成失败，使用原始内容
            if not summary:
                deeplogger.info("生成摘要失败，使用原始内容")
                summary = final_content           
            deeplogger.info(f"生成的摘要: {summary[:200]}...")
            deeplogger.info("-" * 80)
            
            return {
                "title": title,
                "url": url,
                "description": summary,
                "totaltokens": totaltokens
            }
            
        except Exception as e:
            deeplogger.info(f"内容处理过程中出错: {str(e)}")
            return {
                "title": title,
                "url": url,
                "description": content or "处理过程中发生错误",
                "totaltokens": 0
            }






    def fetch_url_content(self, url: str) -> str:
        """获取URL内容，包含错误处理和特殊情况处理"""
        original_error = None
        
        # 首先尝试使用代理
        try:
            content = self._fetch_with_proxy(url)
            if content:
                return content
        except Exception as e:
            original_error = e
            deeplogger.warning(f"使用代理获取内容失败: {str(e)}")

        # 如果代理失败，尝试直接连接
        try:
            content = self._fetch_without_proxy(url)
            if content:
                return content
        except Exception as e:
            deeplogger.warning(f"直接连接获取内容失败: {str(e)}")

        # 如果普通请求都失败了，尝试使用浏览器
        try:
            content = self._fetch_with_browser(url)
            if content:
                return content
        except Exception as e:
            deeplogger.warning(f"浏览器获取内容失败: {str(e)}")

        # 如果浏览器也失败了，尝试使用备用方法
        try:
            content = self._get_content_alternative(url)
            if content:
                return content
        except Exception as e:
            deeplogger.warning(f"备用方法获取内容失败: {str(e)}")

        # 所有方法都失败，返回错误信息
        error_msg = str(original_error) if original_error else "所有获取方法均失败"
        return f"无法获取URL内容: {url}, 错误: {error_msg}"

    def _fetch_with_proxy(self, url: str) -> Optional[str]:
        """使用代理获取内容，支持自动切换代理"""
        last_error = None
        
        # 尝试最多3个不同的代理
        for _ in range(min(3, len(self.proxy_list))):
            proxy = self._get_next_proxy()
            proxy_str = f"{proxy['host']}:{proxy['port']}"
            
            try:
                session = requests.Session()
                retries = Retry(
                    total=2,
                    backoff_factor=0.5,
                    status_forcelist=[403, 429, 500, 502, 503, 504],
                    allowed_methods=["HEAD", "GET", "POST", "OPTIONS"]
                )
                session.mount("https://", HTTPAdapter(max_retries=retries))
                session.mount("http://", HTTPAdapter(max_retries=retries))

                # 更真实的浏览器头部
                headers = {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
                    'Accept-Language': 'en-US,en;q=0.9',
                    'Accept-Encoding': 'gzip, deflate, br',
                    'Connection': 'keep-alive',
                    'Cache-Control': 'max-age=0',
                    'Sec-Ch-Ua': '"Chromium";v="122", "Not(A:Brand";v="24", "Google Chrome";v="122"',
                    'Sec-Ch-Ua-Mobile': '?0',
                    'Sec-Ch-Ua-Platform': '"Windows"',
                    'Sec-Fetch-Dest': 'document',
                    'Sec-Fetch-Mode': 'navigate',
                    'Sec-Fetch-Site': 'none',
                    'Sec-Fetch-User': '?1',
                    'Upgrade-Insecure-Requests': '1',
                    'Referer': 'https://www.google.com/'
                }

                # 添加随机Cookie
                cookies = {
                    'visitor_id': f'{random.randint(10000, 99999)}',
                    'session_id': f'{uuid.uuid4()}',
                    'last_visit': f'{int(time.time())}'
                }

                proxies = {
                    'http': f"{proxy['protocol']}://{proxy['host']}:{proxy['port']}",
                    'https': f"{proxy['protocol']}://{proxy['host']}:{proxy['port']}"
                }

                # 首先发送一个HEAD请求
                try:
                    session.head(url, headers=headers, proxies=proxies, timeout=5, verify=False)
                    time.sleep(random.uniform(1, 2))  # 随机延迟
                except:
                    pass

                # 使用更短的超时时间
                response = session.get(
                    url,
                    headers=headers,
                    cookies=cookies,
                    proxies=proxies,
                    timeout=(5, 10),  # (连接超时, 读取超时)
                    verify=False,
                    allow_redirects=True
                )

                if response.status_code == 200:
                    # 重置这个代理的失败计数
                    self.proxy_fail_count[proxy_str] = 0
                    
                    # 随机延迟，模拟人类行为
                    time.sleep(random.uniform(0.5, 1.5))
                    
                    content_type = response.headers.get('content-type', '').lower()
                    if 'application/pdf' in content_type:
                        return self._extract_pdf_content(response.content)
                    else:
                        return self._extract_html_content(response)
                elif response.status_code == 403:
                    deeplogger.warning(f"访问被拒绝，尝试使用浏览器模式: {url}")
                    return self._fetch_with_browser(url)
                        
            except Exception as e:
                last_error = e
                self.proxy_fail_count[proxy_str] = self.proxy_fail_count.get(proxy_str, 0) + 1
                deeplogger.warning(f"代理 {proxy_str} 获取失败: {str(e)}")
                continue

        if last_error:
            raise last_error
        return None

    def _fetch_without_proxy(self, url: str) -> Optional[str]:
        """不使用代理直接获取内容"""
        session = requests.Session()
        retries = Retry(
            total=2,
            backoff_factor=0.5,
            status_forcelist=[403, 429, 500, 502, 503, 504],
            allowed_methods=["HEAD", "GET", "POST", "OPTIONS"]
        )
        session.mount("https://", HTTPAdapter(max_retries=retries))
        session.mount("http://", HTTPAdapter(max_retries=retries))

        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        }

        response = session.get(
            url,
            headers=headers,
            timeout=15,
            verify=False
        )

        if response.status_code == 200:
            content_type = response.headers.get('content-type', '').lower()
            if 'application/pdf' in content_type:
                return self._extract_pdf_content(response.content)
            else:
                return self._extract_html_content(response)
        return None

    def _is_captcha_page(self, response) -> bool:
        """检查是否是验证码页面"""
        content = response.text.lower()
        captcha_indicators = [
            'captcha',
            'robot verification',
            'human verification',
            'please verify',
            'security check',
            'are you a robot',
            'verify you are human',
            'cloudflare',
            'ddos protection',
            'challenge-form',
            'distil_r_captcha',
            'hcaptcha',
            'recaptcha'
        ]
        return any(indicator in content for indicator in captcha_indicators)

    def _is_blocked(self, response) -> bool:
        """检查是否被网站封锁"""
        content = response.text.lower()
        blocked_indicators = [
            'access denied',
            'forbidden',
            'blocked',
            'too many requests',
            'rate limit exceeded',
            '您的访问受限',
            '访问频率过高',
            '请求过于频繁'
        ]
        return (response.status_code in [403, 429, 503] or 
                any(indicator in content for indicator in blocked_indicators))

    def _get_content_alternative(self, url: str) -> Optional[str]:
        """尝试使用备用方法获取内容"""
        try:
            # 1. 尝试使用Google Cache
            cache_url = f"https://webcache.googleusercontent.com/search?q=cache:{url}"
            headers = {'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) Chrome/120.0.0.0 Safari/537.36'}
            response = requests.get(cache_url, headers=headers, timeout=self.timeout)
            if response.status_code == 200:
                return self._extract_html_content(response)

            # 2. 尝试使用Archive.org
            archive_url = f"https://web.archive.org/web/{url}"
            response = requests.get(archive_url, headers=headers, timeout=self.timeout)
            if response.status_code == 200:
                return self._extract_html_content(response)

            return None
        except Exception as e:
            deeplogger.error(f"备用方法获取内容失败: {str(e)}")
            return None

    def _extract_pdf_content(self, content: bytes) -> str:
        """
        从PDF内容中提取文本
        
        Args:
            content: PDF文件的二进制内容
            
        Returns:
            str: 提取的文本内容
        """
        try:
            # 创建一个BytesIO对象
            pdf_file = BytesIO(content)
            
            # 创建PDF阅读器
            reader = PdfReader(pdf_file)
            
            # 提取所有页面的文本
            text_content = []
            for page in reader.pages:
                try:
                    text_content.append(page.extract_text())
                except Exception as e:
                    deeplogger.info(f"提取PDF页面时出错: {str(e)}")
                    continue
            
            # 关闭BytesIO对象
            pdf_file.close()
            
            # 合并所有文本
            return "\n".join(text_content)
            
        except Exception as e:
            deeplogger.info(f"PDF处理失败: {str(e)}")
            return ""

    def _extract_html_content(self, response: requests.Response) -> str:
        try:
            detected_encoding = chardet.detect(response.content)['encoding']
            response.encoding = detected_encoding
            
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 移除脚本和样式元素
            for script in soup(["script", "style"]):
                script.decompose()
            
            # 获取文并去除所有换行符和多余的空白
            text_content = ' '.join(soup.stripped_strings)
            # text_content=self._filter_sensitive_content(text_content)
            return text_content
        except Exception as e:
            deeplogger.info(f"Error extracting HTML content: {e}")
            return ""




    def _create_summary_prompt(self, content: str, urlcontent: str, keyword: str) -> str:
        """
        根据关键词语言创建相应的摘要提示词
        
        Args:
            content: 搜索结果内容
            urlcontent: 网页内容
            keyword: 搜索关键词
            
        Returns:
            str: 根据语言生成的提示词
        """
        # 检测关键词语言
        if self.language == "Chinese":
            is_chinese = True
        else:
            is_chinese = False
        
        if is_chinese:
            return f"""
            任务：从提供的内容中提取和总结与"{keyword}"相关的信息。
            
            内容来源：
            1. 网页内容：
            {urlcontent[:10000]}
                 
            预处理要求：
            1. 内容过滤：
               - 首先仔细阅读所有内容
               - 识别并删除所有与"{keyword}"无关的段落和信息
               - 只保留与"{keyword}"直接相关或强相关的内容
               
            2. 相关性判断标准：
               - 核心主题必须是"{keyword}"或与之密切相关
               - 内容必须对理解或解释"{keyword}"有实质性帮助
               - 仅保留对"{keyword}"有明确解释、补充或支持作用的信息
            
            分析要求：
            1. 重点关注：
               - 仅分析筛选后的相关内容
               - 如果筛选后没有相关信息，请返回空字符串
               
            2. 相关性检查：
               - 查找"{keyword}"的精确匹配
               - 查找相关同义词和相关术语
               - 查找与"{keyword}"相关的上下文信息
            
            摘要要求：
            1. 长度：提供简明扼要的摘要（约1000字）
            2. 内容优先级：
               - 最新信息优先
               - 与"{keyword}"最相关的信息
               - 事实数据和统计信息

            质量标准：
            1. 准确性：仅包含来源中明确陈述的信息
            2. 客观性：不包含个人观点或解释
            3. 相关性：确保每一句话都与"{keyword}"相关
            4. 清晰度：使用清晰简洁的语言
            
            特别说明：
            - 如果在内容中找不到与"{keyword}"相关的信息，必须返回空字符串
            - 不要生成或推测任何未在原始内容中明确提到的信息
            - 宁可少写也不要包含无关信息
            """
        else:
            return f"""
            Task: Extract and summarize information related to "{keyword}" from the provided content.
            
            Content Sources:
            1. Web Page Content:
            {urlcontent[:10000]}
            
            2. Search Result Content:
            {content[:10000]}
            
            Pre-processing Requirements:
            1. Content Filtering:
               - First, carefully read all content
               - Identify and remove all paragraphs and information unrelated to "{keyword}"
               - Keep only content directly or strongly related to "{keyword}"
               
            2. Relevance Judgment Criteria:
               - Core topic must be "{keyword}" or closely related
               - Content must substantially contribute to understanding or explaining "{keyword}"
               - Keep only information that clearly explains, supplements, or supports "{keyword}"
            
            Analysis Requirements:
            1. Focus:
               - Only analyze filtered relevant content
               - Return an empty string if no relevant information remains after filtering
               
            2. Relevance Check:
               - Look for exact matches of "{keyword}"
               - Look for synonyms and related terms
               - Look for contextual information about "{keyword}"
            
            Summarization Requirements:
            1. Length: Provide a concise summary (about 1000 words)
            2. Content Priority:
               - Most recent information first
               - Most relevant information to "{keyword}"
               - Factual data and statistics
            
            Quality Guidelines:
            1. Accuracy: Only include information explicitly stated in the sources
            2. Objectivity: No personal opinions or interpretations
            3. Relevance: Ensure every sentence relates to "{keyword}"
            4. Clarity: Use clear, simple language
            
            Special Instructions:
            - Return an empty string if no information related to "{keyword}" is found
            - Do not generate or infer information not explicitly mentioned in the original content
            - Better to write less than include irrelevant information
            """

    def _preprocess_content(self, content: str) -> str:
        """预处理内容，移除无效字符并清理文本"""
        if not content:
            return ""
            
        # 定义要保留的字符模式
        pattern = (
            r'[^\u4e00-\u9fa5'  # 中文字符
            r'a-zA-Z'           # 英文字母
            r'\u0370-\u03FF'    # 希腊字母
            r'\u1F00-\u1FFF'    # 希腊语扩展
            r'0-9'              # 数字
            r'.,;!?，。；！？、'  # 中英文标点
            r'$()（）\[\]【】'   # 括号
            r'\s'               # 空白字符
            r'\+\-\*/=≠≈<>≤≥'   # 基本数学符号
            r'±×÷∈∉∋∌⊂⊄⊃⊅⊆⊈⊇⊉'  # 集合运算符
            r'∪∩∧∨¬→←↔⇒⇔'      # 逻辑运算符
            r'∀∃∄Σ∏∑∫∬∭∇∂'      # 高等数学符号
            r'√∛∜∝∞∠∟∥∦∧∨⊕⊗'   # 其他数学符号
            r']'
        )
        
        # 清理文本
        cleaned_content = re.sub(pattern, '', content)
        
        # 额外的清理步骤
        cleaned_content = (
            cleaned_content
            .replace('\u3000', ' ')  # 全角空格转半角
            .replace('\t', ' ')      # 制表符转空格
            .strip()                 # 移除首尾空白
        )
        
        # 合并多个空格为单个空格
        cleaned_content = re.sub(r'\s+', ' ', cleaned_content)
        
        # 移除空行
        cleaned_content = '\n'.join(
            line.strip() for line in cleaned_content.splitlines() if line.strip()
        )
        
        return cleaned_content[:5000]



    def _generate_summary(self, prompt: str, enable_thinking=False, temperature: float = 0.5) -> Tuple[str, int]:
        """Generate text summary using standard generator"""
        if temperature==0.5:
            temperature=random.uniform(0.3, 0.7)
        filtered_prompt = self._filter_sensitive_content(prompt)
        if self.api_key_type == "deepseek":
            answer, totaltokens = generate_summary(self.api_key, filtered_prompt, enable_thinking, temperature)
            return answer, totaltokens
        elif self.api_key_type == "qwen":
            answer, totaltokens = qwen_generate_summary(self.api_key, filtered_prompt, enable_thinking, temperature)
            return answer, totaltokens
        return self.standard_generator.generate(filtered_prompt)
        

    def _generate_summary_with_system_prompt(self, sys_prompt: str, prompt: str, enable_thinking=False, temperature: float = 0.5) -> Tuple[str, int]:
        """Generate text summary with system prompt"""
        if temperature==0.5:
            temperature=random.uniform(0.3, 0.7)
        filtered_prompt = self._filter_sensitive_content(prompt)
        if self.api_key_type == "deepseek":
            answer, totaltokens = generate_summary_with_system_prompt(self.api_key, sys_prompt, prompt, enable_thinking, temperature)
            return answer, totaltokens
        elif self.api_key_type == "qwen":
            answer, totaltokens = qwen_generate_summary_with_system_prompt(self.api_key, sys_prompt, prompt, enable_thinking, temperature)
            return answer, totaltokens
        self.model="deepseek-ai/DeepSeek-V3"
        return self.standard_generator.generate(filtered_prompt, sys_prompt)


    def _generate_summary_with_system_prompt_R1(self, sys_prompt: str, prompt: str, enable_thinking=False, temperature: float = 0.5) -> Tuple[str, int]:
        """Generate text summary with system prompt"""
        if temperature==0.5:
            temperature=random.uniform(0.3, 0.7)
        filtered_prompt = self._filter_sensitive_content(prompt)
        if self.api_key_type == "deepseek":
            answer, totaltokens = generate_summary_with_system_prompt(self.api_key, sys_prompt, prompt, enable_thinking, temperature)
            return answer, totaltokens
        elif self.api_key_type == "qwen":
            answer, totaltokens = qwen_generate_summary_with_system_prompt(self.api_key, sys_prompt, prompt, enable_thinking, temperature)
            return answer, totaltokens
        self.model="deepseek-ai/DeepSeek-V3"
        return self.standard_generator.generate(filtered_prompt, sys_prompt)






    def _generate_summary_r1(self, prompt: str, enable_thinking=False, temperature: float = 0.5) -> Tuple[str, int]:
        """
        生成文本摘要（非流式输出）
        
        Args:
            prompt (str): 提示词
            
        Returns:
            str: 生成的摘要文本
        """
        if temperature==0.5:
            temperature=random.uniform(0.3, 0.7)
        if self.api_key_type == "deepseek":
            answer, totaltokens = generate_summary(self.api_key, prompt, enable_thinking, temperature)
            return answer, totaltokens
        elif self.api_key_type == "qwen":
            answer, totaltokens = qwen_generate_summary(self.api_key, prompt, enable_thinking, temperature)
            return answer, totaltokens


        url = "https://api.siliconflow.cn/v1/chat/completions"
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        # 内容过滤
        filtered_prompt = self._filter_sensitive_content(prompt)    
        # 准备多个温度值
        temperatures = [random.uniform(0.3, 0.7) for _ in range(3)]
        
        max_retries = 3
        for attempt in range(max_retries):
            try:
                # 选择温度
                temperature = temperatures[attempt % len(temperatures)]
                
                # 准备请求数据
                data = {
                    "model": "deepseek-ai/DeepSeek-V3",
                    "messages": [
                        {
                            "role": "user",
                            "content": filtered_prompt
                        }
                    ],
                    "stream": True,  # 启用流式输出
                    "max_tokens": 4096,
                    "temperature": temperature,
                }
                deeplogger.info(f" model temperature: {temperature}")
                response = requests.request("POST", url, headers=headers, json=data, stream=True)
                # 打印流式返回信息
                if response.status_code == 200:                    
                    # 收集所有chunk
                    all_content = []  # 初始化一个列表来存储所有内容
                    total_tokens = 0  # 初始化 total_tokens
                    buffer = ""  # 初始化一个缓冲区
                    for chunk in response.iter_content(chunk_size=8192): 
                        if chunk:
                            decoded_chunk = chunk.decode('utf-8')
                            buffer += decoded_chunk  # 将chunk添加到缓冲区
                            
                            # 检查是否为错误信息
                            if 'code' in decoded_chunk and '"code":60000' in decoded_chunk:
                                deeplogger.info("接收到错误代码60000，跳过此chunk。")
                                continue  # 跳过此chunk
                            
                            # 去掉前缀 'data: '，如果存在
                            if decoded_chunk.startswith('data: '):
                                decoded_chunk = decoded_chunk[6:]  # 去掉前6个字符
                            
                            # 检查是否为结束标志
                            if decoded_chunk.strip() == "[DONE]":
                                deeplogger.info("接收到结束标志，停止处理。")
                                break  # 停止处理

                            # 处理可能的多个 JSON 对象
                            json_objects = decoded_chunk.splitlines()  # 假设每个 JSON 对象在新的一行
                            for json_str in json_objects:
                                if json_str.strip():  # 确保不是空行
                                    try:
                                        json_data = json.loads(json_str)
                                        content = json_data['choices'][0]['delta'].get('content', '')
                                        if content:  # 如果content不为空，则添加到列表
                                            all_content.append(content)
                                    # 更新 total_tokens（如果存在）
                                        if 'usage' in json_data and 'total_tokens' in json_data['usage']:
                                            total_tokens = json_data['usage']['total_tokens']
                                    except json.JSONDecodeError as e:
                                        deeplogger.info(f"解析JSON时出错: {str(e)}，chunk内容: {json_str}")  # 打印错误信息和chunk内容
                                        # 使用正则表达式提取content字段的值
                                        match = re.search(r'"content":"(.*?)"', json_str)
                                        if match:
                                            extracted_content = match.group(1)  # 提取内容
                                            all_content.append(extracted_content)  # 添加到列表
                    
                    # 合并所有提取的内容
                    # log_with_request_id(f"大模型提取后输出combined_content: {all_content[:1000]}", request_id=request_id)
                    combined_content = ''.join(all_content)
                    log_with_request_id(f"大模型的输出: {combined_content[:100]},消耗的token为:{total_tokens}", request_id=request_id)
                else:
                    deeplogger.info('Request failed with status code:', response.status_code)

                return combined_content, total_tokens
                       
            except requests.RequestException as e:
                if hasattr(e.response, 'json'):
                    error_json = e.response.json()
                    error_message = error_json.get('error', {}).get('message', '')
                    if "Content Exists Risk" in error_message:
                        deeplogger.info(f"Content risk detected (attempt {attempt + 1}). Trying different approach.")
                        continue
                deeplogger.info(f"Error in API request (attempt {attempt + 1}): {str(e)}")
                if hasattr(e.response, 'text'):
                    deeplogger.info(f"Response content: {e.response.text}")           
            time.sleep(1)  # 在重试之前等待1秒
        
        deeplogger.info("Failed to generate summary after all attempts.")
        return "", 0  # 如果所有尝试都失败，返回空字符串和0 token




    def _generate_summary_graph(self, prompt: str, enable_thinking=False, temperature: float = 0.5) -> Tuple[str, int]:
        """
        生成文本摘要（非流式输出）
        
        Args:
            prompt (str): 提示词
            
        Returns:
            Tuple[str, int]: 生成的知识图谱(格式化文本)和使用的token数
        """
        if temperature==0.5:
            temperature=random.uniform(0.3, 0.7)
        if self.api_key_type == "deepseek":
            answer, totaltokens = generate_summary_graph(self.api_key, prompt, enable_thinking, temperature)
            return answer, totaltokens
        elif self.api_key_type == "qwen":
            answer, totaltokens = qwen_generate_summary_graph(self.api_key, prompt, enable_thinking, temperature)
            return answer, totaltokens


        url = "https://api.siliconflow.cn/v1/chat/completions"
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        # 内容过滤
        filtered_prompt = self._filter_sensitive_content(prompt)    
        
        # 准备多个温度值
        temperatures = [random.uniform(0.3, 0.7) for _ in range(3)]
        
        max_retries = 3
        for attempt in range(max_retries):
            try:
                # 选择温度
                temperature = temperatures[attempt % len(temperatures)]
                
                # 准备请求数据
                data = {
                    "model": "deepseek-ai/DeepSeek-V3",
                    "messages": [
                        {
                            "role": "system",
                            "content": """你是一个专业的知识图谱生成助手。你的任务是从用户提供的内容中提取实体和关系，并生成JSON格式的知识图谱。

                    请严格遵循以下输出格式规范：
                    1. 输出必须是有效的JSON格式
                    2. JSON结构应该包含一个数组，每个元素代表一个关系三元组
                    3. 每个三元组包含三个字段：entity1, entity2, relation
                    4. 所有字段都必须是字符串类型
                    5. 不要输出任何解释或额外文本
                    6. 实体应该是名词或名词短语
                    7. 关系应该是描述实体之间的物理联系的语句

                    示例输出：
                    {
                        "knowledge_graph": [
                            {
                                "entity1": "量子力学",
                                "entity2": "波函数",
                                "relation": "使用波函数描述粒子状态"
                            },
                            {
                                "entity1": "光子",
                                "entity2": "光",
                                "relation": "是基本组成单位"
                            }
                        ]
                    }
                    
                    请确保输出是严格的JSON格式，可以被JSON解析器直接解析。不要输出任何其他内容。"""
                        },
                        {
                            "role": "user",
                            "content": filtered_prompt
                        }
                    ],
                    "stream": True,  # 启用流式输出
                    "max_tokens": 4096,
                    "temperature": temperature,
                }
                
                deeplogger.info(f" model temperature: {temperature}")
                log_with_request_id(f" model temperature: {temperature}", request_id=request_id)
                response = requests.request("POST", url, headers=headers, json=data, stream=True)
                
                if response.status_code == 200:                    
                    json_content = ""  # 用于存储实际的JSON内容
                    total_tokens = 0  # 初始化 total_tokens
                    buffer = ""  # 初始化一个缓冲区
                    
                    for chunk in response.iter_content(chunk_size=8192): 
                        if chunk:
                            decoded_chunk = chunk.decode('utf-8')
                            buffer += decoded_chunk  # 将chunk添加到缓冲区
                            
                            # 检查是否为错误信息
                            if 'code' in decoded_chunk and '"code":60000' in decoded_chunk:
                                log_with_request_id("接收到错误代码60000，跳过此chunk。", request_id=request_id)
                                continue
                            
                            # 处理data:前缀
                            chunks = decoded_chunk.split('data: ')
                            for chunk_part in chunks:
                                if not chunk_part.strip():
                                    continue
                                    
                                # 检查是否为结束标志
                                if chunk_part.strip() == "[DONE]":
                                    deeplogger.info("接收到结束标志，停止处理。")
                                    continue
                                    
                                try:
                                    json_data = json.loads(chunk_part)
                                    # 从delta中获取content
                                    content = json_data['choices'][0]['delta'].get('content', '')
                                    if content:  # 如果content不为空，则添加到json_content
                                        json_content += content
                                    if 'usage' in json_data:
                                        total_tokens = json_data['usage'].get('total_tokens', 0)
                                        
                                except json.JSONDecodeError as e:
                                    log_with_request_id(f"解析chunk JSON时出错: {str(e)}，chunk内容: {chunk_part}", request_id=request_id)
                                    # 使用正则表达式提取content字段的值
                                    match = re.search(r'"content":"(.*?)"', chunk_part)
                                    if match:
                                        extracted_content = match.group(1)
                                        log_with_request_id(f"出错后提取的content: {extracted_content}", request_id=request_id)
                                        json_content += extracted_content
                                        log_with_request_id(f"出错后提取的content添加到字符串: {json_content}", request_id=request_id)
                    
                    # 清理并解析最终的JSON内容
                    try:
                        # 清理可能的前缀和后缀
                        json_content = json_content.strip()
                        if json_content.startswith('```json'):
                            json_content = json_content[7:]
                        if json_content.endswith('```'):
                            json_content = json_content[:-3]
                        json_content = json_content.strip()
                        
                        # 解析JSON
                        json_output = json.loads(json_content)
                        
                        # 确保输出包含必要的结构
                        if not isinstance(json_output, dict) or "knowledge_graph" not in json_output:
                            if isinstance(json_output, list):
                                json_output = {"knowledge_graph": json_output}
                            else:
                                json_output = {"knowledge_graph": []}
                                
                        # 将JSON格式转换为指定的文本格式
                        result_lines = []
                        for item in json_output["knowledge_graph"]:
                            line = f"{item['entity1']},{item['entity2']},{{{item['relation']}}}"
                            result_lines.append(line)
                        
                        # 合并所有行
                        combined_content = "\n".join(result_lines)
                        
                    except json.JSONDecodeError as e:
                        deeplogger.info(f"解析最终JSON时出错: {str(e)}")
                        deeplogger.info(f"JSON内容: {json_content}")
                        combined_content = ""
                    
                    log_with_request_id(f"大模型的输出: {combined_content[:100]},消耗的token为:{total_tokens}", request_id=request_id)
                    return combined_content, total_tokens
                    
                else:
                    deeplogger.info('Request failed with status code:', response.status_code)
                    deeplogger.info('Response content:', response.text)
                    
            except requests.RequestException as e:
                if hasattr(e.response, 'json'):
                    error_json = e.response.json()
                    error_message = error_json.get('error', {}).get('message', '')
                    if "Content Exists Risk" in error_message:
                        deeplogger.info(f"Content risk detected (attempt {attempt + 1}). Trying different approach.")
                        continue
                deeplogger.info(f"Error in API request (attempt {attempt + 1}): {str(e)}")
                if hasattr(e.response, 'text'):
                    deeplogger.info(f"Response content: {e.response.text}")           
            time.sleep(1)  # 在重试之前等待1秒
        
        deeplogger.info("Failed to generate summary after all attempts.")
        return "", 0  # 如果所有尝试都失败，返回空字符串和0 token








    def _generate_summary_r11(self, prompt: str, enable_thinking=False, temperature: float = 0.5) -> Tuple[str, int]: 
        """
        生成文本摘要（非流式输出）
        
        Args:
            prompt (str): 提示词
            
        Returns:
            Tuple[str, int]: 生成的摘要文本和使用的token数
        """
        if temperature==0.5:
            temperature=random.uniform(0.3, 0.7)
        if self.api_key_type == "deepseek":
            answer, totaltokens = generate_summary_r11(self.api_key, prompt, enable_thinking, temperature)
            return answer, totaltokens
        elif self.api_key_type == "qwen":
            answer, totaltokens = qwen_generate_summary_r11(self.api_key, prompt, enable_thinking, temperature)
            return answer, totaltokens


        url = "https://api.siliconflow.cn/v1/chat/completions"
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
           
        # 准备多个温度值
        temperatures = [random.uniform(0.3, 0.7) for _ in range(3)]
        
        max_retries = 3
        for attempt in range(max_retries):
            try:
                # 选择温度
                temperature = temperatures[attempt % len(temperatures)]
                
                # 准备请求数据
                data = {
                    "model": "deepseek-ai/DeepSeek-R1",
                    "messages": [
                        {
                            "role": "system",
                            "content": """你是一个专业的知识图谱生成助手。你的任务是从用户提供的内容中提取实体和关系，并生成标准格式的知识图谱。

                        请严格遵循以下输出格式规范：
                        1. 每行表示一个关系三元组
                        2. 每个三元组的格式必须是：实体1,实体2,{{关系}}
                        3. 实体和关系之间用英文逗号分隔
                        4. 关系必须用双大括号{{}}包围
                        5. 不要输出任何解释、标题或额外文本
                        6. 每行只包含一个三元组
                        7. 实体应该是名词或名词短语
                        8. 关系应该是动词或动词短语

                        示例输出：
                        量子力学,波函数,{{使用波函数描述粒子状态}}
                        光子,光,{{是基本组成单位}}
                        欧姆定律,电流,{{规定电流与电压成正比}}
                        电磁感应,法拉第定律,{{由法拉第发现并描述}}
                        
                        无论用户提供什么内容，你都必须严格按照上述格式输出知识图谱，不添加任何额外说明。"""
                            },
                        {
                            "role": "user",
                            "content": prompt
                        }
                    ],
                    "stream": True,  # 启用流式输出
                    "max_tokens": 4096,
                    "temperature": temperature,
                }
                deeplogger.info(f" model temperature: {temperature}")
                response = requests.request("POST", url, headers=headers, json=data, stream=True)
                
                # 打印流式返回信息
                if response.status_code == 200:                    
                    # 收集所有chunk
                    all_content = []  # 初始化一个列表来存储所有内容
                    total_tokens = 0  # 初始化 total_tokens
                    buffer = ""  # 初始化一个缓冲区
                    
                    for chunk in response.iter_content(chunk_size=8192): 
                        if chunk:
                            decoded_chunk = chunk.decode('utf-8')
                            buffer += decoded_chunk  # 将chunk添加到缓冲区
                            
                            # 检查是否为错误信息
                            if 'code' in decoded_chunk and '"code":60000' in decoded_chunk:
                                deeplogger.info("接收到错误代码60000，跳过此chunk。")
                                continue  # 跳过此chunk
                                
                            # 处理可能的多个数据块
                            # 每个数据块以 "data: " 开头
                            data_chunks = decoded_chunk.split("data: ")
                            for data_chunk in data_chunks:
                                if not data_chunk.strip():
                                    continue
                                    
                                # 检查是否为结束标志
                                if data_chunk.strip() == "[DONE]":
                                    deeplogger.info("接收到结束标志，停止处理。")
                                    break  # 停止处理
                                    
                                try:
                                    json_data = json.loads(data_chunk.strip())
                                    
                                    # 从 content 字段获取内容
                                    content = json_data['choices'][0]['delta'].get('content', '')
                                    if content:  # 如果content不为空，则添加到列表
                                        all_content.append(content)
                                        
                                    # 更新 total_tokens（如果存在）
                                    if 'usage' in json_data and 'total_tokens' in json_data['usage']:
                                        total_tokens = json_data['usage']['total_tokens']
                                        
                                except json.JSONDecodeError as e:
                                    deeplogger.info(f"解析JSON时出错: {str(e)}，chunk内容: {data_chunk}")
                                    # 使用正则表达式提取content字段的值
                                    match = re.search(r'"content":"(.*?)"', data_chunk)
                                    if match:
                                        extracted_content = match.group(1)  # 提取内容
                                        all_content.append(extracted_content)  # 添加到列表
                    
                    # 合并所有提取的内容
                    combined_content = ''.join(all_content)
                    deeplogger.info(f"大模型的输出: {combined_content[:100]}, 消耗的token为: {total_tokens}")
                    
                    return combined_content, total_tokens
                else:
                    deeplogger.info('Request failed with status code:', response.status_code)
                    deeplogger.info('Response content:', response.text)
                    
            except requests.RequestException as e:
                if hasattr(e, 'response') and hasattr(e.response, 'json'):
                    try:
                        error_json = e.response.json()
                        error_message = error_json.get('error', {}).get('message', '')
                        if "Content Exists Risk" in error_message:
                            deeplogger.info(f"Content risk detected (attempt {attempt + 1}). Trying different approach.")
                            continue
                    except:
                        pass
                deeplogger.info(f"Error in API request (attempt {attempt + 1}): {str(e)}")
                if hasattr(e, 'response') and hasattr(e.response, 'text'):
                    deeplogger.info(f"Response content: {e.response.text}")
                    
            time.sleep(1)  # 在重试之前等待1秒
        
        deeplogger.info("Failed to generate summary after all attempts.")
        return "", 0  # 如果所有尝试都失败，返回空字符串和0 token


    def _generate_summary_translation(self, prompt: str, enable_thinking=False, temperature: float = 0.5) -> Tuple[str, int]:
        """
        生成文本摘要（非流式输出）
        
        Args:
            prompt (str): 提示词
            
        Returns:
            str: 生成的摘要文本
        """
        if temperature==0.5:
            temperature=random.uniform(0.3, 0.7)
        if self.api_key_type == "deepseek":
            answer, totaltokens = generate_summary(self.api_key, prompt, enable_thinking, temperature)
            return answer, totaltokens  
        elif self.api_key_type == "qwen":
            answer, totaltokens = qwen_generate_summary(self.api_key, prompt, enable_thinking, temperature)
            return answer, totaltokens


        url = "https://api.siliconflow.cn/v1/chat/completions"
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        # 内容过滤
        filtered_prompt = self._filter_sensitive_content(prompt)    
        
        # 准备多个温度值
        temperatures = [random.uniform(0.3, 0.7) for _ in range(3)]
        
        max_retries = 3
        for attempt in range(max_retries):
            try:
                # 选择温度
                temperature = temperatures[attempt % len(temperatures)]
                
                # 准备请求数据
                data = {
                    "model": "deepseek-ai/DeepSeek-R1-Distill-Qwen-14B",
                    "messages": [
                        {
                            "role": "user",
                            "content": filtered_prompt
                        }
                    ],
                    "stream": True,  # 启用流式输出
                    "max_tokens": 4096,
                    "temperature": temperature,
                }
                deeplogger.info(f" model temperature: {temperature}")
                response = requests.request("POST", url, headers=headers, json=data, stream=True)
                # 打印流式返回信息
                if response.status_code == 200:                    
                    # 收集所有chunk
                    all_content = []  # 初始化一个列表来存储所有内容
                    total_tokens = 0  # 初始化 total_tokens
                    buffer = ""  # 初始化一个缓冲区
                    for chunk in response.iter_content(chunk_size=8192): 
                        if chunk:
                            decoded_chunk = chunk.decode('utf-8')
                            buffer += decoded_chunk  # 将chunk添加到缓冲区
                            
                            # 检查是否为错误信息
                            if 'code' in decoded_chunk and '"code":60000' in decoded_chunk:
                                deeplogger.info("接收到错误代码60000，跳过此chunk。")
                                continue  # 跳过此chunk
                            
                            # 去掉前缀 'data: '，如果存在
                            if decoded_chunk.startswith('data: '):
                                decoded_chunk = decoded_chunk[6:]  # 去掉前6个字符
                            
                            # 检查是否为结束标志
                            if decoded_chunk.strip() == "[DONE]":
                                deeplogger.info("接收到结束标志，停止处理。")
                                break  # 停止处理

                            # 处理可能的多个 JSON 对象
                            json_objects = decoded_chunk.splitlines()  # 假设每个 JSON 对象在新的一行
                            for json_str in json_objects:
                                if json_str.strip():  # 确保不是空行
                                    try:
                                        json_data = json.loads(json_str)
                                        content = json_data['choices'][0]['delta'].get('content', '')
                                        if content:  # 如果content不为空，则添加到列表
                                            all_content.append(content)
                                    # 更新 total_tokens（如果存在）
                                        if 'usage' in json_data and 'total_tokens' in json_data['usage']:
                                            total_tokens = json_data['usage']['total_tokens']
                                    except json.JSONDecodeError as e:
                                        deeplogger.info(f"解析JSON时出错: {str(e)}，chunk内容: {json_str}")  # 打印错误信息和chunk内容
                                        # 使用正则表达式提取content字段的值
                                        match = re.search(r'"content":"(.*?)"', json_str)
                                        if match:
                                            extracted_content = match.group(1)  # 提取内容
                                            all_content.append(extracted_content)  # 添加到列表
                    

                    combined_content = ''.join(all_content)
                else:
                    deeplogger.info('Request failed with status code:', response.status_code)

                return combined_content, total_tokens
                       
            except requests.RequestException as e:
                if hasattr(e.response, 'json'):
                    error_json = e.response.json()
                    error_message = error_json.get('error', {}).get('message', '')
                    if "Content Exists Risk" in error_message:
                        deeplogger.info(f"Content risk detected (attempt {attempt + 1}). Trying different approach.")
                        continue
                deeplogger.info(f"Error in API request (attempt {attempt + 1}): {str(e)}")
                if hasattr(e.response, 'text'):
                    deeplogger.info(f"Response content: {e.response.text}")           
            time.sleep(1)  # 在重试之前等待1秒
        
        deeplogger.info("Failed to generate summary after all attempts.")
        return "", 0  # 如果所有尝试都失败，返回空字符串和0 token






    def _load_sensitive_words(self):
        sensitive_words = []
        file_path = os.path.join(os.path.dirname(__file__), 'General_SpamWords.txt')        
        with open(file_path, 'r', encoding='utf-8') as file:
             sensitive_words = [line.strip() for line in file if line.strip()]
        return sensitive_words

    def _filter_sensitive_content(self, text: str) -> str:
        original_text = text
        for word in self.sensitive_words:
            # 使用正则表达式匹配整个敏感词
            pattern = re.escape(word)  # 直接使用敏感词进行匹配
            if re.search(pattern, text, flags=re.UNICODE):  # 检查是否存在敏感词
                deeplogger.info(f"Filtering sensitive word: '{word}'")  # 打印被过滤的敏词
            text = re.sub(pattern, '[FILTERED]', text, flags=re.UNICODE)
        
        if text != original_text:
            deeplogger.info(f"Sensitive content filtered. Original length: {len(original_text)}, New length: {len(text)}")
        
        return text
 





   




# 示例调用
if __name__ == "__main__":
    api_key = "sk-izwfqiufrwdqfkvqzklcyaashpmraqaaosuchkjnettqpabb"
    api_key_type = "deepseek"
    api_key_deepseek = "***********************************"
    api_key_qwen = "sk-61010d52e30c4fb096d240ad7fae39df"
    agent = Web_Agent(api_key=api_key_deepseek, api_key_type="deepseek")
    sys_prompt_json = "你是一个结构化信息输出助手。请根据用户问题，严格以如下JSON格式输出：{\"title\": string, \"summary\": string, \"keywords\": [string]}，不要输出任何多余内容。"
    test_prompt_json = "请用中文简要介绍人工智能的发展历史，并给出3个相关关键词。"
    content, tokens = agent._generate_summary_with_system_prompt_R1(sys_prompt_json, test_prompt_json)
    deeplogger.info(content)
    deeplogger.info(f"消耗的token为: {tokens}")

    # query = """我需要优化以下物理知识图谱，使其更加准确、连贯和完整。
    #             当前物理知识图谱:
    #             闪电,雷暴,{每天发生约400000次}
    #             闪电,电压,{云与地球之间可达30亿伏特}
    #             雷暴,闪电,{每秒发生约100次}
    #             雷云,正电荷,{顶部带正电荷}
    #             雷云,负电荷,{底部带负电荷}
    #             电压,电场强度,{通过电势差形成}
    #             电场强度,电击穿,{每米约300万伏特时发生}
    #             电击穿,火花,{导致离子中和并发光}
    #             火花,光,{由离子中和产生}
    #             电荷,导体表面,{分布在固体导体表面}
    #             导体球,电荷量,{与半径成正比}
    #             导体球,表面电荷密度,{与半径成反比}
    #             高斯面,电场强度,{等于表面电荷密度除以真空介电常数}
    #             电场强度,放电,{首先发生在最高电场强度处}
    #             电击穿,空气,{导致空气放电}
    #             电子,电场,{在电场中加速}
    #             离子,中性状态,{变为中性时发光}
    #             电子伏特,能量,{等于1.6×10^-19焦耳}
    #             电势差,电子,{使电子移动}
    #             电场强度,火花,{在尖点处产生}
    #             请执行以下优化任务:
    #             1. 合并表示相同物理概念的实体，使用最准确、最规范的物理学术语
    #             2. 考虑所有物理实体两两之间是否存在有意义的物理关系，如果存在，则添加关系
    #             3. 优化关系描述，使其更符合物理学的精确表述
    #             4. 保持物理知识图谱的科学准确性、连贯性和一致性
    #             5. 避免创建重复或冗余的关系，特别是避免在两个物理概念之间创建双向关系
    #             实体列表（供参考）:
    #             空气, 电荷, 负电荷, 正电荷, 电荷量, 高斯面, 雷暴, 电击穿, 导体球, 电子, 闪电, 电场强度, 电子伏特, 中性状态, 电压, 表面电荷密度, 电势差, 火花, 放电, 导体表面, 电场, 离子, 能量, 雷云, 光
    #             请注意:
    #             - 如果物理概念A和物理概念B之间有关系，只需要一行表示它们之间的关系
    #             - 不要同时包含"实体A,实体B,{关系1}"和"实体B,实体A,{关系2}"这样的双向关系
    #             - 选择最能准确描述两个物理概念之间科学关系的一种方向
    #             - 确保所有关系描述都符合物理学的科学准确性
    #             - 保持原有的有效物理关系，除非需要合并或优化
    #             - 可以使用Unicode字符表示希腊字母（如σ, ε, μ, π等）和数学符号（如±, ×, ÷, ∞等）
    #             - 可以使用上标（如²,³）和下标（如₀,₁,₂）表示数学公式
    #             - 例如：表面电荷密度与电场强度的关系可以表示为"表面电荷密度,电场强度,满足{E=σ/ε₀}"
    #             请输出优化后的物理知识图谱，每行使用以下格式:
    #             实体1,实体2,{关系描述}"""
    # message,tokens=agent._generate_summary_r11(query)
    # deeplogger.info(message)
    # deeplogger.info(f"消耗的token为: {tokens}")
    
    # message="""
    # 雷暴,闪电,{{每秒产生约100次}}
    # 闪电,电势差,{{云地间达3×10⁹ V}}
    # 雷云,正电荷,{{顶部聚集}}
    # 雷云,负电荷,{{底部聚集}}
    # 电势差,电场强度,{{满足E=-∇V}}
    # 电场强度,电击穿,{{当达到3×10⁶ V/m时引发}}
    # 电击穿,放电,{{引发}}
    # 放电,火花,{{产生}}
    # 火花,光,{{通过电子跃迁发射}}
    # 导体表面,电荷,{{分布于表面}}
    # 导体球,电荷量,{{Q=4πε₀RV}}
    # 导体球,表面电荷密度,{{σ=Q/(4πR²)}}
    # 表面电荷密度,电场强度,{{满足E=σ/ε₀}}
    # 电场强度,导体表面,{{在尖端处增强}}
    # 电击穿,空气,{{导致电离}}
    # 电场,电子,{{施加力导致加速}}
    # 电势差,电子,{{提供动能}}
    # 电子伏特,能量,{{1 eV=1.6×10⁻¹⁹ J}}
    # 离子,中性状态,{{复合时发光}}
    # 高斯面,电场强度,{{∮E·dA=Q/ε₀}}
    # 电荷,电场,{{产生}}
    # 电势差,电流,{{满足欧姆定律V=IR}}
    # """

    # log_with_request_id(f"优化后的知识图谱: {message}", request_id=request_id)

    # results = agent._fetch_with_browser("https://www.fpri.org/")
    # deeplogger.info(results)
 


    # try:
    #     results = agent.chain_of_thought_search("西安今天的天气")
        
    #     if results['final_summary']:
    #         deeplogger.info("\n最终结果：")
    #         deeplogger.info(results['final_summary'])
            
    #         if results['sources']:
    #             deeplogger.info("\n信息来源：")
    #             for source in results['sources']:
    #                 deeplogger.info(f"- [{source['step']}] {source['title']}: {source['url']}")
    #     else:
    #         deeplogger.info("未能获取到搜索结果")
            
    # except Exception as e:
    #     deeplogger.info(f"搜索过程中发生错误: {str(e)}")
    #     deeplogger.info(f"搜索过程中发生错误: {str(e)}")

