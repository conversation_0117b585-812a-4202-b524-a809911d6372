<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <title>电磁波演示合集</title>
  <style>
    body {
      margin: 0;
      padding: 0;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      font-family: 'Microsoft YaHei', Arial, sans-serif;
      min-height: 100vh;
    }
    
    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 40px 20px;
    }
    
    .header {
      text-align: center;
      color: white;
      margin-bottom: 50px;
    }
    
    .header h1 {
      font-size: 3em;
      margin: 0;
      text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    }
    
    .header p {
      font-size: 1.2em;
      margin: 20px 0;
      opacity: 0.9;
    }
    
    .demos-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
      gap: 30px;
      margin-top: 40px;
    }
    
    .demo-card {
      background: rgba(255, 255, 255, 0.95);
      border-radius: 15px;
      padding: 30px;
      box-shadow: 0 10px 30px rgba(0,0,0,0.2);
      transition: transform 0.3s ease, box-shadow 0.3s ease;
      cursor: pointer;
    }
    
    .demo-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 15px 40px rgba(0,0,0,0.3);
    }
    
    .demo-card h3 {
      color: #333;
      font-size: 1.5em;
      margin: 0 0 15px 0;
      display: flex;
      align-items: center;
    }
    
    .demo-card .icon {
      font-size: 1.8em;
      margin-right: 10px;
    }
    
    .demo-card p {
      color: #666;
      line-height: 1.6;
      margin-bottom: 20px;
    }
    
    .demo-card .features {
      list-style: none;
      padding: 0;
      margin: 20px 0;
    }
    
    .demo-card .features li {
      color: #555;
      margin: 8px 0;
      padding-left: 20px;
      position: relative;
    }
    
    .demo-card .features li:before {
      content: "✓";
      position: absolute;
      left: 0;
      color: #4ECDC4;
      font-weight: bold;
    }
    
    .demo-card .launch-btn {
      background: linear-gradient(45deg, #4ECDC4, #44A08D);
      color: white;
      border: none;
      padding: 12px 25px;
      border-radius: 25px;
      font-size: 1em;
      font-weight: bold;
      cursor: pointer;
      transition: all 0.3s ease;
      width: 100%;
    }
    
    .demo-card .launch-btn:hover {
      background: linear-gradient(45deg, #45B7B8, #3D8B7A);
      transform: scale(1.02);
    }
    
    .demo-card .launch-btn:active {
      transform: scale(0.98);
    }
    
    .footer {
      text-align: center;
      color: rgba(255,255,255,0.8);
      margin-top: 60px;
      padding: 20px;
      border-top: 1px solid rgba(255,255,255,0.2);
    }
    
    .theory-section {
      background: rgba(255,255,255,0.1);
      border-radius: 15px;
      padding: 30px;
      margin: 40px 0;
      color: white;
    }
    
    .theory-section h2 {
      margin-top: 0;
      color: #FFD700;
    }
    
    .theory-section .formula {
      background: rgba(0,0,0,0.2);
      padding: 15px;
      border-radius: 8px;
      font-family: 'Courier New', monospace;
      margin: 15px 0;
      text-align: center;
      font-size: 1.1em;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>🌊 电磁波演示合集</h1>
      <p>交互式电磁波传播、干涉与衍射现象演示</p>
      <p>适用于物理教学和科研演示</p>
    </div>
    
    <div class="theory-section">
      <h2>📚 理论基础</h2>
      <p>电磁波是电场和磁场相互激发形成的横波，遵循麦克斯韦方程组。波动方程为：</p>
      <div class="formula">
        ∇²E - μ₀ε₀ ∂²E/∂t² = 0
      </div>
      <p>在真空中，电磁波速度 c = 1/√(μ₀ε₀) ≈ 3×10⁸ m/s</p>
      <p>波长 λ、频率 f 和波速 c 的关系：<span class="formula" style="display:inline; padding:5px;">c = λf</span></p>
    </div>
    
    <div class="demos-grid">
      <div class="demo-card" onclick="openDemo('elcetric_wave.html')">
        <h3><span class="icon">🌊</span>基础电磁波传播</h3>
        <p>演示单个波源产生的电磁波在二维空间中的传播过程，支持波源拖动和参数调节。</p>
        <ul class="features">
          <li>可拖动波源位置</li>
          <li>实时调节振幅、频率、波速</li>
          <li>距离衰减效果</li>
          <li>蓝-红热力图显示</li>
          <li>移动波源后原波继续传播</li>
        </ul>
        <button class="launch-btn">🚀 启动演示</button>
      </div>
      
      <div class="demo-card" onclick="openDemo('electromagnetic_interference.html')">
        <h3><span class="icon">🔄</span>多波源干涉</h3>
        <p>展示多个波源产生的电磁波相互干涉的现象，可观察到明显的干涉条纹。</p>
        <ul class="features">
          <li>支持多达8个波源</li>
          <li>每个波源可独立拖动</li>
          <li>实时显示干涉图样</li>
          <li>不同波源用不同颜色标识</li>
          <li>动态添加/删除波源</li>
        </ul>
        <button class="launch-btn">🚀 启动演示</button>
      </div>
      
      <div class="demo-card" onclick="openDemo('single_slit_diffraction.html')">
        <h3><span class="icon">🚪</span>单缝衍射</h3>
        <p>基于惠更斯原理演示电磁波通过单缝时的衍射现象，符合夫琅禾费衍射理论。</p>
        <ul class="features">
          <li>可调节缝宽和波长</li>
          <li>实时计算衍射角度</li>
          <li>显示λ/a比值和衍射条件</li>
          <li>可拖动波源改变入射角</li>
          <li>基于惠更斯原理的精确计算</li>
        </ul>
        <button class="launch-btn">🚀 启动演示</button>
      </div>

      <div class="demo-card" onclick="openDemo('double_slit_diffraction.html')">
        <h3><span class="icon">🔀</span>双缝衍射</h3>
        <p>演示电磁波通过双缝时的衍射和干涉现象，展示经典的杨氏双缝实验。</p>
        <ul class="features">
          <li>可调节双缝间距和缝宽</li>
          <li>实时显示干涉条纹</li>
          <li>计算条纹间距和衍射角</li>
          <li>可拖动波源和双缝位置</li>
          <li>同时展示衍射和干涉效应</li>
        </ul>
        <button class="launch-btn">🚀 启动演示</button>
      </div>
    </div>
    
    <div class="theory-section">
      <h2>🔬 物理现象说明</h2>
      <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
        <div>
          <h3 style="color: #4ECDC4;">干涉现象</h3>
          <p>两个或多个相干波源产生的波在空间中相遇时，会发生相长干涉和相消干涉，形成稳定的干涉图样。</p>
        </div>
        <div>
          <h3 style="color: #4ECDC4;">衍射现象</h3>
          <p>当波遇到障碍物或通过狭缝时，会发生绕射现象。当缝宽与波长相当时，衍射效应最为明显。</p>
        </div>
        <div>
          <h3 style="color: #4ECDC4;">波的叠加</h3>
          <p>多个波在同一点的振幅会按照叠加原理相加，形成复杂的波形和拍频现象。</p>
        </div>
      </div>
    </div>
    
    <div class="footer">
      <p>💡 这些演示基于经典电磁学理论，使用JavaScript和Canvas技术实现</p>
      <p>适用于大学物理、电磁学课程教学和科研演示</p>
    </div>
  </div>
  
  <script>
    function openDemo(filename) {
      window.open(filename, '_blank');
    }
    
    // 添加一些交互效果
    document.addEventListener('DOMContentLoaded', function() {
      const cards = document.querySelectorAll('.demo-card');
      
      cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
          this.style.background = 'rgba(255, 255, 255, 1)';
        });
        
        card.addEventListener('mouseleave', function() {
          this.style.background = 'rgba(255, 255, 255, 0.95)';
        });
      });
    });
  </script>
</body>
</html>
