<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试 eqnarray 环境渲染</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.13.11/dist/katex.min.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #f9f9f9;
        }
        .math-display {
            margin: 20px 0;
            text-align: center;
        }
        h2 {
            color: #333;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }
        .source-code {
            background-color: #f4f4f4;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            margin: 10px 0;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>eqnarray 环境渲染测试</h1>
    
    <div class="test-section">
        <h2>测试 1: 基本的 eqnarray 环境</h2>
        <div class="source-code">原始 LaTeX 代码：
\begin{eqnarray}
y_{1}+y_{2} &=2 A \sin \left(\frac{k_{1} x-\omega_{1} t+k_{2} x-\omega_{2} t}{2}\right) \cos \left(\frac{k_{1} x-\omega_{1} t-k_{2} x+\omega_{2} t}{2}\right) \\
&=2 A \sin \left(\left(\frac{k_{1}+k_{2}}{2}\right) x-\left(\frac{\omega_{1}+\omega_{2}}{2}\right) t\right) \cos \left(\left(\frac{k_{1}-k_{2}}{2}\right) x-\left(\frac{\omega_{1}-\omega_{2}}{2}\right) t\right) \\
&=2 A \sin \left(k_{\text {avg }} x-\omega_{\text {avg }} t\right) \cos \left(\frac{\Delta k}{2} x-\frac{\Delta \omega}{2} t\right)
\end{eqnarray}</div>
        
        <div class="math-display" id="test1">
            $$\begin{align}
            y_{1}+y_{2} &=2 A \sin \left(\frac{k_{1} x-\omega_{1} t+k_{2} x-\omega_{2} t}{2}\right) \cos \left(\frac{k_{1} x-\omega_{1} t-k_{2} x+\omega_{2} t}{2}\right) \\
            &=2 A \sin \left(\left(\frac{k_{1}+k_{2}}{2}\right) x-\left(\frac{\omega_{1}+\omega_{2}}{2}\right) t\right) \cos \left(\left(\frac{k_{1}-k_{2}}{2}\right) x-\left(\frac{\omega_{1}-\omega_{2}}{2}\right) t\right) \\
            &=2 A \sin \left(k_{\text {avg }} x-\omega_{\text {avg }} t\right) \cos \left(\frac{\Delta k}{2} x-\frac{\Delta \omega}{2} t\right)
            \end{align}$$
        </div>
    </div>

    <div class="test-section">
        <h2>测试 2: 简单的多行公式</h2>
        <div class="source-code">原始 LaTeX 代码：
\begin{eqnarray}
a &= b + c \\
d &= e + f \\
g &= h + i
\end{eqnarray}</div>
        
        <div class="math-display" id="test2">
            $$\begin{align}
            a &= b + c \\
            d &= e + f \\
            g &= h + i
            \end{align}$$
        </div>
    </div>

    <div class="test-section">
        <h2>测试 3: 带编号的公式组</h2>
        <div class="source-code">原始 LaTeX 代码：
\begin{eqnarray}
E &= mc^2 \\
F &= ma \\
P &= \frac{F}{A}
\end{eqnarray}</div>

        <div class="math-display" id="test3">
            $$\begin{align}
            E &= mc^2 \\
            F &= ma \\
            P &= \frac{F}{A}
            \end{align}$$
        </div>
    </div>

    <div class="test-section">
        <h2>测试 4: 无编号的 eqnarray* 环境</h2>
        <div class="source-code">原始 LaTeX 代码：
\begin{eqnarray*}
x &= a + b \\
y &= c + d \\
z &= e + f
\end{eqnarray*}</div>

        <div class="math-display" id="test4">
            $$\begin{align*}
            x &= a + b \\
            y &= c + d \\
            z &= e + f
            \end{align*}$$
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/katex@0.13.11/dist/katex.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/katex@0.13.11/dist/contrib/auto-render.min.js"></script>
    <script>
        document.addEventListener("DOMContentLoaded", function() {
            renderMathInElement(document.body, {
                delimiters: [
                    {left: "$$", right: "$$", display: true},
                    {left: "$", right: "$", display: false},
                    {left: "\\[", right: "\\]", display: true},
                    {left: "\\(", right: "\\)", display: false}
                ],
                throwOnError: false,
                output: 'htmlAndMathml',
                trust: true,
                strict: false,
                fleqn: true,
                macros: {
                    "\\eqnarray": "\\begin{align}",
                    "\\endeqnarray": "\\end{align}",
                    "\\eqnarray*": "\\begin{align*}",
                    "\\endeqnarray*": "\\end{align*}"
                }
            });
        });
    </script>
</body>
</html>
