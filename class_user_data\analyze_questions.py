#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
分析班级学生问题，提取共性问题并进行汇总
"""

import os
import json
import traceback
import logging
import sys
import time
import datetime
from typing import List, Dict, Any, Optional, Tuple

# 添加父目录到路径，以便导入其他模块
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import duckduckgosearch


# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('analyze_questions')


class QuestionAnalyzer:

    def __init__(self, api_key: Optional[str] = None, api_key_type: str = "deepseek"):
        """
        初始化问题分析器
        
        Args:
            api_key: API密钥（当使用系统函数时可忽略）
        """
        self.api_key = api_key
        self.api_key_type = api_key_type
        # 缓存目录路径
        self.cache_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "cache")
        # 确保缓存目录存在
        if not os.path.exists(self.cache_dir):
            try:
                os.makedirs(self.cache_dir)
                logger.info(f"创建缓存目录: {self.cache_dir}")
            except Exception as e:
                logger.error(f"创建缓存目录失败: {str(e)}")
                # 如果无法创建缓存目录，使用临时目录
                self.cache_dir = os.path.join(os.path.dirname(__file__), "cache")
                if not os.path.exists(self.cache_dir):
                    os.makedirs(self.cache_dir)

    def _get_cache_path(self, class_name: str) -> str:
        """
        获取特定班级分析结果的缓存文件路径
        
        Args:
            class_name: 班级名称
            
        Returns:
            缓存文件的完整路径
        """
        # 使用班级名称创建缓存文件名，避免文件名中的特殊字符
        safe_class_name = "".join(c if c.isalnum() else "_" for c in class_name)
        return os.path.join(self.cache_dir, f"questions_analysis_{safe_class_name}.json")

    def _save_to_cache(self, class_name: str, result: Dict[str, Any]) -> bool:
        """
        将分析结果保存到缓存文件
        
        Args:
            class_name: 班级名称
            result: 分析结果
            
        Returns:
            是否成功保存
        """
        try:
            cache_path = self._get_cache_path(class_name)
            # 添加缓存时间戳
            result_with_timestamp = result.copy()
            timestamp = time.time()
            result_with_timestamp['cache_timestamp'] = timestamp
            result_with_timestamp['cache_time'] = datetime.datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S')
            
            with open(cache_path, 'w', encoding='utf-8') as f:
                json.dump(result_with_timestamp, f, ensure_ascii=False, indent=2)
            
            logger.info(f"分析结果已缓存到: {cache_path}")
            return True
        except Exception as e:
            logger.error(f"保存缓存失败: {str(e)}")
            logger.error(traceback.format_exc())
            return False

    def _load_from_cache(self, class_name: str) -> Optional[Dict[str, Any]]:
        """
        从缓存加载分析结果
        
        Args:
            class_name: 班级名称
            
        Returns:
            缓存的分析结果，如果不存在则返回None
        """
        try:
            cache_path = self._get_cache_path(class_name)
            if not os.path.exists(cache_path):
                return None
            
            with open(cache_path, 'r', encoding='utf-8') as f:
                result = json.load(f)
            
            # 添加一个标志表明这是从缓存加载的结果
            result['from_cache'] = True
            
            logger.info(f"从缓存加载分析结果: {cache_path}")
            return result
        except Exception as e:
            logger.error(f"加载缓存失败: {str(e)}")
            logger.error(traceback.format_exc())
            return None

    def analyze_class_questions(self, class_name: str, force_refresh: bool = False) -> Dict[str, Any]:
        """
        分析班级学生的共性问题
        
        Args:
            class_name: 班级名称
            force_refresh: 是否强制刷新，忽略缓存
            
        Returns:
            包含分析结果的字典
        """
        try:
            # 如果不是强制刷新，先尝试从缓存加载
            if not force_refresh:
                cached_result = self._load_from_cache(class_name)
                if cached_result:
                    logger.info(f"使用缓存的分析结果: {class_name}")
                    return cached_result
            
            # 读取班级数据文件
            class_file_path = os.path.join(os.path.dirname(__file__), 'class_user_data', f"{class_name}.json")
            if not os.path.exists(class_file_path):
                logger.warning(f"找不到班级数据文件: {class_file_path}")
                return {
                    "success": False, 
                    "message": f"找不到班级 {class_name} 的数据"
                }
            
            # 读取JSON文件
            with open(class_file_path, 'r', encoding='utf-8') as file:
                class_data = json.load(file)
            
            # 收集所有学生的聊天消息
            all_messages = []
            for student in class_data:
                if 'chat_views' in student and student['chat_views']:
                    for chat in student['chat_views']:
                        if 'message' in chat and chat['message']:
                            all_messages.append(chat['message'])
            
            # 检查消息数量
            if len(all_messages) < 5:
                logger.warning(f"班级 {class_name} 的消息数量不足: {len(all_messages)}")
                return {
                    "success": False, 
                    "message": f"班级{class_name}的消息数量不足以进行有效分析",
                    "count": len(all_messages)
                }
            
            # 调用大模型进行分析
            questions = self._analyze_student_questions(all_messages)
            
            # 构建结果
            result = {
                "success": True,
                "questions": questions,
                "message_count": len(all_messages),
                "cache_time": datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                "from_cache": False
            }
            
            # 保存到缓存
            self._save_to_cache(class_name, result)
            
            return result
            
        except Exception as e:
            logger.error(f"分析班级问题时出错: {str(e)}")
            logger.error(traceback.format_exc())
            return {
                "success": False, 
                "message": f"处理请求时出错: {str(e)}"
            }
    
    def _analyze_student_questions(self, messages: List[str], max_questions: int = 10) -> List[Dict[str, Any]]:
        """
        使用大模型分析学生问题并提取共性问题
        
        Args:
            messages: 学生消息列表
            max_questions: 最多返回的问题数量
            
        Returns:
            问题列表，每个问题是一个包含问题描述、解释等的字典
        """
        try:
            # 准备系统提示和用户提示
            system_prompt = """你是一位教育分析专家，你的任务是从学生的聊天消息中识别并总结共性问题。

严格按照以下要求输出：
1. 你必须且只能输出一个严格有效的JSON数组，不包含任何额外说明、前缀或后缀文本
2. 不要使用markdown格式的代码块
3. 不要包含任何非JSON语法的文本
4. 输出必须以'['开始，以']'结束

分析提供的学生消息列表，识别出这些消息中反映的主要问题和关注点。
你需要：
1. 识别出学生提问的模式和共性
2. 将相似的问题归类为一个主题
3. 提供每个问题的简洁描述
4. 如果可能，提供该问题出现的频率或例子

每个问题对象必须严格包含以下字段：
- "question": 问题的简短描述（必需，字符串类型）
- "explanation": 问题的详细解释（可选，字符串类型）
- "frequency": 问题出现的大致频率（可选，数字类型）
- "examples": 该问题的1-2个典型例子（可选，字符串数组类型）

示例格式（请确保你的输出与此格式完全一致）：
[
  {
    "question": "问题1",
    "explanation": "解释1",
    "frequency": 5,
    "examples": ["例子1", "例子2"]
  },
  {
    "question": "问题2",
    "explanation": "解释2",
    "frequency": 3,
    "examples": ["例子3"]
  }
]

记住：仅返回JSON数组，不要添加任何其他说明文字、引号或代码块标记。"""

            user_prompt = f"""以下是从学生聊天记录中提取的消息列表，请分析这些消息并总结出最多{max_questions}个主要共性问题。

请记住：你的回答必须是一个严格有效的JSON数组，且必须直接以'['开始，以']'结束，不包含任何额外文本或代码块标记。

以下是学生消息：
{json.dumps(messages, ensure_ascii=False)}"""
            WebAgentClass = duckduckgosearch.Web_Agent
            web_agent = WebAgentClass(api_key=self.api_key, api_key_type=self.api_key_type)
            # 使用系统提供的函数调用大模型
            content, _ = web_agent._generate_summary_with_system_prompt(user_prompt, system_prompt)
            
            # 确保内容是有效的JSON
            try:
                # 如果内容被包裹在代码块中，提取JSON部分
                if content.startswith("```json"):
                    content = content.split("```json")[1]
                if content.endswith("```"):
                    content = content.split("```")[0]
                
                # 尝试解析JSON
                content = content.strip()
                questions = json.loads(content)
                return questions[:max_questions]  # 限制返回数量
                
            except json.JSONDecodeError as e:
                logger.error(f"解析AI响应时出错: {str(e)}")
                logger.error(f"原始响应: {content}")
                # 尝试使用更宽松的解析方式
                try:
                    # 移除可能的前缀和后缀
                    if "[" in content and "]" in content:
                        content = content[content.find("["):content.rfind("]")+1]
                        questions = json.loads(content)
                        return questions[:max_questions]
                except Exception:
                    pass
                    
                return [{"question": "无法解析AI响应", "explanation": "服务返回的数据格式不正确"}]
                
        except Exception as e:
            logger.error(f"调用AI分析服务时出错: {str(e)}")
            logger.error(traceback.format_exc())
            return [{"question": "分析服务出错", "explanation": str(e)}]

# 创建一个Flask应用提供API
if __name__ == "__main__":
   print(f"运行测试")
   analyzer = QuestionAnalyzer()
   result = analyzer.analyze_class_questions("test_class")
   print(json.dumps(result, ensure_ascii=False, indent=2))
   