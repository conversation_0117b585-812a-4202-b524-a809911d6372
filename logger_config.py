import logging
import uuid
from logging.handlers import RotatingFileHandler
import os
from datetime import datetime
import sys
import io

# 创建日志目录
log_dir = 'logs'
if not os.path.exists(log_dir):
    os.makedirs(log_dir)

class LimitedRotatingFileHandler(RotatingFileHandler):
    """限制日志行数的处理器"""
    def __init__(self, filename, max_lines=1000, **kwargs):
        # 确保编码参数存在且为UTF-8
        kwargs['encoding'] = 'utf-8'  # 强制使用UTF-8，覆盖任何其他设置
        kwargs['delay'] = True  # 延迟打开文件
        super().__init__(filename, **kwargs)
        self.max_lines = max_lines
        
    def emit(self, record):
        try:
            # 确保消息可以被正确编码
            if hasattr(record, 'msg') and isinstance(record.msg, str):
                # 将消息转换为安全的UTF-8字符串
                record.msg = record.msg.encode('utf-8', errors='replace').decode('utf-8')
            
            # 如果有格式化参数，也确保它们是UTF-8安全的
            if hasattr(record, 'args') and record.args:
                args = []
                for arg in record.args:
                    if isinstance(arg, str):
                        args.append(arg.encode('utf-8', errors='replace').decode('utf-8'))
                    else:
                        args.append(arg)
                record.args = tuple(args)
            
            # 使用 try-finally 确保文件被正确关闭
            try:
                if self.stream is None:
                    self.stream = self._open()
                super().emit(record)
            finally:
                if self.stream:
                    self.stream.close()
                    self.stream = None
                    
            # 限制日志行数
            try:
                with open(self.baseFilename, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                if len(lines) > self.max_lines:
                    with open(self.baseFilename, 'w', encoding='utf-8') as f:
                        f.writelines(lines[-self.max_lines:])  # 保留最后max_lines行
            except Exception as e:
                print(f"限制日志行数时出错: {str(e)}")
                
        except Exception as e:
            # 记录错误但不中断程序
            print(f"日志记录错误: {str(e)}")
            
    def doRollover(self):
        """重写轮转方法，添加错误处理"""
        if self.stream:
            self.stream.close()
            self.stream = None
        
        try:
            if os.path.exists(self.baseFilename + ".1"):
                os.remove(self.baseFilename + ".1")
            if os.path.exists(self.baseFilename):
                os.rename(self.baseFilename, self.baseFilename + ".1")
        except OSError:
            # 如果重命名失败，尝试直接写入新文件
            pass
            
        if not self.delay:
            self.stream = self._open()

# 添加一个处理Unicode字符的函数
def safe_unicode(text):
    """确保文本是UTF-8安全的"""
    if not isinstance(text, str):
        text = str(text)
    
    # 处理可能包含特殊Unicode字符的文本
    try:
        # 尝试直接使用UTF-8编码
        return text.encode('utf-8', errors='replace').decode('utf-8')
    except UnicodeError:
        # 如果出现编码错误，替换所有非ASCII字符
        result = []
        for char in text:
            if ord(char) < 128:  # ASCII字符范围
                result.append(char)
            else:
                result.append('_')  # 用下划线替换非ASCII字符
        return ''.join(result)

def setup_logger(name, log_file=None):
    """统一的日志设置函数"""
    logger = logging.getLogger(name)
    
    # 如果logger已经配置过，直接返回
    if logger.handlers:
        return logger
        
    logger.setLevel(logging.INFO)
    
    # 设置格式
    formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    
    # 添加控制台处理器，使用默认的系统编码
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # 如果指定了日志文件，添加文件处理器
    if log_file:
        try:
            # 确保日志目录存在
            log_dir = os.path.dirname(log_file)
            if log_dir and not os.path.exists(log_dir):
                os.makedirs(log_dir)
                
            # 明确指定使用UTF-8编码，而不是系统默认编码
            file_handler = LimitedRotatingFileHandler(
                filename=log_file,
                max_lines=5000,
                maxBytes=1024*1024,  # 1MB
                backupCount=3,
                encoding='utf-8',  # 明确指定UTF-8编码
                delay=True  # 延迟打开文件
            )
            file_handler.setFormatter(formatter)
            logger.addHandler(file_handler)
        except Exception as e:
            print(f"添加文件处理器时出错: {str(e)}")
            # 如果添加文件处理器失败，至少保留控制台输出
    
    # 禁用日志传播，防止日志被重复记录
    logger.propagate = False
    
    return logger

# API日志配置
api_logger = setup_logger('api_logger', os.path.join(log_dir, f'api_{datetime.now().strftime("%Y%m%d")}.log'))

# 添加一个更强大的Unicode字符处理函数
def handle_unicode_safely(text):
    """
    全面处理Unicode字符，确保它们可以被安全地记录到日志中
    
    Args:
        text: 需要处理的文本
        
    Returns:
        str: 处理后的安全文本
    """
    if not isinstance(text, str):
        text = str(text)
    
    try:
        # 尝试直接使用UTF-8编码
        return text.encode('utf-8', errors='replace').decode('utf-8')
    except Exception:
        # 如果出现任何编码错误，使用更安全的方式
        return ''.join(c if ord(c) < 128 else f'\\u{ord(c):04x}' for c in text)

# 修改log_with_request_id函数，使用新的Unicode处理方法
def log_with_request_id(message, level=logging.INFO, request_id=None):
    """带有请求ID的日志记录函数"""
    if request_id is None:
        request_id = str(uuid.uuid4())[:8]
    
    # 使用增强的Unicode处理函数
    message = handle_unicode_safely(message)
    
    message_lines = message.split('\n')
    formatted_lines = []
    for line in message_lines:
        if line.strip():
            formatted_lines.append(line.strip())
    
    formatted_message = '\n'.join(formatted_lines)
    
    # 确保日志记录不会因编码问题而失败
    try:
        extra = {'request_id': request_id}
        api_logger.log(level, formatted_message, extra=extra)
    except Exception as e:
        # 如果仍然失败，尝试使用纯ASCII
        fallback_message = f"[日志编码失败] 原始消息长度: {len(message)}"
        api_logger.log(level, fallback_message, extra=extra)
        # 打印到控制台以便调试
        print(f"日志记录失败: {str(e)}")

# 监控服务器日志配置
monitor_logger = setup_logger('monitor_logger', os.path.join(log_dir, f'monitor_{datetime.now().strftime("%Y%m%d")}.log'))

# 在文件末尾添加视频服务器的日志配置
video_server_logger = setup_logger('video_server', os.path.join(log_dir, f'video_server_{datetime.now().strftime("%Y%m%d")}.log'))

class CustomTransLogger:
    """自定义的WSGI请求日志记录器"""
    def __init__(self, app, setup_console_handler=True):
        self.app = app
        self.logger = video_server_logger
        
    def __call__(self, environ, start_response):
        start_time = datetime.now()
        
        if environ.get('PATH_INFO') == '/favicon.ico':
            return self.app(environ, start_response)
            
        def custom_start_response(status, headers, exc_info=None):
            duration = datetime.now() - start_time
            status_code = int(status.split()[0])
            path = environ.get('PATH_INFO', '-')
            method = environ.get('REQUEST_METHOD', '-')
            request_id = str(uuid.uuid4())[:8]
            
            log_message = (
                f"Request: {method} {path} - "
                f"Status: {status_code} - "
                f"Duration: {duration.total_seconds()*1000:.2f}ms"
            )
            
            # 确保日志消息是UTF-8安全的
            log_message = safe_unicode(log_message)
            
            # 根据状态码选择日志级别
            if status_code >= 500:
                self.logger.error(log_message, extra={'request_id': request_id})
            elif status_code >= 400:
                self.logger.warning(log_message, extra={'request_id': request_id})
            else:
                self.logger.info(log_message, extra={'request_id': request_id})
                
            return start_response(status, headers, exc_info)
            
        try:
            return self.app(environ, custom_start_response)
        except Exception as e:
            error_msg = safe_unicode(f"Request failed: {str(e)}")
            self.logger.error(error_msg, extra={'request_id': str(uuid.uuid4())[:8]})
            raise 

# 配置日志记录到文件
def setup_logging(logger_name, log_file):
    # 创建文件处理器并指定编码
    logger = logging.getLogger(logger_name) 
    file_handler = LimitedRotatingFileHandler(
        log_file,
        max_lines=10000,  # 设置最大行数为10000
        maxBytes=1024*1024,  # 1MB
        backupCount=3,
        encoding='utf-8'
    )
    file_handler.setLevel(logging.INFO)
    # 设置日志格式
    formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    file_handler.setFormatter(formatter)
    # 创建根日志记录器
    logger.setLevel(logging.INFO)
    logger.addHandler(file_handler)
    # 添加控制台处理器（可选）
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    return logger  # 返回配置好的logger

deepsearch_logger = setup_logging('deepsearch_logger', os.path.join(log_dir, f'deepsearch_{datetime.now().strftime("%Y%m%d")}.log'))

# 添加一个处理Unicode字符的函数，用于处理日志消息中的特殊字符
def sanitize_log_message(message):
    """
    处理日志消息中的Unicode字符，确保它们可以被安全地记录
    
    Args:
        message (str): 原始日志消息
        
    Returns:
        str: 处理后的日志消息
    """
    if not isinstance(message, str):
        message = str(message)
    
    # 处理可能包含特殊Unicode字符的消息
    try:
        # 尝试直接使用UTF-8编码
        return message.encode('utf-8', errors='replace').decode('utf-8')
    except UnicodeError:
        # 如果出现编码错误，替换所有非ASCII字符
        result = []
        for char in message:
            if ord(char) < 128:  # ASCII字符范围
                result.append(char)
            else:
                # 对于希腊字母和常见数学符号，尝试使用ASCII替代
                replacement = get_ascii_replacement(char)
                result.append(replacement)
        return ''.join(result)

def get_ascii_replacement(char):
    """
    将Unicode字符转换为ASCII替代字符
    
    Args:
        char (str): 单个Unicode字符
        
    Returns:
        str: ASCII替代字符
    """
    # 希腊字母映射
    greek_letters = {
        'α': 'alpha', 'β': 'beta', 'γ': 'gamma', 'δ': 'delta', 'ε': 'epsilon',
        'ζ': 'zeta', 'η': 'eta', 'θ': 'theta', 'ι': 'iota', 'κ': 'kappa',
        'λ': 'lambda', 'μ': 'mu', 'ν': 'nu', 'ξ': 'xi', 'ο': 'omicron',
        'π': 'pi', 'ρ': 'rho', 'σ': 'sigma', 'τ': 'tau', 'υ': 'upsilon',
        'φ': 'phi', 'χ': 'chi', 'ψ': 'psi', 'ω': 'omega',
        'Α': 'Alpha', 'Β': 'Beta', 'Γ': 'Gamma', 'Δ': 'Delta', 'Ε': 'Epsilon',
        'Ζ': 'Zeta', 'Η': 'Eta', 'Θ': 'Theta', 'Ι': 'Iota', 'Κ': 'Kappa',
        'Λ': 'Lambda', 'Μ': 'Mu', 'Ν': 'Nu', 'Ξ': 'Xi', 'Ο': 'Omicron',
        'Π': 'Pi', 'Ρ': 'Rho', 'Σ': 'Sigma', 'Τ': 'Tau', 'Υ': 'Upsilon',
        'Φ': 'Phi', 'Χ': 'Chi', 'Ψ': 'Psi', 'Ω': 'Omega'
    }
    
    # 数学符号映射
    math_symbols = {
        '×': '*', '÷': '/', '±': '+/-', '≈': '~=', '≠': '!=', 
        '≤': '<=', '≥': '>=', '∞': 'inf', '∑': 'sum', '∏': 'prod',
        '∂': 'd', '∫': 'int', '∈': 'in', '∉': 'not in', '∩': 'intersection',
        '∪': 'union', '⊂': 'subset', '⊃': 'superset', '⊆': 'subset or equal',
        '⊇': 'superset or equal', '√': 'sqrt', '∛': 'cbrt', '∜': '4rt',
        '⋅': '.', '∴': 'therefore', '∵': 'because', '∝': 'proportional to'
    }
    
    # 上标和下标映射
    superscripts = {
        '⁰': '^0', '¹': '^1', '²': '^2', '³': '^3', '⁴': '^4',
        '⁵': '^5', '⁶': '^6', '⁷': '^7', '⁸': '^8', '⁹': '^9',
        '⁺': '^+', '⁻': '^-', '⁼': '^=', '⁽': '^(', '⁾': '^)'
    }
    
    subscripts = {
        '₀': '_0', '₁': '_1', '₂': '_2', '₃': '_3', '₄': '_4',
        '₅': '_5', '₆': '_6', '₇': '_7', '₈': '_8', '₉': '_9',
        '₊': '_+', '₋': '_-', '₌': '_=', '₍': '_(', '₎': '_)'
    }
    
    # 尝试从各个映射中查找替代字符
    if char in greek_letters:
        return greek_letters[char]
    elif char in math_symbols:
        return math_symbols[char]
    elif char in superscripts:
        return superscripts[char]
    elif char in subscripts:
        return subscripts[char]
    else:
        # 对于其他未知的Unicode字符，返回下划线
        return '_'

# 修改日志记录函数，确保所有消息都经过sanitize_log_message处理
def safe_log(logger, level, message, *args, **kwargs):
    """安全的日志记录函数，确保消息可以被正确编码"""
    safe_message = sanitize_log_message(message)
    logger.log(level, safe_message, *args, **kwargs)

# 为常用日志级别提供便捷函数
def safe_info(logger, message, *args, **kwargs):
    safe_log(logger, logging.INFO, message, *args, **kwargs)

def safe_error(logger, message, *args, **kwargs):
    safe_log(logger, logging.ERROR, message, *args, **kwargs)

def safe_warning(logger, message, *args, **kwargs):
    safe_log(logger, logging.WARNING, message, *args, **kwargs)

def safe_debug(logger, message, *args, **kwargs):
    safe_log(logger, logging.DEBUG, message, *args, **kwargs)

