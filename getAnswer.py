import requests
import json
import time
import uuid
import re
import os
import duckduckgosearch
from typing import List, Optional, Dict, Union, Tuple, Any
import logging
from logger_config import log_with_request_id  # 从新模块导入
from ragflow_sdk import RAGFlow
#blog id=5252db8a8d5311efab1e0242ac120005
# chat id=1477989a766611efac910242ac120006
# test id=8a3a5cfa71e511efb3a40242ac120005
# DM  id=2fbe8d6e6e1111ef9b760242ac120002
# dataset_ids = ["ac4cfa1affa111ef9d352a5c03e306d6"]  





chat_id="3b58bf3e8e2611ef98110242ac120006" #blog_chat
request_id = str(uuid.uuid4())[:8]
def create_chat(api_key: str, name: str, dataset_ids: list[str], **kwargs):
    """
    创建一个新的chat助手
    
    Args:
        api_key (str): API密钥
        name (str): chat助手的名称
        dataset_ids (list[str]): 数据集ID列表
        **kwargs: 可选参数，包括avatar, llm配置和prompt配置
    
    Returns:
        dict: 响应数据，成功时返回包含chat_id的字典，失败时返回None
    """
    
    url = 'http://localhost:8080/api/v1/chats'
    headers = {
        'Content-Type': 'application/json',
        'Authorization': f'Bearer {api_key}'
    }
    
    # 构建基本请求数据
    data = {
        "name": name,
        "dataset_ids": dataset_ids,
        "description": "A helpful Assistant",  # 添加描述
        "language": "English",                 # 添加语言设置
        "status": "1",                        # 添加状态
        "do_refer": "1"                       # 添加引用设置
    }
    
    # 默认的LLM配置
    default_llm = {
        "model_name": "deepseek-chat",
        "temperature": 0.1,
        "top_p": 0.3,
        "presence_penalty": 0.4,
        "frequency_penalty": 0.7,
        "max_tokens": 512
    }
    
    # 默认的prompt配置
    default_prompt = {
        "similarity_threshold": 0.2,
        "keywords_similarity_weight": 0.3,
        "top_n": 6,
        "variables": [{"key": "knowledge", "optional": True}],
        "empty_response": "Sorry! No relevant content was found in the knowledge base!",
        "opener": "Hi! I'm your assistant, what can I do for you?",
        "show_quote": True,
        "prompt": """You are an intelligent assistant. Your task is to help answer questions based on the provided knowledge: {knowledge}

When answering:
1. Use the knowledge provided to give accurate responses
2. If the knowledge is insufficient, say "The answer you are looking for is not found in the knowledge base"
3. Consider the chat history for context
4. Be clear and detailed in your explanations

Please proceed with answering the user's question.""",
        "prompt_type": "simple",
        "rerank_model": ""
    }
    
    # 更新配置
    if 'llm' in kwargs:
        default_llm.update(kwargs['llm'])
    if 'prompt' in kwargs:
        default_prompt.update(kwargs['prompt'])
    if 'avatar' in kwargs:
        data['avatar'] = kwargs['avatar']
    
    # 添加配置到请求数据
    data['llm'] = default_llm
    data['prompt'] = default_prompt
    
    try:
        response = requests.post(url, headers=headers, json=data, verify=False)
        response_data = response.json()
        
        # 检查响应状态
        if response_data.get('code') == 0:
            print(f"Chat created successfully: {response_data['data']['id']}")
            return response_data
        else:
            error_message = response_data.get('message', 'Unknown error')
            print(f"Failed to create chat: {error_message}")
            return None
            
    except requests.exceptions.RequestException as e:
        print(f"Error creating chat: {e}")
        return None


def create_session(api_key: str, chat_id: str, name: str) -> dict:
    """
    为指定的chat创建一个新的session
    
    Args:
        api_key (str): API密钥
        chat_id (str): chat助手的ID
        name (str): session的名称
    
    Returns:
        dict: 响应数据，成功时返回包含session信息的字典，失败时返回None
    """
    
    url = f'http://localhost:8080/api/v1/chats/{chat_id}/sessions'
    headers = {
        'Content-Type': 'application/json',
        'Authorization': f'Bearer {api_key}'
    }
    
    data = {
        "name": name
    }
    
    try:
        response = requests.post(url, headers=headers, json=data, verify=False)
        response_data = response.json()
        
        if response_data.get('code') == 0:
            print(f"Session created successfully: {response_data['data']['id']}")
            return response_data
        else:
            error_message = response_data.get('message', 'Unknown error')
            print(f"Failed to create session: {error_message}")
            return None
            
    except requests.exceptions.RequestException as e:
        print(f"Error creating session: {e}")
        return None



def delete_sessions(api_key: str, chat_id: str, session_ids: list[str] = None) -> bool:
    """
    删除指定chat下的一个或多个sessions
    
    Args:
        api_key (str): API密钥
        chat_id (str): chat助手的ID
        session_ids (list[str], optional): 要删除的session ID列表。
                                         如果不指定，将删除该chat下的所有sessions
    
    Returns:
        bool: 删除成功返回True，失败返回False
    """
    
    url = f'http://localhost:8080/api/v1/chats/{chat_id}/sessions'
    headers = {
        'Content-Type': 'application/json',
        'Authorization': f'Bearer {api_key}'
    }
    
    # 如果提供了session_ids，则构建请求体
    data = {"ids": session_ids} if session_ids else {}
    
    try:
        response = requests.delete(url, headers=headers, json=data, verify=False)
        response_data = response.json()
        
        if response_data.get('code') == 0:
            if session_ids:
                print(f"Successfully deleted sessions: {', '.join(session_ids)}")
            else:
                print(f"Successfully deleted all sessions for chat: {chat_id}")
            return True
        else:
            error_message = response_data.get('message', 'Unknown error')
            print(f"Failed to delete sessions: {error_message}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"Error deleting sessions: {e}")
        return False




def send_chat_message(api_key: str, chat_id: str, question: str, session_id: str = None, stream: bool = True) -> dict:
    """
    向chat助手发送问题并获取回答
    
    Args:
        api_key (str): API密钥
        chat_id (str): chat助手的ID
        question (str): 要问的问题
        session_id (str, optional): session的ID。如果不提供，将创建新的session
        stream (bool, optional): 是否使用流式输出，默认为True
    
    Returns:
        dict: 最终的完整响应数据
    """
    url = f'http://localhost:8080/api/v1/chats/{chat_id}/completions'
    headers = {
        'Content-Type': 'application/json',
        'Authorization': f'Bearer {api_key}'
    }
    
    data = {
        "question": question,
        "stream": stream
    }
    
    if session_id:
        data["session_id"] = session_id
    
    try:
        response = requests.post(url, headers=headers, json=data, verify=False, stream=stream)
        
        if not stream:
            # 非流式输出模式
            response_data = response.json()
            if response_data.get('code') == 0:
                return response_data['data']
            else:
                print(f"Error: {response_data.get('message', 'Unknown error')}")
                return None
        
        # 流式输出模式
        last_answer = None
        for line in response.iter_lines():
            if line:
                try:
                    # 去掉 'data:' 前缀并解析 JSON
                    json_data = json.loads(line.decode('utf-8').replace('data:', '').strip())
                    
                    if json_data['code'] == 0:
                        if isinstance(json_data['data'], dict):
                            answer = json_data['data'].get('answer', '')
                            if answer:
                                # print(answer, end='\r')  # 实时打印答案
                                last_answer = json_data['data']
                        elif json_data['data'] is True:
                            # 这是最后的完成信号
                            # print("\nResponse completed.")
                            break
                except json.JSONDecodeError:
                    print(f"Failed to decode JSON: {line}")
                except Exception as e:
                    print(f"Error processing response: {e}")
        
        return last_answer
            
    except requests.exceptions.RequestException as e:
        print(f"Error sending message: {e}")
        return None




def retrieve_chunks(api_key_rag: str, question: str = "", dataset_ids: list[str] = None, 
                   document_ids: list[str] = None, page: int = 1, page_size: int = 30,
                   similarity_threshold: float = 0.2, vector_similarity_weight: float = 0.3,
                   top_k: int = 1024, rerank_id: str = None, keyword: bool = False,
                   highlight: bool = False, **kwargs) -> dict:
    """
    从指定的数据集或文档中检索相关内容块
    
    Args:
        api_key_rag (str): API密钥
        question (str): 用户查询或关键词，默认为空字符串
        dataset_ids (list[str], optional): 要搜索的数据集ID列表
        document_ids (list[str], optional): 要搜索的文档ID列表
        page (int, optional): 指定显示的页码，默认为1
        page_size (int, optional): 每页的最大块数，默认为30
        similarity_threshold (float, optional): 最小相似度分数，默认为0.2
        vector_similarity_weight (float, optional): 向量余弦相似度权重，默认为0.3
        top_k (int, optional): 参与向量余弦计算的块数量，默认为1024
        rerank_id (str, optional): 重排序模型ID，默认为None
        keyword (bool, optional): 是否启用关键词匹配，默认为False
        highlight (bool, optional): 是否启用匹配词高亮，默认为False
    
    Returns:
        dict: 响应数据，成功时返回包含相关内容块的字典，失败时返回空字典
    """
    # 参数验证
    if not api_key_rag:
        print("Error: api_key_rag is required")
        log_with_request_id("Error: api_key_rag is required", level=logging.ERROR, request_id=request_id)
        return {}
    
    # 确保dataset_ids和document_ids至少有一个不为空
    if not dataset_ids and not document_ids:
        print("Error: Either dataset_ids or document_ids must be provided")
        log_with_request_id("Error: Either dataset_ids or document_ids must be provided", level=logging.ERROR, request_id=request_id)
        return {}
    
    url = 'http://localhost:8080/api/v1/retrieval'
    headers = {
        'Content-Type': 'application/json',
        'Authorization': f'Bearer {api_key_rag}'
    }
    
    # 只保留必要的参数
    data = {
        "question": question,
        "page": page,
        "page_size": page_size,
        "similarity_threshold": similarity_threshold,
        "vector_similarity_weight": vector_similarity_weight,
        "top_k": top_k,
        "keyword": keyword,
        "highlight": highlight
    }
    
    # 添加rerank_id参数（如果提供）
    if rerank_id:
        data["rerank_id"] = rerank_id
    
    # 确保 dataset_ids 是一维数组
    if dataset_ids:
        if isinstance(dataset_ids, str):
            dataset_ids = [dataset_ids]
        elif isinstance(dataset_ids, list):
            # 如果是嵌套列表，取第一层
            if dataset_ids and isinstance(dataset_ids[0], list):
                dataset_ids = dataset_ids[0]
            # 过滤掉空字符串和None
            dataset_ids = [id for id in dataset_ids if id]
        
        if dataset_ids:  # 只有在列表非空时才添加到请求中
            data["dataset_ids"] = dataset_ids
        
    # 确保 document_ids 是一维数组
    if document_ids:
        if isinstance(document_ids, str):
            document_ids = [document_ids]
        elif isinstance(document_ids, list):
            # 如果是嵌套列表，取第一层
            if document_ids and isinstance(document_ids[0], list):
                document_ids = document_ids[0]
            # 过滤掉空字符串和None
            document_ids = [id for id in document_ids if id]
            
        if document_ids:  # 只有在列表非空时才添加到请求中
            data["document_ids"] = document_ids
    
    # 最终检查是否有有效的ID
    if not data.get("dataset_ids") and not data.get("document_ids"):
        print("Error: No valid dataset_ids or document_ids after processing")
        log_with_request_id("Error: No valid dataset_ids or document_ids after processing", level=logging.ERROR, request_id=request_id)
        return {}
    
    try:
        print(f"Sending request to {url}")
        print(f"Request data: {data}")
        log_with_request_id(f"Sending request to {url}", request_id=request_id)
        log_with_request_id(f"Request data: {data}", request_id=request_id)
        
        response = requests.post(url, headers=headers, json=data, verify=False, timeout=30)

        print(f"Response status code: {response.status_code}")
        log_with_request_id(f"Response status code: {response.status_code}", request_id=request_id)
        
        if response.status_code != 200:
            print(f"Error: Received status code {response.status_code}")
            log_with_request_id(f"Error: Received status code {response.status_code}", level=logging.ERROR, request_id=request_id)
            try:
                error_content = response.json()
                print(f"Error response: {error_content}")
                log_with_request_id(f"Error response: {error_content}", level=logging.ERROR, request_id=request_id)
            except:
                print(f"Error response content: {response.text}")
                log_with_request_id(f"Error response content: {response.text}", level=logging.ERROR, request_id=request_id)
            return {}
        
        try:
            response_data = response.json()
            log_with_request_id(f"Response data: {response_data}", request_id=request_id)
        except ValueError as e:
            print(f"Error parsing JSON response: {str(e)}")
            log_with_request_id(f"Error parsing JSON response: {str(e)}", level=logging.ERROR, request_id=request_id)
            return {}
        
        if response_data.get('code') == 0:
            # 返回整个data部分，而不仅仅是chunks数组
            return response_data.get('data', {})
        else:
            error_message = response_data.get('message', 'Unknown error')
            print(f"Failed to retrieve chunks: {error_message}")
            log_with_request_id(f"Failed to retrieve chunks: {error_message}", level=logging.ERROR, request_id=request_id)
            return {}
            
    except Exception as e:
        print(f"Error in retrieve_chunks: {str(e)}")
        log_with_request_id(f"Error in retrieve_chunks: {str(e)}", level=logging.ERROR, request_id=request_id)
        return {}






def create_prompt(content: str, query: str,) -> str:
    return f"""
    综合知识库的内容 {content[:8000]}.
    一步步思考如下问题：
    {query}
    然后给出回答，严格遵守以下格式规则：
    1. 所有独立的数学公式必须使用双美元符号($$)包裹，例如：
       $$ E = mc^2 $$
    2. 行内的数学符号或简短公式使用单美元符号($)包裹，例如：质量 $m$ 和速度 $v$
    3. 在 LaTeX 公式中，使用 \\( 和 \\) 来表示括号，例如：$\\( x^2 + y^2 = r^2 \\)$
    4. 对于矩阵，使用 \\begin{{matrix}} 和 \\end{{matrix}}，例如：
       $$ \\begin{{matrix}} a & b \\\\ c & d \\end{{matrix}} $$
    5. 确保所有数学符号、下标、上标等都正确使用 LaTeX 语法
    6. 不要使用 [和] 来包裹公式，始终使用 $ 或 $$

    不限制字数，请严格按照这些规则，开始你的完整回答：
    """


######################################################################################
#获取doc的所有chunks 
def get_document_chunks(
    api_key_rag: str,
    base_url: str,
    dataset_id: str,
    document_id: str,
    keywords: Optional[str] = None,
    chunk_id: Optional[str] = None,
    offset: int = 0,
    limit: int = 1024
) -> Dict:
    """
    获取文档的chunks并组合内容
    
    Returns:
        Dict: {
            'code': int,
            'data': {
                'content': str,  # 组合后的内容
                'chunks': List[Dict],  # 原始chunks
                'total': int  # chunks总数
            }
        }
    """
    try:
        chunks_url = f"{base_url.rstrip('/')}/api/v1/datasets/{dataset_id}/documents/{document_id}/chunks"
        
        params = {}
        if offset > 0:
            params['offset'] = str(offset)
        if limit > 0:
            params['limit'] = str(limit)
        if keywords:
            params['keywords'] = keywords
        if chunk_id:
            params['id'] = chunk_id
            
        headers = {
            'Authorization': f'Bearer {api_key_rag}',
            'Content-Type': 'application/json'
        }
        
        try:
            response = requests.get(
                chunks_url,
                headers=headers,
                params=params,
                timeout=30
            )
            
            data = response.json()
            
            if data.get('code', -1) == 0 and 'data' in data:
                chunks = data['data'].get('chunks', [])
                
                # 组合所有chunks的content
                combined_content = ''
                for chunk in chunks:
                    content = chunk.get('content', '')
                    if content:
                        # 添加换行以分隔不同chunk的内容
                        if combined_content:
                            combined_content += '\n\n'
                        combined_content += content
                
                return {
                    'code': 0,
                    'data': {
                        'content': combined_content,  # 组合后的内容
                        'chunks': chunks,  # 原始chunks
                        'total': len(chunks)  # chunks总数
                    }
                }
            else:
                error_msg = data.get('message', '未知错误')
                logging.warning(f"获取chunks失败 (dataset_id: {dataset_id}, document_id: {document_id}): {error_msg}")
                return {
                    'code': data.get('code', -1),
                    'message': error_msg
                }
            
        except requests.RequestException as e:
            logging.error(f"请求失败: {str(e)}")
            return {
                'code': -1,
                'message': f"请求失败: {str(e)}"
            }
            
    except Exception as e:
        logging.error(f"未预期的错误: {str(e)}")
        return {
            'code': -1,
            'message': str(e)
        }





######################################################
#blog等多文件回答，利用retrieval检索和deepseek-chat回答

def retrieve_and_combine_content(api_key_rag: str, question: str, dataset_ids: list[str] = None, 
                               document_ids: list[str] = None, page: int = 1, page_size: int = 50,
                               similarity_threshold: float = 0.20, vector_similarity_weight: float = 0.7,
                               top_k: int = 1024, keyword: bool = True,
                               highlight: bool = False, **kwargs) -> tuple[str, list[str]]:
    """
    检索指定数据集或文档的内容并将其拼接
    
    Args:
        api_key (str): API密钥
        question (str): 用户查询或关键词
        dataset_ids (list[str], optional): 要搜索的数据集ID列表
        document_ids (list[str], optional): 要搜索的文档ID列表
        page (int, optional): 指定显示的页码，默认为1
        page_size (int, optional): 每页的最大块数，默认为30
        similarity_threshold (float, optional): 最小相似度分数，默认为0.2
        vector_similarity_weight (float, optional): 向量余弦相似度权重，默认为0.3
        top_k (int, optional): 参与向量余弦计算的块数量，默认为1024
        rerank_id (str, optional): 重排序模型ID
        keyword (bool, optional): 是否启用关键词匹配，默认为False
        highlight (bool, optional): 是否启用匹配词高亮，默认为False
        **kwargs: 其他可选参数
    
    Returns:
        tuple[str, list[str]]: 包含两个元素的元组:
            - str: 拼接后的内容。如果检索失败返回空字符串
            - list[str]: 去重后的文档名称列表作为参考文献
    """
    # 验证必要参数
    if not dataset_ids and not document_ids:
        print("Error: Neither dataset_ids nor document_ids provided")
        return "", []
    
    # 设置默认参数（与retrieve_chunks保持一致）
    default_params = {
        "page": page,
        "page_size": page_size,
        "similarity_threshold": similarity_threshold,
        "vector_similarity_weight": vector_similarity_weight,
        "top_k": top_k,
        "keyword": keyword,
        "highlight": highlight
    }
    
    # 更新参数
    default_params.update(kwargs)
    
    try:
        # 执行检索
        result = retrieve_chunks(
            api_key_rag=api_key_rag,
            question=question,
            dataset_ids=dataset_ids,
            document_ids=document_ids,
            **default_params
        )
        
        if not result:
            log_with_request_id(f"No result returned from retrieve_chunks", level=logging.WARNING, request_id=request_id)
            return "", []
            
        if not result.get('chunks'):
            log_with_request_id(f"No chunks found in the result: {result}", level=logging.WARNING, request_id=request_id)
            return "", []
        
        # 按相似度排序chunks
        sorted_chunks = sorted(result['chunks'], 
                             key=lambda x: x.get('similarity', 0), 
                             reverse=True)
        
        # 拼接内容和收集文档名称
        combined_content = []
        doc_names = set()  # 使用集合来自动去重
        
        for chunk in sorted_chunks:
            # 获取文档信息
            doc_name = chunk.get('document_keyword', 'Unknown Document')
            content = chunk.get('content', '').strip()
            similarity = chunk.get('similarity', 0)
            
            # 添加文档名称到集合中（自动去重）
            if doc_name != 'Unknown Document':
                doc_names.add(doc_name)
            
            # 添加内容
            if content:
                # 确保内容是UTF-8编码，使用更安全的方法
                try:
                    if isinstance(content, bytes):
                        content = content.decode('utf-8', errors='replace')
                    elif isinstance(content, str):
                        # 先编码再解码，确保清理无效字符
                        content = content.encode('utf-8', errors='replace').decode('utf-8', errors='replace')
                    combined_content.append(content)
                except Exception as e:
                    log_with_request_id(f"Error processing content encoding: {e}", level=logging.WARNING, request_id=request_id)
                    # 如果编码处理失败，跳过这个chunk
                    continue

        # 如果没有有效内容，返回空字符串和空列表
        if not combined_content:
            log_with_request_id("No valid content found in chunks", level=logging.WARNING, request_id=request_id)
            return "", []
            
        # 返回拼接后的内容和去重后的文档名称列表
        return "\n\n".join(combined_content), sorted(list(doc_names))
        
    except Exception as e:
        print(f"Error retrieving and combining content: {e}")
        log_with_request_id(f"Error retrieving and combining content: {str(e)}", level=logging.ERROR, request_id=request_id)
        return "", []





def count_text_length(text: str) -> int:
    """
    计算文本的有效长度，区分中英文
    
    Args:
        text: 输入文本
        
    Returns:
        int: 文本的有效长度(中文字符数 + 英文单词数)
    """
    # 分离中文和非中文部分
    chinese_chars = 0
    english_text = []
    current_word = []
    
    for char in text:
        if '\u4e00' <= char <= '\u9fff':  # 中文字符范围
            # 如果之前有英文单词，保存它
            if current_word:
                english_text.append(''.join(current_word))
                current_word = []
            chinese_chars += 1
        elif char.isalpha():  # 英文字母
            current_word.append(char)
        else:  # 空格、标点等
            if current_word:  # 结束当前英文单词
                english_text.append(''.join(current_word))
                current_word = []
    
    # 处理最后一个英文单词
    if current_word:
        english_text.append(''.join(current_word))
    
    # 计算英文单词数
    english_words = len([word for word in english_text if word])
    
    return chinese_chars + english_words

def count_chars(text):
    chinese_count = sum(1 for char in text if '\u4e00' <= char <= '\u9fff')
    english_words = len([word for word in text.split() if any(c.isascii() for c in word)])
    return chinese_count + english_words

def get_summary_from_dataset(api_key_rag: str, api_key: str, api_key_type: str, question: str, 
                           dataset_ids: list[str] = None, document_ids: list[str] = None, 
                           query_length: int = 8000, history: str = None, **kwargs) -> tuple[str, list[str]]:
    """
    从数据集检索内容并生成总结回答
    
    Args:
        api_key_rag (str): RAGFlow API密钥
        api_key (str): Deepseek或者 Siliconflow API密钥
        question (str): 用户问题
        dataset_ids (list[str], optional): 要搜索的数据集ID列表
        document_ids (list[str], optional): 要搜索的文档ID列表
        query_length (int, optional): 查询长度阈值，默认为8000
        **kwargs: 其他检索参数，如similarity_threshold等
    
    Returns:
        str: 生成的总结回答。如果处理失败返回None
    """
    try:
        # 1. 检索并组合内容
        content, references = retrieve_and_combine_content(
            api_key_rag=api_key_rag,
            question=question,
            dataset_ids=dataset_ids,
            document_ids=document_ids,
            **kwargs
        )
        
        # 删除无意义的符号，如 *、#号
        content = re.sub(r'[*#]', '', content)  # 删除所有 *、#号
        content = re.sub(r'\s+', ' ', content).strip()  # 删除多余的空格

        log_with_request_id(f"检索到的原始内容字数为:{count_text_length(content)}，内容为:{content[:100]}", request_id=request_id)
        # 初始化 current_count
        current_count = count_text_length(content)
        
        # 如果中英文内容超过15000个字符/单词，需要截取
        if current_count > 8000:
            current_count = 0
            result_chars = []           
            for char in content:
                # 所有字符都保留并计数
                if '\u4e00' <= char <= '\u9fff':  # 中文字符
                    current_count += 1
                elif char.isspace():  # 空格
                    # 检查是否是英文单词的分隔符
                    if result_chars and result_chars[-1].isascii():
                        current_count += 1
                else:  # 所有其他字符（包括英文、特殊字符等）
                    if not result_chars or not result_chars[-1].isascii():
                        current_count += 1
                        
                if current_count > 8000:
                    break
                    
                result_chars.append(char)
                
            content = ''.join(result_chars)
        
        log_with_request_id(f"截取后检索到的内容字数为:{current_count}，内容为:{content[:100]}", level=logging.WARNING, request_id=request_id)
        print(f"截取后检索到的内容字数为:{current_count}")
        if not content:
            print("Failed to retrieve content")
            log_with_request_id(f"检索出错了！", level=logging.ERROR, request_id=request_id)
            return None        
        return content, references
        
    except Exception as e:
        print(f"Error generating summary: {e}")
        return None

def clean_tokens_information(text: str) -> str:
    """
    清理文本中可能包含的tokens消耗信息
    """
    # 使用正则表达式移除常见的tokens消耗信息格式
    import re
    # 匹配"本次消耗tokens:数字"或类似格式
    cleaned_text = re.sub(r'\n*本次消耗tokens[:：]\s*\d+\s*', '', text)
    # 匹配"消耗tokens:数字"或类似格式
    cleaned_text = re.sub(r'\n*消耗tokens[:：]\s*\d+\s*', '', cleaned_text)
    # 匹配"tokens:数字"或类似格式
    cleaned_text = re.sub(r'\n*tokens[:：]\s*\d+\s*', '', cleaned_text)
    return cleaned_text.strip()

class SmartChatbot:
    def __init__(self, api_key_rag: str, api_key: str, api_key_type: str, folder: str):
        """
        初始化智能聊天机器人
        
        Args:
            api_key (str): RAGFlow API密钥
            api_key (str): Deepseek或者 Siliconflow API密钥
        """
        self.api_key_rag = api_key_rag
        self.api_key = api_key
        self.api_key_type = api_key_type
        self.folder = folder
        # 定义问题类型和关键词
        self.question_patterns = {
            'overview': {
                'patterns': [
                    r'(主要|整体|全部)(内容|介绍)',  # 更严格的概述模式
                    r'^(介绍|简介|概述|总结).*',     # 以这些词开头
                    r'.*?(有哪些|列举|总共).*?$',    # 列举类问题
                    r'文档.*?(是|包含).*?'          # 文档整体内容
                ],
                'keywords': ['整体', '全部', '主要', '文档'],
                'use_full_text': True
            },
            'specific': {
                'patterns': [
                    r'具体.*?说明',                # 具体说明类
                    r'(详细|具体).*?(介绍|描述)',   # 具体描述类
                    r'第.*?[个].*?(选题|内容)',    # 特定编号内容
                    r'怎么样|如何|为什么'           # 方法和原因类
                ],
                'keywords': ['具体', '详细', '原理', '什么', '如何', '怎么', '计算', '解释', '步骤', '第四', '第五','综述'],
                'use_full_text': False
            }
        }

    def analyze_question(self, question: str) -> Tuple[str, bool]:
        """
        分析问题类型并决定检索策略
        
        Args:
            question (str): 用户问题
            
        Returns:
            Tuple[str, bool]: (问题类型, 是否使用全文检索)
        """
        # 计算每种问题类型的匹配分数
        scores = {}
        matches = {}
        for q_type, config in self.question_patterns.items():
            score = 0
            pattern_matches = []
            
            # 检查关键词匹配
            for keyword in config['keywords']:
                if keyword in question:
                    score += 2  # 关键词匹配得2分
                    pattern_matches.append(f"关键词: {keyword}")
            
            # 检查模式匹配
            for pattern in config['patterns']:
                if re.search(pattern, question):
                    score += 3  # 模式匹配得3分
                    pattern_matches.append(f"模式: {pattern}")
            
            scores[q_type] = score
            matches[q_type] = pattern_matches
        
        # 确定问题类型
        question_type = max(scores.items(), key=lambda x: x[1])[0]
        use_full_text = self.question_patterns[question_type]['use_full_text']
        
        # 打印详细的匹配信息
        
        print(f"\n问题分析:")
        print(f"问题: {question}")
        logging.warning(f"n问题分析 {question}")
        for q_type in scores:
            print(f"{q_type}得分: {scores[q_type]}")
            if matches[q_type]:
                print(f"匹配项: {', '.join(matches[q_type])}")
        print(f"最终类型: {question_type}")
        print(f"使用全文检索: {use_full_text}\n")
        logging.warning(f"使用全文检索: {use_full_text}\n")
        return question_type, use_full_text

    def get_answer(self, 
                  question: str,
                  history: Optional[List[Dict]] = None,
                  dataset_ids: Optional[List[str]] = None,
                  document_ids: Optional[List[str]] = None,
                  **kwargs) -> str:
        

        WebAgentClass = duckduckgosearch.Web_Agent
        web_agent = WebAgentClass(api_key=self.api_key, api_key_type=self.api_key_type)
        try:
            # 确保dataset_ids和document_ids是有效的列表
            if dataset_ids is None:
                dataset_ids = []
            elif isinstance(dataset_ids, str):
                # 如果是逗号分隔的字符串，拆分为列表
                if ',' in dataset_ids:
                    dataset_ids = [id.strip() for id in dataset_ids.split(',')]
                else:
                    dataset_ids = [dataset_ids]
            
            if document_ids is None:
                document_ids = []
            elif isinstance(document_ids, str):
                # 如果是逗号分隔的字符串，拆分为列表
                if ',' in document_ids:
                    document_ids = [id.strip() for id in document_ids.split(',')]
                else:
                    document_ids = [document_ids]
            
            # 记录参数信息
            print(f"处理问题: {question}")
            print(f"Dataset IDs: {dataset_ids}")
            print(f"Document IDs: {document_ids}")
            log_with_request_id(f"处理问题: {question}", request_id=request_id)
            log_with_request_id(f"Dataset IDs: {dataset_ids}", request_id=request_id)
            log_with_request_id(f"Document IDs: {document_ids}", request_id=request_id)
            
            # 检查是否有有效的IDs
            if not dataset_ids and not document_ids:
                print("警告: 没有提供有效的dataset_ids或document_ids")
                log_with_request_id("警告: 没有提供有效的dataset_ids或document_ids", level=logging.WARNING, request_id=request_id)
                return "抱歉，无法处理您的请求，因为没有提供有效的数据集ID或文档ID。请检查参数并重试。"
            
            # 1. 分析问题类型
            question_type, use_full_text = self.analyze_question(question)
            
            # 2. 获取内容
            if use_full_text:
                print("使用全文检索策略...")
                log_with_request_id("使用全文检索策略...", request_id=request_id)
                content = ""                
                
                try:
                    # 调试信息
                    print(f"\n开始获取文档内容:")
                    log_with_request_id(f"开始获取文档内容:", request_id=request_id)
                    print(f"API Key: {self.api_key}")
                    print(f"Dataset ID: {dataset_ids}")
                    log_with_request_id(f"Document ID: {document_ids}", request_id=request_id)
                    
                    # 获取文档内容
                    if not dataset_ids or len(dataset_ids) == 0:
                        print("警告: dataset_ids为空")
                        log_with_request_id("警告: dataset_ids为空", level=logging.WARNING, request_id=request_id)
                    
                    if not document_ids or len(document_ids) == 0:
                        print("警告: document_ids为空")
                        log_with_request_id("警告: document_ids为空", level=logging.WARNING, request_id=request_id)
                    
                    if dataset_ids and len(dataset_ids) > 0 and document_ids and len(document_ids) > 0:
                        doc_content = get_document_chunks(
                            api_key_rag=self.api_key_rag,
                            base_url="http://localhost:8080",
                            dataset_id=dataset_ids[0],
                            document_id=document_ids[0],
                            keywords=None,
                            chunk_id=None,
                            offset=0,
                            limit=1024
                        )
                        
                        # 打印响应内容
                        print("\n获取到的响应:")
                        # print(f"Response: {doc_content}")
                        
                        # 直接获取content
                        if doc_content and doc_content.get('code') == 0:
                            content = doc_content['data']['content']
                            print(f"\n成功获取文档内容，长度: {count_text_length(content)}")
                        else:
                            print("\n未找到有效内容")
                            print(f"Response: {doc_content}")
                    else:
                        print("无法获取文档内容: dataset_ids或document_ids为空")
                        log_with_request_id("无法获取文档内容: dataset_ids或document_ids为空", level=logging.WARNING, request_id=request_id)
                        
                except Exception as e:
                    print(f"\n获取文档内容时出错: {str(e)}")
                    log_with_request_id(f"获取文档内容时出错: {str(e)}", level=logging.ERROR, request_id=request_id)
                    content = ""
                    
            else:
                print("使用相关性检索策略...")
                print(f"\n开始获取文档内容:")
                print(f"API Key: {self.api_key}")
                print(f"Dataset ID: {dataset_ids}")
                print(f"Document ID: {document_ids}")
                log_with_request_id("使用相关性检索策略...", request_id=request_id)
                
                content, references = retrieve_and_combine_content(
                    api_key_rag=self.api_key_rag,   
                    question=question,
                    dataset_ids=dataset_ids,
                    document_ids=document_ids,
                    **kwargs
                )
                
                # 检查content是否为空
                if not content:
                    print("警告: 检索内容为空")
                    log_with_request_id("警告: 检索内容为空", level=logging.WARNING, request_id=request_id)
                    content = ""
                else:
                    print(f"检索到的视频字幕内容字数为:{count_text_length(content)}，内容为:{content[:100]}")
                    log_with_request_id(f"检索到的视频字幕内容字数为:{count_text_length(content)}，内容为:{content[:100]}", request_id=request_id)
                
                # 如果folder不为空，获取并合并PDF内容
                if self.folder:
                    logging.warning(f"开始获取PDF内容，folder: {self.folder}")
                    print(f"开始获取PDF内容，folder: {self.folder}")
                    content_pdf = get_vidpdf_content(self.folder, question, self.api_key)
                    if content_pdf:
                        content = f"{content}\n\n{content_pdf}"
                        log_with_request_id(f"成功合并PDF内容，合并后内容长度: {count_text_length(content)}", request_id=request_id)
                        print(f"成功合并PDF内容，合并后内容长度: {count_text_length(content)}")
                    else:
                        logging.warning("PDF内容获取失败或为空")
                        print("PDF内容获取失败或为空")

            if not content or content.strip() == "":
                logging.warning("抱歉，无法找到相关内容。请检查文档ID是否正确，或尝试其他问题。")
                return "抱歉，我无法找到相关内容。请检查文档ID是否正确，或尝试其他问题。"

            try:
                # 确保content是UTF-8编码，使用更安全的方法
                if isinstance(content, bytes):
                    content = content.decode('utf-8', errors='replace')
                elif isinstance(content, str):
                    # 如果已经是字符串，确保它是有效的UTF-8
                    content = content.encode('utf-8', errors='replace').decode('utf-8', errors='replace')


                # 删除无意义的符号，如 *、#号
                content = re.sub(r'[*#]', '', content)  # 删除所有 *、#号
                content = re.sub(r'\s+', ' ', content).strip()  # 删除多余的空格
                # 判断内容语言并截取
                if all('\u4e00' <= char <= '\u9fff' for char in content):  # 检查是否全为中文字符
                    content_compact = content[:50000]  # 截取前20000个汉字
                else:
                    content_words = content.split()  # 按空格分割为单词
                    content_compact = ' '.join(content_words[:50000])  # 截取前20000个单词




                context = f"""
                                基于以下参考内容回答问题。如果内容中没有相关信息，请明确说明。

                                在回答中：
                                1. 使用 $...$ 来表示行内数学公式
                                2. 使用 $$...$$ 来表示块级数学公式
                                3. 使用 Markdown 语法来格式化文本
                                4. 代码块请使用 ```语言名 来标注
                                5. 不要在回答中包含tokens消耗信息，这将由系统自动添加

                                参考内容:
                                {content_compact}

                                历史对话:
                                {history if history else '无历史对话'}

                                问题: {question}

                                请提供准确、相关的回答，并确保数学公式和代码格式正确:
                                """
                
                print(f"联合检索内容长度: {count_text_length(context)}")
                print(f"联合检索内容: {context[:300]}")
                # 4. 使用LLM生成回答
                answer, totaltokens = web_agent._generate_summary(context) 
                
                # 清理answer中可能包含的tokens消耗信息
                answer = clean_tokens_information(answer)
                
                log_with_request_id(f"最后联合检索结果: {answer[:800]}", request_id=request_id)
                # 添加当前消耗的tokens信息，但确保它不会被包含在历史对话中
                answer_with_tokens = f"{answer}\n\n本次消耗tokens:{totaltokens}"
                log_with_request_id(f"最后联合检索结果带消耗tokens: {answer_with_tokens}", request_id=request_id)          
                return answer_with_tokens
              


            except UnicodeError as e:
                print(f"编码处理错误: {str(e)}")
                # 如果编码处理失败，尝试更激进的清理
                cleaned_content = ''.join(char for char in content_compact if ord(char) < 128)
                context = f"""
                                基于以下参考内容回答问题。如果内容中没有相关信息，请明确说明。

                                在回答中：
                                1. 使用 $...$ 或 \\(...\\) 来表示行内数学公式
                                2. 使用 $$...$$  或 \\[...\\] 来表示块级数学公式
                                3. 使用 Markdown 语法来格式化文本
                                4. 代码块请使用 ```语言名 来标注

                                参考内容:
                                {cleaned_content}

                                历史对话:
                                {history if history else '无历史对话'}

                                问题: {question}

                                请提供准确、相关的回答，并确保数学公式和代码格式正确:
                                """
  
                answer, totaltokens = web_agent._generate_summary(context)  
                answer += f"\n\n 本次消耗tokens:{totaltokens}"         
                return answer
                
        except Exception as e:
            print(f"生成回答时出错: {e}")
            error_message = str(e)
            
            # 对常见错误类型进行分类，提供更具体的错误信息
            if 'dataset_ids' in error_message or 'document_ids' in error_message:
                return f"抱歉，处理您的问题时出现错误: 资源初始化中，请稍后再试 (错误代码: INIT_001)"
            elif 'Invalid argument' in error_message or 'Errno 22' in error_message:
                return f"抱歉，处理您的问题时出现错误: 系统连接暂时不可用，请稍后再试 (错误代码: CONN_001)"
            elif '无法找到相关内容' in error_message or '无法获取文档内容' in error_message:
                return f"抱歉，处理您的问题时出现错误: 无法找到相关内容，请稍后再试 (错误代码: CONTENT_001)"
            else:
                # 通用错误信息
                return f"抱歉，处理您的问题时出现错误: {error_message} (错误代码: GEN_001)"


    #在SmartChatbot类的get_graph方法中修改，添加图谱优化步骤
    def get_graph(self, 
                dataset_ids: Optional[List[str]] = None,
                document_ids: Optional[List[str]] = None,
                **kwargs) -> str:
        try:
            print("使用全文检索策略...")
            content = ""                
            # 确保是列表格式
            if isinstance(dataset_ids, str):
                dataset_ids = [dataset_ids]
            if isinstance(document_ids, str):
                document_ids = [document_ids]          
            try:
                # 调试信息
                print(f"\n开始获取文档内容:")
                print(f"API Key: {self.api_key}")
                print(f"Dataset ID: {dataset_ids[0]}")
                print(f"Document ID: {document_ids[0]}")   
                log_with_request_id(f"开始获取文档内容:", request_id=request_id)
                log_with_request_id(f"API Key: {self.api_key}", request_id=request_id)
                log_with_request_id(f"Dataset ID: {dataset_ids[0]}", request_id=request_id)
                log_with_request_id(f"Document ID: {document_ids[0]}", request_id=request_id)
                # 获取文档内容
                doc_content = get_document_chunks(
                    api_key_rag=self.api_key_rag,
                    base_url="http://localhost:8080",
                    dataset_id=dataset_ids[0],
                    document_id=document_ids[0],
                    keywords=None,
                    chunk_id=None,
                    offset=0,
                    limit=1024
                )         
                # 直接获取content
                if doc_content and doc_content.get('code') == 0:
                    content = doc_content['data']['content']
                    print(f"\n成功获取文档内容，长度: {count_text_length(content)}")
                else:
                    print("\n未找到有效内容")
                    print(f"Response: {doc_content}")                  
            except Exception as e:
                print(f"\n获取文档内容时出错: {str(e)}")
                content = None
            if not content:
                logging.warning("抱歉，无法多文档检索，我无法找到相关内容。请检查文档ID是否正确，或尝试其他问题。getanswer1026")
                return "抱歉，我无法找到相关内容。请检查文档ID是否正确，或尝试其他问题。"

            try:
                # 确保content是UTF-8编码，使用更安全的方法
                if isinstance(content, bytes):
                    content = content.decode('utf-8', errors='replace')
                elif isinstance(content, str):
                    # 如果已经是字符串，确保它是有效的UTF-8
                    content = content.encode('utf-8', errors='replace').decode('utf-8', errors='replace')
                
                # 删除无意义的符号，如 *、#号
                content = re.sub(r'[*#]', '', content)  # 删除所有 *、#号
                content = re.sub(r'\s+', ' ', content).strip()  # 删除多余的空格
                # 判断内容语言并截取
                if all('\u4e00' <= char <= '\u9fff' for char in content):  # 检查是否全为中文字符
                    content_compact = content[:20000]  # 截取前20000个汉字
                else:
                    content_words = content.split()  # 按空格分割为单词
                    content_compact = ' '.join(content_words[:20000])  # 截取前20000个单词
                
                # 首先尝试中文提示词
                context_zh = f"""
                # 知识图谱生成任务

                ## 指令
                分析提供的内容，生成一个表示关键物理概念、实体及其关系的知识图谱。关系描述必须精确、具体，不要使用省略号或模糊表达。

                ## 待分析内容
                主要内容:
                {content_compact}

                ## 输出格式要求
                1. 生成的知识图谱必须严格遵循以下格式（特别注意双花括号）:
                实体1,实体2,{{关系}}
                实体3,实体4,{{关系}}
                实体5,实体1,{{关系}}

                2. 格式规则:
                - 每行必须包含三个元素，用逗号分隔
                - 关系必须用双花括号{{}}括起来
                - 逗号前后不要有空格
                - 实体和关系不要用引号括起来


                3. 实体和关系指南:
                - 实体应该是表示关键物理概念、物理术语、物理理论、物理学家或物理现象的具体名词
                - 关系必须是精确完整的描述，不要使用"..."或模糊表达
                - 关系应该清晰表达两个物理概念之间的确切联系，如"定义了"、"是组成部分"、"导致"等
                - 保持实体简洁（1-5个词）
                - 确保关系在"源实体→关系→目标实体"的阅读方式下有明确的物理学逻辑意义
                - 在整个图谱中使用一致的物理学术语

                4. 生成20个以上有意义的物理概念连接，以创建一个全面的物理知识图谱

                ## 示例（严格按照此格式，注意关系描述的精确性）:
                电力,电流,{{产生}}
                电阻,欧姆定律,{{是核心概念}}
                电压,电场,{{通过电势差形成}}
                量子力学,波函数,{{使用波函数描述粒子状态}}
                光子,光,{{是基本组成单位}}
                欧姆定律,电流,{{规定电流与电压成正比}}
                电磁感应,法拉第定律,{{由法拉第发现并描述}}

                ## 错误示例（不要这样做）:
                铜导线线圈,总电阻,{{是...的一部分}} ❌ (使用了省略号)
                欧姆定律,感应电动势,{{等于...乘以电阻}} ❌ (关系不明确)
                磁场,电动势,{{决定...的方向}} ❌ (使用了省略号)


                ## 正确示例（这样做）:
                铜导线线圈,总电阻,{{构成主要组成部分}} ✓
                欧姆定律,电流电阻关系,{{定义电压等于电流乘以电阻}} ✓
                磁场,电动势方向,{{决定感应电流的方向}} ✓
                质量,能量,{{满足E=mc^2关系}} ✓
                电子,原子,{{是原子的基本组成部分}} ✓

                ## 重要提示:
                - 再次检查所有关系是否都用双花括号{{}}括起来
                - 验证每行是否都遵循确切格式: 实体1,实体2,{{关系}}
                - 确保关系描述是精确完整的，不使用省略号或模糊表达
                - 不要包含任何解释性文本、标题或额外格式
                - 专注于物理学概念、定律、现象和理论之间的关系
                - 避免使用任何Unicode特殊字符，使用ASCII字符表示数学符号

                现在，基于内容生成物理知识图谱:
                """
                
                log_with_request_id(f"联合检索内容长度: {count_text_length(context_zh)}", request_id=request_id)
                # 使用LLM生成知识图谱
                WebAgentClass = duckduckgosearch.Web_Agent
                web_agent = WebAgentClass(api_key=self.api_key,api_key_type=self.api_key_type)
                answer, totaltokens = web_agent._generate_summary_graph(context_zh)                
                # 提取有效的知识图谱行
                valid_lines = extract_valid_graph_lines(answer)
                
                # 如果有效行数小于5，尝试英文提示词
                if len(valid_lines) < 5:
                    print(f"中文提示词生成的有效行数不足5行（当前{len(valid_lines)}行），尝试英文提示词")
                    log_with_request_id(f"中文提示词生成的有效行数不足5行（当前{len(valid_lines)}行），尝试英文提示词", request_id=request_id)
                    
                    # 英文提示词
                    context_en = f"""
                    # Knowledge Graph Generation Task for Physics

                    ## Instructions
                    Analyze the provided content and generate a knowledge graph that represents the key physics concepts, entities, and their relationships. Ensure that relationship descriptions are precise, specific, and complete - avoid using ellipses or vague expressions.

                    ## Content to Analyze
                    Primary Content:
                    {content_compact}

                    ## Output Format Requirements
                    1. Generate a knowledge graph following this EXACT format (pay special attention to the double curly braces):
                    entity1,entity2,{{relationship}}
                    entity3,entity4,{{relationship}}
                    entity5,entity1,{{relationship}}

                    2. Format rules:
                    - Each line must contain exactly three elements separated by commas
                    - The relationship MUST be enclosed in double curly braces {{}}
                    - No spaces before or after commas
                    - No quotation marks around entities or relationships
                    - Use "^" for powers (e.g., E=mc^2), "*" for multiplication, "/" for division

                    3. Guidelines for entities and relationships:
                    - Entities should be specific nouns representing key physics concepts, physics terms, physics theories, physicists, or physical phenomena
                    - Relationships MUST be precise and complete descriptions - do NOT use "..." or vague expressions
                    - Relationships should clearly express the exact connection between physics entities, such as "defines", "is a component of", "causes"
                    - Keep entities concise (1-5 words)
                    - Ensure relationships make logical sense in physics when read as "source → relationship → target"
                    - Use consistent physics terminology throughout the graph

                    4. Generate exactly above 20 meaningful connections to create a comprehensive physics knowledge graph

                    ## Examples (FOLLOW THIS FORMAT EXACTLY, note the precision of relationships):
                    Electricity,Current,{{generates movement of electrons}}
                    Resistance,Ohm's Law,{{is a fundamental concept in}}
                    Voltage,Electric Field,{{creates through potential difference}}
                    Quantum Mechanics,Wave Function,{{uses to describe particle states}}
                    Photon,Light,{{serves as the basic unit of}}
                    Ohm's Law,Current,{{states is proportional to voltage}}
                    Electromagnetic Induction,Faraday's Law,{{was discovered and described by}}

                    ## INCORRECT EXAMPLES (DO NOT DO THIS):
                    Copper Wire Coil,Total Resistance,{{is...part of}} ❌ (uses ellipsis)
                    Ohm's Law,Induced EMF,{{equals...times resistance}} ❌ (relationship unclear)
                    Magnetic Field,Electromotive Force,{{determines...direction}} ❌ (uses ellipsis)


                    ## CORRECT EXAMPLES (DO THIS):
                    Copper Wire Coil,Total Resistance,{{constitutes a major component of}} ✓
                    Ohm's Law,Current-Resistance Relationship,{{defines voltage equals current times resistance}} ✓
                    Magnetic Field,Electromotive Force Direction,{{determines the direction of induced current}} ✓
                    Mass,Energy,{{follows the equation E=mc^2}} ✓
                    Electron,Atom,{{is a fundamental component of}} ✓

                    ## IMPORTANT:
                    - Re-check that ALL relationships are enclosed in double curly braces {{}}
                    - Verify that each line follows the exact format: entity1,entity2,{{relationship}}
                    - Ensure relationship descriptions are precise and complete, without ellipses or vague expressions
                    - Do not include any explanatory text, headers, or additional formatting
                    - Focus on physics concepts, laws, phenomena, and theories and their relationships
                    - Avoid using any special Unicode characters, use ASCII characters for mathematical symbols

                    Now, generate the physics knowledge graph based on the content:
                    """

                    answer, totaltokens = web_agent._generate_summary_r1(context_en)
                    valid_lines = extract_valid_graph_lines(answer)
                    
                    # 如果英文提示词后仍不足5行
                    if len(valid_lines) < 5:
                        print(f"英文提示词生成的有效行数仍不足5行（当前{len(valid_lines)}行）")
                        log_with_request_id(f"英文提示词生成的有效行数仍不足5行（当前{len(valid_lines)}行）", request_id=request_id)
                
                # 最终结果
                result = "\n".join(valid_lines)
                log_with_request_id(f"生成知识图谱，有效行数:{len(valid_lines)}，消耗tokens:{totaltokens}", request_id=request_id)
                
                # 优化知识图谱
                if len(valid_lines) >= 5:  # 只有当有足够的行数时才进行优化
                    optimized_result = optimize_knowledge_graph(result, self.api_key, self.api_key_type, request_id)
                    return optimized_result 
                elif len(valid_lines) > 0:
                    return result
                else:
                    return None

            except UnicodeError as e:
                print(f"编码处理错误: {str(e)}")
                # 如果编码处理失败，尝试更激进的清理
                cleaned_content = ''.join(char for char in content_compact if ord(char) < 128)
                context = f"""
                # Knowledge Graph Generation Task for Physics

                ## Instructions
                Analyze the provided content and generate a knowledge graph that represents the key physics concepts, entities, and their relationships.

                ## Content to Analyze
                Primary Content:
                {cleaned_content}

                ## Output Format Requirements
                1. Generate a knowledge graph following this EXACT format (pay special attention to the double curly braces):
                entity1,entity2,{{relationship}}
                entity3,entity4,{{relationship}}
                entity5,entity1,{{relationship}}

                2. Format rules:
                - Each line must contain exactly three elements separated by commas
                - The relationship MUST be enclosed in double curly braces {{}}
                - No spaces before or after commas
                - No quotation marks around entities or relationships

                3. Guidelines for entities and relationships:
                - Entities should be specific nouns representing key physics concepts, physics terms, physics theories, physicists, or physical phenomena
                - Relationships should be precise verbs or phrases (e.g., "defines", "is a type of", "contributes to")
                - Keep entities concise (1-5 words)
                - Ensure relationships make logical sense in physics when read as "source → relationship → target"
                - Use consistent physics terminology throughout the graph

                4. Generate exactly 20 meaningful connections to create a comprehensive physics knowledge graph

                ## Examples (FOLLOW THIS FORMAT EXACTLY):
                Electricity,Current,{{is the flow of}}
                Resistance,Ohm's Law,{{is described by}}
                Voltage,Electric Field,{{creates}}
                Quantum Mechanics,Wave Function,{{is described by}}
                Photon,Light,{{is a particle of}}

                ## IMPORTANT:
                - Double-check that ALL relationships are enclosed in double curly braces {{}}
                - Verify that each line follows the exact format: entity1,entity2,{{relationship}}
                - Do not include any explanatory text, headers, or additional formatting
                - Focus on physics concepts, laws, phenomena, and theories and their relationships

                Now, generate the physics knowledge graph based on the content:
                """

    
                answer, totaltokens = web_agent._generate_summary_r1(context)
                valid_lines = extract_valid_graph_lines(answer)
                result = "\n".join(valid_lines)
                log_with_request_id(f"生成知识图谱，有效行数:{len(valid_lines)}，消耗tokens:{totaltokens}", request_id=request_id)
                
                # 优化知识图谱
                if len(valid_lines) >= 5:  # 只有当有足够的行数时才进行优化
                    optimized_result = optimize_knowledge_graph(result, self.api_key, self.api_key_type, request_id)
                    return optimized_result
                elif len(valid_lines) > 0:  
                    return result
                else:
                    return None
            
        except Exception as e:
            print(f"生成知识图谱时出错: {e}")
            return f"抱歉，处理您的问题时出现错误: {str(e)}"




def optimize_knowledge_graph(graph_text: str, api_key: str, api_key_type: str, request_id: str = None) -> str:
    """
    优化知识图谱：合并相似实体，增强实体间关系
    
    Args:
        graph_text (str): 原始知识图谱文本
        api_key_deepseek (str): DeepSeek API密钥
        request_id (str, optional): 请求ID，用于日志记录
        
    Returns:
        str: 优化后的知识图谱文本
    """
    try:
        # 如果图谱为空或行数太少，直接返回原图谱
        valid_lines = extract_valid_graph_lines(graph_text)
        if len(valid_lines) < 3:
            log_with_request_id("知识图谱行数太少，不进行优化", request_id=request_id)
            return graph_text
            
        # 提取所有实体
        entities = set()
        for line in valid_lines:
            parts = line.split(',', 2)
            if len(parts) == 3:
                entities.add(parts[0].strip())
                entities.add(parts[1].strip())
                
        optimization_prompt = f"""
            我需要使用中文优化以下物理知识图谱，使其更加准确、连贯和完整。

            当前物理知识图谱:
            {valid_lines}

            请执行以下优化任务:
            1. 合并表示相同物理概念的实体，使用最准确、最规范的物理学术语
            2. 考虑所有物理实体两两之间是否存在有意义的物理关系，如果存在，则添加关系
            3. 优化关系描述，使其更符合物理学的精确表述
            4. 保持物理知识图谱的科学准确性、连贯性和一致性
            5. 避免创建重复或冗余的关系，特别是避免在两个物理概念之间创建双向关系

            实体列表（供参考）:
            {', '.join(entities)}

            请注意:
            - 如果物理概念A和物理概念B之间有关系，只需要一行表示它们之间的关系
            - 不要同时包含"实体A,实体B,{{关系1}}"和"实体B,实体A,{{关系2}}"这样的双向关系
            - 选择最能准确描述两个物理概念之间科学关系的一种方向
            - 确保所有关系描述都符合物理学的科学准确性
            - 保持原有的有效物理关系，除非需要合并或优化
            - 使用Unicode字符表示希腊字母（如σ, ε, μ, π等）和数学符号（如±, ×, ÷, ∞等）
            - 使用上标（如²,³）和下标（如₀,₁,₂）表示数学公式
            - 例如：表面电荷密度与电场强度的关系可以表示为:表面电荷密度,电场强度,满足E=σ/ε₀

            请输出优化后的物理知识图谱，每行使用以下格式:
            实体1,实体2,{{关系描述}}
            """      
        log_with_request_id(f"开始优化知识图谱，原图谱有{len(valid_lines)}行", request_id=request_id)
        
        # 使用LLM优化知识图谱
        WebAgentClass = duckduckgosearch.Web_Agent
        web_agent = WebAgentClass(api_key=api_key, api_key_type=api_key_type)
        
        try:
            # 确保优化提示词是UTF-8安全的
            safe_prompt = optimization_prompt.encode('utf-8', errors='replace').decode('utf-8')
            
            # 使用处理过的提示词调用API

            optimized_graph, tokens = web_agent._generate_summary_r11(safe_prompt)
            
            # 确保返回的结果是UTF-8编码
            if isinstance(optimized_graph, str):
                optimized_graph = optimized_graph.encode('utf-8', errors='replace').decode('utf-8')
            elif isinstance(optimized_graph, bytes):
                optimized_graph = optimized_graph.decode('utf-8', errors='replace')
            
            log_with_request_id(f"优化后的知识图谱tokens: {tokens}", request_id=request_id)
            
            # 使用安全的日志记录方式
            print(f"优化后的知识图谱已生成")
            # 避免记录可能包含问题字符的完整图谱
            log_with_request_id(f"优化后的知识图谱: 容长度 {len(optimized_graph)}", request_id=request_id)
        except Exception as api_error:
            # 记录API调用错误
            error_msg = str(api_error).encode('utf-8', errors='replace').decode('utf-8')
            log_with_request_id(f"API调用错误: {error_msg}", level=logging.ERROR, request_id=request_id)
            # 出错时返回原图谱
            return graph_text
            
        # 清理模型返回的输出，移除可能的额外字符
        cleaned_output = clean_model_output(optimized_graph)
        log_with_request_id(f"清理后的输出: {cleaned_output[:200]}...", request_id=request_id)       
        # 提取有效的知识图谱行
        optimized_lines = extract_valid_graph_lines(cleaned_output)
        log_with_request_id(f"提取后的知识图谱行数: {len(optimized_lines)}", request_id=request_id)
        log_with_request_id(f"知识图谱优化完成，优化后有{len(optimized_lines)}行，消耗tokens:{tokens}", request_id=request_id)
        
        # 如果优化后的行数为0，尝试使用更宽松的方式提取
        if len(optimized_lines) == 0:
            log_with_request_id("标准提取方式未找到有效行，尝试使用宽松提取方式", request_id=request_id)
            optimized_lines = extract_valid_graph_lines_lenient(cleaned_output)
            log_with_request_id(f"宽松提取后有{len(optimized_lines)}行", request_id=request_id)
            
        # 如果优化后的行数明显减少，可能是优化出了问题，返回原图谱
        if len(optimized_lines) < len(valid_lines) * 0.6 and len(optimized_lines) <20:
            log_with_request_id(f"优化后行数明显减少（从{len(valid_lines)}减少到{len(optimized_lines)}），返回原图谱", request_id=request_id)
            return graph_text
            
        # 清理可能的重复关系
        optimized_graph = clean_graph_relations("\n".join(optimized_lines))
        log_with_request_id(f"清理后的知识图谱: {optimized_graph[:200]}...", request_id=request_id)
        return optimized_graph
    except Exception as e:
        # 确保错误消息是UTF-8编码的
        error_msg = str(e).encode('utf-8', errors='replace').decode('utf-8')
        log_with_request_id(f"优化知识图谱时出错: {error_msg}", level=logging.ERROR, request_id=request_id)
        print(f"优化知识图谱时出错: {e}")
        # 出错时返回原图谱
        return graph_text



# def optimize_knowledge_graph_json(graph_text: str, api_key_deepseek: str, request_id: str = None) -> str:
#     """
#     使用JSON格式优化知识图谱：合并相似实体，增强实体间关系，并确保输出的科学准确性
    
#     Args:
#         graph_text (str): 原始知识图谱文本
#         api_key_deepseek (str): DeepSeek API密钥
#         request_id (str, optional): 请求ID，用于日志记录
        
#     Returns:
#         str: 优化后的知识图谱文本，每行格式为"实体1,实体2,{关系描述}"
#     """
#     try:
#         # 验证输入参数
#         if not graph_text or not api_key_deepseek:
#             log_with_request_id("输入参数无效", request_id=request_id)
#             return graph_text

#         # 提取有效的图谱行
#         valid_lines = extract_valid_graph_lines(graph_text)
#         if len(valid_lines) < 3:
#             log_with_request_id("知识图谱行数太少，不进行优化", request_id=request_id)
#             return graph_text

#         # 提取并清理所有实体
#         entities = set()
#         for line in valid_lines:
#             parts = line.split(',', 2)
#             if len(parts) == 3:
#                 entity1 = parts[0].strip()
#                 entity2 = parts[1].strip()
#                 relation = parts[2].strip()
#                 if entity1 and entity2:
#                     entities.add(entity1)
#                     entities.add(entity2)
#         # 构建优化提示
#         optimization_prompt = f"""
#         请优化以下物理知识图谱，使其更加准确、连贯和完整。严格按照JSON格式输出。

#         当前物理知识图谱:
#         {valid_lines}

#         优化要求:
#         1. 合并表示相同物理概念的实体，使用最准确、最规范的中文物理学术语
#         2. 考虑所有物理实体两两之间是否存在有意义的物理关系，如果存在，则添加关系
#         3. 优化关系描述，使其更符合物理学的精确表述
#         4. 保持物理知识图谱的科学准确性、连贯性和一致性
#         5. 避免创建重复或冗余的关系，特别是避免在两个物理概念之间创建双向关系
#         6. 使用标准的物理量符号和单位

#         实体列表:
#         {', '.join(sorted(entities))}

#         请注意:
#         - 如果物理概念A和物理概念B之间有关系，只需要一行表示它们之间的关系
#         - 不要同时包含"实体A,实体B,关系1和"实体B,实体A,关系2"这样的双向关系
#         - 选择最能准确描述两个物理概念之间科学关系的一种方向
#         - 确保所有关系描述都符合物理学的科学准确性
#         - 使用Unicode字符表示希腊字母（如σ, ε, μ, π等）和数学符号（如±, ×, ÷, ∞等）
#         - 使用上标（如²,³）和下标（如₀,₁,₂）表示数学公式
#         - 例如：表面电荷密度与电场强度的关系可以表示为:表面电荷密度,电场强度,满足E=σ/ε₀
#           请输出优化后的json格式的物理知识图谱:
#         """
#         log_with_request_id(f"优化知识图谱提示词: {optimization_prompt}", request_id=request_id)
#         # 调用API优化知识图谱
#         try:
#             optimized_json, tokens = generate_summary_r11(api_key_deepseek, optimization_prompt)
#             log_with_request_id(f"API调用成功，消耗tokens: {tokens}", request_id=request_id)
#             log_with_request_id(f"优化后的知识图谱: {optimized_json}...", request_id=request_id)

#             # 解析和验证JSON响应
#             if isinstance(optimized_json, str):
#                 optimized_data = json.loads(optimized_json)
#             else:
#                 optimized_data = optimized_json

#             # 验证JSON结构
#             if not isinstance(optimized_data, dict) or "knowledge_graph" not in optimized_data:
#                 raise ValueError("优化结果格式不正确")

#             # 验证和清理关系
#             optimized_lines = []
#             for item in optimized_data["knowledge_graph"]:
#                 if not all(k in item for k in ["entity1", "entity2", "relation"]):
#                     continue
                    
#                 entity1 = item["entity1"].strip()
#                 entity2 = item["entity2"].strip()
#                 relation = item["relation"].strip()
                
#                 # 跳过空实体或关系
#                 if not entity1 or not entity2 or not relation:
#                     continue                 
#                 optimized_lines.append(f"{entity1},{entity2},{relation}")
           
#             # 将列表转换为字符串，然后再进行处理
#             optimized_text = "\n".join(optimized_lines)
#             optimized_lines = extract_valid_graph_lines(optimized_text)
            
#             # 清理和格式化最终结果
#             optimized_graph = clean_graph_relations("\n".join(optimized_lines))            
#             # 验证优化结果
#             if len(optimized_lines) < len(valid_lines) * 0.5 and len(optimized_lines) < 20:
#                 log_with_request_id(
#                     f"优化后行数明显减少（从{len(valid_lines)}减少到{len(optimized_lines)}），返回原图谱",
#                     request_id=request_id
#                 )
#                 return graph_text

                       
#             log_with_request_id(
#                 f"知识图谱优化完成，原始行数:{len(valid_lines)}，优化后行数:{len(optimized_lines)}，tokens:{tokens}",
#                 request_id=request_id
#             )
#             return optimized_graph      

#         except json.JSONDecodeError as json_error:
#             log_with_request_id(f"JSON解析错误: {str(json_error)}", level=logging.ERROR, request_id=request_id)
#             return graph_text
#         except Exception as api_error:
#             log_with_request_id(f"API调用错误: {str(api_error)}", level=logging.ERROR, request_id=request_id)
#             return graph_text

#     except Exception as e:
#         error_msg = str(e).encode('utf-8', errors='replace').decode('utf-8')
#         log_with_request_id(f"优化知识图谱时出错: {error_msg}", level=logging.ERROR, request_id=request_id)
#         return graph_text

















def clean_model_output(output: str) -> str:
    """
    清理模型输出，移除可能的额外字符
    
    Args:
        output (str): 模型原始输出
        
    Returns:
        str: 清理后的输出
    """
    # 检查是否是元组形式的字符串 ('内容', 数字)
    tuple_match = re.match(r"^\s*\(\s*['\"](.+)['\"],\s*\d+\s*\)\s*$", output, re.DOTALL)
    if tuple_match:
        # 提取元组中的内容部分
        content = tuple_match.group(1)
        # 替换可能的转义字符
        content = content.replace('\\n', '\n')
        return content
    
    # 如果不是元组形式，尝试其他清理
    # 移除可能的引号和括号
    cleaned = re.sub(r'^[\s\'"(]*|[\s\'")]*$', '', output)
    # 替换可能的转义字符
    cleaned = cleaned.replace('\\n', '\n')
    return cleaned

def extract_valid_graph_lines(text: str) -> list:
    """
    从文本中提取有效的知识图谱行，更灵活地处理各种格式
    
    Args:
        text (str): 包含知识图谱的文本
        
    Returns:
        list: 有效的知识图谱行列表
    """
    valid_lines = []
    
    # 按行分割
    lines = text.strip().split('\n')
    
    for line in lines:
        line = line.strip()
        if not line:
            continue
        
        # 记录原始行，用于调试
        original_line = line
        
        # 移除可能的行号前缀
        line = re.sub(r'^\d+\.\s*', '', line)
        
        # 尝试多种格式匹配
        
        # 格式1: 实体1,实体2,{{关系}}
        match1 = re.match(r'^([^,]+),([^,]+),\{\{([^{}]+)\}\}$', line)
        
        # 格式2: 实体1,实体2,{关系}
        match2 = re.match(r'^([^,]+),([^,]+),\{([^{}]+)\}$', line)
        
        # 格式3: 实体1,实体2,关系 (没有大括号)
        match3 = re.match(r'^([^,]+),([^,]+),(.+)$', line)
        
        # 选择匹配到的格式
        match = match1 or match2 or match3
        
        if match:
            entity1 = match.group(1).strip()
            entity2 = match.group(2).strip()
            relation = match.group(3).strip()
            
            # 检查实体和关系是否为空
            if not entity1 or not entity2 or not relation:
                print(f"跳过无效行 (空实体或关系): {original_line}")
                continue
                
            # 检查关系是否包含未闭合的大括号
            if relation.count('{') != relation.count('}'):
                print(f"跳过无效行 (未闭合大括号): {original_line}")
                continue
                
            # 标准化输出格式 - 使用单大括号
            valid_lines.append(f"{entity1},{entity2},{{{relation}}}")
        else:
            # 尝试更宽松的匹配: 查找两个逗号
            parts = line.split(',', 2)
            if len(parts) == 3:
                entity1 = parts[0].strip()
                entity2 = parts[1].strip()
                relation = parts[2].strip()
                
                # 从关系中提取实际内容，移除大括号
                relation = re.sub(r'^\s*\{\{|\}\}\s*$', '', relation)
                relation = re.sub(r'^\s*\{|\}\s*$', '', relation)
                
                # 检查实体和关系是否为空
                if not entity1 or not entity2 or not relation:
                    print(f"跳过无效行 (空实体或关系): {original_line}")
                    continue
                
                # 标准化输出格式 - 使用单大括号
                valid_lines.append(f"{entity1},{entity2},{{{relation}}}")
            else:
                print(f"无法解析行: {original_line}")
    
    return valid_lines

def extract_valid_graph_lines_lenient(text: str) -> list:
    """
    使用更宽松的方式从文本中提取有效的知识图谱行
    
    Args:
        text (str): 包含知识图谱的文本
        
    Returns:
        list: 有效的知识图谱行列表
    """
    valid_lines = []
    
    # 按行分割
    lines = text.strip().split('\n')
    
    for line in lines:
        line = line.strip()
        if not line:
            continue
        
        # 首先尝试找到最后一个逗号位置
        last_comma_index = line.rfind(',')
        if last_comma_index == -1:
            continue
            
        # 分割实体和关系
        entities_part = line[:last_comma_index].strip()
        relation_part = line[last_comma_index+1:].strip()
        
        # 再找实体部分的最后一个逗号
        entity_comma_index = entities_part.rfind(',')
        if entity_comma_index == -1:
            continue
            
        entity1 = entities_part[:entity_comma_index].strip()
        entity2 = entities_part[entity_comma_index+1:].strip()
        
        # 处理关系部分
        # 移除关系中的大括号，然后重新添加
        relation = relation_part.strip('{}')
        
        # 标准化输出
        valid_lines.append(f"{entity1},{entity2},{{{relation}}}")
    
    return valid_lines

# 添加清理图谱关系的函数
def clean_graph_relations(graph_text):
    """
    清理知识图谱中的重复关系，处理以下情况：
    1. 当两个实体之间存在双向关系时，只保留关系描述更详细的那一行
    2. 当两行具有完全相同的实体和实体顺序时，只保留关系描述更详细的那一行
    
    Args:
        graph_text (str): 原始知识图谱文本
        
    Returns:
        str: 清理后的知识图谱文本
    """
    if not graph_text or not graph_text.strip():
        return graph_text
        
    # 解析图谱行
    lines = [line.strip() for line in graph_text.split('\n') if line.strip()]
    relation_map = {}  # 用于存储实体对之间的关系（不考虑顺序）
    exact_relation_map = {}  # 用于存储完全相同实体顺序的关系
    
    # 第一遍：收集所有关系
    for line in lines:
        parts = line.split(',', 2)
        if len(parts) != 3:
            continue
            
        source = parts[0].strip()
        target = parts[1].strip()
        relation = parts[2].strip()
        
        # 处理情况1：实体对相同但顺序可能不同
        entity_pair = tuple(sorted([source, target]))
        if entity_pair not in relation_map:
            relation_map[entity_pair] = []
        
        relation_map[entity_pair].append({
            'source': source,
            'target': target,
            'relation': relation,
            'original_line': line,
            'relation_length': len(relation)
        })
        
        # 处理情况2：实体和实体顺序完全相同
        exact_pair = (source, target)
        if exact_pair not in exact_relation_map:
            exact_relation_map[exact_pair] = []
            
        exact_relation_map[exact_pair].append({
            'relation': relation,
            'original_line': line,
            'relation_length': len(relation)
        })
    
    # 第二遍：对于每对实体，处理完全相同顺序的情况
    # 这会更新relation_map中的条目，移除重复的关系
    for exact_pair, relations in exact_relation_map.items():
        if len(relations) > 1:
            # 有多个完全相同顺序的关系，按关系长度排序，只保留最长的
            relations.sort(key=lambda x: x['relation_length'], reverse=True)
            best_relation = relations[0]
            
            # 更新对应的entity_pair中的关系列表
            entity_pair = tuple(sorted(exact_pair))
            relation_map[entity_pair] = [rel for rel in relation_map[entity_pair] 
                                        if rel['source'] != exact_pair[0] or 
                                           rel['target'] != exact_pair[1] or 
                                           rel['relation'] == best_relation['relation']]
            
            # 确保最佳关系在列表中
            if not any(rel['original_line'] == best_relation['original_line'] for rel in relation_map[entity_pair]):
                relation_map[entity_pair].append({
                    'source': exact_pair[0],
                    'target': exact_pair[1],
                    'relation': best_relation['relation'],
                    'original_line': best_relation['original_line'],
                    'relation_length': best_relation['relation_length']
                })
    
    # 第三遍：对于每对实体，只保留关系描述最详细的那一行
    cleaned_lines = []
    for entity_pair, relations in relation_map.items():
        if len(relations) == 1:
            # 只有一个关系，直接保留
            cleaned_lines.append(relations[0]['original_line'])
        else:
            # 有多个关系，按关系长度排序，保留最长的
            relations.sort(key=lambda x: x['relation_length'], reverse=True)
            cleaned_lines.append(relations[0]['original_line'])
    
    return '\n'.join(cleaned_lines)










def get_matching_ids(folder, base_path):
    """
    获取匹配的PDF文档ID和数据集ID
    
    Args:
        folder (str): 文件夹名称
        base_path (str): 基础路径
        
    Returns:
        str: 数据集ID
    """
    viddata_kb = ''
    
    # 读取dataset_info.json获取数据集ID
    dataset_info_path = os.path.join(base_path, 'dataset_info.json')
    if os.path.exists(dataset_info_path):
        try:
            with open(dataset_info_path, 'r', encoding='utf-8') as f:
                dataset_info = json.load(f)
                if folder in dataset_info:
                    # 直接返回ID字符串，而不是列表
                    viddata_kb = dataset_info[folder]['id']
        except Exception as e:
            print(f"Error reading dataset_info.json: {str(e)}")
            
    return viddata_kb





def get_content_summary(api_key_rag, message, dataset_ids, document_ids=None):
    """
    获取内容总结
    
    Args:
        api_key (str): API密钥
        message (str): 用户消息
        dataset_ids (str|list): 数据集ID或ID列表
        document_ids (str|list): 文档ID或ID列表
        **kwargs: 额外的参数，如 similarity_threshold 和 top_k
        
    Returns:
        str: 检索并组合的内容
    """
    try:
        if not all([api_key_rag, message]):
            print("Missing required parameters api_key or message")
            return ""
            
        if not dataset_ids and not document_ids:
            print(f"Empty dataset_ids or document_ids: dataset_ids={dataset_ids}, document_ids={document_ids}")
            return ""


        print(f"Retrieving content with dataset_ids={dataset_ids}, document_ids={document_ids},api_key_rag={api_key_rag}")
        content, references = retrieve_and_combine_content_video(
            api_key_rag=api_key_rag,
            question=message,
            dataset_ids=dataset_ids,
            document_ids=document_ids,
        )

       # 删除无意义的符号，如 *、#号
        content = re.sub(r'[*#]', '', content)  # 删除所有 *、#号
        content = re.sub(r'\s+', ' ', content).strip()  # 删除多余的空格

        log_with_request_id(f"检索到的视频pdf原始内容字数为:{count_text_length(content)}，内容为:{content[:100]}", request_id='request_id')
        # 初始化 current_count
        current_count = count_text_length(content)
        
        # 如果中英文内容超过15000个字符/单词，需要截取
        if current_count > 8000:
            current_count = 0
            result_chars = []           
            for char in content:
                # 所有字符都保留并计数
                if '\u4e00' <= char <= '\u9fff':  # 中文字符
                    current_count += 1
                elif char.isspace():  # 空格
                    # 检查是否是英文单词的分隔符
                    if result_chars and result_chars[-1].isascii():
                        current_count += 1
                else:  # 所有其他字符（包括英文、特殊字符等）
                    if not result_chars or not result_chars[-1].isascii():
                        current_count += 1
                        
                if current_count > 8000:
                    break
                    
                result_chars.append(char)
                
            content = ''.join(result_chars)




        return content or ""
        
    except Exception as e:
        print(f"Error getting content summary: {str(e)}")
        return ""



def retrieve_and_combine_content_video(api_key_rag, question, dataset_ids=None, document_ids=None, vector_similarity_weight=0.4, similarity_threshold=0.36, top_k=1024):
    """
    从RAGFlow检索内容并组合结果
    
    Args:
        api_key (str): RAGFlow API密钥
        question (str): 用户问题
        dataset_ids (list[str]): 数据集ID列表
        document_ids (list[str]): 文档ID列表
        vector_similarity_weight (float): 向量相似度权重，默认0.4
        similarity_threshold (float): 相似度阈值，默认0.36
        top_k (int): 返回的最大结果数，默认1024
        
    Returns:
        str: 检索到的组合内容，如果出错则返回空字符串
    """
    try:
        # 检查参数
        if not api_key_rag:
            print("错误: api_key_rag 为空")
            return ""
            
        if not question:
            print("错误: question 为空")
            return ""
            
        if not dataset_ids and not document_ids:
            print("警告: dataset_ids 和 document_ids 都为空")
            return ""
        
        # 准备请求数据
        data = {
            "question": question,
            "similarity_threshold": similarity_threshold,
            "top_k": top_k,
            "vector_similarity_weight": vector_similarity_weight
        }
        
        # 添加dataset_ids如果提供的话
        if dataset_ids:
            data["dataset_ids"] = dataset_ids if isinstance(dataset_ids, list) else [dataset_ids]
            
        # 添加document_ids如果提供的话
        if document_ids:
            data["document_ids"] = document_ids if isinstance(document_ids, list) else [document_ids]
            
        # 发送POST请求到RAGFlow API
        headers = {
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {api_key_rag}'
        }
        
        try:
            response = requests.post(
                'http://localhost:8080/api/v1/retrieval',
                headers=headers,
                json=data,
                timeout=30  # 添加超时
            )
            
            # 检查响应状态
            response.raise_for_status()
        except requests.exceptions.RequestException as e:
            print(f"请求错误: {str(e)}")
            return ""
            
        try:
            result = response.json()
        except ValueError as e:
            print(f"JSON解析错误: {str(e)}")
            return ""
        
        if result.get('code') != 0:
            print(f"RAGFlow API error: {result.get('message')}")
            return ""
            
        # 从响应中提取chunks并组合内容
        data = result.get('data', {})
        if not data:
            print("响应中没有data字段")
            return ""
            
        chunks = data.get('chunks', [])
        if not chunks:
            print("没有找到匹配的内容块")
            return ""
            
        combined_content = []
        
        for chunk in chunks:
            content = chunk.get('content', '')
            if content:
                # 确保内容是UTF-8编码，使用更安全的方法
                try:
                    if isinstance(content, bytes):
                        content = content.decode('utf-8', errors='replace')
                    elif isinstance(content, str):
                        content = content.encode('utf-8', errors='replace').decode('utf-8', errors='replace')
                    combined_content.append(content)
                except Exception as e:
                    print(f"Error processing content encoding: {e}")
                    # 如果编码处理失败，跳过这个chunk
                    continue
                
        return "\n".join(combined_content)
        
    except Exception as e:
        print(f"检索内容时出错: {str(e)}")
        return ""





# 在handle_video_chat中使用这些函数
def get_vidpdf_content(folder, message, api_key_rag):
    """
    获取组合的内容总结
    
    Args:
        folder (str): 文件夹名称
        message (str): 用户消息
        api_key (str): API密钥
        
    Returns:
        str: 组合后的内容总结
    """
    main_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    COURSE_VIDEO_BASE = os.path.join(main_dir, 'coursevideo')
    try:
        # 获取匹配的ID
        viddata_kb = get_matching_ids(folder, COURSE_VIDEO_BASE)
        
        print(f"viddata_kb: {viddata_kb}")
        # 获取PDF内容总结
        pdf_content = get_content_summary(
            api_key_rag=api_key_rag,
            message=message,
            dataset_ids=viddata_kb,
        )
        print(f"pdf_content: {pdf_content[:300]}")

        return pdf_content
        
    except Exception as e:
        print(f"Error in get_vidpdf_content: {str(e)}")
        return ""








# 使用示例
if __name__ == "__main__":
    api_key_rag = 'ragflow-********************************'  # 替换为你的API密钥
    # dataset_ids=["********************************"]
    # document_ids=["07929a42ffa711efa016ee3d992e99ff"]
    # api_key_deepseek = "sk-izwfqiufrwdqfkvqzklcyaashpmraqaaosuchkjnettqpabb" 
    api_key_deepseek = "***********************************"  

    # 测试get_vidpdf_content
    # folder = "en-MIT Physics II Electricity and Magnetism"
    # message = "什么是偶极子(dipoles)"
    # content = get_vidpdf_content(folder, message, api_key_rag)
    # print(f"content: {content[:300]}")

   ############################################################### # 


    # result = retrieve_chunks(
    #     api_key=api_key_deepseek,
    #     question="机器视觉的大作业选题有哪些",
    #     dataset_ids=["5252db8a8d5311efab1e0242ac120005"]
    # )
    
    # 使用文档ID和高级选项
    try:
        result = retrieve_chunks(
            api_key_rag=api_key_rag,
            question="Ampere's Law",
            dataset_ids=["9d63c1da053411f0b8918608606d5123"],
            document_ids=["1c8b3a96058b11f0ac69ea72cc6ea170","dbb41196053f11f0b8c78608606d5123","0a60303e053f11f08dc48608606d5123"]
        )
        
        if result:
            print(f"result: {result}")
            if not result.get('chunks'):
                print("Response successful but no chunks found")
            else:
                print(f"\nRetrieved {len(result['chunks'])} chunks:")
                for chunk in result['chunks']:
                    print(f"\nDocument: {chunk.get('document_keyword', 'Unknown')}")
                    print(f"Content: {chunk.get('content', 'No content')}")
                    print(f"Similarity: {chunk.get('similarity', 'N/A')}")
                    if 'highlight' in chunk:
                        print(f"Highlighted: {chunk['highlight']}")
                
                if 'doc_aggs' in result:
                    print("\nDocument statistics:")
                    for doc in result['doc_aggs']:
                        print(f"{doc.get('doc_name', 'Unknown')}: {doc.get('count', 0)} chunks")
        else:
            print("No result returned from retrieve_chunks")
            
    except Exception as e:
        print(f"Error in main program: {str(e)}")


    # combined_content, references = retrieve_and_combine_content(
    #         api_key_rag=api_key_rag,
    #         question="Ampere's Law",
    #         dataset_ids=["9d63c1da053411f0b8918608606d5123"],
    #         document_ids=["1c8b3a96058b11f0ac69ea72cc6ea170"]
    #     )
    # print(f"combined_content_num: {count_text_length(combined_content)}")
    # print(f"combined_content: {combined_content}")
    # print(f"references: {references}")



    
    # chatbot = SmartChatbot( 
    #     api_key=api_key,
    #     api_key_deepseek=api_key_deepseek,
    #     folder="en-MIT Physics II Electricity and Magnetism"    
    # )
    # graphresult = chatbot.get_graph(
    #     dataset_ids=["********************************"],
    #     document_ids=['986888ccf07711efbc9a0242ac120006'],
    # )
    # print(graphresult)
    
    # optimization_prompt="""                
    #             闪电,雷暴,{每天发生约400000次}
    #             闪电,电压,{云与地球之间可达30亿伏特}
    #             雷暴,闪电,{每秒发生约100次}
    #             雷云,正电荷,{顶部带正电荷}
    #             雷云,负电荷,{底部带负电荷}
    #             电压,电场强度,{通过电势差形成}
    #             电场强度,电击穿,{每米约300万伏特时发生}
    #             电击穿,火花,{导致离子中和并发光}
    #             火花,光,{由离子中和产生}
    #             电荷,导体表面,{分布在固体导体表面}
    #             导体球,电荷量,{与半径成正比}
    #             导体球,表面电荷密度,{与半径成反比}
    #             高斯面,电场强度,{等于表面电荷密度除以真空介电常数}
    #             电场强度,放电,{首先发生在最高电场强度处}
    #             电击穿,空气,{导致空气放电}
    #             电子,电场,{在电场中加速}
    #             离子,中性状态,{变为中性时发光}
    #             电子伏特,能量,{等于1.6×10^-19焦耳}
    #             电势差,电子,{使电子移动}
    #             电场强度,火花,{在尖点处产生}  """









    # base_url = "http://localhost:8080"
    # dataset_id='5252db8a8d5311efab1e0242ac120005'
    # document_id='7901beb88e2511efb6050242ac120006'
    # api_key ='ragflow-********************************'  # 替换为你的API密钥
    # # content = get_combined_content("en-MIT Physics II Electricity and Magnetism", "9-8.02x - Lect 9 - Electric Currents, Resistivity, Conductivity, Ohm's Law.json", "欧姆定律定义", "84cdeb5cf0a611ef8c950242ac120006", "********************************", api_key)
    # # print(content[:300])
    # content_video = retrieve_and_combine_content_video(api_key, "什么是偶极子(dipoles)", dataset_ids="ef7d11f0f14e11efb0f70242ac120002")
    # print(len(content_video))
    # print(content_video[:300])

    


    # summary = get_summary_from_dataset(
    #     api_key=api_key,
    #     api_key_deepseek=api_key_deepseek,
    #     question="就二维材料如光纤，波导等的集成，以及外场诱导下的二维材料的非线性性质的内容写一篇综述",
    #     dataset_ids=["********************************"],  # 确保是一维数组
    #     document_ids=['35f5c480eaea11ef97680242ac120006', '1d3bab96eae811ef94110242ac120006', '04855f0eeae611ef98710242ac120006', 'ebd2d5a6eae311efb2070242ac120006', 'd3171a1aeae111efa0610242ac120006', '7144fa8cead711ef8b6e0242ac120006'],  # 确保是一维数组
    #     similarity_threshold=0.3,  # 可选参数
    #     top_k=50                   # 可选参数
    # )   
    # if summary:
    #     print("\nGenerated Summary:")
    #     print(summary)


    # try:
    #     result = get_document_chunks(
    #         api_key=api_key,
    #         base_url="http://localhost:8080",
    #         dataset_id="5252db8a8d5311efab1e0242ac120005",
    #         document_id="bfdcf1699f7211ef8f290242ac120006"
    #     )
        
    #     if result['code'] == 0:
    #         print(f"\n成功获取到 {result['data']['total']} 个chunks")
    #         print("\n组合后的内容:")
    #         print("="*80)
    #         print(result['data']['content'])
    #         print("="*80)
    #     else:
    #         print(f"错误: {result.get('message', '未知错误')}")
            
    # except Exception as e:
    #     print(f"错误: {str(e)}")
        # 测试对话
        # 示例问题
    # dataset_id="********************************"
    # document_id="986888ccf07711efbc9a0242ac120006"        
    
    # chatbot = SmartChatbot(
    # api_key="ragflow-********************************",
    # api_key_deepseek="sk-izwfqiufrwdqfkvqzklcyaashpmraqaaosuchkjnettqpabb",
    # folder="en-MIT Physics II Electricity and Magnetism"    
    #     )    
    # # 首先测试直接获取文档内容
    # question = "guss's law 是什么"
    # answer = chatbot.get_answer(question, dataset_ids=dataset_id, document_ids=document_id)
    # print(answer)
    # 然后测试chatbot
    # print("\n测试chatbot:")
    # chatbot = SmartChatbot(
    #     api_key=api_key,
    #     api_key_deepseek=api_key_deepseek
    # )   
    # questions = [
    #     "就二维材料如光纤，波导等的集成，以及外场诱导下的二维材料的非线性性质的内容写一篇综述"
    # ]
    # history = []
    # for question in questions:
    #     print(f"\n问题: {question}")
    #     answer = chatbot.get_answer(
    #         question=question,
    #         history=history,
    #         dataset_ids=["********************************"],  # 确保是一维数组
    #         document_ids=['35f5c480eaea11ef97680242ac120006', '1d3bab96eae811ef94110242ac120006']   # 确保是一维数组
    #     )
    #     print(f"回答: {answer}")
        
    #     history.append({
    #         "question": question,
    #         "answer": answer
    #     })

