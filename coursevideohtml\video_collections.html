<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>视频课程合集 - AI助教平台</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px 30px;
            margin-bottom: 40px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(102,126,234,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
        }

        .header-content {
            position: relative;
            z-index: 2;
        }

        .header h1 {
            font-size: 2.8rem;
            color: #667eea;
            margin-bottom: 15px;
            font-weight: 700;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 20px;
        }

        .header .subtitle {
            color: #666;
            font-size: 1.2rem;
            font-weight: 400;
            margin-bottom: 20px;
        }

        .stats-bar {
            display: flex;
            justify-content: center;
            gap: 30px;
            margin-top: 20px;
        }

        .stat-item {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #667eea;
            font-weight: 600;
        }

        .collections-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
            gap: 30px;
            padding: 0;
        }

        .collection-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            cursor: pointer;
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
        }

        .collection-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: 1;
        }

        .collection-card:hover::before {
            opacity: 1;
        }

        .collection-card:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        .thumbnail-container {
            position: relative;
            width: 100%;
            padding-top: 56.25%; /* 16:9 宽高比 */
            overflow: hidden;
            border-radius: 15px 15px 0 0;
        }

        .thumbnail {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: all 0.4s ease;
            z-index: 2;
        }

        .collection-card:hover .thumbnail {
            transform: scale(1.08);
            filter: brightness(1.1);
        }

        .play-overlay {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.7);
            border-radius: 50%;
            width: 60px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            opacity: 0;
            transition: all 0.3s ease;
            z-index: 3;
        }

        .collection-card:hover .play-overlay {
            opacity: 1;
            transform: translate(-50%, -50%) scale(1.1);
        }

        .collection-info {
            padding: 25px;
            position: relative;
            z-index: 2;
        }

        .collection-name {
            margin: 0 0 10px 0;
            font-size: 1.3rem;
            font-weight: 600;
            color: #333;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            line-clamp: 2;
            -webkit-box-orient: vertical;
            line-height: 1.4;
        }

        .collection-meta {
            display: flex;
            align-items: center;
            gap: 15px;
            color: #666;
            font-size: 0.9rem;
            margin-top: 10px;
        }

        .meta-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .collection-badge {
            position: absolute;
            top: 15px;
            right: 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
            z-index: 4;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .collection-badge.badge-empty {
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
            opacity: 0.8;
        }

        .collection-card.empty-collection {
            opacity: 0.7;
        }

        .collection-card.empty-collection:hover {
            opacity: 0.9;
            transform: translateY(-5px) scale(1.01);
        }

        .loading {
            grid-column: 1 / -1;
            text-align: center;
            padding: 60px 20px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .loading-text {
            font-size: 1.2rem;
            color: #667eea;
            font-weight: 600;
        }

        .error {
            grid-column: 1 / -1;
            text-align: center;
            padding: 40px 30px;
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
            color: white;
            border-radius: 20px;
            box-shadow: 0 8px 25px rgba(255, 107, 107, 0.3);
            margin: 20px 0;
        }

        .error-icon {
            font-size: 3rem;
            margin-bottom: 15px;
            opacity: 0.8;
        }

        .error-title {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .error-message {
            font-size: 1rem;
            opacity: 0.9;
        }

        .empty-state {
            grid-column: 1 / -1;
            text-align: center;
            padding: 80px 30px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .empty-icon {
            font-size: 4rem;
            color: #ccc;
            margin-bottom: 20px;
        }

        .empty-title {
            font-size: 1.5rem;
            color: #666;
            margin-bottom: 10px;
            font-weight: 600;
        }

        .empty-message {
            color: #999;
            font-size: 1rem;
        }

        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }

            .header {
                padding: 30px 20px;
                margin-bottom: 30px;
            }

            .header h1 {
                font-size: 2.2rem;
                flex-direction: column;
                gap: 10px;
            }

            .stats-bar {
                flex-direction: column;
                gap: 15px;
                align-items: center;
            }

            .collections-grid {
                grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
                gap: 20px;
            }

            .collection-info {
                padding: 20px;
            }

            .collection-name {
                font-size: 1.1rem;
            }
        }

        @media (max-width: 480px) {
            .collections-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面头部 -->
        <div class="header">
            <div class="header-content">
                <h1>
                    <i class="fas fa-play-circle"></i>
                    视频课程合集
                </h1>
                <p class="subtitle">探索丰富的学习资源，开启知识之旅</p>
                <div class="stats-bar">
                    <div class="stat-item">
                        <i class="fas fa-video"></i>
                        <span id="total-collections">加载中...</span>
                    </div>
                    <div class="stat-item">
                        <i class="fas fa-clock"></i>
                        <span>随时随地学习</span>
                    </div>
                    <div class="stat-item">
                        <i class="fas fa-graduation-cap"></i>
                        <span>专业课程内容</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 视频合集网格 -->
        <div id="collections" class="collections-grid">
            <div class="loading">
                <div class="loading-spinner"></div>
                <div class="loading-text">正在加载课程合集...</div>
            </div>
        </div>
    </div>

    <script>
        // 获取 URL 参数
        const urlParams = new URLSearchParams(window.location.search);
        const user = urlParams.get('user');
        const key = urlParams.get('key');
        const token = urlParams.get('token');

        // 显示通知消息
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `notification notification-${type}`;

            const icon = type === 'success' ? 'fas fa-check-circle' :
                        type === 'error' ? 'fas fa-exclamation-circle' :
                        'fas fa-info-circle';

            notification.innerHTML = `
                <i class="${icon}"></i>
                <span>${message}</span>
                <button class="notification-close" onclick="this.parentElement.remove()">
                    <i class="fas fa-times"></i>
                </button>
            `;

            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'success' ? 'linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%)' :
                           type === 'error' ? 'linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%)' :
                           'linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%)'};
                color: ${type === 'success' ? '#155724' : type === 'error' ? '#721c24' : '#0c5460'};
                padding: 15px 20px;
                border-radius: 10px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                z-index: 10000;
                display: flex;
                align-items: center;
                gap: 10px;
                min-width: 300px;
                animation: slideIn 0.3s ease-out;
            `;

            document.body.appendChild(notification);

            setTimeout(() => {
                if (notification.parentElement) {
                    notification.style.animation = 'slideIn 0.3s ease-out reverse';
                    setTimeout(() => notification.remove(), 300);
                }
            }, 4000);
        }

        // 格式化显示名称
        function formatDisplayName(name) {
            if (name.includes('-')) {
                return name.split('-')[1].trim();
            }
            return name;
        }

        // 格式化视频数量显示
        function formatVideoCount(count) {
            if (count === 0) {
                return '暂无视频';
            } else if (count === 1) {
                return '1 个视频';
            } else {
                return `${count} 个视频`;
            }
        }

        // 获取合集类型标签
        function getCollectionType(name, videoCount) {
            if (videoCount === 0) {
                return '空合集';
            } else if (videoCount >= 50) {
                return '大型课程';
            } else if (videoCount >= 20) {
                return '完整课程';
            } else if (videoCount >= 10) {
                return '系列课程';
            } else {
                return '精选课程';
            }
        }

        // 创建合集卡片
        function createCollectionCard(collection, index) {
            const card = document.createElement('div');
            card.className = 'collection-card';

            // 添加加载动画延迟
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';

            // 使用后端返回的显示名称或格式化名称
            const displayName = collection.display_name || formatDisplayName(collection.name);
            const videoCount = collection.video_count || 0;
            const collectionType = getCollectionType(collection.name, videoCount);

            // 为空合集添加特殊样式
            if (videoCount === 0) {
                card.classList.add('empty-collection');
            }

            card.innerHTML = `
                <div class="thumbnail-container">
                    <img class="thumbnail" src="${collection.thumbnail}" alt="${collection.name}"
                         onerror="this.src='/static/default-thumbnail.jpg'">
                    <div class="play-overlay">
                        <i class="fas fa-play"></i>
                    </div>
                    <div class="collection-badge ${videoCount === 0 ? 'badge-empty' : ''}">
                        <i class="fas fa-video"></i>
                        ${videoCount}
                    </div>
                </div>
                <div class="collection-info">
                    <h3 class="collection-name">${displayName}</h3>
                    <div class="collection-meta">
                        <div class="meta-item">
                            <i class="fas fa-play-circle"></i>
                            <span>${formatVideoCount(videoCount)}</span>
                        </div>
                        <div class="meta-item">
                            <i class="fas fa-tag"></i>
                            <span>${collectionType}</span>
                        </div>
                    </div>
                </div>
            `;

            // 点击事件
            card.onclick = () => {
                // 检查是否为空合集
                if (videoCount === 0) {
                    showNotification(`"${displayName}" 合集暂无视频内容`, 'info');
                    return;
                }

                // 添加点击动画
                card.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    const url = `/api/videos_page?folder=${encodeURIComponent(collection.name)}&user=${encodeURIComponent(user)}&key=${encodeURIComponent(key)}&token=${encodeURIComponent(token)}`;
                    window.location.href = url;
                }, 150);
            };

            // 添加加载动画
            setTimeout(() => {
                card.style.transition = 'all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275)';
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, index * 100);

            return card;
        }

        async function loadCollections() {
            try {
                const response = await fetch('/api/video_collections');
                const data = await response.json();

                if (data.success) {
                    const collectionsContainer = document.getElementById('collections');
                    collectionsContainer.innerHTML = '';

                    if (data.collections.length === 0) {
                        collectionsContainer.innerHTML = `
                            <div class="empty-state">
                                <div class="empty-icon">
                                    <i class="fas fa-video-slash"></i>
                                </div>
                                <div class="empty-title">暂无课程合集</div>
                                <div class="empty-message">请稍后再试或联系管理员</div>
                            </div>
                        `;
                        return;
                    }

                    // 更新统计信息
                    const totalCollections = data.total_collections || data.collections.length;
                    const totalVideos = data.total_videos || data.collections.reduce((sum, c) => sum + (c.video_count || 0), 0);

                    document.getElementById('total-collections').textContent = `${totalCollections} 个合集`;

                    // 如果有总视频数，也可以显示
                    if (totalVideos > 0) {
                        // 可以选择更新页面上的其他统计信息
                        console.log(`Total videos across all collections: ${totalVideos}`);
                    }

                    // 创建合集卡片
                    data.collections.forEach((collection, index) => {
                        const card = createCollectionCard(collection, index);
                        collectionsContainer.appendChild(card);
                    });

                    // 显示成功消息
                    setTimeout(() => {
                        const videoText = totalVideos > 0 ? `，共 ${totalVideos} 个视频` : '';
                        showNotification(`成功加载 ${totalCollections} 个课程合集${videoText}`, 'success');
                    }, 1000);

                } else {
                    throw new Error(data.error || '加载失败');
                }
            } catch (error) {
                console.error('Error:', error);
                document.getElementById('collections').innerHTML = `
                    <div class="error">
                        <div class="error-icon">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="error-title">加载失败</div>
                        <div class="error-message">${error.message}</div>
                    </div>
                `;
                showNotification('加载课程合集失败，请刷新页面重试', 'error');
            }
        }

        // 页面加载完成后加载合集
        document.addEventListener('DOMContentLoaded', function() {
            loadCollections();

            // 添加CSS动画样式
            const style = document.createElement('style');
            style.textContent = `
                @keyframes slideIn {
                    from {
                        transform: translateX(100%);
                        opacity: 0;
                    }
                    to {
                        transform: translateX(0);
                        opacity: 1;
                    }
                }
                .notification-close {
                    background: none;
                    border: none;
                    color: inherit;
                    cursor: pointer;
                    padding: 0;
                    margin-left: auto;
                    opacity: 0.7;
                    transition: opacity 0.2s;
                }
                .notification-close:hover {
                    opacity: 1;
                }
            `;
            document.head.appendChild(style);
        });
    </script>
</body>
</html>