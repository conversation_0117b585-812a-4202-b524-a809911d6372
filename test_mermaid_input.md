# Mermaid 测试用例

## 测试1：包含中文冒号的序列图

```mermaid
sequenceDiagram
    participant 光源
    participant 准直镜
    participant 测试区
    participant 聚焦镜
    participant 相机
    光源->>准直镜: 平行光生成
    准直镜->>测试区：入射 
    测试区->>聚焦镜：：折射偏移 
    聚焦镜->>相机：：形成密度梯度图像 
```

## 测试2：包含多种中文符号的流程图

```mermaid
graph TD
    A["开始"] --> B{"判断条件？"}
    B -->|是| C["执行操作１"]
    B -->|否| D["执行操作２"]
    C --> E["结束。"]
    D --> E
```

## 测试3：包含全角符号的类图

```mermaid
classDiagram
    class 用户｛
        ＋姓名： String
        ＋年龄： Integer
        ＋获取信息（）： String
    ｝
    class 管理员｛
        ＋权限： String
        ＋管理用户（）： Boolean
    ｝
    用户 <|-- 管理员
```

这些测试用例包含了各种中文符号，应该能够验证我们的符号转换功能是否正常工作。
