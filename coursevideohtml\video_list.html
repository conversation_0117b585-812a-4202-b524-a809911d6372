<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title id="pageTitle">视频课程 - AI助教平台</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(102,126,234,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
        }

        .header-content {
            position: relative;
            z-index: 2;
        }

        .header h1 {
            font-size: 2.5rem;
            color: #667eea;
            margin-bottom: 15px;
            font-weight: 700;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
        }

        .header .subtitle {
            color: #666;
            font-size: 1.1rem;
            font-weight: 400;
            margin-bottom: 20px;
        }

        .stats-bar {
            display: flex;
            justify-content: center;
            gap: 30px;
            margin-top: 20px;
            flex-wrap: wrap;
        }

        .stat-item {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #667eea;
            font-weight: 600;
            padding: 8px 16px;
            background: rgba(102, 126, 234, 0.1);
            border-radius: 20px;
        }

        .video-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
            gap: 25px;
            padding: 0;
        }

        .video-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            text-decoration: none;
            color: inherit;
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
        }

        .video-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: 1;
        }

        .video-card:hover::before {
            opacity: 1;
        }

        .video-card:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }
        .video-thumbnail {
            width: 100%;
            height: 200px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
            border-radius: 15px 15px 0 0;
        }

        .video-thumbnail img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: all 0.4s ease;
            z-index: 2;
        }

        .video-card:hover .video-thumbnail img {
            transform: scale(1.08);
            filter: brightness(1.1);
        }

        .video-thumbnail .play-icon {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 60px;
            height: 60px;
            background: rgba(0, 0, 0, 0.8);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: all 0.3s ease;
            z-index: 3;
            backdrop-filter: blur(5px);
        }

        .video-card:hover .play-icon {
            opacity: 1;
            transform: translate(-50%, -50%) scale(1.1);
        }

        .play-icon svg {
            margin-left: 3px; /* 调整播放图标位置 */
        }

        .video-duration {
            position: absolute;
            bottom: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: 600;
            z-index: 4;
        }

        .video-quality-badge {
            position: absolute;
            top: 10px;
            left: 10px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.7rem;
            font-weight: 600;
            z-index: 4;
        }

        .video-info {
            padding: 25px;
            position: relative;
            z-index: 2;
        }

        .video-title {
            margin: 0 0 12px 0;
            font-size: 1.2rem;
            font-weight: 600;
            color: #333;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            line-clamp: 2;
            -webkit-box-orient: vertical;
            line-height: 1.4;
        }

        .video-meta {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-top: 15px;
            flex-wrap: wrap;
            gap: 10px;
        }

        .video-size {
            display: flex;
            align-items: center;
            gap: 5px;
            color: #666;
            font-size: 0.9rem;
            font-weight: 500;
        }

        .size-warning {
            display: flex;
            align-items: center;
            gap: 5px;
            color: #ff9800;
            font-size: 0.8rem;
            font-weight: 600;
            background: rgba(255, 152, 0, 0.1);
            padding: 4px 8px;
            border-radius: 12px;
            margin-top: 8px;
        }

        .video-actions {
            display: flex;
            gap: 8px;
            margin-top: 15px;
        }

        .action-btn {
            flex: 1;
            padding: 8px 12px;
            border: none;
            border-radius: 8px;
            font-size: 0.8rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 5px;
        }

        .btn-play {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-play:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }

        .btn-info {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            color: #666;
            border: 1px solid #dee2e6;
        }

        .btn-info:hover {
            background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
            color: #495057;
        }
        .loading-placeholder {
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg,
                rgba(102, 126, 234, 0.1) 25%,
                rgba(118, 75, 162, 0.2) 50%,
                rgba(102, 126, 234, 0.1) 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
            border-radius: 15px 15px 0 0;
        }

        @keyframes loading {
            0% {
                background-position: 200% 0;
            }
            100% {
                background-position: -200% 0;
            }
        }

        .loading-state {
            grid-column: 1 / -1;
            text-align: center;
            padding: 60px 20px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .loading-text {
            font-size: 1.2rem;
            color: #667eea;
            font-weight: 600;
        }

        .error-state {
            grid-column: 1 / -1;
            text-align: center;
            padding: 40px 30px;
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
            color: white;
            border-radius: 20px;
            box-shadow: 0 8px 25px rgba(255, 107, 107, 0.3);
            margin: 20px 0;
        }

        .error-icon {
            font-size: 3rem;
            margin-bottom: 15px;
            opacity: 0.8;
        }

        .error-title {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .error-message {
            font-size: 1rem;
            opacity: 0.9;
        }

        /* 知识图谱按钮样式 */
        .knowledge-graph-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .knowledge-graph-btn {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border: none;
            border-radius: 12px;
            padding: 15px 30px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
            display: inline-flex;
            align-items: center;
            gap: 10px;
        }

        .knowledge-graph-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(40, 167, 69, 0.4);
        }

        .knowledge-graph-btn:active {
            transform: translateY(0);
        }

        .knowledge-graph-description {
            color: #666;
            font-size: 0.9rem;
            margin-top: 10px;
            line-height: 1.5;
        }
        
        /* 模态窗口样式 */
        .modal {
            display: none;
            position: fixed;
            z-index: 2000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            overflow: auto;
            background-color: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(5px);
            animation: fadeIn 0.3s ease-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        .modal-content {
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(10px);
            margin: 3vh auto;
            padding: 0;
            border-radius: 20px;
            width: 85%;
            max-width: 1300px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            position: relative;
            height: 85vh;
            border: 1px solid rgba(255, 255, 255, 0.2);
            overflow: hidden;
            animation: slideUp 0.3s ease-out;
        }

        @keyframes slideUp {
            from {
                transform: translateY(50px);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px 30px;
            border-radius: 20px 20px 0 0;
        }

        .modal-header h2 {
            margin: 0;
            font-size: 1.5rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .modal-controls {
            display: flex;
            gap: 12px;
            align-items: center;
        }

        .modal-btn {
            padding: 8px 16px;
            border: none;
            border-radius: 8px;
            font-size: 0.9rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .close {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: none;
            border-radius: 8px;
            width: 36px;
            height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .close:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.1);
        }

        .modal-body {
            height: calc(85vh - 80px);
            overflow: hidden;
            padding: 0;
        }

        #knowledgeGraphContainer {
            width: 100%;
            height: 100%;
        }

        /* 控制按钮样式 */
        #maximizeGraphBtn {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
        }

        #maximizeGraphBtn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
        }

        #downloadGraphBtn {
            background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
            color: white;
        }

        #downloadGraphBtn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
        }

        /* 最大化状态样式 */
        .modal-maximized .modal-content {
            width: 98%;
            max-width: 98%;
            height: 98vh;
            margin: 1vh auto;
            border-radius: 10px;
        }

        .modal-maximized .modal-body {
            height: calc(98vh - 80px);
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }

            .header {
                padding: 30px 20px;
                margin-bottom: 20px;
            }

            .header h1 {
                font-size: 2rem;
                flex-direction: column;
                gap: 10px;
            }

            .stats-bar {
                flex-direction: column;
                gap: 10px;
                align-items: center;
            }

            .video-grid {
                grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
                gap: 20px;
            }

            .video-info {
                padding: 20px;
            }

            .video-title {
                font-size: 1.1rem;
            }

            .modal-content {
                width: 95%;
                height: 90vh;
                margin: 5vh auto;
            }

            .modal-header {
                padding: 15px 20px;
            }

            .modal-header h2 {
                font-size: 1.2rem;
            }

            .modal-controls {
                gap: 8px;
            }

            .modal-btn {
                padding: 6px 12px;
                font-size: 0.8rem;
            }
        }

        @media (max-width: 480px) {
            .video-grid {
                grid-template-columns: 1fr;
            }

            .video-meta {
                flex-direction: column;
                align-items: flex-start;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面头部 -->
        <div class="header">
            <div class="header-content">
                <h1 id="courseTitle">
                    <i class="fas fa-play-circle"></i>
                    视频课程
                </h1>
                <p class="subtitle" id="courseSubtitle">高质量的学习视频内容</p>
                <div class="stats-bar">
                    <div class="stat-item">
                        <i class="fas fa-video"></i>
                        <span id="total-videos">加载中...</span>
                    </div>
                    <div class="stat-item">
                        <i class="fas fa-clock"></i>
                        <span>随时随地学习</span>
                    </div>
                    <div class="stat-item">
                        <i class="fas fa-hd-video"></i>
                        <span>高清画质</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 知识图谱功能区 -->
        <div class="knowledge-graph-section">
            <button id="showKnowledgeGraphBtn" class="knowledge-graph-btn">
                <i class="fas fa-project-diagram"></i>
                AI 生成课程知识图谱
            </button>
            <div class="knowledge-graph-description">
                利用人工智能技术分析课程内容，生成直观的知识关系图谱
            </div>
        </div>

        <!-- 视频网格 -->
        <div id="videoGrid" class="video-grid">
            <div class="loading-state">
                <div class="loading-spinner"></div>
                <div class="loading-text">正在加载视频列表...</div>
            </div>
        </div>
    </div>

    <!-- 知识图谱模态窗口 -->
    <div id="knowledgeGraphModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>
                    <i class="fas fa-project-diagram"></i>
                    课程知识图谱
                </h2>
                <div class="modal-controls">
                    <button id="maximizeGraphBtn" class="modal-btn">
                        <i class="fas fa-expand"></i>
                        最大化
                    </button>
                    <button id="downloadGraphBtn" class="modal-btn">
                        <i class="fas fa-download"></i>
                        下载图片
                    </button>
                    <button class="close">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
            <div class="modal-body">
                <div id="knowledgeGraphContainer"></div>
            </div>
        </div>
    </div>
    
    <!-- 添加 ECharts 库 -->
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    
    <script>
        // 获取 URL 参数
        const urlParams = new URLSearchParams(window.location.search);
        const currentFolder = urlParams.get('folder');
        const user = urlParams.get('user');
        const key = urlParams.get('key');
        const token = urlParams.get('token');
        
        // 知识图谱相关变量
        let graphChart = null;
        let isMaximized = false;
        let if_vip_user = false;

        // 全局变量，用于存储节点位置
        var nodePositions = {};

        // 显示通知消息
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `notification notification-${type}`;

            const icon = type === 'success' ? 'fas fa-check-circle' :
                        type === 'error' ? 'fas fa-exclamation-circle' :
                        'fas fa-info-circle';

            notification.innerHTML = `
                <i class="${icon}"></i>
                <span>${message}</span>
                <button class="notification-close" onclick="this.parentElement.remove()">
                    <i class="fas fa-times"></i>
                </button>
            `;

            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'success' ? 'linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%)' :
                           type === 'error' ? 'linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%)' :
                           'linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%)'};
                color: ${type === 'success' ? '#155724' : type === 'error' ? '#721c24' : '#0c5460'};
                padding: 15px 20px;
                border-radius: 10px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                z-index: 10000;
                display: flex;
                align-items: center;
                gap: 10px;
                min-width: 300px;
                animation: slideIn 0.3s ease-out;
            `;

            document.body.appendChild(notification);

            setTimeout(() => {
                if (notification.parentElement) {
                    notification.style.animation = 'slideIn 0.3s ease-out reverse';
                    setTimeout(() => notification.remove(), 300);
                }
            }, 4000);
        }

        // 更新页面标题和信息
        function updatePageInfo(folderName, videoCount = 0) {
            const displayName = folderName.includes('-') ? folderName.split('-')[1] : folderName;
            document.getElementById('courseTitle').innerHTML = `
                <i class="fas fa-play-circle"></i>
                ${displayName}
            `;
            document.getElementById('pageTitle').textContent = displayName + ' - 视频课程';
            document.getElementById('courseSubtitle').textContent = `${displayName} 系列课程视频`;

            if (videoCount > 0) {
                document.getElementById('total-videos').textContent = `${videoCount} 个视频`;
            }
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // 获取文件大小警告
        function getSizeWarning(bytes) {
            const mb = bytes / (1024 * 1024);
            if (mb > 500) {
                return { show: true, text: '大文件', icon: 'fas fa-exclamation-triangle' };
            } else if (mb > 100) {
                return { show: true, text: '中等文件', icon: 'fas fa-info-circle' };
            }
            return { show: false };
        }

        // 获取视频质量标签
        function getQualityBadge(filename) {
            const name = filename.toLowerCase();
            if (name.includes('4k') || name.includes('2160p')) {
                return '4K';
            } else if (name.includes('1080p') || name.includes('fhd')) {
                return 'HD';
            } else if (name.includes('720p') || name.includes('hd')) {
                return 'HD';
            }
            return 'SD';
        }

        // 估算视频时长（基于文件大小的粗略估算）
        function estimateDuration(bytes) {
            const mb = bytes / (1024 * 1024);
            const estimatedMinutes = Math.round(mb / 10); // 粗略估算：10MB ≈ 1分钟
            if (estimatedMinutes < 60) {
                return `${estimatedMinutes}分钟`;
            } else {
                const hours = Math.floor(estimatedMinutes / 60);
                const minutes = estimatedMinutes % 60;
                return `${hours}小时${minutes}分钟`;
            }
        }

        // 创建视频卡片
        function createVideoCard(video, index) {
            const card = document.createElement('a');
            card.className = 'video-card';
            card.href = getVideoPlayerUrl(video);

            // 添加加载动画延迟
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';

            // 处理视频名称，去掉第一个'-'前面的内容
            const displayfileName = video.name.includes('-')
                ? video.name.substring(video.name.indexOf('-') + 1).trim()
                : video.name;

            // 创建用于显示的名称（不含扩展名）
            const displayNameWithoutExt = displayfileName.replace(/\.[^/.]+$/, '');

            const sizeWarning = getSizeWarning(video.size);
            const qualityBadge = getQualityBadge(video.name);
            const estimatedDuration = estimateDuration(video.size);

            // 创建缩略图元素
            const thumbnailDiv = document.createElement('div');
            thumbnailDiv.className = 'video-thumbnail';

            const placeholder = document.createElement('div');
            placeholder.className = 'loading-placeholder';

            const img = document.createElement('img');
            img.src = getThumbnailUrl(video);
            img.alt = displayfileName;
            img.style.display = 'none';

            // 图片加载成功处理
            img.onload = function() {
                this.style.display = 'block';
                placeholder.style.display = 'none';
            };

            // 图片加载失败处理
            img.onerror = function() {
                this.style.display = 'none';
                placeholder.innerHTML = '<i class="fas fa-video" style="font-size: 3rem; color: #ccc;"></i>';
                placeholder.style.display = 'flex';
                placeholder.style.alignItems = 'center';
                placeholder.style.justifyContent = 'center';
            };

            thumbnailDiv.appendChild(placeholder);
            thumbnailDiv.appendChild(img);

            // 创建播放图标
            const playIcon = document.createElement('div');
            playIcon.className = 'play-icon';
            playIcon.innerHTML = `
                <svg width="24" height="24" viewBox="0 0 24 24" fill="white">
                    <path d="M8 5v14l11-7z"/>
                </svg>
            `;
            thumbnailDiv.appendChild(playIcon);

            // 创建时长标签
            const durationBadge = document.createElement('div');
            durationBadge.className = 'video-duration';
            durationBadge.textContent = estimatedDuration;
            thumbnailDiv.appendChild(durationBadge);

            // 创建质量标签
            const qualityBadgeEl = document.createElement('div');
            qualityBadgeEl.className = 'video-quality-badge';
            qualityBadgeEl.textContent = qualityBadge;
            thumbnailDiv.appendChild(qualityBadgeEl);

            // 创建视频信息区域
            const videoInfo = document.createElement('div');
            videoInfo.className = 'video-info';

            const title = document.createElement('h3');
            title.className = 'video-title';
            title.textContent = displayNameWithoutExt;
            videoInfo.appendChild(title);

            // 创建元数据区域
            const metaDiv = document.createElement('div');
            metaDiv.className = 'video-meta';

            const sizeDiv = document.createElement('div');
            sizeDiv.className = 'video-size';
            sizeDiv.innerHTML = `
                <i class="fas fa-hdd"></i>
                <span>${formatFileSize(video.size)}</span>
            `;
            metaDiv.appendChild(sizeDiv);

            // 添加大小警告
            if (sizeWarning.show) {
                const warningDiv = document.createElement('div');
                warningDiv.className = 'size-warning';
                warningDiv.innerHTML = `
                    <i class="${sizeWarning.icon}"></i>
                    <span>${sizeWarning.text}</span>
                `;
                metaDiv.appendChild(warningDiv);
            }

            videoInfo.appendChild(metaDiv);

            // 创建操作按钮区域
            const actionsDiv = document.createElement('div');
            actionsDiv.className = 'video-actions';

            const playBtn = document.createElement('button');
            playBtn.className = 'action-btn btn-play';
            playBtn.innerHTML = '<i class="fas fa-play"></i> 播放';
            playBtn.onclick = function(event) {
                event.preventDefault();
                playVideo(video.name);
            };

            const infoBtn = document.createElement('button');
            infoBtn.className = 'action-btn btn-info';
            infoBtn.innerHTML = '<i class="fas fa-info"></i> 详情';
            infoBtn.onclick = function(event) {
                event.preventDefault();
                showVideoInfo(displayNameWithoutExt, video.size);
            };

            actionsDiv.appendChild(playBtn);
            actionsDiv.appendChild(infoBtn);
            videoInfo.appendChild(actionsDiv);

            // 组装卡片
            card.appendChild(thumbnailDiv);
            card.appendChild(videoInfo);

            // 添加加载动画
            setTimeout(() => {
                card.style.transition = 'all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275)';
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, index * 100);

            return card;
        }

        // 播放视频
        function playVideo(videoName) {
            const video = { name: videoName };
            const url = getVideoPlayerUrl(video);
            window.open(url, '_blank');
        }

        // 显示视频详情
        function showVideoInfo(videoName, size) {
            const sizeInfo = formatFileSize(size);
            const quality = getQualityBadge(videoName);
            const duration = estimateDuration(size);

            // 创建详情内容
            const detailsContent = document.createElement('div');
            detailsContent.innerHTML = `
                <div style="font-weight: 600; margin-bottom: 10px; color: #667eea;">视频详情</div>
                <div style="margin-bottom: 8px;"><strong>名称：</strong>${videoName}</div>
                <div style="margin-bottom: 8px;"><strong>大小：</strong>${sizeInfo}</div>
                <div style="margin-bottom: 8px;"><strong>质量：</strong>${quality}</div>
                <div><strong>预估时长：</strong>${duration}</div>
            `;

            showNotification(detailsContent.innerHTML, 'info');
        }

        async function loadVideos() {
            try {
                const response = await fetch(`/api/videos?folder=${encodeURIComponent(currentFolder)}`);

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const videos = await response.json();
                const grid = document.getElementById('videoGrid');
                grid.innerHTML = '';

                if (videos.length === 0) {
                    grid.innerHTML = `
                        <div class="error-state">
                            <div class="error-icon">
                                <i class="fas fa-video-slash"></i>
                            </div>
                            <div class="error-title">暂无视频</div>
                            <div class="error-message">该合集中暂时没有视频内容</div>
                        </div>
                    `;
                    return;
                }

                // 更新页面信息
                updatePageInfo(currentFolder, videos.length);

                // 创建视频卡片
                videos.forEach((video, index) => {
                    const card = createVideoCard(video, index);
                    grid.appendChild(card);
                });

                // 显示成功消息
                setTimeout(() => {
                    showNotification(`成功加载 ${videos.length} 个视频`, 'success');
                }, 1000);

            } catch (error) {
                console.error('Error:', error);
                const grid = document.getElementById('videoGrid');
                grid.innerHTML = `
                    <div class="error-state">
                        <div class="error-icon">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="error-title">加载失败</div>
                        <div class="error-message">${error.message}</div>
                    </div>
                `;
                showNotification('加载视频列表失败，请刷新页面重试', 'error');
            }
        }

        // 修改缩略图 URL 构建
        function getThumbnailUrl(video) {
            return `/api/thumbnail/${encodeURIComponent(currentFolder)}/${encodeURIComponent(video.name)}?user=${encodeURIComponent(user)}&key=${encodeURIComponent(key)}&token=${encodeURIComponent(token)}`;
        }

        // 修改视频播放链接
        function getVideoPlayerUrl(video) {
            return `/api/player/?filename=${encodeURIComponent(video.name)}&folder=${encodeURIComponent(currentFolder)}&user=${encodeURIComponent(user)}&key=${encodeURIComponent(key)}&token=${encodeURIComponent(token)}`;
        }

        // 处理文件夹名称显示
        const displayName = currentFolder.includes('-') 
            ? currentFolder.split('-')[1] 
            : currentFolder;

        // 更新页面标题和标题栏
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化页面信息
            updatePageInfo(currentFolder);

            // 添加CSS动画样式
            const style = document.createElement('style');
            style.textContent = `
                @keyframes slideIn {
                    from {
                        transform: translateX(100%);
                        opacity: 0;
                    }
                    to {
                        transform: translateX(0);
                        opacity: 1;
                    }
                }
                .notification-close {
                    background: none;
                    border: none;
                    color: inherit;
                    cursor: pointer;
                    padding: 0;
                    margin-left: auto;
                    opacity: 0.7;
                    transition: opacity 0.2s;
                }
                .notification-close:hover {
                    opacity: 1;
                }
            `;
            document.head.appendChild(style);
            
            // 知识图谱相关功能
            const modal = document.getElementById('knowledgeGraphModal');
            const btn = document.getElementById('showKnowledgeGraphBtn');
            const closeBtn = document.querySelector('.close');
            const maximizeBtn = document.getElementById('maximizeGraphBtn');
            const downloadBtn = document.getElementById('downloadGraphBtn');
            
            // 辅助函数
            function getRandomColor() {
                return '#' + Math.floor(Math.random()*16777215).toString(16).padStart(6, '0');
            }
            
            function getComplementaryColor(hex) {
                // 将十六进制颜色转换为RGB
                var r = parseInt(hex.slice(1, 3), 16);
                var g = parseInt(hex.slice(3, 5), 16);
                var b = parseInt(hex.slice(5, 7), 16);
                
                // 计算补色
                r = 255 - r;
                g = 255 - g;
                b = 255 - b;
                
                // 转换回十六进制
                return '#' + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1);
            }
            
            function getDarkerColor(color) {
                // 将颜色转换为RGB
                var r = parseInt(color.slice(1, 3), 16);
                var g = parseInt(color.slice(3, 5), 16);
                var b = parseInt(color.slice(5, 7), 16);
                
                // 使颜色变深（这里我们减少了亮度）
                r = Math.max(0, r - 50);
                g = Math.max(0, g - 50);
                b = Math.max(0, b - 50);
                
                // 转换回十六进制
                return '#' + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1);
            }
            
            // 添加关系到图表数据的函数
            function addRelation(source, target, value, data, links) {
                function addOrUpdateNode(name) {
                    var node = data.find(item => item.name === name);
                    if (!node) {
                        var backgroundColor = getRandomColor();
                        var textColor = getComplementaryColor(backgroundColor);
                        node = {
                            name: name, 
                            symbolSize: 50,
                            itemStyle: {
                                color: backgroundColor,
                                borderColor: getDarkerColor(backgroundColor),
                                borderWidth: 2
                            },
                            label: {
                                color: textColor,
                                fontSize: 12
                            },
                            x: Math.random() * graphChart.getWidth(),
                            y: Math.random() * graphChart.getHeight()
                        };
                        data.push(node);
                    }
                    // 修改这里：进一步减小增长速度，使用更平缓的对数函数
                    var connectionCount = links.filter(link => 
                        link.source === name || link.target === name).length;
                    node.symbolSize = Math.min(120, 50 + 10 * Math.log(1 + connectionCount*3));
                    return node;
                }
                
                addOrUpdateNode(source);
                addOrUpdateNode(target);
                
                var linkColors = ['#8B0000', '#006400', '#00008B', '#8B4513', '#2F4F4F'];
                var existingLinkColor = links.find(link => link.value === value)?.lineStyle?.color;
                var linkColor = existingLinkColor || getDarkerColor(linkColors[links.length % linkColors.length]);
                
                links.push({
                    source: source,
                    target: target,
                    value: value,
                    lineStyle: {
                        color: linkColor,
                        width: 2
                    },
                    label: {
                        show: true,
                        formatter: function(params) {
                            var text = params.data.value;
                            var lines = [];
                            var maxLineLength = 20; // 每行最大字符数
                            for (var i = 0; i < text.length; i += maxLineLength) {
                                lines.push(text.substr(i, maxLineLength));
                            }
                            return lines.join('\n');
                        },
                        fontSize: 12,
                        color: '#000',
                        backgroundColor: 'rgba(255, 255, 255, 0.7)',
                        padding: [4, 8],
                        borderRadius: 4
                    }
                });
            }
            
            // 生成知识图谱函数
            function generateKnowledgeGraph() {
                // 设置加载中的状态
                graphChart.showLoading({
                    text: '正在生成课程知识图谱...',
                    color: '#4CAF50',
                    textColor: '#000',
                    maskColor: 'rgba(255, 255, 255, 0.8)',
                });
                
                // 准备请求参数
                var requestData = {
                    user: user,
                    key: key,
                    token: token,
                    folder: currentFolder
                };
                
                console.log("发送知识图谱请求:", {
                    ...requestData,
                    token: requestData.token ? (requestData.token.substring(0, 10) + '...') : null
                });
                
                // 添加重试计数器
                let retryCount = 0;
                const maxRetries = 2;
                
                function attemptGeneration() {
                    // 调用后端API
                    fetch('/api/merge_graphs', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Accept': 'application/json'
                        },
                        body: JSON.stringify(requestData)
                    })
                    .then(response => {
                        if (!response.ok) {
                            // 如果是服务器错误且还有重试次数，则重试
                            if (response.status >= 500 && retryCount < maxRetries) {
                                retryCount++;
                                console.log(`服务器错误，正在进行第${retryCount}次重试...`);
                                
                                // 延迟3秒后重试
                                setTimeout(attemptGeneration, 3000);
                                return Promise.reject(new Error(`服务器错误 (${response.status})，正在重试...`));
                            }
                            
                            throw new Error(`网络响应不正常: ${response.status}`);
                        }
                        return response.json();
                    })
                    .then(data => {
                        console.log("知识图谱API响应:", data);
                        
                        if (!data.success) {
                            throw new Error(data.message || '生成知识图谱失败');
                        }
                        
                        // 隐藏加载状态
                        graphChart.hideLoading();
                        
                        // 解析图谱数据
                        var graphData = data.graph;
                        var graphNodes = [];
                        var graphLinks = [];
                        
                        // 如果服务器返回了VIP状态，更新全局变量
                        if (data.if_vip_user !== undefined) {
                            if_vip_user = data.if_vip_user;
                        }
                        
                        // 处理图谱数据
                        if (graphData) {
                            var lines = graphData.split('\n');
                            console.log("解析图谱数据，行数:", lines.length);
                            
                            // 不限制关系数量，处理所有数据
                            const processedLines = lines;
                            
                            // 预先计算节点位置
                            const nodeMap = new Map(); // 用于存储节点信息
                            
                            // 第一遍：收集所有节点
                            for (var i = 0; i < processedLines.length; i++) {
                                var line = processedLines[i].trim();
                                if (!line) continue;
                                
                                var parts = line.split(',', 3);
                                if (parts.length === 3) {
                                    var source = parts[0].trim();
                                    var target = parts[1].trim();
                                    
                                    // 记录节点
                                    if (!nodeMap.has(source)) {
                                        nodeMap.set(source, {
                                            name: source,
                                            x: 0,
                                            y: 0,
                                            connections: 0
                                        });
                                    }
                                    if (!nodeMap.has(target)) {
                                        nodeMap.set(target, {
                                            name: target,
                                            x: 0,
                                            y: 0,
                                            connections: 0
                                        });
                                    }
                                    
                                    // 增加连接计数
                                    nodeMap.get(source).connections++;
                                    nodeMap.get(target).connections++;
                                }
                            }
                            
                            // 按连接数排序节点（从高到低）
                            const sortedNodes = Array.from(nodeMap.values())
                                .sort((a, b) => b.connections - a.connections);
                            
                            // 计算同心圆布局
                            const nodeCount = nodeMap.size;
                            const centerX = graphChart.getWidth() / 2;
                            const centerY = graphChart.getHeight() / 2;
                            const maxRadius = Math.min(graphChart.getWidth(), graphChart.getHeight()) * 0.45;
                            
                            // 确定同心圆的层数（根据节点数量）
                            const layerCount = Math.min(10, Math.max(3, Math.ceil(Math.sqrt(nodeCount) / 2)));
                            console.log(`使用${layerCount}层同心圆布局`);
                            
                            // 在第一遍处理中，修改节点布局算法
                            sortedNodes.forEach(node => {
                                // 这样连接数相近的节点会在同一层，连接数差异大的节点会在不同层
                                const maxConnections = sortedNodes[0].connections;
                                const connectionRatio = node.connections / maxConnections;
                                // 使用非线性映射，让连接数差异更明显地反映在层级上
                                const layerIndex = Math.min(layerCount - 1, 
                                    Math.floor((1 - Math.pow(connectionRatio, 0.7)) * layerCount));
                                
                                // 计算该层的半径 - 使用非线性增长，让外层更分散
                                const radiusRatio = (layerIndex + 1) / layerCount;
                                // 使用更强的幂函数，让外层节点（小实体）距离圆心更远
                                let radius = maxRadius * Math.pow(radiusRatio, 0.4); 
                                
                                // 修改：对连线数为1的节点，增加其距离中心的距离为原来的1.5倍
                                if (node.connections === 1) {
                                    radius = radius * 2.5;
                                }
                                
                                // 计算该层内的节点数和索引
                                // 先找出同层的所有节点
                                const nodesInSameLayer = sortedNodes.filter(n => 
                                    Math.floor((1 - Math.pow(n.connections / maxConnections, 0.7)) * layerCount) === layerIndex
                                );
                                const layerSize = nodesInSameLayer.length;
                                const indexInLayer = nodesInSameLayer.indexOf(node);
                                
                                // 计算角度（均匀分布在圆周上）- 添加随机偏移防止完全对齐
                                const baseAngle = (2 * Math.PI * indexInLayer) / layerSize;
                                // 增加随机偏移量，进一步防止重叠
                                const angleOffset = (Math.random() * 0.7 - 0.35) * (2 * Math.PI / layerSize);
                                const angle = baseAngle + angleOffset;
                                
                                // 计算坐标
                                node.x = centerX + radius * Math.cos(angle);
                                node.y = centerY + radius * Math.sin(angle);
                                
                                // 保存层级和连接数信息（用于调整大小）
                                node.layer = layerIndex;
                                node.radius = radius; // 保存半径信息，用于后续调整
                            });
                            
                            // 第二遍：确保相邻层的节点径向距离足够大
                            // 按层级对节点进行分组
                            const nodesByLayer = Array(layerCount).fill().map(() => []);
                            sortedNodes.forEach(node => {
                                nodesByLayer[node.layer].push(node);
                            });
                            
                            // 从内层到外层检查并调整节点
                            for (let layer = 0; layer < layerCount - 1; layer++) {
                                const currentLayerNodes = nodesByLayer[layer];
                                const nextLayerNodes = nodesByLayer[layer + 1];
                                
                                // 如果当前层或下一层没有节点，跳过
                                if (currentLayerNodes.length === 0 || nextLayerNodes.length === 0) continue;
                                
                                // 计算当前层的最大节点大小
                                const currentLayerMaxSize = Math.max(...currentLayerNodes.map(node => {
                                    // 计算节点大小 - 使用新的计算公式，减小为原来的一半
                                    const baseSize = 15; // 从30减小到15
                                    const connectionFactor = Math.pow(node.connections, 1/2.5);
                                    let size = Math.min(60, baseSize + 5 * connectionFactor); // 从120减小到60，从10减小到5
                                    
                                    // 确保最少连线的节点也有足够大的尺寸
                                    if (node.connections <= 1) {
                                        size = Math.max(size, 20); // 从40减小到20
                                    }
                                    
                                    // 根据层级微调大小
                                    size += Math.max(0, (10 - Math.min(10, node.layer)) * 1); // 从2减小到1
                                    
                                    return size;
                                }));
                                
                                // 计算下一层的最大节点大小
                                const nextLayerMaxSize = Math.max(...nextLayerNodes.map(node => {
                                    // 计算节点大小 - 使用新的计算公式，减小为原来的一半
                                    const baseSize = 15; // 从30减小到15
                                    const connectionFactor = Math.pow(node.connections, 1/2.5);
                                    let size = Math.min(60, baseSize + 5 * connectionFactor); // 从120减小到60，从10减小到5
                                    
                                    // 确保最少连线的节点也有足够大的尺寸
                                    if (node.connections <= 1) {
                                        size = Math.max(size, 20); // 从40减小到20
                                    }
                                    
                                    // 根据层级微调大小
                                    size += Math.max(0, (10 - Math.min(10, node.layer)) * 1); // 从2减小到1
                                    
                                    return size;
                                }));
                                
                                // 计算两层之间需要的最小径向距离
                                const minRadialDistance = (currentLayerMaxSize + nextLayerMaxSize) * 2;
                                
                                // 计算当前层的平均半径
                                const currentLayerAvgRadius = currentLayerNodes.reduce((sum, node) => sum + node.radius, 0) / currentLayerNodes.length;
                                
                                // 计算下一层的平均半径
                                const nextLayerAvgRadius = nextLayerNodes.reduce((sum, node) => sum + node.radius, 0) / nextLayerNodes.length;
                                
                                // 计算当前的径向距离
                                const currentRadialDistance = nextLayerAvgRadius - currentLayerAvgRadius;
                                
                                // 如果径向距离不够，调整外层节点的半径
                                if (currentRadialDistance < minRadialDistance) {
                                    console.log(`调整层 ${layer+1} 的径向距离: 当前=${currentRadialDistance}, 最小需要=${minRadialDistance}`);
                                    
                                    // 计算需要增加的距离
                                    const additionalDistance = minRadialDistance - currentRadialDistance;
                                    
                                    // 调整外层所有节点的半径
                                    nextLayerNodes.forEach(node => {
                                        // 计算新半径
                                        const newRadius = node.radius + additionalDistance;
                                        
                                        // 计算角度（相对于圆心）
                                        const angle = Math.atan2(node.y - centerY, node.x - centerX);
                                        
                                        // 更新坐标
                                        node.x = centerX + newRadius * Math.cos(angle);
                                        node.y = centerY + newRadius * Math.sin(angle);
                                        
                                        // 更新半径
                                        node.radius = newRadius;
                                        
                                        console.log(`调整节点 ${node.name} 的半径: ${node.radius} -> ${newRadius}`);
                                    });
                                }
                            }
                            
                            // 第三遍：处理连接数相同的相连节点，确保它们不在同一层
                            // 创建一个映射来存储已处理的节点对
                            const processedPairs = new Set();
                            
                            for (var i = 0; i < processedLines.length; i++) {
                                var line = processedLines[i].trim();
                                if (!line) continue;
                                
                                var parts = line.split(',', 3);
                                if (parts.length === 3) {
                                    var source = parts[0].trim();
                                    var target = parts[1].trim();
                                    
                                    // 检查是否已处理过这对节点
                                    const pairKey = source < target ? `${source}-${target}` : `${target}-${source}`;
                                    if (processedPairs.has(pairKey)) continue;
                                    processedPairs.add(pairKey);
                                    
                                    const sourceNode = nodeMap.get(source);
                                    const targetNode = nodeMap.get(target);
                                    
                                    // 如果两个节点连接数相同且在同一层，将其中一个移到外层
                                    if (sourceNode.connections === targetNode.connections && 
                                        sourceNode.layer === targetNode.layer) {
                                        // 选择一个节点移到外层（如果不是最外层）
                                        if (sourceNode.layer < layerCount - 1) {
                                            // 将源节点移到外一层
                                            const newLayer = sourceNode.layer + 1;
                                            sourceNode.layer = newLayer;
                                            
                                            // 计算新层的平均半径
                                            const newLayerNodes = nodesByLayer[newLayer];
                                            const newLayerAvgRadius = newLayerNodes.length > 0 
                                                ? newLayerNodes.reduce((sum, node) => sum + node.radius, 0) / newLayerNodes.length
                                                : maxRadius * Math.pow((newLayer + 1) / layerCount, 0.4);
                                            
                                            // 计算源节点和目标节点的大小
                                            const sourceSize = Math.min(50, 10 + 4 * Math.pow(sourceNode.connections, 1/3)); // 从100减小到50，从20减小到10，从8减小到4
                                            const targetSize = Math.min(50, 10 + 4 * Math.pow(targetNode.connections, 1/3)); // 从100减小到50，从20减小到10，从8减小到4
                                            
                                            // 确保新半径至少比当前半径大两个节点直径之和
                                            const minNewRadius = sourceNode.radius + (sourceSize + targetSize) * 2;
                                            const newRadius = Math.max(newLayerAvgRadius, minNewRadius);
                                            
                                            // 保持角度不变，只调整半径
                                            const angle = Math.atan2(
                                                sourceNode.y - centerY, 
                                                sourceNode.x - centerX
                                            );
                                            
                                            sourceNode.x = centerX + newRadius * Math.cos(angle);
                                            sourceNode.y = centerY + newRadius * Math.sin(angle);
                                            sourceNode.radius = newRadius;
                                            
                                            // 将节点添加到新层
                                            nodesByLayer[newLayer].push(sourceNode);
                                            
                                            console.log(`移动节点 ${source} 到外层 ${newLayer}, 新半径: ${newRadius}`);
                                        }
                                    }
                                    
                                    // 增加相邻连线实体之间的距离差
                                    // 计算两个节点的直径
                                    const sourceSize = Math.min(50, 10 + 4 * Math.pow(sourceNode.connections, 1/3)); // 从100减小到50，从20减小到10，从8减小到4
                                    const targetSize = Math.min(50, 10 + 4 * Math.pow(targetNode.connections, 1/3)); // 从100减小到50，从20减小到10，从8减小到4
                                    
                                    // 计算两个节点之间的距离
                                    const dx = targetNode.x - sourceNode.x;
                                    const dy = targetNode.y - sourceNode.y;
                                    const distance = Math.sqrt(dx * dx + dy * dy);
                                    
                                    // 计算最小所需距离（两个实体直径之和的2倍）
                                    const minDistance = (sourceSize + targetSize) * 2;
                                    
                                    // 如果距离小于最小所需距离，则调整位置
                                    if (distance < minDistance) {
                                        console.log(`调整节点距离: ${source} 和 ${target}, 当前距离: ${distance}, 最小所需距离: ${minDistance}`);
                                        
                                        // 计算单位向量
                                        const unitX = dx / distance;
                                        const unitY = dy / distance;
                                        
                                        // 计算需要增加的距离
                                        const additionalDistance = minDistance - distance;
                                        
                                        // 如果两个节点都不是中心节点，则平均分配移动距离
                                        if (sourceNode.layer > 0 && targetNode.layer > 0) {
                                            // 移动源节点
                                            sourceNode.x -= unitX * additionalDistance / 2;
                                            sourceNode.y -= unitY * additionalDistance / 2;
                                            
                                            // 移动目标节点
                                            targetNode.x += unitX * additionalDistance / 2;
                                            targetNode.y += unitY * additionalDistance / 2;
                                        } 
                                        // 如果其中一个是中心节点，则只移动另一个
                                        else if (sourceNode.layer === 0) {
                                            targetNode.x += unitX * additionalDistance;
                                            targetNode.y += unitY * additionalDistance;
                                        } else {
                                            sourceNode.x -= unitX * additionalDistance;
                                            sourceNode.y -= unitY * additionalDistance;
                                        }
                                    }
                                }
                            }
                            
                            // 第四遍：创建节点和连接，并保存最终位置到nodePositions
                            for (var i = 0; i < processedLines.length; i++) {
                                var line = processedLines[i].trim();
                                if (!line) continue;
                                
                                var parts = line.split(',', 3);
                                if (parts.length === 3) {
                                    var source = parts[0].trim();
                                    var target = parts[1].trim();
                                    var relation = parts[2].trim();
                                    
                                    // 提取关系文本（去除花括号）
                                    relation = relation.replace(/^\{|\}$/g, '');
                                    
                                    // 使用预计算的位置添加节点
                                    const sourceNode = nodeMap.get(source);
                                    const targetNode = nodeMap.get(target);
                                    
                                    // 保存最终位置到nodePositions
                                    nodePositions[source] = { x: sourceNode.x, y: sourceNode.y };
                                    nodePositions[target] = { x: targetNode.x, y: targetNode.y };
                                    
                                    // 添加节点和关系，传递nodeMap作为参数
                                    addRelationWithPosition(source, target, relation, graphNodes, graphLinks, 
                                        sourceNode.x, sourceNode.y, targetNode.x, targetNode.y, 
                                        sourceNode.connections, sourceNode.layer, nodeMap);
                                }
                            }
                        }
                        
                        // 修改添加关系的函数，接受nodeMap作为参数
                        function addRelationWithPosition(source, target, value, data, links, sourceX, sourceY, targetX, targetY, connections, layer, nodeMap) {
                            function addOrUpdateNode(name, x, y, connections, layer) {
                                var node = data.find(item => item.name === name);
                                if (!node) {
                                    // 根据连接数和层级计算颜色（内层节点颜色更深）
                                    var baseColor = getRandomColor();
                                    var backgroundColor = layer !== undefined ? adjustColorBrightness(baseColor, -layer * 10) : baseColor;
                                    var textColor = getComplementaryColor(backgroundColor);
                                    
                                    // 计算节点大小 - 减小为原来的一半
                                    var baseSize = 15; // 从30减小到15
                                    var connectionFactor = Math.pow(connections, 1/2.5);
                                    var size = Math.min(60, baseSize + 5 * connectionFactor); // 从120减小到60，从10减小到5
                                    
                                    // 确保最少连线的节点也有足够大的尺寸
                                    if (connections <= 1) {
                                        size = Math.max(size, 20); // 从40减小到20
                                    }
                                    
                                    // 根据层级微调大小
                                    if (layer !== undefined) {
                                        size += Math.max(0, (10 - Math.min(10, layer)) * 1); // 从2减小到1
                                    }
                                    
                                    // 使用存储的位置或初始位置
                                    var nodeX = nodePositions[name] ? nodePositions[name].x : x;
                                    var nodeY = nodePositions[name] ? nodePositions[name].y : y;
                                    
                                    // 存储初始位置
                                    if (!nodePositions[name]) {
                                        nodePositions[name] = { x: nodeX, y: nodeY };
                                    }
                                    
                                    node = {
                                        name: name, 
                                        symbolSize: size,
                                        itemStyle: {
                                            color: backgroundColor,
                                            borderColor: getDarkerColor(backgroundColor),
                                            borderWidth: 2
                                        },
                                        label: {
                                            color: textColor,
                                            fontSize: 10, // 从12减小到10
                                        },
                                        x: nodeX,
                                        y: nodeY,
                                        fixed: true, // 固定节点位置
                                        z: 10 // 提高z值，确保节点在连线上方
                                    };
                                    data.push(node);
                                }
                                return node;
                            }
                            
                            // 添加调整颜色亮度的辅助函数
                            function adjustColorBrightness(hex, percent) {
                                // 将十六进制颜色转换为RGB
                                var r = parseInt(hex.slice(1, 3), 16);
                                var g = parseInt(hex.slice(3, 5), 16);
                                var b = parseInt(hex.slice(5, 7), 16);
                                
                                // 调整亮度
                                r = Math.max(0, Math.min(255, r + percent));
                                g = Math.max(0, Math.min(255, g + percent));
                                b = Math.max(0, Math.min(255, b + percent));
                                
                                // 转换回十六进制
                                return '#' + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1);
                            }
                            
                            // 添加源节点
                            var sourceNode = addOrUpdateNode(source, sourceX, sourceY, connections, layer);
                            
                            // 安全地获取目标节点的连接数和层级
                            const targetConnections = nodeMap.get(target)?.connections || 0;
                            const targetLayer = nodeMap.get(target)?.layer || 0;
                            
                            // 添加目标节点
                            var targetNode = addOrUpdateNode(target, targetX, targetY, targetConnections, targetLayer);
                            
                            // 计算两个节点的直径
                            const sourceSize = sourceNode.symbolSize;
                            const targetSize = targetNode.symbolSize;
                            
                            // 计算两个节点之间的距离
                            const dx = targetX - sourceX;
                            const dy = targetY - sourceY;
                            const distance = Math.sqrt(dx * dx + dy * dy);
                            
                            // 计算最小所需距离（两个实体直径之和的2倍）
                            const minDistance = (sourceSize + targetSize) * 6;
                            
                            // 如果距离小于最小所需距离，则调整位置
                            if (distance < minDistance) {
                                console.log(`调整节点距离: ${source} 和 ${target}, 当前距离: ${distance}, 最小所需距离: ${minDistance}`);
                                
                                // 计算单位向量
                                const unitX = dx / distance;
                                const unitY = dy / distance;
                                
                                // 计算需要增加的距离
                                const additionalDistance = minDistance - distance;
                                
                                // 如果两个节点都不是中心节点，则平均分配移动距离
                                if (layer > 0 && targetLayer > 0) {
                                    // 移动源节点
                                    const newSourceX = sourceX - unitX * additionalDistance / 2;
                                    const newSourceY = sourceY - unitY * additionalDistance / 2;
                                    
                                    // 移动目标节点
                                    const newTargetX = targetX + unitX * additionalDistance / 2;
                                    const newTargetY = targetY + unitY * additionalDistance / 2;
                                    
                                    // 更新节点位置
                                    sourceNode.x = newSourceX;
                                    sourceNode.y = newSourceY;
                                    targetNode.x = newTargetX;
                                    targetNode.y = newTargetY;
                                    
                                    // 更新存储的位置
                                    nodePositions[source] = { x: newSourceX, y: newSourceY };
                                    nodePositions[target] = { x: newTargetX, y: newTargetY };
                                    
                                    console.log(`节点位置已调整: ${source}(${newSourceX},${newSourceY}) 和 ${target}(${newTargetX},${newTargetY})`);
                                } 
                                // 如果其中一个是中心节点，则只移动另一个
                                else if (layer === 0) {
                                    const newTargetX = targetX + unitX * additionalDistance;
                                    const newTargetY = targetY + unitY * additionalDistance;
                                    
                                    targetNode.x = newTargetX;
                                    targetNode.y = newTargetY;
                                    
                                    // 更新存储的位置
                                    nodePositions[target] = { x: newTargetX, y: newTargetY };
                                    
                                    console.log(`目标节点位置已调整: ${target}(${newTargetX},${newTargetY})`);
                                } else {
                                    const newSourceX = sourceX - unitX * additionalDistance;
                                    const newSourceY = sourceY - unitY * additionalDistance;
                                    
                                    sourceNode.x = newSourceX;
                                    sourceNode.y = newSourceY;
                                    
                                    // 更新存储的位置
                                    nodePositions[source] = { x: newSourceX, y: newSourceY };
                                    
                                    console.log(`源节点位置已调整: ${source}(${newSourceX},${newSourceY})`);
                                }
                            }
                            
                            // 根据关系类型选择不同的连线颜色
                            var linkColors = ['#8B0000', '#006400', '#00008B', '#8B4513', '#2F4F4F'];
                            var existingLinkColor = links.find(link => link.value === value)?.lineStyle?.color;
                            var linkColor = existingLinkColor || getDarkerColor(linkColors[links.length % linkColors.length]);
                            
                            links.push({
                                source: source,
                                target: target,
                                value: value,
                                lineStyle: {
                                    color: linkColor,
                                    width: 2,
                                    opacity: 0.7 // 默认连线透明度
                                },
                                emphasis: {
                                    lineStyle: {
                                        width: 4,
                                        opacity: 1
                                    },
                                    label: {
                                        show: true // 确保在高亮状态下显示标签
                                    }
                                },
                                label: {
                                    show: false, // 默认不显示标签
                                    formatter: function(params) {
                                        return params.data.value;
                                    },
                                    fontSize: 12,
                                    color: '#000',
                                    backgroundColor: 'rgba(255, 255, 255, 0.7)',
                                    padding: [4, 8],
                                    borderRadius: 4,
                                    z: 5 // 确保标签在连线上方但在节点下方
                                },
                                z: 1 // 设置连线的z值最低
                            });
                        }
                        
                        // 设置图表选项
                        var option = {
                            backgroundColor: '#FFFAFA',
                            title: {
                                text: displayName + ' 课程知识图谱',
                                subtext: `共 ${graphNodes.length} 个概念，${graphLinks.length} 个关系`,
                                top: 'top',
                                left: 'center'
                            },
                            tooltip: {
                                show: true,
                                formatter: function(params) {
                                    if (params.dataType === 'edge') {
                                        return params.data.value;
                                    }
                                    return params.name;
                                }
                            },
                            series: [{
                                type: 'graph',
                                layout: 'none', // 使用自定义布局，不使用力导向
                                roam: true,
                                draggable: true,
                                focusNodeAdjacency: true,
                                animation: false,
                                hoverAnimation: true,
                                // 完全禁用力导向布局
                                force: {
                                    layoutAnimation: false,
                                    repulsion: 0,
                                    gravity: 0,
                                    edgeLength: 0,
                                    friction: 1
                                },
                                emphasis: {
                                    focus: 'adjacency',
                                    lineStyle: {
                                        width: 4,
                                        opacity: 1
                                    },
                                    edgeLabel: {
                                        show: true
                                    },
                                    itemStyle: {
                                        shadowBlur: 10,
                                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                                    }
                                },
                                label: {
                                    show: true,
                                    position: 'inside',
                                    fontSize: 10
                                },
                                edgeLabel: {
                                    show: false,
                                    formatter: '{c}',
                                    fontSize: 12,
                                    color: '#000',
                                    backgroundColor: 'rgba(255, 255, 255, 0.7)',
                                    padding: [4, 8],
                                    borderRadius: 4
                                },
                                data: graphNodes,
                                links: graphLinks,
                                lineStyle: {
                                    curveness: 0.1,
                                    opacity: 0.7
                                }
                            }]
                        };
                        
                        // 应用图表选项
                        graphChart.setOption(option);
                        
                        // 在图表初始化完成后添加事件监听
                        graphChart.on('mouseover', function(params) {
                            if (params.dataType === 'node') {
                                // 当鼠标悬停在节点上时，显示与该节点相关的所有边的标签
                                var option = graphChart.getOption();
                                var links = option.series[0].links;
                                var updatedLinks = links.map(function(link) {
                                    if (link.source === params.name || link.target === params.name) {
                                        // 为与当前节点相关的边添加标签显示
                                        return {
                                            ...link,
                                            label: {
                                                ...link.label,
                                                show: true
                                            }
                                        };
                                    } else {
                                        // 其他边不显示标签
                                        return {
                                            ...link,
                                            label: {
                                                ...link.label,
                                                show: false
                                            }
                                        };
                                    }
                                });
                                
                                // 更新图表选项
                                graphChart.setOption({
                                    series: [{
                                        links: updatedLinks
                                    }]
                                });
                            }
                        });

                        graphChart.on('mouseout', function(params) {
                            if (params.dataType === 'node') {
                                // 当鼠标离开节点时，隐藏所有边的标签
                                var option = graphChart.getOption();
                                var links = option.series[0].links;
                                var updatedLinks = links.map(function(link) {
                                    return {
                                        ...link,
                                        label: {
                                            ...link.label,
                                            show: false
                                        }
                                    };
                                });
                                
                                // 更新图表选项
                                graphChart.setOption({
                                    series: [{
                                        links: updatedLinks
                                    }]
                                });
                            }
                        });

                        // 添加节点拖拽结束事件
                        graphChart.on('dragend', function(params) {
                            if (params.dataType === 'node') {
                                console.log('Node dragged:', params.name, 'to position:', params.event.offsetX, params.event.offsetY);
                                
                                // 更新存储的节点位置
                                nodePositions[params.name] = {
                                    x: params.event.offsetX,
                                    y: params.event.offsetY
                                };
                                
                                // 获取当前图表选项
                                var option = graphChart.getOption();
                                
                                // 更新所有节点位置
                                var updatedData = option.series[0].data.map(function(node) {
                                    if (node.name === params.name) {
                                        return {
                                            ...node,
                                            x: params.event.offsetX,
                                            y: params.event.offsetY,
                                            fixed: true
                                        };
                                    }
                                    // 确保其他节点也使用存储的位置
                                    if (nodePositions[node.name]) {
                                        return {
                                            ...node,
                                            x: nodePositions[node.name].x,
                                            y: nodePositions[node.name].y,
                                            fixed: true
                                        };
                                    }
                                    return node;
                                });
                                
                                // 应用更新
                                graphChart.setOption({
                                    series: [{
                                        data: updatedData,
                                        force: {
                                            layoutAnimation: false,
                                            repulsion: 0,
                                            gravity: 0,
                                            edgeLength: 0,
                                            friction: 1
                                        }
                                    }]
                                });
                                
                                console.log('Node positions updated:', nodePositions);
                            }
                        });

                        // 监听图表重绘事件，确保节点位置不变
                        graphChart.on('rendered', function() {
                            // 获取当前图表选项
                            var option = graphChart.getOption();
                            
                            // 检查是否需要更新节点位置
                            var needsUpdate = false;
                            var updatedData = option.series[0].data.map(function(node) {
                                if (nodePositions[node.name] && 
                                    (node.x !== nodePositions[node.name].x || 
                                     node.y !== nodePositions[node.name].y)) {
                                    
                                    console.log('Node position changed, restoring:', node.name);
                                    needsUpdate = true;
                                    return {
                                        ...node,
                                        x: nodePositions[node.name].x,
                                        y: nodePositions[node.name].y,
                                        fixed: true
                                    };
                                }
                                return node;
                            });
                            
                            // 如果有节点位置需要更新，重新应用选项
                            if (needsUpdate) {
                                graphChart.setOption({
                                    series: [{
                                        data: updatedData,
                                        force: {
                                            layoutAnimation: false,
                                            repulsion: 0,
                                            gravity: 0,
                                            edgeLength: 0,
                                            friction: 1
                                        }
                                    }]
                                });
                            }
                        });
                    })
                    .catch(error => {
                        console.error("生成知识图谱失败:", error);
                        
                        // 如果是重试中的错误，不显示失败UI
                        if (error.message && error.message.includes('正在重试')) {
                            return;
                        }
                        
                        graphChart.hideLoading();
                        graphChart.setOption({
                            title: {
                                text: '生成知识图谱失败',
                                subtext: error.message,
                                left: 'center',
                                top: 'center',
                                textStyle: {
                                    fontSize: 18,
                                    color: '#e74c3c'
                                },
                                subtextStyle: {
                                    fontSize: 14,
                                    color: '#7f8c8d'
                                }
                            }
                        });
                        
                        // 如果是服务器错误，提供重试选项
                        if (error.message && error.message.includes('500')) {
                            if (confirm("服务器暂时无法处理请求。是否稍后重试？")) {
                                setTimeout(function() {
                                    // 重置重试计数器
                                    retryCount = 0;
                                    attemptGeneration();
                                }, 5000);
                            }
                        }
                    });
                }
                
                // 开始第一次尝试
                attemptGeneration();
            }
            
            // 当用户点击按钮时，打开模态窗口并加载知识图谱
            btn.onclick = function() {
                console.log("知识图谱按钮被点击");
                modal.style.display = "block";
                
                // 如果图表已初始化，先销毁它
                if (graphChart) {
                    graphChart.dispose();
                    graphChart = null;
                }
                
                // 延迟初始化图表，确保DOM已完全渲染
                setTimeout(function() {
                    try {
                        graphChart = echarts.init(document.getElementById('knowledgeGraphContainer'), 'white', {renderer: 'canvas'});
                        console.log("图表初始化成功");
                        
                        // 设置加载中的状态
                        graphChart.showLoading({
                            text: '正在生成知识图谱...',
                            color: '#4CAF50',
                            textColor: '#000',
                            maskColor: 'rgba(255, 255, 255, 0.8)',
                        });
                        
                        // 调用后端API生成知识图谱
                        generateKnowledgeGraph();
                    } catch (error) {
                        console.error("图表初始化失败:", error);
                        alert("图表初始化失败: " + error.message);
                    }
                }, 300); // 添加300ms延迟确保DOM已渲染
                
                // 确保图表大小适应容器
                window.addEventListener('resize', function() {
                    if (graphChart) graphChart.resize();
                });
            };
            
            // 当用户点击关闭按钮时，关闭模态窗口
            closeBtn.onclick = function() {
                modal.style.display = "none";
                // 如果图表存在，销毁它以便下次重新创建
                if (graphChart) {
                    graphChart.dispose();
                    graphChart = null;
                }
            };
            
            // 当用户点击模态窗口外部时，关闭模态窗口
            window.onclick = function(event) {
                if (event.target == modal) {
                    modal.style.display = "none";
                    // 如果图表存在，销毁它以便下次重新创建
                    if (graphChart) {
                        graphChart.dispose();
                        graphChart = null;
                    }
                }
            };
            
            // 最大化/还原按钮功能
            maximizeBtn.addEventListener('click', function() {
                isMaximized = !isMaximized;
                if (isMaximized) {
                    // 最大化
                    modal.classList.add('modal-maximized');
                    maximizeBtn.textContent = '还原';
                } else {
                    // 还原
                    modal.classList.remove('modal-maximized');
                    maximizeBtn.textContent = '最大化';
                }
                
                // 延迟一点时间后调整图表大小，确保DOM已更新
                setTimeout(function() {
                    if (graphChart) {
                        graphChart.resize();
                    }
                }, 300);
            });
            
            // 添加下载图片按钮功能
            downloadBtn.addEventListener('click', function() {
                if (!graphChart) {
                    alert('图表尚未加载完成，请稍后再试');
                    return;
                }
                
                try {
                    // 获取当前文件夹名作为图片文件名的一部分
                    var filename = currentFolder || 'course';
                    // 添加时间戳
                    var timestamp = new Date().toISOString().replace(/[:.]/g, '-').substring(0, 19);
                    var imgName = `${filename}_graph_${timestamp}.png`;
                    
                    // 获取图表的数据URL
                    var url = graphChart.getDataURL({
                        type: 'png',
                        pixelRatio: 2, // 使用2倍像素比以获得更高质量的图像
                        backgroundColor: '#fff'
                    });
                    
                    // 创建下载链接
                    var link = document.createElement('a');
                    link.download = imgName;
                    link.href = url;
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                } catch (error) {
                    console.error('下载图片失败:', error);
                    alert('下载图片失败: ' + error.message);
                }
            });
        });

        loadVideos();
    </script>
</body>
</html>