# 工具栏插入 eqnarray 环境测试

## 测试说明

本文档用于测试博客编辑器工具栏中的 eqnarray 环境插入功能。

## 预期行为

### 1. 点击"公式组"按钮应该插入：
```
\begin{eqnarray}
a &= b \\
c &= d
\end{eqnarray}
```

### 2. 点击"公式组(无编号)"按钮应该插入：
```
\begin{eqnarray*}
a &= b \\
c &= d
\end{eqnarray*}
```

## 测试步骤

1. 打开 blog_page.html
2. 点击数学公式工具栏按钮（∑ 符号）
3. 在下拉菜单中找到"公式组"和"公式组(无编号)"按钮
4. 点击按钮，验证插入的内容是否正确
5. 确认插入的内容没有被 $$ 符号包围
6. 在预览面板中验证渲染效果

## 验证要点

✅ 插入的 eqnarray 环境不包含 $$ 符号  
✅ 插入的内容格式正确  
✅ 预览面板能正确渲染公式组  
✅ 支持带编号和无编号两种模式  
✅ 其他数学公式仍然正常添加 $$ 符号  

## 示例渲染效果

插入后应该能看到类似这样的渲染效果：

### 带编号的公式组
\begin{eqnarray}
E &= mc^2 \\
F &= ma \\
P &= \frac{F}{A}
\end{eqnarray}

### 无编号的公式组
\begin{eqnarray*}
x &= a + b \\
y &= c + d \\
z &= e + f
\end{eqnarray*}
